-- Create ENUM types for PostgreSQL
CREATE TYPE organizational_unit_type AS ENUM ('organization', 'division', 'department', 'team', 'squad', 'unit');
CREATE TYPE skillset_category AS ENUM ('programming', 'infrastructure', 'database', 'cloud', 'security', 'devops', 'networking', 'frontend', 'backend', 'mobile', 'data', 'ai_ml', 'project_management', 'soft_skills');
CREATE TYPE skill_level AS ENUM ('beginner', 'intermediate', 'advanced', 'expert');
CREATE TYPE project_status AS ENUM ('planning', 'active', 'on_hold', 'completed', 'cancelled');
CREATE TYPE project_priority AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE assignment_role AS ENUM ('lead', 'developer', 'tester', 'analyst', 'designer', 'consultant');
CREATE TYPE assessment_status AS ENUM ('draft', 'active', 'archived');
CREATE TYPE assessment_type AS ENUM ('performance', 'skills', 'feedback', 'goal_setting');
CREATE TYPE rule_type AS ENUM ('bonus', 'penalty', 'threshold', 'conditional');
CREATE TYPE instance_status AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');
CREATE TYPE criteria_level_type AS ENUM ('hr_level', 'organizational_level', 'team_level');
CREATE TYPE criteria_type AS ENUM ('performance', 'behavior', 'skill', 'goal');
CREATE TYPE kpi_type AS ENUM ('percentage', 'number', 'currency', 'ratio', 'boolean');
CREATE TYPE traffic_light_status AS ENUM ('green', 'yellow', 'red');
CREATE TYPE submission_status AS ENUM ('draft', 'submitted', 'approved', 'rejected');
