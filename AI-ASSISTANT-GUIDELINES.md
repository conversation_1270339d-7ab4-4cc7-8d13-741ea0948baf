# 🤖 AI Assistant Guidelines for eHRx Project

## 🚨 CRITICAL SYSTEM PROTECTION 🚨

**NEVER modify, uncomment, or change ANY of the following systems without EXPLICIT permission:**

- ❌ **Authentication/login logic** (auth modules, services, controllers)
- ❌ **User management and role systems** 
- ❌ **Security configurations and middleware**
- ❌ **Database schema or migrations**
- ❌ **LoggingModule imports/exports** (causes dependency injection issues)
- ❌ **JWT configuration or security settings**
- ❌ **Password hashing or validation logic**
- ❌ **Session management**
- ❌ **CORS configuration**
- ❌ **Direct SQL calls or database queries** (always use API endpoints)
- ❌ **Mock data creation** (create real data in database instead)

### 🔒 **INSTEAD: Always Follow This Process**

1. **ANALYZE** the issue and identify the root cause
2. **REPORT** your findings to the user with detailed explanation
3. **ASK** for explicit permission: "Should I modify [system name]?"
4. **WAIT** for approval before making ANY changes to protected systems
5. **IMPLEMENT** only after receiving clear permission

### ⚠️ **REASON FOR THIS RULE**

These systems are working and modifications can cause cascading failures that take hours to debug and restore. Authentication issues can lock users out of the entire system.

---

## � CRITICAL DATA & API RULES

### 📡 **API-First Architecture**

**NEVER make direct SQL calls or database queries.** Always use API endpoints.

✅ **Correct Approach:**
```typescript
// Use existing API endpoint
const users = await fetch('/api/users').then(r => r.json());

// If endpoint doesn't exist, create it in backend first
// backend/src/users/users.controller.ts
@Get('active')
async getActiveUsers() {
  return this.usersService.findActiveUsers();
}
```

❌ **WRONG - Never Do This:**
```sql
-- Never write direct SQL
SELECT * FROM users WHERE status = 'active';
```

**If API endpoint is missing:**
1. **Create the endpoint** in the appropriate backend controller
2. **Implement the service method** using TypeORM repositories
3. **Test the endpoint** before using it in frontend
4. **Document the new endpoint** in comments

### 🎭 **Real Data, Not Mock Data**

**NEVER create mock data or hardcoded arrays.** Create real example data in the database.

✅ **Correct Approach:**
```sql
-- Create real data in database/sample-data.sql
INSERT INTO users (email, name, role, status) VALUES
('<EMAIL>', 'John Doe', 'manager', 'active'),
('<EMAIL>', 'Jane Smith', 'employee', 'active');
```

❌ **WRONG - Never Do This:**
```typescript
// Never create mock data in code
const mockUsers = [
  { id: 1, name: 'John Doe', role: 'manager' },
  { id: 2, name: 'Jane Smith', role: 'employee' }
];
```

**For example/demo data:**
1. **Create SQL insert statements** in `database/` folder
2. **Use real-looking data** that represents actual use cases
3. **Follow existing data patterns** and relationships
4. **Test with the real data** to ensure it works properly

---

## �📋 General Development Guidelines

### 🎯 **Fundamental Principles**

- Write clean, simple, readable code
- Reliability is the top priority – if you can't make it reliable, don't build it
- Implement features in the simplest possible way
- Keep files small and focused (<500 lines)
- Test after every meaningful change
- Focus on core functionality before optimization
- Use clear, consistent naming
- Think thoroughly before coding. Write 2–3 reasoning paragraphs.

### 🔧 **Error Fixing Process**

- Consider multiple possible causes before deciding. Do not jump to conclusions.
- Explain the problem in plain English
- Make minimal necessary changes, changing as few lines of code as possible
- Always verify the fix
- In case of strange errors, ask the user to perform a ChatGPT web search to find the latest up-to-date information

### 🏗️ **Building Process**

- Understand requirements completely before starting
- Plan the next steps in detail
- Focus on one step at a time
- Document all changes and their reasoning
- Verify each new feature works by telling the user how to test it

### 📦 **Package Management**

- **Always use package managers** for installing, updating, or removing dependencies
- **Never manually edit** package.json, requirements.txt, Cargo.toml, go.mod, etc.
- Use appropriate commands: `npm install`, `pip install`, `cargo add`, etc.
- Package managers handle version resolution and dependency conflicts automatically

### 🧪 **Testing Guidelines**

- Write unit tests for new functionality
- Suggest testing approaches to the user
- Run tests after making changes
- Focus on making tests pass through iteration

---

## 🚀 Project-Specific Guidelines

### 📁 **Project Structure**

- **Frontend**: React application on port 3080
- **Backend**: NestJS application on port 4000  
- **Database**: MySQL with TypeORM
- **Services**: Use `start-all-services.sh` and `stop-all-services.sh`

### 🔐 **Security & Compliance**

- This project follows **NIS2 compliance** requirements
- All security-related changes require explicit approval
- Audit logging is critical for compliance
- Never store sensitive data in localStorage
- Always use secure backend APIs for data access

### 🗃️ **Database Guidelines**

- **NEVER make direct SQL calls** - always use API endpoints
- **If API endpoint is missing** - create the endpoint in the backend first
- **NEVER create mock data** - create real example data in the database instead
- Use TypeORM repository patterns in backend only
- No direct database calls in frontend code
- All database access goes through backend APIs
- Respect existing entity relationships

### 🎨 **Frontend Guidelines**

- Use Material-UI components consistently
- Follow established theming patterns
- Maintain responsive design principles
- Keep components focused and reusable

### 🔧 **Backend Guidelines**

- Follow NestJS module structure
- Use dependency injection properly
- Implement proper error handling
- Maintain API documentation

---

## ⚡ Service Management

### 🚀 **Starting Services**

```bash
# Use the established service management script
./start-all-services.sh
```

### 🛑 **Stopping Services**

```bash
# Use the established service management script  
./stop-all-services.sh
```

### 🔍 **Debugging Services**

```bash
# Check service status
ps aux | grep -E "(node|npm)" | grep -v grep

# Check port usage
lsof -i :3080  # Frontend
lsof -i :4000  # Backend

# Kill processes by port if needed
lsof -ti:4000 | xargs kill -9
```

---

## 📞 **When to Ask for Help**

- Before modifying any protected systems (see Critical System Protection above)
- When encountering authentication or security-related issues
- Before making database schema changes
- When unsure about compliance requirements
- If you find yourself going in circles or repeating the same actions
- Before installing new dependencies
- When deployment or production changes are needed

---

## ✅ **Success Criteria**

- Changes work reliably without breaking existing functionality
- Authentication and security systems remain intact
- All tests pass
- User can successfully use the application
- No cascading failures or system-wide issues
- Compliance requirements are maintained

---

*Remember: It's better to ask for permission and get it right the first time than to break working systems and spend hours fixing them.*
