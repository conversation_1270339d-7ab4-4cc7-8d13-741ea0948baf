# eHRx Database API Documentation

## Overview

This document provides comprehensive documentation for the eHRx Employee Performance Management Dashboard database API after the successful migration from MariaDB to PostgreSQL.

## Migration Summary

### ✅ **Migration Completed Successfully**

- **From**: MariaDB/MySQL
- **To**: PostgreSQL 
- **Status**: All database calls verified and working
- **Schema**: Fully converted with PostgreSQL-specific optimizations

### Key Migration Changes

1. **Database Type**: Changed from `mysql` to `postgres`
2. **Port**: Updated from `3306` to `5432`
3. **Data Types**: 
   - `AUTO_INCREMENT` → `SERIAL`
   - `TINYINT` → `BOOLEAN`
   - `JSON` → `JSONB` (better performance)
   - `DATETIME(6)` → `TIMESTAMP(6)`
4. **Constraints**: Added proper named constraints
5. **Indexes**: Converted to PostgreSQL syntax
6. **ENUM Types**: Created proper PostgreSQL ENUM types
7. **Triggers**: Added automatic `updated_at` triggers

## Database Configuration

### Environment Variables
```bash
# PostgreSQL Configuration
DB_HOST=127.0.0.1
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_secure_password
DB_NAME=ehrx
```

### Connection Settings
- **Type**: PostgreSQL
- **Connection Pool**: Optimized for production
- **SSL**: Enabled in production
- **Synchronize**: Disabled (security best practice)

## Database Schema

### Core Tables

#### 1. Users Table
- **Purpose**: User accounts and authentication
- **Key Features**: NIS2-compliant security, role-based access
- **Columns**: 
  - `id` (SERIAL PRIMARY KEY)
  - `email` (VARCHAR, UNIQUE)
  - `password` (VARCHAR, hashed)
  - `role` (user_role ENUM)
  - `account_status` (account_status ENUM)
  - Security fields (MFA, session management)

#### 2. Organizational Units Table
- **Purpose**: Company structure and hierarchy
- **Key Features**: Infinite hierarchy support
- **Columns**:
  - `id` (SERIAL PRIMARY KEY)
  - `name` (VARCHAR)
  - `type` (organizational_unit_type ENUM)
  - `parent_id` (INTEGER, self-referencing)

#### 3. Assessment System Tables
- **assessment_templates**: Template definitions
- **assessment_areas**: Assessment criteria areas
- **assessment_instances**: Individual assessments
- **assessment_responses**: Assessment results

#### 4. Team Management Tables
- **teams**: Team definitions
- **team_members**: Team membership
- **skillsets**: Skill definitions
- **user_skillsets**: User skill assignments

#### 5. Performance Management Tables
- **analytics_dashboards**: Dashboard configurations
- **performance_metrics**: Performance data
- **recognition_badges**: Achievement system
- **recognition_instances**: Badge awards

## API Endpoints

### Authentication Endpoints

#### POST /api/auth/login
- **Purpose**: User authentication
- **Authentication**: None required
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**:
  ```json
  {
    "access_token": "jwt_token",
    "refresh_token": "refresh_token",
    "user": { "id": 1, "email": "<EMAIL>", "role": "employee" }
  }
  ```

#### POST /api/auth/refresh
- **Purpose**: Refresh JWT token
- **Authentication**: Refresh token required
- **Request Body**:
  ```json
  {
    "refreshToken": "refresh_token_here"
  }
  ```

#### GET /api/auth/profile
- **Purpose**: Get current user profile
- **Authentication**: JWT required
- **Roles**: All authenticated users

### User Management Endpoints

#### GET /api/users
- **Purpose**: Get all users
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER
- **Response**: Array of user objects

#### POST /api/users
- **Purpose**: Create new user
- **Authentication**: JWT required
- **Roles**: HR_ADMIN
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "employee",
    "organizationalUnitId": 1
  }
  ```

#### GET /api/users/:id
- **Purpose**: Get specific user
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER (own team), EMPLOYEE (self only)

#### PATCH /api/users/:id
- **Purpose**: Update user
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER (limited), EMPLOYEE (profile only)

#### DELETE /api/users/:id
- **Purpose**: Delete user
- **Authentication**: JWT required
- **Roles**: HR_ADMIN only

#### GET /api/users/search/advanced
- **Purpose**: Advanced user search
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER
- **Query Parameters**:
  - `role`: Filter by role
  - `isActive`: Filter by active status
  - `search`: Text search
  - `page`: Pagination
  - `limit`: Results per page

### Team Management Endpoints

#### GET /api/teams
- **Purpose**: Get all teams (filtered by role)
- **Authentication**: JWT required
- **Roles**: All authenticated users

#### POST /api/teams
- **Purpose**: Create new team
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER

#### GET /api/teams/:id
- **Purpose**: Get specific team
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER, team members

#### GET /api/teams/:id/members
- **Purpose**: Get team members
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER, team members

#### POST /api/teams/:id/members
- **Purpose**: Add team member
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, team managers

### Assessment Endpoints

#### GET /api/assessments/templates
- **Purpose**: Get assessment templates
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER

#### POST /api/assessments/templates
- **Purpose**: Create assessment template
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER

#### GET /api/assessments/employees/:employeeId
- **Purpose**: Get employee assessment data
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER, EMPLOYEE (self only)

#### POST /api/assessments/employees/:employeeId/assessments
- **Purpose**: Create assessment for employee
- **Authentication**: JWT required
- **Roles**: HR_ADMIN, MANAGER

### Database Health Endpoints

#### GET /api/database/health
- **Purpose**: Database health check
- **Authentication**: JWT required
- **Roles**: HR_ADMIN only
- **Response**:
  ```json
  {
    "success": true,
    "status": "healthy",
    "data": {
      "tables": [...],
      "totalRecords": 1234,
      "connectionStatus": "active"
    }
  }
  ```

#### GET /api/database/tables
- **Purpose**: Get available tables metadata
- **Authentication**: JWT required
- **Roles**: HR_ADMIN only

## Security Features

### Authentication & Authorization
- **JWT Tokens**: Secure token-based authentication
- **Role-Based Access Control**: Fine-grained permissions
- **Session Management**: Secure session handling
- **MFA Support**: Two-factor authentication ready

### Data Protection
- **Password Hashing**: bcrypt with salt
- **Data Encryption**: AES-256-GCM for sensitive data
- **SQL Injection Protection**: Parameterized queries via TypeORM
- **XSS Protection**: Input validation and sanitization

### Compliance
- **NIS2 Compliant**: Enhanced security features
- **GDPR Ready**: Data retention and anonymization
- **Audit Logging**: Comprehensive activity tracking

## Error Handling

### Standard Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "statusCode": 400,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Performance Optimizations

### Database Level
- **Connection Pooling**: Optimized pool settings
- **Indexes**: Strategic indexing for performance
- **Query Optimization**: Efficient queries via TypeORM
- **JSONB**: Better performance than JSON

### API Level
- **Rate Limiting**: Prevents abuse
- **Caching**: Query result caching
- **Pagination**: Large dataset handling
- **Compression**: Response compression

## Testing & Validation

### Database Connection Test
```bash
# Test database connectivity
curl -H "Authorization: Bearer <token>" \
     http://localhost:4000/api/database/health
```

### API Endpoint Test
```bash
# Test user endpoint
curl -H "Authorization: Bearer <token>" \
     http://localhost:4000/api/users/count
```

## Migration Verification

### ✅ Verified Components
1. **Database Schema**: All tables created successfully
2. **TypeORM Entities**: All entities compatible with PostgreSQL
3. **API Endpoints**: All endpoints tested and working
4. **Authentication**: JWT and session management working
5. **Role-Based Access**: Permissions enforced correctly
6. **Data Types**: All PostgreSQL data types working
7. **Constraints**: Foreign keys and constraints active
8. **Indexes**: Performance indexes created
9. **Sample Data**: Test data loads successfully

### 🔧 Fixed Issues
1. **MySQL Syntax**: Converted to PostgreSQL
2. **Data Types**: Updated for PostgreSQL compatibility
3. **ENUM Types**: Created proper PostgreSQL ENUMs
4. **Auto-increment**: Changed to SERIAL
5. **Boolean Types**: Updated from TINYINT
6. **JSON Types**: Upgraded to JSONB
7. **Constraints**: Added proper named constraints
8. **Triggers**: Added updated_at triggers

## Deployment Notes

### Prerequisites
- PostgreSQL 12+ installed
- Node.js 18+ with npm
- Environment variables configured

### Database Setup
1. Create PostgreSQL database
2. Run `database/init.sql` script
3. Verify schema creation
4. Load sample data (optional)

### Application Startup
1. Install dependencies: `npm install`
2. Build application: `npm run build`
3. Start application: `npm start`
4. Verify API: `http://localhost:4000/api/docs`

## Support & Maintenance

### Monitoring
- Database health endpoint available
- API metrics tracking
- Performance monitoring
- Error logging

### Backup Strategy
- Regular database backups
- Schema versioning
- Data retention policies
- Recovery procedures

---

**Status**: ✅ Migration Complete - All database API calls verified and working
**Last Updated**: 2024-01-01
**Version**: 1.0.0 (PostgreSQL)
