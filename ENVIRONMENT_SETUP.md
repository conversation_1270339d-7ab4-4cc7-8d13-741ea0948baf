# 🔐 eHRx Environment Configuration Guide

This guide explains how to configure environment variables for the eHRx application with enterprise-grade security, encryption, and compliance features.

## 📋 Quick Setup

### Development Environment

1. **Backend Configuration**:
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your development settings
   ```

2. **Frontend Configuration**:
   ```bash
   cd frontend
   cp .env.example .env
   # Edit .env with your development settings
   ```

3. **Generate Development Secrets** (Optional):
   ```bash
   cd backend
   npm run secrets:generate
   # Copy generated secrets to your .env file
   ```

### Production Environment

1. **Generate Production Secrets**:
   ```bash
   cd backend
   npm run secrets:generate
   ```

2. **Review Generated Files**:
   - `backend/production-secrets/.env.production` - Complete environment template
   - `backend/production-secrets/secrets-manifest.json` - Secrets inventory
   - Individual `.secret` files for secure distribution

3. **Deploy Secrets Securely**:
   - Use your cloud provider's secrets management service
   - Never commit secrets to version control
   - Rotate secrets every 90 days

## 🔐 Critical Environment Variables

### Backend (.env)

#### **Database Configuration**
```bash
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=your_db_username
DB_PASSWORD=your_secure_db_password
DB_NAME=ehrx
```

#### **Encryption Configuration** ⚠️ CRITICAL
```bash
# Master encryption key for data at rest (AES-256-GCM)
# Generate with: openssl rand -base64 32
ENCRYPTION_MASTER_KEY=your_32_byte_base64_encoded_master_encryption_key

# Salt for PBKDF2 key derivation
# Generate with: openssl rand -base64 16
ENCRYPTION_SALT=your_16_byte_base64_encoded_salt

# Key derivation iterations (recommended: 100000+)
ENCRYPTION_ITERATIONS=100000
```

#### **JWT Authentication**
```bash
# JWT secret (minimum 32 characters)
# Generate with: openssl rand -base64 32
JWT_SECRET=your_jwt_secret_key_minimum_32_characters_long
JWT_EXPIRATION=3600
```

#### **Security Configuration**
```bash
# Session timeout in milliseconds (30 minutes)
SESSION_TIMEOUT=1800000

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Security headers
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_CSP_ENABLED=true
```

#### **Compliance Configuration**
```bash
# GDPR settings
GDPR_DATA_RETENTION_DAYS=2555
GDPR_ANONYMIZATION_ENABLED=true
GDPR_AUDIT_RETENTION_DAYS=3650

# SOC2 settings
SOC2_AUDIT_ENABLED=true
SOC2_MONITORING_ENABLED=true
SOC2_INCIDENT_REPORTING=true

# NIS2 settings
NIS2_SECURITY_MONITORING=true
NIS2_INCIDENT_RESPONSE=true
NIS2_VULNERABILITY_MANAGEMENT=true
```

### Frontend (.env)

```bash
# API Configuration
REACT_APP_API_URL=http://localhost:4000/api
REACT_APP_ENV=development

# Security Settings
REACT_APP_CSP_ENABLED=true
REACT_APP_SESSION_TIMEOUT=1800000
REACT_APP_RATE_LIMIT_WINDOW=60000
REACT_APP_RATE_LIMIT_MAX_REQUESTS=100

# Compliance Features
REACT_APP_GDPR_ENABLED=true
REACT_APP_PRIVACY_MODE_AVAILABLE=true
REACT_APP_AUDIT_LOGGING=true

# Feature Flags
REACT_APP_FEATURE_COMPLIANCE_DASHBOARD=true
REACT_APP_FEATURE_SECURITY_MONITORING=true
REACT_APP_FEATURE_AUDIT_TRAIL=true
```

## 🛠️ Available Scripts

### Backend Scripts

```bash
# Generate production secrets
npm run secrets:generate

# Validate configuration
npm run config:validate

# Complete production setup
npm run production:setup

# Security validation
npm run security:full
```

### Environment Validation

The application automatically validates critical configuration on startup:

- ✅ JWT secret length (minimum 32 characters)
- ✅ Encryption key presence and format
- ✅ Database credentials
- ✅ Production-specific security checks
- ✅ Compliance settings validation

## 🔒 Security Best Practices

### 1. **Secret Management**
- Use environment-specific secrets management systems
- Never commit `.env` files to version control
- Rotate encryption keys every 90 days
- Use strong, unique passwords and keys

### 2. **Production Deployment**
- Use HTTPS for all production URLs
- Enable all security headers
- Configure proper CORS settings
- Set up monitoring and alerting

### 3. **Compliance Requirements**
- Enable audit logging in production
- Configure data retention policies
- Set up compliance monitoring
- Regular security assessments

### 4. **Backup and Recovery**
- Backup encryption keys securely
- Test disaster recovery procedures
- Document key rotation procedures
- Maintain compliance audit trails

## 🚨 Troubleshooting

### Common Issues

1. **"ENCRYPTION_MASTER_KEY is required"**
   - Generate a new key: `openssl rand -base64 32`
   - Add to your `.env` file

2. **"JWT_SECRET must be at least 32 characters"**
   - Generate a new secret: `openssl rand -base64 32`
   - Update JWT_SECRET in `.env`

3. **Database connection errors**
   - Verify DB_HOST, DB_PORT, DB_USERNAME, DB_PASSWORD
   - Ensure database server is running
   - Check network connectivity

4. **Configuration validation errors**
   - Run `npm run config:validate` to see specific issues
   - Check the console output for detailed error messages

### Validation Commands

```bash
# Check backend configuration
cd backend && npm run config:validate

# Validate security settings
cd backend && npm run security:validate

# Test database connection
cd backend && npm run build && node -e "
  require('./dist/config/environment.config');
  console.log('✅ Configuration valid');
"
```

## 📞 Support

For security-related issues or questions about environment configuration:

1. Check the application logs for detailed error messages
2. Verify all required environment variables are set
3. Ensure proper file permissions on secret files
4. Review the security validation output

## 🔄 Regular Maintenance

### Monthly Tasks
- [ ] Review security logs
- [ ] Check for configuration updates
- [ ] Validate backup procedures

### Quarterly Tasks
- [ ] Rotate encryption keys
- [ ] Update security certificates
- [ ] Review compliance settings
- [ ] Security assessment

### Annual Tasks
- [ ] Complete security audit
- [ ] Update compliance documentation
- [ ] Review and update security policies
- [ ] Disaster recovery testing
