# Manager Dashboard Integration Test Guide

## 🧪 Testing the Complete Implementation

This guide provides step-by-step instructions to test the Manager Dashboard implementation end-to-end.

## Prerequisites

1. **Database Setup**
   ```bash
   # Apply the migration
   psql -d ehrx -f database/migrations/05-manager-dashboard-schema.sql
   ```

2. **Backend Running**
   ```bash
   cd backend
   npm run start:dev
   ```

3. **Frontend Running**
   ```bash
   cd frontend
   npm start
   ```

## 🔍 Backend API Testing

### 1. Test Authentication
```bash
# Login to get JWT token
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# Save the token for subsequent requests
export JWT_TOKEN="your_jwt_token_here"
```

### 2. Test Manager Dashboard Endpoints

#### Get Dashboard Metrics
```bash
curl -X GET "http://localhost:3000/api/analytics/manager-dashboard/metrics?managerId=1" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

#### Create New Metrics
```bash
curl -X POST http://localhost:3000/api/analytics/manager-dashboard/metrics \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationalUnitId": 1,
    "managerId": 1,
    "reportingPeriod": "2024-01-01",
    "fteCount": 10.5,
    "attritionResigned": 1,
    "attritionInvoluntary": 0,
    "slaPercentage": 95.5,
    "utilizationPercentage": 87.2,
    "status": "draft"
  }'
```

#### Update Metrics
```bash
curl -X PUT http://localhost:3000/api/analytics/manager-dashboard/metrics/1 \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "fteCount": 12.0,
    "slaPercentage": 96.0,
    "status": "submitted"
  }'
```

#### Get Organizational Units
```bash
curl -X GET http://localhost:3000/api/analytics/manager-dashboard/organizational-units/1 \
  -H "Authorization: Bearer $JWT_TOKEN"
```

#### Get Dashboard Summary
```bash
curl -X GET http://localhost:3000/api/analytics/manager-dashboard/summary/1 \
  -H "Authorization: Bearer $JWT_TOKEN"
```

#### Test Reminder Settings
```bash
# Get reminder settings
curl -X GET http://localhost:3000/api/analytics/manager-dashboard/reminder-settings/1 \
  -H "Authorization: Bearer $JWT_TOKEN"

# Update reminder settings
curl -X PUT http://localhost:3000/api/analytics/manager-dashboard/reminder-settings/1 \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "reminderDayOfMonth": 20,
    "reminderEnabled": true
  }'
```

## 🖥️ Frontend Testing

### 1. Navigation Test
1. Open http://localhost:3080
2. Login with manager credentials
3. Navigate to **Analytics → Manager Dashboard**
4. Verify the dashboard loads without errors

### 2. Dashboard Functionality Test

#### Summary Cards
- ✅ Verify summary cards display correct data
- ✅ Check that metrics are properly formatted (percentages, numbers)
- ✅ Confirm cards show loading states during data fetch

#### Controls
- ✅ Test manager dropdown selection
- ✅ Test reporting period date picker
- ✅ Test refresh button functionality
- ✅ Verify status chips display correctly

#### Interactive Table
- ✅ Click edit icon on a metrics row
- ✅ Modify FTE count, SLA percentage, utilization
- ✅ Save changes and verify they persist
- ✅ Test validation (negative numbers, out-of-range values)
- ✅ Cancel editing and verify changes are discarded

### 3. Error Handling Test
- ✅ Disconnect network and verify error messages
- ✅ Test with invalid manager ID
- ✅ Test with future reporting periods
- ✅ Verify graceful degradation

## 🔐 Security Testing

### 1. Authentication Test
```bash
# Test without token (should fail)
curl -X GET http://localhost:3000/api/analytics/manager-dashboard/metrics

# Test with invalid token (should fail)
curl -X GET http://localhost:3000/api/analytics/manager-dashboard/metrics \
  -H "Authorization: Bearer invalid_token"
```

### 2. Authorization Test
```bash
# Test employee trying to access manager data (should fail)
curl -X GET http://localhost:3000/api/analytics/manager-dashboard/metrics?managerId=2 \
  -H "Authorization: Bearer $EMPLOYEE_TOKEN"
```

### 3. Input Validation Test
```bash
# Test invalid data types
curl -X POST http://localhost:3000/api/analytics/manager-dashboard/metrics \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationalUnitId": "invalid",
    "managerId": -1,
    "reportingPeriod": "invalid-date",
    "fteCount": -5,
    "slaPercentage": 150
  }'
```

## 📊 Performance Testing

### 1. Load Testing
```bash
# Test with large datasets
curl -X GET "http://localhost:3000/api/analytics/manager-dashboard/metrics?limit=100" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### 2. Concurrent Users
```bash
# Use Apache Bench for concurrent requests
ab -n 100 -c 10 -H "Authorization: Bearer $JWT_TOKEN" \
  http://localhost:3000/api/analytics/manager-dashboard/metrics
```

## 🔄 Reminder System Testing

### 1. Manual Reminder Test
```bash
# Trigger manual reminder (if endpoint exists)
curl -X POST http://localhost:3000/api/analytics/manager-dashboard/send-reminder/1 \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### 2. Cron Job Test
1. Check server logs for cron job execution
2. Verify reminder logic with different day settings
3. Test edge cases (end of month, leap years)

## 📋 Test Checklist

### Backend Tests
- [ ] All API endpoints respond correctly
- [ ] Authentication and authorization work
- [ ] Input validation prevents invalid data
- [ ] Audit logging captures all operations
- [ ] Database constraints prevent data corruption
- [ ] Error handling returns appropriate HTTP codes

### Frontend Tests
- [ ] Dashboard loads and displays data
- [ ] Interactive editing works correctly
- [ ] Form validation prevents invalid input
- [ ] Loading states and error messages display
- [ ] Navigation and routing work properly
- [ ] Responsive design works on different screen sizes

### Integration Tests
- [ ] End-to-end data flow works correctly
- [ ] Real-time updates reflect in UI
- [ ] Audit logs are created for all operations
- [ ] Email reminders are triggered correctly
- [ ] Performance meets requirements (<2s load time)

### Security Tests
- [ ] Unauthorized access is prevented
- [ ] SQL injection attempts are blocked
- [ ] XSS attacks are prevented
- [ ] Input sanitization works correctly
- [ ] Audit trail is tamper-proof

## 🐛 Common Issues & Solutions

### Database Connection Issues
```bash
# Check database connection
psql -d ehrx -c "SELECT COUNT(*) FROM manager_dashboard_metrics;"
```

### TypeScript Compilation Errors
```bash
# Check for TypeScript errors
cd frontend && npm run type-check
cd backend && npm run build
```

### API Response Issues
- Check server logs for detailed error messages
- Verify JWT token is valid and not expired
- Ensure user has proper permissions

### Frontend Rendering Issues
- Check browser console for JavaScript errors
- Verify API responses are in expected format
- Check network tab for failed requests

## ✅ Success Criteria

The implementation is successful when:
- ✅ All API endpoints return expected data
- ✅ Frontend displays and updates data correctly
- ✅ Security controls prevent unauthorized access
- ✅ Performance meets sub-2-second requirements
- ✅ Audit logging captures all operations
- ✅ Error handling provides user-friendly messages

## 📞 Support

If you encounter issues during testing:
1. Check server and browser console logs
2. Verify database schema is properly applied
3. Ensure all environment variables are set
4. Review the implementation documentation
5. Check network connectivity and firewall settings

Happy testing! 🚀
