# EHRX Service Management Migration Guide

## 🚀 Migration to Enterprise Scripts

The EHRX project has been upgraded with **enterprise-grade service management scripts** that replace the previous basic startup/shutdown scripts. This guide helps you migrate from old scripts to the new system.

## 📋 What Changed

### ❌ Deprecated Scripts (Moved to .ghost files)
- `start-all-services.sh` → `start-all-services.sh.ghost`
- `stop-all-services.sh` → `stop-all-services.sh.ghost`
- Various other legacy scripts → `*.ghost` files

### ✅ New Enterprise Scripts
- **Master Script:** `scripts/ehrx-manager.sh` - Unified service management
- **Environment-Specific:** `scripts/start-dev.sh`, `scripts/start-prod.sh`
- **Infrastructure:** PID management, health monitoring, logging, dependency checking

## 🔄 Migration Commands

### Quick Migration (Recommended)

#### Old Way → New Way

| **Old Command** | **New Command** | **Notes** |
|----------------|----------------|-----------|
| `./start-all-services.sh` | `npm start` or `./scripts/ehrx-manager.sh start dev` | Now uses npm scripts |
| `./stop-all-services.sh` | `npm run stop` or `./scripts/ehrx-manager.sh stop` | Graceful shutdown with PID management |
| Manual port checking | `npm run status` or `./scripts/ehrx-manager.sh status` | Comprehensive status reporting |
| Manual log checking | `npm run logs` or `./scripts/ehrx-manager.sh logs` | Structured logging with rotation |
| No health monitoring | `npm run health` or `./scripts/ehrx-manager.sh health` | Built-in health checks |
| No dependency checking | `npm run deps` or `./scripts/ehrx-manager.sh deps` | Comprehensive dependency validation |

### Step-by-Step Migration

#### 1. **Stop Old Services**
```bash
# If you have services running with old scripts
./scripts/ehrx-manager.sh stop
./scripts/ehrx-manager.sh cleanup
```

#### 2. **Update Your Workflow**
```bash
# Instead of: ./start-all-services.sh
npm start

# Instead of: ./stop-all-services.sh  
npm run stop

# Check status
npm run status

# View logs
npm run logs
```

#### 3. **Load Shortcuts (Optional)**
```bash
# Add convenient aliases
source scripts/ehrx-shortcuts.sh

# Now you can use:
ehrx-start      # Start development
ehrx-stop       # Stop services
ehrx-status     # Check status
ehrx-health     # Health checks
```

#### 4. **Update Your Development Workflow**
```bash
# Full development setup
npm run deps        # Check dependencies
npm start           # Start development environment
npm run monitor     # Start health monitoring (optional)
```

## 🆕 New Features Available

### 1. **Environment Management**
```bash
# Development (hot reload, debug logging)
npm run start:dev

# Production (SSL, PM2, optimizations)
npm run start:prod
```

### 2. **Health Monitoring**
```bash
# Check service health
npm run health

# Start continuous monitoring with auto-restart
npm run monitor
```

### 3. **Dependency Management**
```bash
# Check all dependencies
npm run deps

# Auto-fix common issues
npm run fix-deps
```

### 4. **Advanced Logging**
```bash
# View different log types
npm run logs           # Main logs
npm run logs:error     # Error logs only
npm run logs:audit     # Audit logs
```

### 5. **Maintenance Operations**
```bash
# Clean up resources
npm run cleanup

# Restart services
npm run restart
```

## 🔧 VS Code Integration

The new scripts are integrated with VS Code tasks:

1. **Open Command Palette:** `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
2. **Type:** "Tasks: Run Task"
3. **Select:** Any EHRX task (e.g., "EHRX: Start Development")

Available VS Code tasks:
- EHRX: Start Development
- EHRX: Start Production
- EHRX: Stop Services
- EHRX: Check Status
- EHRX: Health Check
- EHRX: View Logs
- And more...

## 🚨 Breaking Changes

### 1. **Port Management**
- **Old:** Manual port conflict resolution
- **New:** Automatic port checking and conflict resolution

### 2. **Process Management**
- **Old:** Basic `kill` commands by port
- **New:** PID-based management with graceful shutdown

### 3. **Logging**
- **Old:** Console output only
- **New:** Structured logging to files with rotation

### 4. **Error Handling**
- **Old:** Basic error messages
- **New:** Comprehensive error handling with auto-recovery

## 🔍 Troubleshooting Migration Issues

### Issue: "Old scripts still running"
```bash
# Solution: Clean up old processes
./scripts/ehrx-manager.sh cleanup
pkill -f "node.*ehrx"  # If needed
```

### Issue: "Port conflicts"
```bash
# Solution: Use new port management
npm run status         # Check what's running
npm run cleanup        # Clean up old processes
npm start              # Start with new scripts
```

### Issue: "Dependencies not working"
```bash
# Solution: Use new dependency checker
npm run deps           # Check dependencies
npm run fix-deps       # Auto-fix issues
```

### Issue: "Can't find logs"
```bash
# Solution: Use new logging system
npm run logs           # View main logs
npm run logs:error     # View error logs
ls /var/log/ehrx/      # Check log directory
```

## 📚 Additional Resources

### Documentation
- **Main Documentation:** [`scripts/README.md`](scripts/README.md)
- **Implementation Summary:** [`ENTERPRISE-SCRIPTS-SUMMARY.md`](ENTERPRISE-SCRIPTS-SUMMARY.md)
- **Project README:** [`README.md`](README.md)

### Quick Reference
```bash
# Load shortcuts for easy access
source scripts/ehrx-shortcuts.sh

# Get help
ehrx-help

# Quick development setup
ehrx-dev-setup

# Troubleshooting guide
ehrx-troubleshoot
```

## ✅ Migration Checklist

- [ ] Stop any running services with old scripts
- [ ] Clean up old processes: `./scripts/ehrx-manager.sh cleanup`
- [ ] Test new scripts: `npm start`
- [ ] Verify status: `npm run status`
- [ ] Check health: `npm run health`
- [ ] Update development workflow to use npm scripts
- [ ] Load shortcuts: `source scripts/ehrx-shortcuts.sh`
- [ ] Update any automation/CI to use new scripts
- [ ] Remove references to old scripts in documentation
- [ ] Train team members on new commands

## 🎯 Benefits of Migration

### ✅ **Reliability**
- PID-based process management
- Graceful shutdown procedures
- Automatic dependency verification
- Health monitoring with auto-restart

### ✅ **Observability**
- Structured logging with rotation
- Performance monitoring
- Comprehensive status reporting
- Audit trails for compliance

### ✅ **Developer Experience**
- Hot reloading in development
- Environment-specific configurations
- VS Code integration
- Convenient shortcuts and aliases

### ✅ **Operations**
- Enterprise-grade service management
- Automated troubleshooting
- Resource cleanup
- Production-ready deployment

## 🆘 Need Help?

1. **Check documentation:** [`scripts/README.md`](scripts/README.md)
2. **Run diagnostics:** `npm run health`
3. **View troubleshooting:** `ehrx-troubleshoot` (after loading shortcuts)
4. **Check logs:** `npm run logs:error`

---

**Note:** Old scripts are preserved as `.ghost` files for reference but should not be used. The new enterprise scripts provide all functionality with significant improvements in reliability, monitoring, and ease of use.
