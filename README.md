# EHRX - Employee Performance Management Dashboard

A secure, scalable web application for employee performance evaluations, replacing the previous Excel-based system.

## 🚀 Quick Start

### Start Development Environment
```bash
npm start              # Start development environment
# OR
npm run start:dev      # Explicit development start
```

### Start Production Environment
```bash
npm run start:prod     # Start production environment
```

### Common Operations
```bash
npm run status         # Check service status
npm run health         # Run health checks
npm run stop           # Stop services
npm run restart        # Restart services
npm run logs           # View logs
npm run monitor        # Start health monitoring
```

## 🛠️ Enterprise Service Management

This project uses **enterprise-grade service management scripts** with advanced features:

- ✅ **PID Management** - Process tracking and graceful shutdown
- ✅ **Health Monitoring** - Automatic restart on failure
- ✅ **Dependency Verification** - Database and environment validation
- ✅ **Structured Logging** - Enterprise logging with rotation
- ✅ **Environment Isolation** - Separate dev/prod configurations
- ✅ **Security Compliance** - NIS2-compliant with audit trails

### Advanced Usage
```bash
# Master script (recommended)
./scripts/ehrx-manager.sh start dev --verbose
./scripts/ehrx-manager.sh start prod
./scripts/ehrx-manager.sh health
./scripts/ehrx-manager.sh monitor

# Environment-specific scripts
./scripts/start-dev.sh     # Development with hot reload
./scripts/start-prod.sh    # Production with SSL & PM2
./scripts/stop-dev.sh      # Stop development
./scripts/stop-prod.sh     # Stop production

# Maintenance
./scripts/ehrx-manager.sh deps      # Check dependencies
./scripts/ehrx-manager.sh fix-deps  # Auto-fix issues
./scripts/ehrx-manager.sh cleanup   # Clean up resources
```

📚 **Full Documentation:** See [`scripts/README.md`](scripts/README.md) for comprehensive usage guide.

## Project Structure
- `/backend` - NestJS backend API
- `/frontend` - React TypeScript frontend
- `/database` - Database scripts and migrations
- `/docs` - Documentation files
- `/scripts` - **Enterprise service management scripts**

## Key Features
- Assessment Templates & Scoring Logic
- Performance Assessments Workflow
- Status Trackers & Action Plans
- Team & User Management
- Role-Based Access Control
- Analytics & Visualization
- Reporting & Export (future)

## Development Workflow

### 1. Start Development
```bash
npm start              # Starts both backend and frontend with hot reload
```

### 2. Check Status
```bash
npm run status         # View service status and ports
```

### 3. Monitor Health
```bash
npm run monitor        # Start automatic health monitoring
```

### 4. View Logs
```bash
npm run logs           # Main logs
npm run logs:error     # Error logs only
npm run logs:audit     # Audit logs
```

### 5. Stop Services
```bash
npm run stop           # Graceful shutdown
```

## Production Deployment

### Prerequisites
- Node.js >= 16.0.0
- MySQL database
- SSL certificates (for HTTPS)
- PM2 (recommended)

### Setup
1. **Configure Environment:**
   ```bash
   cp backend/.env.example backend/.env.production
   # Edit .env.production with production values
   ```

2. **Start Production:**
   ```bash
   npm run start:prod
   ```

3. **Monitor:**
   ```bash
   npm run monitor        # Start health monitoring
   npm run health         # Check current health
   ```

## Troubleshooting

### Common Issues
```bash
# Dependency issues
npm run deps           # Check all dependencies
npm run fix-deps       # Auto-fix common issues

# Port conflicts
npm run cleanup        # Clean up PID files and processes

# Service not starting
npm run logs:error     # Check error logs
npm run health         # Run health diagnostics
```

### Getting Help
1. Check logs: `npm run logs:error`
2. Run health checks: `npm run health`
3. Review documentation: [`scripts/README.md`](scripts/README.md)
4. Check troubleshooting guide in scripts documentation

---

**Note:** This project uses enterprise-grade service management. Always use the provided npm scripts or the `ehrx-manager.sh` script for service operations.
