# 🔐 eHRx Environment Configuration Template
# Copy this file to .env and update with your actual values

# ==================== DATABASE CONFIGURATION (POSTGRESQL) ====================
DB_HOST=127.0.0.1
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_secure_db_password
DB_NAME=ehrx

# ==================== JWT AUTHENTICATION ====================
# SECURITY: Use a strong, random secret key (minimum 32 characters)
# Generate with: openssl rand -base64 32
JWT_SECRET=your_jwt_secret_key_minimum_32_characters_long
JWT_EXPIRATION=3600

# ==================== SERVER CONFIGURATION ====================
PORT=4000
NODE_ENV=development
FRONTEND_URL=http://localhost:3080

# ==================== ENCRYPTION CONFIGURATION ====================
# CRITICAL: Master encryption key for data at rest (AES-256-GCM)
# Generate with: openssl rand -base64 32
# NEVER commit this to version control!
ENCRYPTION_MASTER_KEY=your_32_byte_base64_encoded_master_encryption_key

# Salt for PBKDF2 key derivation (minimum 16 bytes)
# Generate with: openssl rand -base64 16
ENCRYPTION_SALT=your_16_byte_base64_encoded_salt

# Key derivation iterations (recommended: 100000+)
ENCRYPTION_ITERATIONS=100000

# ==================== SECURITY CONFIGURATION ====================
# Session timeout in milliseconds (default: 30 minutes)
SESSION_TIMEOUT=1800000

# Rate limiting configuration
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Security headers configuration
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_CSP_ENABLED=true

# ==================== COMPLIANCE CONFIGURATION ====================
# GDPR compliance settings
GDPR_DATA_RETENTION_DAYS=2555  # 7 years default
GDPR_ANONYMIZATION_ENABLED=true
GDPR_AUDIT_RETENTION_DAYS=3650  # 10 years for audit logs

# SOC2 compliance settings
SOC2_AUDIT_ENABLED=true
SOC2_MONITORING_ENABLED=true
SOC2_INCIDENT_REPORTING=true

# NIS2 compliance settings
NIS2_SECURITY_MONITORING=true
NIS2_INCIDENT_RESPONSE=true
NIS2_VULNERABILITY_MANAGEMENT=true

# ==================== AUDIT LOGGING ====================
# Audit log retention and storage
AUDIT_LOG_RETENTION_DAYS=3650
AUDIT_LOG_LEVEL=info
AUDIT_LOG_ENCRYPTION=true

# ==================== EMAIL CONFIGURATION ====================
# For compliance notifications and alerts
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Compliance notification recipients
COMPLIANCE_ALERT_EMAIL=<EMAIL>
SECURITY_ALERT_EMAIL=<EMAIL>

# ==================== BACKUP CONFIGURATION ====================
# Database backup encryption
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key
BACKUP_RETENTION_DAYS=90

# ==================== MONITORING & ALERTING ====================
# External monitoring services
MONITORING_WEBHOOK_URL=https://your-monitoring-service.com/webhook
SECURITY_INCIDENT_WEBHOOK=https://your-security-service.com/incident

# ==================== PRODUCTION SECURITY ====================
# Only set these in production
# HTTPS_CERT_PATH=/path/to/ssl/cert.pem
# HTTPS_KEY_PATH=/path/to/ssl/private.key
# HTTPS_CA_PATH=/path/to/ssl/ca.pem

# Trusted proxy configuration
# TRUSTED_PROXIES=127.0.0.1,::1

# ==================== DEVELOPMENT SETTINGS ====================
# Only for development - remove in production
DEBUG_MODE=false
VERBOSE_LOGGING=false
MOCK_DATA_ENABLED=false

# ==================== THIRD-PARTY INTEGRATIONS ====================
# External service API keys (if needed)
# EXTERNAL_API_KEY=your_external_api_key
# EXTERNAL_API_SECRET=your_external_api_secret

# ==================== NOTES ====================
# 1. Never commit .env files to version control
# 2. Use strong, unique passwords and keys
# 3. Rotate encryption keys regularly
# 4. Monitor for unauthorized access
# 5. Keep backups of encryption keys in secure storage
# 6. Test disaster recovery procedures regularly
