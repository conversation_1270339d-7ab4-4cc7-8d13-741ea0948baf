# 🔐 NIS2-Compliant Production Environment Configuration Template
# Copy this file to .env.production and fill in the actual values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
PORT=3000

# Frontend URL (REQUIRED - must be production domain)
FRONTEND_URL=https://your-production-domain.com

# =============================================================================
# DATABASE CONFIGURATION (POSTGRESQL)
# =============================================================================
DB_HOST=your-production-db-host
DB_PORT=5432
DB_USERNAME=ehrx_production_user
DB_PASSWORD=your-strong-database-password-min-12-chars
DB_NAME=ehrx

# Database Performance Optimization
DB_CONNECTION_LIMIT=20
DB_POOL_SIZE=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
DB_IDLE_TIMEOUT=300000
DB_CACHE_DURATION=30000
DB_MAX_QUERY_TIME=5000
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=3000

# Database Security
DB_SSL_REJECT_UNAUTHORIZED=true

# Database Monitoring
DB_HEALTH_TIMEOUT=5000
DB_HEALTH_INTERVAL=30000
DB_SLOW_QUERY_THRESHOLD=1000
DB_LOG_QUERIES=false
DB_ENABLE_METRICS=true
DB_MONITOR_CONNECTIONS=true_production

# Database SSL Configuration (recommended for production)
DB_SSL=true
DB_SSL_CA=/path/to/ca-certificate.pem
DB_SSL_CERT=/path/to/client-certificate.pem
DB_SSL_KEY=/path/to/client-key.pem

# =============================================================================
# JWT AUTHENTICATION (CRITICAL SECURITY)
# =============================================================================
# JWT Secret MUST be at least 64 characters for NIS2 compliance
# Generate with: openssl rand -base64 64
JWT_SECRET=your-super-secure-jwt-secret-at-least-64-characters-long-for-nis2-compliance
JWT_EXPIRATION=1h

# Refresh Token Configuration
REFRESH_TOKEN_SECRET=your-super-secure-refresh-token-secret-different-from-jwt
REFRESH_TOKEN_EXPIRATION=7d

# =============================================================================
# HTTPS/SSL CONFIGURATION
# =============================================================================
# Force HTTPS enforcement
FORCE_HTTPS=true

# SSL Certificate Paths
SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.key
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# Cookie Domain (for secure cookie settings)
COOKIE_DOMAIN=.your-production-domain.com

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================
SESSION_SECRET=your-session-secret-different-from-jwt-secret
SESSION_TIMEOUT=8h
SESSION_SECURE=true

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# Authentication Rate Limiting (stricter)
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=5
AUTH_RATE_LIMIT_BLOCK_DURATION=3600000

# =============================================================================
# SECURITY HEADERS CONFIGURATION
# =============================================================================
# Content Security Policy
CSP_REPORT_URI=https://your-domain.com/api/security/csp-report
CSP_REPORT_ONLY=false

# HSTS Configuration
HSTS_MAX_AGE=63072000
HSTS_INCLUDE_SUBDOMAINS=true
HSTS_PRELOAD=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
LOG_MAX_FILE_SIZE=10485760
LOG_MAX_FILES=30
LOG_MAX_AGE=30
LOG_COMPRESSION_ENABLED=true

# Security Audit Logging
SECURITY_LOG_ENABLED=true
SECURITY_LOG_LEVEL=info
AUDIT_LOG_RETENTION_DAYS=2555

# =============================================================================
# MONITORING AND ALERTING
# =============================================================================
# SIEM Integration (optional)
SIEM_ENDPOINT=https://your-siem-system.com/api/events
SIEM_API_KEY=your-siem-api-key

# Security Monitoring
SECURITY_MONITORING_ENABLED=true
THREAT_DETECTION_ENABLED=true

# Email Alerts for Security Events
SECURITY_ALERT_EMAIL=<EMAIL>
SMTP_HOST=your-smtp-server.com
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASS=your-smtp-password
SMTP_SECURE=true

# =============================================================================
# BACKUP AND DISASTER RECOVERY
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=90
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# =============================================================================
# COMPLIANCE CONFIGURATION
# =============================================================================
# NIS2 Compliance
NIS2_COMPLIANCE_ENABLED=true
NIS2_INCIDENT_REPORTING_ENABLED=true
NIS2_AUDIT_LOGGING_ENABLED=true

# GDPR Compliance
GDPR_COMPLIANCE_ENABLED=true
DATA_RETENTION_PERIOD_DAYS=2555
PERSONAL_DATA_ENCRYPTION_ENABLED=true

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# Redis for Session Storage (recommended for production)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_TLS=true

# External Security Services
SECURITY_SCANNER_API_KEY=your-security-scanner-api-key
THREAT_INTELLIGENCE_API_KEY=your-threat-intel-api-key

# =============================================================================
# PERFORMANCE AND SCALING
# =============================================================================
# Cluster Configuration
CLUSTER_ENABLED=true
CLUSTER_WORKERS=auto

# Memory Limits
MAX_MEMORY_USAGE=1024
MEMORY_MONITORING_ENABLED=true

# =============================================================================
# DEVELOPMENT/TESTING OVERRIDES (Remove in production)
# =============================================================================
# These should be removed or set to false in production
DEBUG_MODE=false
VERBOSE_LOGGING=false
DISABLE_RATE_LIMITING=false
SKIP_SSL_VERIFICATION=false

# =============================================================================
# SECURITY CHECKLIST
# =============================================================================
# Before deploying to production, ensure:
# ✅ All secrets are properly generated and secure
# ✅ SSL certificates are valid and properly configured
# ✅ Database credentials are secure and limited
# ✅ HTTPS is enforced
# ✅ Rate limiting is enabled
# ✅ Audit logging is configured
# ✅ Monitoring and alerting are set up
# ✅ Backup and disaster recovery are configured
# ✅ All development/testing overrides are disabled
# ✅ Security validation passes without critical issues
