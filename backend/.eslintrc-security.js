/**
 * 🔐 SECURITY-FOCUSED ESLINT RULES
 * 
 * These rules specifically protect the authentication system from common
 * security vulnerabilities and coding mistakes that could break auth.
 */

module.exports = {
  extends: ['./.eslintrc.js'],
  rules: {
    // 🔐 Authentication & Security Rules
    'no-console': ['error', {
      allow: ['warn', 'error']
    }], // Prevent accidental logging of sensitive data

    'no-eval': 'error', // Prevent code injection
    'no-implied-eval': 'error',
    'no-new-func': 'error',

    // 🔐 Prevent hardcoded secrets
    'no-secrets/no-secrets': 'error',

    // 🔐 SQL Injection Prevention
    'security/detect-sql-injection': 'error',

    // 🔐 XSS Prevention
    'security/detect-unsafe-regex': 'error',
    'security/detect-non-literal-regexp': 'error',

    // 🔐 Prototype Pollution Prevention
    'security/detect-object-injection': 'error',

    // 🔐 Buffer Overflow Prevention
    'security/detect-buffer-noassert': 'error',
    'security/detect-new-buffer': 'error',

    // 🔐 Timing Attack Prevention
    'security/detect-possible-timing-attacks': 'error',

    // 🔐 SQL Security Rules
    'no-restricted-syntax': [
      'error',
      {
        selector: "CallExpression[callee.property.name='query'][arguments.0.type='Literal']",
        message: '🚨 Raw SQL queries are forbidden - use TypeORM with user context validation'
      },
      {
        selector: "CallExpression[callee.property.name='query'][arguments.0.type='TemplateLiteral']",
        message: '🚨 Raw SQL template literals are forbidden - use TypeORM with user context validation'
      },
      {
        selector: "CallExpression[callee.property.name='execute'][arguments.0.type='Literal']",
        message: '🚨 Raw SQL execution is forbidden - use TypeORM with user context validation'
      }
    ],
  },

  // 🔐 Custom rules for authentication system
  overrides: [
    {
      files: ['src/auth/**/*.ts', 'src/users/**/*.ts', 'src/security/**/*.ts'],
      rules: {
        // 🔐 Extra strict rules for auth-related files
        '@typescript-eslint/no-explicit-any': 'error',
        '@typescript-eslint/explicit-function-return-type': 'error',
        'prefer-const': 'error',

        // 🔐 Prevent dangerous patterns in auth code
        'no-var': 'error',
        'no-delete-var': 'error',
        'no-undef': 'error',

        // 🔐 Require error handling
        '@typescript-eslint/no-floating-promises': 'error',
        'prefer-promise-reject-errors': 'error',
      }
    },
    {
      files: ['src/**/*.controller.ts'],
      rules: {
        // 🔐 SQL API compliance rules for controllers
        'no-console': 'warn',
        '@typescript-eslint/explicit-function-return-type': 'warn',

        // 🔐 Custom rules to enforce auth patterns (would need custom ESLint plugin)
        // For now, these are handled by the compliance validator script
      }
    },
    {
      files: ['src/auth/strategies/*.ts'],
      rules: {
        // 🔐 JWT Strategy specific rules
        'no-console': 'error', // No logging in strategies
        '@typescript-eslint/no-unused-vars': 'error',
      }
    },
    {
      files: ['src/**/*.controller.ts'],
      rules: {
        // 🔐 Controller security rules
        'security/detect-non-literal-fs-filename': 'error',
        '@typescript-eslint/explicit-member-accessibility': 'error',
      }
    }
  ],

  // 🔐 Security-focused plugins
  plugins: [
    'security',
    'no-secrets'
  ],

  // 🔐 Custom security patterns to detect
  settings: {
    'no-secrets': {
      'additionalRegexes': {
        'JWT Secret': 'jwt[_-]?secret',
        'Database Password': 'db[_-]?pass',
        'API Key': 'api[_-]?key',
        'Private Key': 'private[_-]?key'
      }
    }
  }
};
