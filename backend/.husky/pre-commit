#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# 🔐 Authentication Security Pre-commit Hook
echo "🔐 Running authentication security checks..."

# Run the comprehensive security check
npm run security:check

# If security check passes, continue with other checks
if [ $? -eq 0 ]; then
    echo "✅ Authentication security checks passed"
else
    echo "❌ Authentication security checks failed - commit blocked"
    exit 1
fi
