{"name": "ehrx-backend", "version": "1.0.0", "description": "Backend API for Employee Performance Management Dashboard", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "security:validate": "node scripts/validate-security-fixes.js", "security:check": "chmod +x scripts/pre-commit-auth-check.sh && ./scripts/pre-commit-auth-check.sh", "security:lint": "eslint --config .eslintrc-security.js src/auth/ src/users/ src/security/ || true", "security:test": "jest --testPathPattern=\"auth.*spec\" --passWithNoTests || true", "validate:auth-compliance": "node scripts/validate-auth-compliance.js", "validate:sql-security": "npm run validate:auth-compliance", "security:sql-check": "npm run security:lint && npm run validate:sql-security", "security:full": "npm run security:lint && npm run security:test && npm run security:validate && npm run security:check && npm run validate:auth-compliance", "deploy:validate": "npm run security:validate && npm run build", "secrets:generate": "node scripts/generate-production-secrets.js", "config:validate": "node -e \"require('./dist/config/environment.config').EnvironmentConfigService\"", "config:test": "node test-config.js", "production:setup": "npm run secrets:generate && npm run build && npm run config:validate"}, "dependencies": {"@nestjs/common": "^10.4.20", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.20", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.20", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^5.2.0", "@nestjs/typeorm": "^10.0.2", "@types/pg": "^8.15.5", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "csv-writer": "^1.6.0", "exceljs": "^4.3.0", "helmet": "^5.0.2", "moment": "^2.29.4", "mysql2": "^3.14.3", "passport": "^0.7.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pdfkit": "^0.13.0", "pg": "^8.16.3", "qrcode": "^1.5.4", "reflect-metadata": "^0.1.14", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "speakeasy": "^2.0.0", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.25"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^10.4.20", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.13", "@types/jest": "^30.0.0", "@types/moment": "^2.11.29", "@types/node": "^16.18.126", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/pdfkit": "^0.12.12", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.3", "jest": "^30.0.5", "nodemon": "^3.1.10", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^4.9.5"}}