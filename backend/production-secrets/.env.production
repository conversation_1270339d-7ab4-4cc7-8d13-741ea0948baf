# 🔐 PRODUCTION ENVIRONMENT CONFIGURATION
# Generated on: 2025-08-01T08:14:33.514Z
# 
# ⚠️  CRITICAL SECURITY NOTICE:
# - Keep this file secure and never commit to version control
# - Rotate these secrets regularly
# - Use a secure secrets management system in production
# - Backup encryption keys in a secure location

# ==================== DATABASE CONFIGURATION ====================
DB_HOST=your_production_db_host
DB_PORT=3306
DB_USERNAME=your_production_db_user
DB_PASSWORD=7zX*^wsHkOt%ROOQ@zYQPJk!d1DaVK9j
DB_NAME=ehrx_production

# ==================== JWT AUTHENTICATION ====================
JWT_SECRET=Sqc5ucESSQd/0GDaelvvRIAmICLKhkzuEBbIivALNt0=
JWT_EXPIRATION=3600

# ==================== SERVER CONFIGURATION ====================
PORT=4000
NODE_ENV=production
FRONTEND_URL=https://your-domain.com

# ==================== ENCRYPTION CONFIGURATION ====================
# CRITICAL: Master encryption key for data at rest (AES-256-GCM)
ENCRYPTION_MASTER_KEY=HDqZmNqYD7bgm9rAtHFcwqAIuCQQOrxJt89b2Lvb6qw=

# Salt for PBKDF2 key derivation
ENCRYPTION_SALT=GDBKvEfhQr6wXCH8QYeEEQ==

# Key derivation iterations
ENCRYPTION_ITERATIONS=100000

# ==================== SECURITY CONFIGURATION ====================
# Session timeout in milliseconds (30 minutes)
SESSION_TIMEOUT=1800000

# Rate limiting configuration
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Security headers configuration
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_CSP_ENABLED=true

# ==================== COMPLIANCE CONFIGURATION ====================
# GDPR compliance settings
GDPR_DATA_RETENTION_DAYS=2555
GDPR_ANONYMIZATION_ENABLED=true
GDPR_AUDIT_RETENTION_DAYS=3650

# SOC2 compliance settings
SOC2_AUDIT_ENABLED=true
SOC2_MONITORING_ENABLED=true
SOC2_INCIDENT_REPORTING=true

# NIS2 compliance settings
NIS2_SECURITY_MONITORING=true
NIS2_INCIDENT_RESPONSE=true
NIS2_VULNERABILITY_MANAGEMENT=true

# ==================== AUDIT LOGGING ====================
AUDIT_LOG_RETENTION_DAYS=3650
AUDIT_LOG_LEVEL=info
AUDIT_LOG_ENCRYPTION=true

# ==================== EMAIL CONFIGURATION ====================
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Compliance notification recipients
COMPLIANCE_ALERT_EMAIL=<EMAIL>
SECURITY_ALERT_EMAIL=<EMAIL>

# ==================== BACKUP CONFIGURATION ====================
BACKUP_ENCRYPTION_KEY=tsjjQBhnf+f7U+7/hRkXe/PkW6/DV3m9c8ga6YC+m4Q=
BACKUP_RETENTION_DAYS=90

# ==================== MONITORING & ALERTING ====================
MONITORING_WEBHOOK_URL=https://your-monitoring-service.com/webhook
SECURITY_INCIDENT_WEBHOOK=https://your-security-service.com/incident

# ==================== PRODUCTION SECURITY ====================
# SSL/TLS Configuration
HTTPS_CERT_PATH=/path/to/ssl/cert.pem
HTTPS_KEY_PATH=/path/to/ssl/private.key
HTTPS_CA_PATH=/path/to/ssl/ca.pem

# Trusted proxy configuration
TRUSTED_PROXIES=127.0.0.1,::1

# ==================== INTERNAL SERVICES ====================
INTERNAL_API_KEY=qDBG3i8U3Fahj/+rnjl2t51JNw+PSE08
WEBHOOK_SECRET=5601mHP5x8LpP/9MYF8W7P97QHkpETZN
SESSION_SECRET=xNRyCjfCtXo2Npo97OCW6bSGkQc8ByCarp/J2/QJ8+Q=

# ==================== DEVELOPMENT SETTINGS ====================
# Disable in production
DEBUG_MODE=false
VERBOSE_LOGGING=false
MOCK_DATA_ENABLED=false
