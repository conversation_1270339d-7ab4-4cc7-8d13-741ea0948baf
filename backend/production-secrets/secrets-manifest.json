{"generated": "2025-08-01T08:14:33.515Z", "description": "Production secrets for eHRx application", "rotation_schedule": "Every 90 days", "backup_required": true, "secrets": [{"name": "JWT_SECRET", "type": "secret", "length": 44, "generated": "2025-08-01T08:14:33.515Z"}, {"name": "ENCRYPTION_MASTER_KEY", "type": "encryption_key", "length": 44, "generated": "2025-08-01T08:14:33.515Z"}, {"name": "ENCRYPTION_SALT", "type": "token", "length": 24, "generated": "2025-08-01T08:14:33.515Z"}, {"name": "DB_PASSWORD", "type": "password", "length": 32, "generated": "2025-08-01T08:14:33.515Z"}, {"name": "SESSION_SECRET", "type": "secret", "length": 44, "generated": "2025-08-01T08:14:33.515Z"}, {"name": "BACKUP_ENCRYPTION_KEY", "type": "encryption_key", "length": 44, "generated": "2025-08-01T08:14:33.515Z"}, {"name": "INTERNAL_API_KEY", "type": "encryption_key", "length": 32, "generated": "2025-08-01T08:14:33.515Z"}, {"name": "WEBHOOK_SECRET", "type": "secret", "length": 32, "generated": "2025-08-01T08:14:33.515Z"}]}