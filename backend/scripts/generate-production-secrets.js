#!/usr/bin/env node

/**
 * 🔐 Production Secrets Generator
 * 
 * Generates secure environment variables for production deployment
 * - JWT secrets
 * - Encryption keys
 * - Salt values
 * - Database passwords
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

console.log('🔐 Generating secure production environment variables...\n');

/**
 * Generate a secure random string
 */
function generateSecureSecret(bytes = 32) {
  return crypto.randomBytes(bytes).toString('base64');
}

/**
 * Generate a secure password with mixed characters
 */
function generateSecurePassword(length = 24) {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, charset.length);
    password += charset[randomIndex];
  }
  
  return password;
}

/**
 * Generate all production secrets
 */
function generateProductionSecrets() {
  const secrets = {
    // JWT Configuration
    JWT_SECRET: generateSecureSecret(32),
    
    // Encryption Configuration
    ENCRYPTION_MASTER_KEY: generateSecureSecret(32),
    ENCRYPTION_SALT: generateSecureSecret(16),
    
    // Database Configuration
    DB_PASSWORD: generateSecurePassword(32),
    
    // Additional Security Keys
    SESSION_SECRET: generateSecureSecret(32),
    BACKUP_ENCRYPTION_KEY: generateSecureSecret(32),
    
    // API Keys (if needed)
    INTERNAL_API_KEY: generateSecureSecret(24),
    WEBHOOK_SECRET: generateSecureSecret(24),
  };

  return secrets;
}

/**
 * Create production .env file template
 */
function createProductionEnvTemplate(secrets) {
  const template = `# 🔐 PRODUCTION ENVIRONMENT CONFIGURATION
# Generated on: ${new Date().toISOString()}
# 
# ⚠️  CRITICAL SECURITY NOTICE:
# - Keep this file secure and never commit to version control
# - Rotate these secrets regularly
# - Use a secure secrets management system in production
# - Backup encryption keys in a secure location

# ==================== DATABASE CONFIGURATION ====================
DB_HOST=your_production_db_host
DB_PORT=3306
DB_USERNAME=your_production_db_user
DB_PASSWORD=${secrets.DB_PASSWORD}
DB_NAME=ehrx_production

# ==================== JWT AUTHENTICATION ====================
JWT_SECRET=${secrets.JWT_SECRET}
JWT_EXPIRATION=3600

# ==================== SERVER CONFIGURATION ====================
PORT=4000
NODE_ENV=production
FRONTEND_URL=https://your-domain.com

# ==================== ENCRYPTION CONFIGURATION ====================
# CRITICAL: Master encryption key for data at rest (AES-256-GCM)
ENCRYPTION_MASTER_KEY=${secrets.ENCRYPTION_MASTER_KEY}

# Salt for PBKDF2 key derivation
ENCRYPTION_SALT=${secrets.ENCRYPTION_SALT}

# Key derivation iterations
ENCRYPTION_ITERATIONS=100000

# ==================== SECURITY CONFIGURATION ====================
# Session timeout in milliseconds (30 minutes)
SESSION_TIMEOUT=1800000

# Rate limiting configuration
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Security headers configuration
SECURITY_HSTS_MAX_AGE=31536000
SECURITY_CSP_ENABLED=true

# ==================== COMPLIANCE CONFIGURATION ====================
# GDPR compliance settings
GDPR_DATA_RETENTION_DAYS=2555
GDPR_ANONYMIZATION_ENABLED=true
GDPR_AUDIT_RETENTION_DAYS=3650

# SOC2 compliance settings
SOC2_AUDIT_ENABLED=true
SOC2_MONITORING_ENABLED=true
SOC2_INCIDENT_REPORTING=true

# NIS2 compliance settings
NIS2_SECURITY_MONITORING=true
NIS2_INCIDENT_RESPONSE=true
NIS2_VULNERABILITY_MANAGEMENT=true

# ==================== AUDIT LOGGING ====================
AUDIT_LOG_RETENTION_DAYS=3650
AUDIT_LOG_LEVEL=info
AUDIT_LOG_ENCRYPTION=true

# ==================== EMAIL CONFIGURATION ====================
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Compliance notification recipients
COMPLIANCE_ALERT_EMAIL=<EMAIL>
SECURITY_ALERT_EMAIL=<EMAIL>

# ==================== BACKUP CONFIGURATION ====================
BACKUP_ENCRYPTION_KEY=${secrets.BACKUP_ENCRYPTION_KEY}
BACKUP_RETENTION_DAYS=90

# ==================== MONITORING & ALERTING ====================
MONITORING_WEBHOOK_URL=https://your-monitoring-service.com/webhook
SECURITY_INCIDENT_WEBHOOK=https://your-security-service.com/incident

# ==================== PRODUCTION SECURITY ====================
# SSL/TLS Configuration
HTTPS_CERT_PATH=/path/to/ssl/cert.pem
HTTPS_KEY_PATH=/path/to/ssl/private.key
HTTPS_CA_PATH=/path/to/ssl/ca.pem

# Trusted proxy configuration
TRUSTED_PROXIES=127.0.0.1,::1

# ==================== INTERNAL SERVICES ====================
INTERNAL_API_KEY=${secrets.INTERNAL_API_KEY}
WEBHOOK_SECRET=${secrets.WEBHOOK_SECRET}
SESSION_SECRET=${secrets.SESSION_SECRET}

# ==================== DEVELOPMENT SETTINGS ====================
# Disable in production
DEBUG_MODE=false
VERBOSE_LOGGING=false
MOCK_DATA_ENABLED=false
`;

  return template;
}

/**
 * Create secrets summary for secure storage
 */
function createSecretsManifest(secrets) {
  const manifest = {
    generated: new Date().toISOString(),
    description: 'Production secrets for eHRx application',
    rotation_schedule: 'Every 90 days',
    backup_required: true,
    secrets: Object.keys(secrets).map(key => ({
      name: key,
      type: key.includes('PASSWORD') ? 'password' : 
            key.includes('KEY') ? 'encryption_key' :
            key.includes('SECRET') ? 'secret' : 'token',
      length: secrets[key].length,
      generated: new Date().toISOString()
    }))
  };

  return JSON.stringify(manifest, null, 2);
}

/**
 * Main execution
 */
function main() {
  try {
    // Generate secrets
    const secrets = generateProductionSecrets();
    
    // Create output directory
    const outputDir = path.join(__dirname, '..', 'production-secrets');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Create production .env template
    const envTemplate = createProductionEnvTemplate(secrets);
    const envPath = path.join(outputDir, '.env.production');
    fs.writeFileSync(envPath, envTemplate);

    // Create secrets manifest
    const manifest = createSecretsManifest(secrets);
    const manifestPath = path.join(outputDir, 'secrets-manifest.json');
    fs.writeFileSync(manifestPath, manifest);

    // Create individual secret files for secure distribution
    Object.entries(secrets).forEach(([key, value]) => {
      const secretPath = path.join(outputDir, `${key.toLowerCase()}.secret`);
      fs.writeFileSync(secretPath, value);
    });

    // Set secure permissions (Unix-like systems)
    if (process.platform !== 'win32') {
      fs.chmodSync(outputDir, 0o700); // rwx------
      fs.readdirSync(outputDir).forEach(file => {
        fs.chmodSync(path.join(outputDir, file), 0o600); // rw-------
      });
    }

    console.log('✅ Production secrets generated successfully!');
    console.log(`📁 Output directory: ${outputDir}`);
    console.log('\n🔐 Generated files:');
    console.log('   - .env.production (complete environment template)');
    console.log('   - secrets-manifest.json (secrets inventory)');
    console.log('   - Individual .secret files for each key');
    
    console.log('\n⚠️  SECURITY REMINDERS:');
    console.log('   1. Never commit these files to version control');
    console.log('   2. Store encryption keys in a secure key management system');
    console.log('   3. Rotate secrets every 90 days');
    console.log('   4. Backup encryption keys securely');
    console.log('   5. Use environment-specific secrets management in production');
    
    console.log('\n📋 Next steps:');
    console.log('   1. Review and customize the .env.production file');
    console.log('   2. Update database and email configuration');
    console.log('   3. Deploy secrets to your production environment');
    console.log('   4. Test the application with production configuration');
    console.log('   5. Set up monitoring and alerting');

  } catch (error) {
    console.error('❌ Error generating production secrets:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  generateProductionSecrets,
  createProductionEnvTemplate,
  createSecretsManifest
};
