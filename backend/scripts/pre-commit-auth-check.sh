#!/bin/bash

# 🔐 PRE-COMMIT AUTHENTICATION SECURITY CHECK
# 
# This script runs before every commit to ensure authentication system integrity.
# It performs multiple security validations and prevents commits that could break auth.

set -e

echo "🔐 [PRE-COMMIT] Starting authentication security validation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Track validation results
ERRORS=0
WARNINGS=0

# Function to log errors
log_error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
    ((ERRORS++))
}

# Function to log warnings
log_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
    ((WARNINGS++))
}

# Function to log success
log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to log info
log_info() {
    echo -e "${BLUE}🔍 $1${NC}"
}

# 1. 🔐 Check for hardcoded secrets in auth files
log_info "Checking for hardcoded secrets in authentication files..."
if grep -r -i --include="*.ts" --include="*.js" \
    -E "(password|secret|key|token)\s*[:=]\s*['\"][^'\"]{8,}" \
    src/auth/ src/users/ src/security/ 2>/dev/null; then
    log_error "Hardcoded secrets detected in authentication files"
else
    log_success "No hardcoded secrets found"
fi

# 2. 🔐 Check for console.log in production auth code
log_info "Checking for console.log statements in authentication code..."
if grep -r --include="*.ts" "console\.log" src/auth/ src/users/ src/security/ 2>/dev/null; then
    log_warning "Console.log statements found in authentication code - ensure they're development-only"
fi

# 3. 🔐 Validate JWT configuration
log_info "Validating JWT configuration..."
if grep -r --include="*.ts" "synchronize.*true" src/ 2>/dev/null; then
    log_error "TypeORM synchronize is set to true - this is dangerous in production"
fi

# 4. 🔐 Check for weak session validation
log_info "Checking for weak session validation patterns..."
if grep -r --include="*.ts" "\.includes.*sessionId" src/auth/ 2>/dev/null; then
    log_error "Weak session validation using .includes() detected"
fi

# 5. 🔐 Validate password hashing
log_info "Checking password hashing implementation..."
if grep -r --include="*.ts" -E "(password.*=.*req\.|password.*=.*body\.)" src/ 2>/dev/null | grep -v "bcrypt\|hash"; then
    log_warning "Potential plain text password assignment detected"
fi

# 6. 🔐 Check for SQL injection vulnerabilities
log_info "Checking for potential SQL injection patterns..."
if grep -r --include="*.ts" -E "query.*\+.*req\.|query.*\+.*body\." src/ 2>/dev/null; then
    log_error "Potential SQL injection vulnerability detected"
fi

# 7. 🔐 Validate authentication middleware
log_info "Checking authentication middleware integrity..."
AUTH_GUARD_FILES=$(find src/ -name "*guard*.ts" -o -name "*strategy*.ts")
for file in $AUTH_GUARD_FILES; do
    if [ -f "$file" ]; then
        if ! grep -q "canActivate\|validate" "$file"; then
            log_warning "Authentication guard/strategy file may be incomplete: $file"
        fi
    fi
done

# 8. 🔐 Check for timing attack protection
log_info "Validating timing attack protection..."
if ! grep -r --include="*.ts" "ensureMinimumDelay\|TIMING ATTACK PROTECTION" src/auth/ 2>/dev/null; then
    log_warning "Timing attack protection may not be implemented"
fi

# 9. 🔐 Validate input sanitization
log_info "Checking input sanitization in security-critical files..."
SECURITY_FILES=$(find src/ -name "*security*" -name "*.ts" -o -name "*auth*" -name "*.ts")
for file in $SECURITY_FILES; do
    if [ -f "$file" ] && grep -q "req\.body\|req\.query\|req\.params" "$file"; then
        if ! grep -q "sanitize\|validate\|escape" "$file"; then
            log_warning "Input sanitization may be missing in: $file"
        fi
    fi
done

# 10. 🔐 Run TypeScript compilation check
log_info "Running TypeScript compilation check..."
if ! npm run build > /dev/null 2>&1; then
    log_error "TypeScript compilation failed - authentication code may have type errors"
fi

# 11. 🔐 Run security-focused linting
log_info "Running security-focused ESLint checks..."
if [ -f ".eslintrc-security.js" ]; then
    if ! npx eslint --config .eslintrc-security.js src/auth/ src/users/ src/security/ --quiet; then
        log_error "Security linting failed - authentication code has security issues"
    fi
else
    log_warning "Security ESLint configuration not found"
fi

# 12. 🔐 Run SQL API authentication compliance check
log_info "Running SQL API authentication compliance validation..."
if [ -f "scripts/validate-auth-compliance.js" ]; then
    if ! node scripts/validate-auth-compliance.js; then
        log_error "SQL API authentication compliance check failed"
        log_error "All controllers must use proper authentication patterns"
        exit 1
    fi
else
    log_warning "Auth compliance validator not found"
fi

# 12. 🔐 Run authentication tests
log_info "Running critical authentication tests..."
if ! npm test -- --testPathPattern="auth.*spec" --passWithNoTests --silent > /dev/null 2>&1; then
    log_error "Authentication tests failed - changes may have broken auth system"
fi

# 13. 🔐 Check for environment variable dependencies
log_info "Validating environment variable usage..."
if grep -r --include="*.ts" "process\.env\." src/auth/ src/users/ | grep -v "NODE_ENV"; then
    log_info "Environment variables used in auth code - ensure they're documented"
fi

# 14. 🔐 Validate security headers configuration
log_info "Checking security headers configuration..."
if [ -f "src/security/middleware/security-headers.middleware.ts" ]; then
    if ! grep -q "helmet\|csp\|hsts" "src/security/middleware/security-headers.middleware.ts"; then
        log_warning "Security headers middleware may be incomplete"
    fi
fi

# Summary and decision
echo ""
echo "🔐 [PRE-COMMIT] Authentication Security Validation Summary:"
echo "=================================================="

if [ $ERRORS -gt 0 ]; then
    echo -e "${RED}❌ COMMIT BLOCKED: $ERRORS critical security issues found${NC}"
    echo -e "${RED}Please fix all errors before committing${NC}"
    exit 1
elif [ $WARNINGS -gt 0 ]; then
    echo -e "${YELLOW}⚠️  $WARNINGS warnings found - please review${NC}"
    echo -e "${GREEN}✅ Commit allowed but consider addressing warnings${NC}"
    exit 0
else
    echo -e "${GREEN}✅ All authentication security checks passed${NC}"
    echo -e "${GREEN}✅ Commit approved${NC}"
    exit 0
fi
