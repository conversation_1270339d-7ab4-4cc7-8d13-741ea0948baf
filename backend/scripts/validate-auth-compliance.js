#!/usr/bin/env node

/**
 * 🔐 SQL API Authentication Compliance Validator
 * 
 * This script ensures all controllers and endpoints properly use
 * the SQL API authentication system without breaking existing functionality.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class AuthComplianceValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.controllerPatterns = {
      jwtGuard: /@UseGuards\([^)]*JwtAuthGuard[^)]*\)/,
      rolesGuard: /@UseGuards\([^)]*RolesGuard[^)]*\)/,
      rolesDecorator: /@Roles\(/,
      requestParam: /@Request\(\)\s+req/,
      controllerDecorator: /@Controller\(/
    };
  }

  /**
   * 🔐 Validate all controllers follow SQL API auth patterns
   */
  validateControllers() {
    console.log('🔐 [AUTH-COMPLIANCE] Validating controller authentication patterns...');

    const controllerFiles = glob.sync('src/**/*.controller.ts', { cwd: process.cwd() });

    for (const file of controllerFiles) {
      this.validateControllerFile(file);
    }
  }

  /**
   * 🔐 Validate individual controller file
   */
  validateControllerFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.basename(filePath);

    // Skip auth controller itself
    if (fileName.includes('auth.controller')) {
      return;
    }

    // Check if it's a controller
    if (!this.controllerPatterns.controllerDecorator.test(content)) {
      return;
    }

    console.log(`🔍 Checking: ${filePath}`);

    // Check for JWT Guard usage
    if (!this.controllerPatterns.jwtGuard.test(content)) {
      this.errors.push({
        file: filePath,
        type: 'MISSING_JWT_GUARD',
        message: 'Controller missing @UseGuards(JwtAuthGuard) - all controllers must use SQL API authentication'
      });
    }

    // Check for proper Request parameter usage
    const methodMatches = content.match(/@(Get|Post|Put|Patch|Delete)\(/g);
    if (methodMatches && methodMatches.length > 0) {
      if (!this.controllerPatterns.requestParam.test(content)) {
        this.warnings.push({
          file: filePath,
          type: 'MISSING_REQUEST_PARAM',
          message: 'Controller methods should use @Request() req to access authenticated user info'
        });
      }
    }

    // Check for role-based endpoints
    const hasRoleProtectedMethods = this.controllerPatterns.rolesDecorator.test(content);
    if (hasRoleProtectedMethods && !this.controllerPatterns.rolesGuard.test(content)) {
      this.errors.push({
        file: filePath,
        type: 'MISSING_ROLES_GUARD',
        message: 'Controller uses @Roles but missing @UseGuards(RolesGuard)'
      });
    }
  }

  /**
   * 🔐 Validate service files don't bypass auth
   */
  validateServices() {
    console.log('🔐 [AUTH-COMPLIANCE] Validating service authentication patterns...');

    const serviceFiles = glob.sync('src/**/*.service.ts', { cwd: process.cwd() });

    for (const file of serviceFiles) {
      this.validateServiceFile(file);
    }
  }

  /**
   * 🔐 Validate against rogue SQL patterns
   */
  validateSqlSecurity() {
    console.log('🔐 [SQL-SECURITY] Validating against rogue SQL patterns...');

    const allFiles = glob.sync('src/**/*.ts', { cwd: process.cwd() });

    for (const file of allFiles) {
      this.validateSqlSecurityInFile(file);
    }
  }

  /**
   * 🔐 Validate individual service file
   */
  validateServiceFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');

    // Skip auth-related services
    if (filePath.includes('/auth/') || filePath.includes('/security/')) {
      return;
    }

    // Check for direct database queries without user context
    const dangerousPatterns = [
      /\.findOne\(\s*{\s*where:\s*{\s*id:/,
      /\.find\(\s*{\s*where:\s*{/,
      /\.update\(\s*\d+/,
      /\.delete\(\s*\d+/
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(content)) {
        this.warnings.push({
          file: filePath,
          type: 'POTENTIAL_AUTH_BYPASS',
          message: 'Service may be performing database operations without user context validation'
        });
        break;
      }
    }
  }

  /**
   * 🔐 Generate compliance report
   */
  generateReport() {
    console.log('\n🔐 [AUTH-COMPLIANCE] Validation Report');
    console.log('=====================================');

    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('✅ All files comply with SQL API authentication patterns!');
      return true;
    }

    if (this.errors.length > 0) {
      console.log('\n❌ CRITICAL ERRORS (Must Fix):');
      this.errors.forEach(error => {
        console.log(`   ${error.file}: ${error.message}`);
      });
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS (Review Recommended):');
      this.warnings.forEach(warning => {
        console.log(`   ${warning.file}: ${warning.message}`);
      });
    }

    console.log('\n📋 COMPLIANCE CHECKLIST:');
    console.log('- All controllers must use @UseGuards(JwtAuthGuard)');
    console.log('- Role-protected endpoints must use @UseGuards(RolesGuard)');
    console.log('- Controller methods should use @Request() req for user context');
    console.log('- Services should validate user permissions before database operations');
    console.log('- Never bypass the SQL API authentication system');

    return this.errors.length === 0;
  }

  /**
   * 🔐 Validate SQL security in individual file
   */
  validateSqlSecurityInFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');

    // Skip auth-related files (they need special SQL access)
    if (filePath.includes('/auth/') || filePath.includes('/security/')) {
      return;
    }

    // 🚨 CRITICAL: Detect raw SQL queries
    const rawSqlPatterns = [
      /\.query\s*\(\s*['"`]/,  // .query("SELECT...")
      /\.query\s*\(\s*`/,      // .query(`SELECT...`)
      /connection\.execute/,    // connection.execute()
      /connection\.query/,      // connection.query()
      /SELECT\s+\*\s+FROM/i,   // SELECT * FROM (dangerous)
      /DELETE\s+FROM.*WHERE/i, // DELETE FROM without user context
      /UPDATE\s+.*SET.*WHERE/i // UPDATE without user context
    ];

    for (const pattern of rawSqlPatterns) {
      if (pattern.test(content)) {
        this.errors.push({
          file: filePath,
          type: 'ROGUE_SQL_DETECTED',
          message: 'Raw SQL query detected - must use TypeORM with user context validation'
        });
        break;
      }
    }

    // 🚨 CRITICAL: Detect dangerous repository patterns
    const dangerousRepoPatterns = [
      /\.find\(\s*\)/,                    // .find() without filters
      /\.findOne\(\s*\d+\s*\)/,          // .findOne(id) without user context
      /\.delete\(\s*\d+\s*\)/,           // .delete(id) without user context
      /\.update\(\s*\d+\s*,/,            // .update(id, ...) without user context
      /\.createQueryBuilder\([^)]*\)(?!\s*\.where)/  // Query builder without where clause
    ];

    for (const pattern of dangerousRepoPatterns) {
      if (pattern.test(content)) {
        this.warnings.push({
          file: filePath,
          type: 'UNSAFE_REPOSITORY_ACCESS',
          message: 'Repository access without user context validation - potential security risk'
        });
        break;
      }
    }

    // 🚨 CRITICAL: Detect missing user context in service methods
    if (filePath.includes('.service.ts')) {
      const methodMatches = content.match(/async\s+\w+\s*\([^)]*\)/g);
      if (methodMatches) {
        for (const method of methodMatches) {
          if (!method.includes('userId') && !method.includes('userRole')) {
            // Skip certain utility methods
            if (!method.includes('validate') && !method.includes('hash') && !method.includes('generate')) {
              this.warnings.push({
                file: filePath,
                type: 'MISSING_USER_CONTEXT',
                message: `Service method may be missing user context: ${method.substring(0, 50)}...`
              });
            }
          }
        }
      }
    }
  }

  /**
   * 🔐 Run full validation
   */
  run() {
    console.log('🔐 [AUTH-COMPLIANCE] Starting SQL API Authentication Compliance Check...\n');

    this.validateControllers();
    this.validateServices();
    this.validateSqlSecurity();

    const isCompliant = this.generateReport();

    if (!isCompliant) {
      console.log('\n🚨 COMPLIANCE FAILURE: Fix critical errors before proceeding');
      process.exit(1);
    }

    console.log('\n✅ SQL API Authentication Compliance Check Passed!');
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new AuthComplianceValidator();
  validator.run();
}

module.exports = AuthComplianceValidator;
