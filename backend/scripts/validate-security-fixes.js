#!/usr/bin/env node

/**
 * 🔐 Security Fixes Validation Script
 * 
 * This script validates that all security fixes have been properly implemented:
 * - Production debug logging disabled
 * - TypeORM synchronize disabled in production
 * - Session token validation improved
 * - Input sanitization implemented
 * - Configuration exposure reduced
 * - Timing attack protection implemented
 */

const fs = require('fs');
const path = require('path');

class SecurityFixesValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
  }

  /**
   * 🔐 Run all security validation checks
   */
  async validate() {
    console.log('🔐 [SECURITY-VALIDATOR] Starting security fixes validation...\n');

    this.validateDebugLoggingFixes();
    this.validateTypeORMSynchronizeFix();
    this.validateSessionTokenValidation();
    this.validateInputSanitization();
    this.validateConfigurationExposure();
    this.validateTimingAttackProtection();
    
    this.printResults();
    
    if (this.errors.length > 0) {
      console.log('\n❌ Security fixes validation FAILED. Critical issues found.');
      process.exit(1);
    } else if (this.warnings.length > 0) {
      console.log('\n⚠️  Security fixes validation passed with warnings.');
      process.exit(0);
    } else {
      console.log('\n✅ All security fixes validation PASSED. System is secure.');
      process.exit(0);
    }
  }

  /**
   * 🔐 Validate debug logging fixes
   */
  validateDebugLoggingFixes() {
    console.log('🔍 Checking debug logging fixes...');

    // Check JWT Strategy
    const jwtStrategyPath = 'src/auth/strategies/jwt.strategy.ts';
    if (fs.existsSync(jwtStrategyPath)) {
      const content = fs.readFileSync(jwtStrategyPath, 'utf8');
      
      if (content.includes('process.env.NODE_ENV !== \'production\'') || 
          content.includes('process.env.NODE_ENV === \'development\'')) {
        this.passed.push('JWT Strategy debug logging properly protected');
      } else {
        this.errors.push('JWT Strategy debug logging not properly protected');
      }
    }

    // Check Auth Module
    const authModulePath = 'src/auth/auth.module.ts';
    if (fs.existsSync(authModulePath)) {
      const content = fs.readFileSync(authModulePath, 'utf8');
      
      if (content.includes('process.env.NODE_ENV !== \'production\'')) {
        this.passed.push('Auth Module debug logging properly protected');
      } else {
        this.errors.push('Auth Module debug logging not properly protected');
      }
    }
  }

  /**
   * 🔐 Validate TypeORM synchronize fix
   */
  validateTypeORMSynchronizeFix() {
    console.log('🔍 Checking TypeORM synchronize fix...');

    const appModulePath = 'src/app.module.ts';
    if (fs.existsSync(appModulePath)) {
      const content = fs.readFileSync(appModulePath, 'utf8');
      
      if (content.includes('synchronize: process.env.NODE_ENV !== \'production\'')) {
        this.passed.push('TypeORM synchronize properly disabled in production');
      } else {
        this.errors.push('TypeORM synchronize not properly configured for production');
      }
    }
  }

  /**
   * 🔐 Validate session token validation improvement
   */
  validateSessionTokenValidation() {
    console.log('🔍 Checking session token validation fixes...');

    const jwtStrategyPath = 'src/auth/strategies/jwt.strategy.ts';
    if (fs.existsSync(jwtStrategyPath)) {
      const content = fs.readFileSync(jwtStrategyPath, 'utf8');
      
      if (content.includes('sessionTokens.includes(payload.sessionId)') && 
          !content.includes('user.sessionToken.includes(payload.sessionId)')) {
        this.passed.push('Session token validation improved with exact matching');
      } else {
        this.errors.push('Session token validation still uses weak includes() method');
      }
    }
  }

  /**
   * 🔐 Validate input sanitization implementation
   */
  validateInputSanitization() {
    console.log('🔍 Checking input sanitization implementation...');

    // Check CSV sanitization
    const bulkImportPath = '../frontend/src/components/users/BulkUserImport.tsx';
    if (fs.existsSync(bulkImportPath)) {
      const content = fs.readFileSync(bulkImportPath, 'utf8');
      
      if (content.includes('sanitizeCsvValue') && content.includes('CSV injection protection')) {
        this.passed.push('CSV injection protection implemented');
      } else {
        this.errors.push('CSV injection protection not implemented');
      }
    }

    // Check CSP report sanitization
    const securityControllerPath = 'src/security/controllers/security.controller.ts';
    if (fs.existsSync(securityControllerPath)) {
      const content = fs.readFileSync(securityControllerPath, 'utf8');
      
      if (content.includes('sanitizeCSPReport') && content.includes('sanitizeString')) {
        this.passed.push('CSP report input sanitization implemented');
      } else {
        this.errors.push('CSP report input sanitization not implemented');
      }
    }
  }

  /**
   * 🔐 Validate configuration exposure reduction
   */
  validateConfigurationExposure() {
    console.log('🔍 Checking configuration exposure reduction...');

    const systemControllerPath = 'src/system/system.controller.ts';
    if (fs.existsSync(systemControllerPath)) {
      const content = fs.readFileSync(systemControllerPath, 'utf8');
      
      if (!content.includes('process.env.DB_HOST') && 
          !content.includes('process.env.DB_PORT') &&
          content.includes('Don\'t expose actual host/port/name')) {
        this.passed.push('Database configuration exposure reduced');
      } else {
        this.errors.push('Database configuration still exposed in system controller');
      }
    }

    const securityControllerPath = 'src/security/controllers/security.controller.ts';
    if (fs.existsSync(securityControllerPath)) {
      const content = fs.readFileSync(securityControllerPath, 'utf8');
      
      if (content.includes('Limited exposure') && 
          content.includes('non-sensitive configuration')) {
        this.passed.push('Security configuration exposure reduced');
      } else {
        this.warnings.push('Security configuration exposure could be further reduced');
      }
    }
  }

  /**
   * 🔐 Validate timing attack protection
   */
  validateTimingAttackProtection() {
    console.log('🔍 Checking timing attack protection...');

    const authServicePath = 'src/auth/auth.service.ts';
    if (fs.existsSync(authServicePath)) {
      const content = fs.readFileSync(authServicePath, 'utf8');
      
      if (content.includes('TIMING ATTACK PROTECTION') && 
          content.includes('ensureMinimumDelay') &&
          content.includes('dummy bcrypt operation')) {
        this.passed.push('Timing attack protection implemented');
      } else {
        this.errors.push('Timing attack protection not properly implemented');
      }
    }
  }

  /**
   * 🔐 Print validation results
   */
  printResults() {
    console.log('\n📊 SECURITY FIXES VALIDATION RESULTS:');
    console.log('=====================================');

    if (this.passed.length > 0) {
      console.log('\n✅ PASSED CHECKS:');
      this.passed.forEach(check => console.log(`  ✅ ${check}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`));
    }

    if (this.errors.length > 0) {
      console.log('\n❌ FAILED CHECKS:');
      this.errors.forEach(error => console.log(`  ❌ ${error}`));
    }

    console.log(`\nSUMMARY: ${this.passed.length} passed, ${this.warnings.length} warnings, ${this.errors.length} errors`);
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new SecurityFixesValidator();
  validator.validate().catch(error => {
    console.error('❌ Validation failed with error:', error);
    process.exit(1);
  });
}

module.exports = SecurityFixesValidator;
