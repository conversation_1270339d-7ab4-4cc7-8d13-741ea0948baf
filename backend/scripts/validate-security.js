#!/usr/bin/env node

/**
 * 🔐 NIS2-Compliant Security Validation Script
 * 
 * Validates security configuration before deployment
 * Run with: node scripts/validate-security.js
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SecurityValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.recommendations = [];
    this.isProduction = process.env.NODE_ENV === 'production';
  }

  /**
   * 🔐 Run complete security validation
   */
  async validate() {
    console.log('🔐 [SECURITY-VALIDATOR] Starting security validation...\n');

    this.validateEnvironmentVariables();
    this.validateJWTSecurity();
    this.validateDatabaseSecurity();
    this.validateHTTPSConfiguration();
    this.validateFilePermissions();
    this.validateDependencies();
    
    this.printResults();
    
    if (this.errors.length > 0) {
      console.log('\n❌ Security validation FAILED. Address critical issues before deployment.');
      process.exit(1);
    } else if (this.warnings.length > 0) {
      console.log('\n⚠️  Security validation passed with warnings. Consider addressing them.');
      process.exit(0);
    } else {
      console.log('\n✅ Security validation PASSED. System is ready for deployment.');
      process.exit(0);
    }
  }

  /**
   * 🔐 Validate environment variables
   */
  validateEnvironmentVariables() {
    console.log('🔍 Validating environment variables...');

    const requiredVars = [
      'JWT_SECRET',
      'DB_PASSWORD',
      'NODE_ENV'
    ];

    // Check required variables
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        this.errors.push(`Missing required environment variable: ${varName}`);
      }
    }

    // Validate JWT_SECRET strength
    const jwtSecret = process.env.JWT_SECRET;
    if (jwtSecret) {
      if (jwtSecret.length < 32) {
        this.errors.push('JWT_SECRET must be at least 32 characters for NIS2 compliance');
      } else if (jwtSecret.length < 64) {
        this.warnings.push('JWT_SECRET should be at least 64 characters for optimal security');
      }

      // Check for weak patterns
      const weakPatterns = ['secret', 'password', 'jwt', 'token', '123456', 'admin'];
      if (weakPatterns.some(pattern => jwtSecret.toLowerCase().includes(pattern))) {
        this.errors.push('JWT_SECRET contains weak or predictable patterns');
      }

      // Check entropy
      const entropy = this.calculateEntropy(jwtSecret);
      if (entropy < 4.0) {
        this.warnings.push(`JWT_SECRET has low entropy (${entropy.toFixed(2)}). Consider using more random characters.`);
      }
    }

    // Production-specific checks
    if (this.isProduction) {
      if (!process.env.FRONTEND_URL || process.env.FRONTEND_URL.includes('localhost')) {
        this.errors.push('FRONTEND_URL must be set to production domain in production');
      }

      if (!process.env.FORCE_HTTPS) {
        this.errors.push('FORCE_HTTPS must be enabled in production');
      }

      if (!process.env.SSL_CERT_PATH) {
        this.warnings.push('SSL_CERT_PATH not configured for production');
      }
    }
  }

  /**
   * 🔐 Validate JWT security
   */
  validateJWTSecurity() {
    console.log('🔍 Validating JWT security...');

    const jwtExpiration = process.env.JWT_EXPIRATION || '1h';
    const expirationMs = this.parseTimeToMs(jwtExpiration);

    if (expirationMs > 24 * 60 * 60 * 1000) {
      this.warnings.push('JWT expiration time is longer than 24 hours');
    }

    if (expirationMs > 7 * 24 * 60 * 60 * 1000) {
      this.errors.push('JWT expiration time exceeds 7 days, violates security best practices');
    }

    // Check for refresh token configuration
    if (!process.env.REFRESH_TOKEN_SECRET && this.isProduction) {
      this.warnings.push('REFRESH_TOKEN_SECRET not configured for production');
    }
  }

  /**
   * 🔐 Validate database security
   */
  validateDatabaseSecurity() {
    console.log('🔍 Validating database security...');

    const dbPassword = process.env.DB_PASSWORD;
    if (dbPassword && dbPassword.length < 12) {
      this.warnings.push('Database password should be at least 12 characters');
    }

    const dbUsername = process.env.DB_USERNAME;
    if (dbUsername === 'root' || dbUsername === 'admin') {
      this.warnings.push('Database username appears to be a privileged account');
    }

    if (this.isProduction && !process.env.DB_SSL) {
      this.warnings.push('Database SSL not enabled in production');
    }
  }

  /**
   * 🔐 Validate HTTPS configuration
   */
  validateHTTPSConfiguration() {
    console.log('🔍 Validating HTTPS configuration...');

    if (this.isProduction && !process.env.FORCE_HTTPS) {
      this.errors.push('HTTPS enforcement not enabled in production');
    }

    const sslCertPath = process.env.SSL_CERT_PATH;
    const sslKeyPath = process.env.SSL_KEY_PATH;

    if (this.isProduction) {
      if (sslCertPath && !fs.existsSync(sslCertPath)) {
        this.errors.push(`SSL certificate not found at: ${sslCertPath}`);
      }

      if (sslKeyPath && !fs.existsSync(sslKeyPath)) {
        this.errors.push(`SSL private key not found at: ${sslKeyPath}`);
      }
    }
  }

  /**
   * 🔐 Validate file permissions
   */
  validateFilePermissions() {
    console.log('🔍 Validating file permissions...');

    const sensitiveFiles = [
      '.env',
      '.env.production',
      'ssl/private.key',
      'ssl/server.key'
    ];

    for (const file of sensitiveFiles) {
      const filePath = path.join(process.cwd(), file);
      if (fs.existsSync(filePath)) {
        try {
          const stats = fs.statSync(filePath);
          const mode = stats.mode & parseInt('777', 8);
          
          // Check if file is readable by others
          if (mode & parseInt('044', 8)) {
            this.warnings.push(`Sensitive file ${file} is readable by others (permissions: ${mode.toString(8)})`);
          }
        } catch (error) {
          this.warnings.push(`Could not check permissions for ${file}: ${error.message}`);
        }
      }
    }
  }

  /**
   * 🔐 Validate dependencies
   */
  validateDependencies() {
    console.log('🔍 Validating dependencies...');

    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

      // Check for known vulnerable packages (basic check)
      const potentiallyVulnerable = [
        'lodash',
        'moment',
        'request',
        'node-uuid'
      ];

      for (const pkg of potentiallyVulnerable) {
        if (dependencies[pkg]) {
          this.recommendations.push(`Consider updating or replacing ${pkg} with more secure alternatives`);
        }
      }

      // Check for development dependencies in production
      if (this.isProduction && packageJson.devDependencies) {
        this.recommendations.push('Consider using npm ci --only=production for production builds');
      }

    } catch (error) {
      this.warnings.push(`Could not validate dependencies: ${error.message}`);
    }
  }

  /**
   * 🔐 Calculate string entropy
   */
  calculateEntropy(str) {
    const freq = {};
    for (let char of str) {
      freq[char] = (freq[char] || 0) + 1;
    }

    let entropy = 0;
    const len = str.length;
    for (let char in freq) {
      const p = freq[char] / len;
      entropy -= p * Math.log2(p);
    }

    return entropy;
  }

  /**
   * 🔐 Parse time string to milliseconds
   */
  parseTimeToMs(timeStr) {
    const match = timeStr.match(/^(\d+)([smhd])$/);
    if (!match) return 0;

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value * 1000;
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: return 0;
    }
  }

  /**
   * 🔐 Print validation results
   */
  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🔐 SECURITY VALIDATION RESULTS');
    console.log('='.repeat(60));

    if (this.errors.length > 0) {
      console.log('\n❌ CRITICAL ISSUES:');
      this.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.warnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning}`);
      });
    }

    if (this.recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      this.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec}`);
      });
    }

    const score = Math.max(0, 100 - (this.errors.length * 20) - (this.warnings.length * 5));
    console.log(`\n📊 SECURITY SCORE: ${score}/100`);
    
    if (score >= 90) {
      console.log('🟢 Excellent security posture');
    } else if (score >= 70) {
      console.log('🟡 Good security posture with room for improvement');
    } else if (score >= 50) {
      console.log('🟠 Moderate security posture, address warnings');
    } else {
      console.log('🔴 Poor security posture, address critical issues');
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const validator = new SecurityValidator();
  validator.validate().catch(error => {
    console.error('❌ Security validation failed:', error);
    process.exit(1);
  });
}

module.exports = SecurityValidator;
