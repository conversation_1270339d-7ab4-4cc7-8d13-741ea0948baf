import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AiInsight, InsightStatus } from './entities/ai-insight.entity';
import { AttritionPrediction } from './entities/attrition-prediction.entity';
import { User } from '../users/entities/user.entity';
import { AttritionPredictionService } from './services/attrition-prediction.service';
import { SentimentAnalysisService } from './services/sentiment-analysis.service';
import { InsightsEngineService } from './services/insights-engine.service';
import { CompetencyMappingService } from './services/competency-mapping.service';
import { CareerPathService } from './services/career-path.service';

@Injectable()
export class AiService {
  constructor(
    @InjectRepository(AiInsight)
    private readonly aiInsightRepository: Repository<AiInsight>,
    @InjectRepository(AttritionPrediction)
    private readonly attritionPredictionRepository: Repository<AttritionPrediction>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly attritionPredictionService: AttritionPredictionService,
    private readonly sentimentAnalysisService: SentimentAnalysisService,
    private readonly insightsEngineService: InsightsEngineService,
    private readonly competencyMappingService: CompetencyMappingService,
    private readonly careerPathService: CareerPathService,
  ) { }

  /**
   * Get AI dashboard summary with key metrics and insights
   */
  async getDashboardSummary(userId: number) {
    try {
      // Get recent insights
      const recentInsights = await this.aiInsightRepository.find({
        where: { status: InsightStatus.NEW },
        order: { generatedAt: 'DESC' },
        take: 5,
      });

      // Get attrition risk summary
      const attritionSummary = await this.attritionPredictionService.getAttritionRiskAnalysis({});

      // Get sentiment trends
      const sentimentTrends = await this.sentimentAnalysisService.getSentimentTrends('3months');

      // Get skill gap summary
      const skillGapSummary = await this.competencyMappingService.getSkillGapAnalysis();

      // Calculate overall AI health score
      const healthScore = await this.calculateAiHealthScore();

      return {
        success: true,
        data: {
          healthScore,
          recentInsights: recentInsights.map(insight => ({
            id: insight.id,
            type: insight.insightType,
            priority: insight.priority,
            summary: this.extractInsightSummary(insight.insightData),
            generatedAt: insight.generatedAt,
          })),
          attritionRisk: {
            overallRisk: attritionSummary.data?.overallRisk || 'low',
            atRiskCount: attritionSummary.data?.atRiskCount || 0,
            totalEmployees: attritionSummary.data?.totalEmployees || 0,
          },
          sentimentTrends: {
            currentScore: sentimentTrends.data?.currentScore || 0,
            trend: sentimentTrends.data?.trend || 'stable',
            keyDrivers: sentimentTrends.data?.keyDrivers || [],
          },
          skillGaps: {
            criticalGaps: skillGapSummary.data?.criticalGaps || 0,
            totalSkillsAssessed: skillGapSummary.data?.totalSkillsAssessed || 0,
            improvementOpportunities: skillGapSummary.data?.improvementOpportunities || [],
          },
          recommendations: await this.generateDashboardRecommendations(),
        },
      };
    } catch (error) {
      console.error('Error getting AI dashboard summary:', error);
      return {
        success: false,
        error: 'Failed to get AI dashboard summary',
      };
    }
  }

  /**
   * Generate AI-powered recommendations based on type and target
   */
  async generateRecommendations(type: string, targetId?: number, userId?: number) {
    try {
      let recommendations = [];

      switch (type) {
        case 'attrition_prevention':
          recommendations = await this.generateAttritionPreventionRecommendations(targetId);
          break;
        case 'engagement_improvement':
          recommendations = await this.generateEngagementRecommendations(targetId);
          break;
        case 'skill_development':
          recommendations = await this.generateSkillDevelopmentRecommendations(targetId);
          break;
        case 'career_advancement':
          recommendations = await this.generateCareerAdvancementRecommendations(targetId);
          break;
        default:
          recommendations = await this.generateGeneralRecommendations(targetId);
      }

      return {
        success: true,
        data: {
          type,
          targetId,
          recommendations,
          generatedAt: new Date(),
          generatedBy: userId,
        },
      };
    } catch (error) {
      console.error('Error generating recommendations:', error);
      return {
        success: false,
        error: 'Failed to generate recommendations',
      };
    }
  }

  /**
   * Calculate overall AI health score based on various metrics
   */
  private async calculateAiHealthScore(): Promise<number> {
    try {
      let score = 0;
      let factors = 0;

      // Factor 1: Attrition risk (25% weight)
      const attritionData = await this.attritionPredictionService.getAttritionRiskAnalysis({});
      if (attritionData.success) {
        const riskScore = attritionData.data?.riskScore || 0;
        score += (100 - riskScore) * 0.25;
        factors++;
      }

      // Factor 2: Engagement sentiment (25% weight)
      const sentimentData = await this.sentimentAnalysisService.getSentimentTrends('1month');
      if (sentimentData.success) {
        const sentimentScore = sentimentData.data?.currentScore || 70;
        score += sentimentScore * 0.25;
        factors++;
      }

      // Factor 3: Skill gap coverage (25% weight)
      const skillData = await this.competencyMappingService.getSkillGapAnalysis();
      if (skillData.success) {
        const gapScore = Math.max(0, 100 - (skillData.data?.criticalGaps || 0) * 10);
        score += gapScore * 0.25;
        factors++;
      }

      // Factor 4: AI insights actionability (25% weight)
      const insightsCount = await this.aiInsightRepository.count({
        where: { status: InsightStatus.ACTED_UPON },
      });
      const totalInsights = await this.aiInsightRepository.count();
      const actionabilityScore = totalInsights > 0 ? (insightsCount / totalInsights) * 100 : 80;
      score += actionabilityScore * 0.25;
      factors++;

      return factors > 0 ? Math.round(score / factors * 100) / 100 : 75;
    } catch (error) {
      console.error('Error calculating AI health score:', error);
      return 75; // Default score
    }
  }

  /**
   * Extract summary from insight data
   */
  private extractInsightSummary(insightData: any): string {
    if (typeof insightData === 'object' && insightData.summary) {
      return insightData.summary;
    }
    if (typeof insightData === 'object' && insightData.description) {
      return insightData.description;
    }
    return 'AI-generated insight available';
  }

  /**
   * Generate dashboard recommendations
   */
  private async generateDashboardRecommendations(): Promise<any[]> {
    const recommendations = [];

    // Check for high attrition risk
    const attritionData = await this.attritionPredictionService.getAttritionRiskAnalysis({});
    if (attritionData.success && attritionData.data?.riskScore > 60) {
      recommendations.push({
        type: 'attrition_alert',
        priority: 'high',
        title: 'High Attrition Risk Detected',
        description: 'Consider implementing retention strategies for at-risk employees',
        action: 'Review attrition predictions and create intervention plans',
      });
    }

    // Check for sentiment issues
    const sentimentData = await this.sentimentAnalysisService.getSentimentTrends('1month');
    if (sentimentData.success && sentimentData.data?.currentScore < 60) {
      recommendations.push({
        type: 'engagement_alert',
        priority: 'medium',
        title: 'Low Engagement Detected',
        description: 'Employee engagement scores are below optimal levels',
        action: 'Conduct pulse surveys and address key engagement drivers',
      });
    }

    // Check for skill gaps
    const skillData = await this.competencyMappingService.getSkillGapAnalysis();
    if (skillData.success && skillData.data?.criticalGaps > 5) {
      recommendations.push({
        type: 'skill_gap_alert',
        priority: 'medium',
        title: 'Critical Skill Gaps Identified',
        description: 'Multiple critical skill gaps detected across teams',
        action: 'Develop targeted training programs and hiring strategies',
      });
    }

    return recommendations;
  }

  /**
   * Generate attrition prevention recommendations
   */
  private async generateAttritionPreventionRecommendations(targetId?: number): Promise<any[]> {
    // Implementation for attrition prevention recommendations
    return [
      {
        type: 'retention_strategy',
        priority: 'high',
        title: 'Implement Stay Interviews',
        description: 'Conduct regular stay interviews with high-risk employees',
        impact: 'Can reduce attrition by 15-25%',
      },
      {
        type: 'career_development',
        priority: 'medium',
        title: 'Create Career Development Plans',
        description: 'Establish clear career progression paths for at-risk employees',
        impact: 'Improves retention and engagement',
      },
    ];
  }

  /**
   * Generate engagement improvement recommendations
   */
  private async generateEngagementRecommendations(targetId?: number): Promise<any[]> {
    return [
      {
        type: 'recognition_program',
        priority: 'medium',
        title: 'Enhance Recognition Programs',
        description: 'Implement peer-to-peer recognition and achievement badges',
        impact: 'Can improve engagement scores by 10-20%',
      },
    ];
  }

  /**
   * Generate skill development recommendations
   */
  private async generateSkillDevelopmentRecommendations(targetId?: number): Promise<any[]> {
    return [
      {
        type: 'training_program',
        priority: 'medium',
        title: 'Targeted Skill Training',
        description: 'Develop training programs for identified skill gaps',
        impact: 'Improves team capability and performance',
      },
    ];
  }

  /**
   * Generate career advancement recommendations
   */
  private async generateCareerAdvancementRecommendations(targetId?: number): Promise<any[]> {
    return [
      {
        type: 'succession_planning',
        priority: 'high',
        title: 'Succession Planning Initiative',
        description: 'Identify and develop high-potential employees for key roles',
        impact: 'Ensures business continuity and employee growth',
      },
    ];
  }

  /**
   * Generate general recommendations
   */
  private async generateGeneralRecommendations(targetId?: number): Promise<any[]> {
    return [
      {
        type: 'data_quality',
        priority: 'low',
        title: 'Improve Data Quality',
        description: 'Ensure all employee data is complete and up-to-date',
        impact: 'Improves AI prediction accuracy',
      },
    ];
  }
}
