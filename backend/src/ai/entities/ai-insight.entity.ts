import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn } from 'typeorm';

export enum InsightType {
  ATTRITION_RISK = 'attrition_risk',
  ENGAGEMENT_TREND = 'engagement_trend',
  SKILL_GAP = 'skill_gap',
  PERFORMANCE_ANOMALY = 'performance_anomaly',
}

export enum TargetType {
  INDIVIDUAL = 'individual',
  TEAM = 'team',
  DEPARTMENT = 'department',
  ORGANIZATION = 'organization',
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum InsightStatus {
  NEW = 'new',
  ACKNOWLEDGED = 'acknowledged',
  ACTED_UPON = 'acted_upon',
  DISMISSED = 'dismissed',
}

@Entity('ai_insights')
export class AiInsight {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({
    name: 'insight_type',
    type: 'enum',
    enum: InsightType,
  })
  insightType!: InsightType;

  @Column({
    name: 'target_type',
    type: 'enum',
    enum: TargetType,
  })
  targetType!: TargetType;

  @Column({ name: 'target_id', nullable: true })
  targetId!: number;

  @Column({ name: 'insight_data', type: 'json' })
  insightData!: object;

  @Column({ name: 'confidence_score', type: 'decimal', precision: 5, scale: 4, nullable: true })
  confidenceScore!: number;

  @Column({
    type: 'enum',
    enum: Priority,
  })
  priority!: Priority;

  @Column({
    type: 'enum',
    enum: InsightStatus,
    default: InsightStatus.NEW,
  })
  status!: InsightStatus;

  @CreateDateColumn({ name: 'generated_at' })
  generatedAt!: Date;

  @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
  expiresAt!: Date;
}
