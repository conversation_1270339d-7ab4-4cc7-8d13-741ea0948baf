import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, ManyToOne, JoinC<PERSON>umn } from 'typeorm';
import { User } from '../../users/entities/user.entity';

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

@Entity('attrition_predictions')
export class AttritionPrediction {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ name: 'user_id' })
  userId!: number;

  @Column({ name: 'risk_score', type: 'decimal', precision: 5, scale: 4 })
  riskScore!: number; // 0.0000 to 1.0000

  @Column({
    name: 'risk_level',
    type: 'enum',
    enum: RiskLevel,
  })
  riskLevel!: RiskLevel;

  @Column({ name: 'contributing_factors', type: 'json', nullable: true })
  contributingFactors!: object;

  @Column({ name: 'prediction_date', type: 'date' })
  predictionDate!: Date;

  @Column({ name: 'model_version', length: 50, nullable: true })
  modelVersion!: string;

  @Column({ name: 'confidence_score', type: 'decimal', precision: 5, scale: 4, nullable: true })
  confidenceScore!: number;

  @Column({ name: 'recommended_actions', type: 'json', nullable: true })
  recommendedActions!: object;

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user!: User;
}
