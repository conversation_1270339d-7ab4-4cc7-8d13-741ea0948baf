import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AttritionPrediction, RiskLevel } from '../entities/attrition-prediction.entity';
import { User } from '../../users/entities/user.entity';
import { PerformanceMetric } from '../../analytics/entities/performance-metric.entity';
import { SurveyResponse } from '../../analytics/entities/survey-response.entity';

@Injectable()
export class AttritionPredictionService {
  constructor(
    @InjectRepository(AttritionPrediction)
    private readonly attritionPredictionRepository: Repository<AttritionPrediction>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(PerformanceMetric)
    private readonly performanceMetricRepository: Repository<PerformanceMetric>,
    @InjectRepository(SurveyResponse)
    private readonly surveyResponseRepository: Repository<SurveyResponse>,
  ) { }

  /**
   * Get attrition predictions with filtering options
   */
  async getAttritionPredictions(filters: {
    teamId?: number;
    departmentId?: number;
    riskLevel?: string;
    limit?: number;
  }) {
    try {
      const queryBuilder = this.attritionPredictionRepository
        .createQueryBuilder('prediction')
        .leftJoinAndSelect('prediction.user', 'user')
        .leftJoinAndSelect('user.organizationalUnit', 'orgUnit')
        .where('prediction.isActive = :isActive', { isActive: true });

      if (filters.teamId) {
        queryBuilder.andWhere('user.teamId = :teamId', { teamId: filters.teamId });
      }

      if (filters.departmentId) {
        queryBuilder.andWhere('orgUnit.id = :departmentId', { departmentId: filters.departmentId });
      }

      if (filters.riskLevel) {
        queryBuilder.andWhere('prediction.riskLevel = :riskLevel', { riskLevel: filters.riskLevel });
      }

      queryBuilder
        .orderBy('prediction.riskScore', 'DESC')
        .limit(filters.limit || 50);

      const predictions = await queryBuilder.getMany();

      return {
        success: true,
        data: predictions.map(prediction => ({
          id: prediction.id,
          userId: prediction.userId,
          userName: `${prediction.user?.firstName} ${prediction.user?.lastName}` || 'Unknown',
          userEmail: prediction.user?.email,
          department: prediction.user?.organizationalUnit?.name,
          role: prediction.user?.role,
          riskScore: prediction.riskScore,
          riskLevel: prediction.riskLevel,
          riskFactors: prediction.contributingFactors,
          recommendations: prediction.recommendedActions,
          predictedDate: prediction.predictionDate,
          confidence: prediction.confidenceScore,
          lastUpdated: prediction.createdAt,
        })),
      };
    } catch (error) {
      console.error('Error getting attrition predictions:', error);
      return {
        success: false,
        error: 'Failed to get attrition predictions',
      };
    }
  }

  /**
   * Get attrition risk analysis with aggregated metrics
   */
  async getAttritionRiskAnalysis(filters: {
    teamId?: number;
    departmentId?: number;
    period?: string;
  }) {
    try {
      const queryBuilder = this.attritionPredictionRepository
        .createQueryBuilder('prediction')
        .leftJoinAndSelect('prediction.user', 'user')
        .leftJoinAndSelect('user.orgUnit', 'orgUnit')
        .where('prediction.isActive = :isActive', { isActive: true });

      if (filters.teamId) {
        queryBuilder.andWhere('user.teamId = :teamId', { teamId: filters.teamId });
      }

      if (filters.departmentId) {
        queryBuilder.andWhere('orgUnit.id = :departmentId', { departmentId: filters.departmentId });
      }

      const predictions = await queryBuilder.getMany();

      // Calculate risk distribution
      const riskDistribution = {
        high: predictions.filter(p => p.riskLevel === 'high').length,
        medium: predictions.filter(p => p.riskLevel === 'medium').length,
        low: predictions.filter(p => p.riskLevel === 'low').length,
      };

      // Calculate average risk score
      const avgRiskScore = predictions.length > 0
        ? predictions.reduce((sum, p) => sum + p.riskScore, 0) / predictions.length
        : 0;

      // Get top risk factors
      const riskFactors = this.aggregateRiskFactors(predictions);

      // Calculate department-wise risk
      const departmentRisk = this.calculateDepartmentRisk(predictions);

      // Get trending data
      const trendData = await this.getAttritionTrends(filters.period || '6months');

      return {
        success: true,
        data: {
          totalEmployees: predictions.length,
          atRiskCount: riskDistribution.high + riskDistribution.medium,
          riskScore: Math.round(avgRiskScore * 100) / 100,
          overallRisk: this.calculateOverallRiskLevel(avgRiskScore),
          riskDistribution,
          topRiskFactors: riskFactors,
          departmentBreakdown: departmentRisk,
          trends: trendData,
          recommendations: this.generateRiskRecommendations(riskDistribution, avgRiskScore),
        },
      };
    } catch (error) {
      console.error('Error getting attrition risk analysis:', error);
      return {
        success: false,
        error: 'Failed to get attrition risk analysis',
      };
    }
  }

  /**
   * Generate new attrition predictions using ML algorithms
   */
  async generatePredictions(userId: number) {
    try {
      // Get all active users
      const users = await this.userRepository.find({
        where: { isActive: true },
        relations: ['organizationalUnit'],
      });

      const predictions = [];

      for (const user of users) {
        const prediction = await this.calculateUserAttritionRisk(user);
        predictions.push(prediction);
      }

      // Save predictions to database
      await this.attritionPredictionRepository.save(predictions);

      return {
        success: true,
        data: {
          predictionsGenerated: predictions.length,
          highRiskCount: predictions.filter(p => p.riskLevel === 'high').length,
          mediumRiskCount: predictions.filter(p => p.riskLevel === 'medium').length,
          lowRiskCount: predictions.filter(p => p.riskLevel === 'low').length,
          generatedAt: new Date(),
          generatedBy: userId,
        },
      };
    } catch (error) {
      console.error('Error generating predictions:', error);
      return {
        success: false,
        error: 'Failed to generate predictions',
      };
    }
  }

  /**
   * Get attrition heatmap data
   */
  async getAttritionHeatmap(groupBy: string = 'department') {
    try {
      const predictions = await this.attritionPredictionRepository.find({
        relations: ['user', 'user.orgUnit'],
      });

      let heatmapData: any[] = [];

      if (groupBy === 'department') {
        const departmentMap = new Map();

        predictions.forEach(prediction => {
          const dept = prediction.user?.organizationalUnit?.name || 'Unknown';
          if (!departmentMap.has(dept)) {
            departmentMap.set(dept, { name: dept, riskScores: [], count: 0 });
          }
          departmentMap.get(dept).riskScores.push(prediction.riskScore);
          departmentMap.get(dept).count++;
        });

        heatmapData = Array.from(departmentMap.values()).map(dept => ({
          name: dept.name,
          avgRiskScore: dept.riskScores.reduce((a: number, b: number) => a + b, 0) / dept.riskScores.length,
          employeeCount: dept.count,
          riskLevel: this.calculateOverallRiskLevel(dept.riskScores.reduce((a: number, b: number) => a + b, 0) / dept.riskScores.length),
        }));
      }

      return {
        success: true,
        data: {
          groupBy,
          heatmapData,
          generatedAt: new Date(),
        },
      };
    } catch (error) {
      console.error('Error getting attrition heatmap:', error);
      return {
        success: false,
        error: 'Failed to get attrition heatmap',
      };
    }
  }

  /**
   * Calculate attrition risk for a specific user
   */
  private async calculateUserAttritionRisk(user: User): Promise<Partial<AttritionPrediction>> {
    let riskScore = 0;
    const riskFactors = [];
    const recommendations = [];

    // Factor 1: Tenure (20% weight)
    const tenure = this.calculateTenure(user.createdAt);
    if (tenure < 6) {
      riskScore += 20; // High risk for new employees
      riskFactors.push('Short tenure (< 6 months)');
      recommendations.push('Implement enhanced onboarding and mentorship');
    } else if (tenure > 60) {
      riskScore += 10; // Moderate risk for long-tenure employees
      riskFactors.push('Long tenure (> 5 years)');
      recommendations.push('Provide new challenges and growth opportunities');
    }

    // Factor 2: Performance metrics (30% weight)
    const performanceRisk = await this.calculatePerformanceRisk(user.id);
    riskScore += performanceRisk.score;
    riskFactors.push(...performanceRisk.factors);
    recommendations.push(...performanceRisk.recommendations);

    // Factor 3: Engagement scores (25% weight)
    const engagementRisk = await this.calculateEngagementRisk(user.id);
    riskScore += engagementRisk.score;
    riskFactors.push(...engagementRisk.factors);
    recommendations.push(...engagementRisk.recommendations);

    // Factor 4: Career progression (15% weight)
    const careerRisk = await this.calculateCareerProgressionRisk(user);
    riskScore += careerRisk.score;
    riskFactors.push(...careerRisk.factors);
    recommendations.push(...careerRisk.recommendations);

    // Factor 5: Manager relationship (10% weight)
    const managerRisk = await this.calculateManagerRelationshipRisk(user.id);
    riskScore += managerRisk.score;
    riskFactors.push(...managerRisk.factors);
    recommendations.push(...managerRisk.recommendations);

    // Normalize risk score to 0-100
    riskScore = Math.min(100, Math.max(0, riskScore));

    return {
      userId: user.id,
      riskScore,
      riskLevel: this.calculateRiskLevel(riskScore),
      contributingFactors: { factors: riskFactors },
      recommendedActions: { actions: recommendations.slice(0, 5) }, // Top 5 recommendations
      predictionDate: this.calculatePredictedDate(riskScore),
      confidenceScore: this.calculateConfidence(riskFactors.length),
    };
  }

  /**
   * Calculate tenure in months
   */
  private calculateTenure(startDate: Date): number {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30)); // Convert to months
  }

  /**
   * Calculate performance-based risk
   */
  private async calculatePerformanceRisk(userId: number): Promise<{ score: number; factors: string[]; recommendations: string[] }> {
    const metrics = await this.performanceMetricRepository.find({
      where: { userId },
      order: { calculationDate: 'DESC' },
      take: 6, // Last 6 months
    });

    let score = 0;
    const factors = [];
    const recommendations = [];

    if (metrics.length === 0) {
      score += 15;
      factors.push('No performance data available');
      recommendations.push('Establish regular performance tracking');
      return { score, factors, recommendations };
    }

    const avgScore = metrics.reduce((sum, m) => sum + m.metricValue, 0) / metrics.length;

    if (avgScore < 60) {
      score += 25;
      factors.push('Below-average performance scores');
      recommendations.push('Provide performance improvement plan');
    } else if (avgScore < 75) {
      score += 10;
      factors.push('Moderate performance concerns');
      recommendations.push('Offer additional training and support');
    }

    return { score, factors, recommendations };
  }

  /**
   * Calculate engagement-based risk
   */
  private async calculateEngagementRisk(userId: number): Promise<{ score: number; factors: string[]; recommendations: string[] }> {
    // Implementation for engagement risk calculation
    return { score: 0, factors: [], recommendations: [] };
  }

  /**
   * Calculate career progression risk
   */
  private async calculateCareerProgressionRisk(user: User): Promise<{ score: number; factors: string[]; recommendations: string[] }> {
    // Implementation for career progression risk
    return { score: 0, factors: [], recommendations: [] };
  }

  /**
   * Calculate manager relationship risk
   */
  private async calculateManagerRelationshipRisk(userId: number): Promise<{ score: number; factors: string[]; recommendations: string[] }> {
    // Implementation for manager relationship risk
    return { score: 0, factors: [], recommendations: [] };
  }

  /**
   * Calculate risk level from score
   */
  private calculateRiskLevel(score: number): RiskLevel {
    if (score >= 70) return RiskLevel.HIGH;
    if (score >= 40) return RiskLevel.MEDIUM;
    return RiskLevel.LOW;
  }

  /**
   * Calculate overall risk level
   */
  private calculateOverallRiskLevel(avgScore: number): string {
    return this.calculateRiskLevel(avgScore);
  }

  /**
   * Calculate predicted attrition date
   */
  private calculatePredictedDate(riskScore: number): Date {
    const now = new Date();
    const monthsToAdd = Math.max(1, Math.round((100 - riskScore) / 10));
    return new Date(now.setMonth(now.getMonth() + monthsToAdd));
  }

  /**
   * Calculate prediction confidence
   */
  private calculateConfidence(factorCount: number): number {
    return Math.min(95, Math.max(60, 60 + (factorCount * 5)));
  }

  /**
   * Aggregate risk factors across predictions
   */
  private aggregateRiskFactors(predictions: AttritionPrediction[]): any[] {
    const factorMap = new Map();

    predictions.forEach(prediction => {
      if (prediction.contributingFactors) {
        (prediction.contributingFactors as any).factors?.forEach(factor => {
          factorMap.set(factor, (factorMap.get(factor) || 0) + 1);
        });
      }
    });

    return Array.from(factorMap.entries())
      .map(([factor, count]) => ({ factor, count, percentage: (count / predictions.length) * 100 }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Calculate department-wise risk
   */
  private calculateDepartmentRisk(predictions: AttritionPrediction[]): any[] {
    const deptMap = new Map();

    predictions.forEach(prediction => {
      const dept = prediction.user?.organizationalUnit?.name || 'Unknown';
      if (!deptMap.has(dept)) {
        deptMap.set(dept, { name: dept, total: 0, high: 0, medium: 0, low: 0 });
      }
      const deptData = deptMap.get(dept);
      deptData.total++;
      deptData[prediction.riskLevel]++;
    });

    return Array.from(deptMap.values());
  }

  /**
   * Get attrition trends over time
   */
  private async getAttritionTrends(period: string): Promise<any[]> {
    // Implementation for trend analysis
    return [];
  }

  /**
   * Generate risk-based recommendations
   */
  private generateRiskRecommendations(riskDistribution: any, avgRiskScore: number): string[] {
    const recommendations = [];

    if (riskDistribution.high > 0) {
      recommendations.push('Implement immediate retention strategies for high-risk employees');
    }

    if (avgRiskScore > 60) {
      recommendations.push('Review overall employee satisfaction and engagement programs');
    }

    if (riskDistribution.medium > riskDistribution.low) {
      recommendations.push('Focus on preventive measures for medium-risk employees');
    }

    return recommendations;
  }
}
