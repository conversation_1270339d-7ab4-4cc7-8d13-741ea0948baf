import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CompetencyFramework } from '../entities/competency-framework.entity';
import { User } from '../../users/entities/user.entity';
import { AssessmentInstance, AssessmentStatus } from '../../assessments/entities/assessment-instance.entity';

@Injectable()
export class CompetencyMappingService {
  constructor(
    @InjectRepository(CompetencyFramework)
    private readonly competencyFrameworkRepository: Repository<CompetencyFramework>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AssessmentInstance)
    private readonly assessmentInstanceRepository: Repository<AssessmentInstance>,
  ) { }

  /**
   * Get competency frameworks
   */
  async getFrameworks(orgUnitId?: number) {
    try {
      const queryBuilder = this.competencyFrameworkRepository.createQueryBuilder('framework');

      if (orgUnitId) {
        queryBuilder.where('framework.orgUnitId = :orgUnitId', { orgUnitId });
      }

      queryBuilder.orderBy('framework.createdAt', 'DESC');

      const frameworks = await queryBuilder.getMany();

      return {
        success: true,
        data: frameworks.map(framework => ({
          id: framework.id,
          name: framework.name,
          description: framework.description,
          competencies: framework.competencies,
          orgUnitId: framework.organizationalUnitId,
          isActive: framework.isActive,
          createdAt: framework.createdAt,
        })),
      };
    } catch (error) {
      console.error('Error getting competency frameworks:', error);
      return {
        success: false,
        error: 'Failed to get competency frameworks',
      };
    }
  }

  /**
   * Create a new competency framework
   */
  async createFramework(frameworkData: any, userId: number) {
    try {
      const framework = this.competencyFrameworkRepository.create({
        name: frameworkData.name,
        description: frameworkData.description,
        competencies: frameworkData.competencies,
        organizationalUnitId: frameworkData.orgUnitId,
        createdById: userId,
        isActive: true,
      });

      const savedFramework = await this.competencyFrameworkRepository.save(framework);

      return {
        success: true,
        data: savedFramework,
      };
    } catch (error) {
      console.error('Error creating competency framework:', error);
      return {
        success: false,
        error: 'Failed to create competency framework',
      };
    }
  }

  /**
   * Get skill gap analysis
   */
  async getSkillGapAnalysis(teamId?: number, role?: string) {
    try {
      // Get users based on filters
      const queryBuilder = this.userRepository.createQueryBuilder('user')
        .leftJoinAndSelect('user.orgUnit', 'orgUnit')
        .where('user.isActive = :isActive', { isActive: true });

      if (teamId) {
        queryBuilder.andWhere('user.teamId = :teamId', { teamId });
      }

      if (role) {
        queryBuilder.andWhere('user.role = :role', { role });
      }

      const users = await queryBuilder.getMany();

      // Get competency frameworks for analysis
      const frameworks = await this.competencyFrameworkRepository.find({
        where: { isActive: true },
      });

      // Get assessment data for skill evaluation
      const assessments = await this.assessmentInstanceRepository.find({
        where: { status: AssessmentStatus.COMPLETED },
        relations: ['user', 'assessment'],
      });

      // Analyze skill gaps
      const skillGapAnalysis = await this.analyzeSkillGaps(users, frameworks, assessments);

      return {
        success: true,
        data: skillGapAnalysis,
      };
    } catch (error) {
      console.error('Error getting skill gap analysis:', error);
      return {
        success: false,
        error: 'Failed to get skill gap analysis',
      };
    }
  }

  /**
   * Analyze skill gaps across users and competencies
   */
  private async analyzeSkillGaps(users: User[], frameworks: CompetencyFramework[], assessments: AssessmentInstance[]): Promise<any> {
    const skillGaps = [];
    const competencyScores = new Map();
    const departmentGaps = new Map();
    const roleGaps = new Map();

    // Create assessment score map for quick lookup
    const assessmentScoreMap = new Map();
    assessments.forEach(assessment => {
      const key = `${assessment.employeeId}-${assessment.template?.name || 'unknown'}`;
      assessmentScoreMap.set(key, assessment.totalScore || 0);
    });

    // Analyze each user against competency frameworks
    for (const user of users) {
      const userSkillProfile = await this.buildUserSkillProfile(user, assessments);

      for (const framework of frameworks) {
        if (framework.competencies && typeof framework.competencies === 'object') {
          const gaps = this.identifyUserSkillGaps(user, userSkillProfile, framework);
          skillGaps.push(...gaps);

          // Aggregate by department
          const dept = user.organizationalUnit?.name || 'Unknown';
          if (!departmentGaps.has(dept)) {
            departmentGaps.set(dept, { total: 0, critical: 0, moderate: 0, minor: 0 });
          }
          const deptData = departmentGaps.get(dept);
          gaps.forEach(gap => {
            deptData.total++;
            deptData[gap.severity]++;
          });

          // Aggregate by role
          const role = user.role || 'Unknown';
          if (!roleGaps.has(role)) {
            roleGaps.set(role, { total: 0, critical: 0, moderate: 0, minor: 0 });
          }
          const roleData = roleGaps.get(role);
          gaps.forEach(gap => {
            roleData.total++;
            roleData[gap.severity]++;
          });
        }
      }
    }

    // Calculate top skill gaps
    const skillGapFrequency = new Map();
    skillGaps.forEach(gap => {
      const key = gap.competency;
      skillGapFrequency.set(key, (skillGapFrequency.get(key) || 0) + 1);
    });

    const topSkillGaps = Array.from(skillGapFrequency.entries())
      .map(([skill, frequency]) => ({ skill, frequency, percentage: (frequency / users.length) * 100 }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 10);

    // Calculate critical gaps
    const criticalGaps = skillGaps.filter(gap => gap.severity === 'critical').length;

    // Generate improvement opportunities
    const improvementOpportunities = this.generateImprovementOpportunities(topSkillGaps, departmentGaps, roleGaps);

    return {
      totalSkillsAssessed: frameworks.reduce((sum, f) => sum + (f.competencies ? Object.keys(f.competencies).length : 0), 0),
      totalUsers: users.length,
      criticalGaps,
      moderateGaps: skillGaps.filter(gap => gap.severity === 'moderate').length,
      minorGaps: skillGaps.filter(gap => gap.severity === 'minor').length,
      topSkillGaps,
      departmentBreakdown: Array.from(departmentGaps.entries()).map(([dept, data]) => ({ department: dept, ...data })),
      roleBreakdown: Array.from(roleGaps.entries()).map(([role, data]) => ({ role, ...data })),
      improvementOpportunities,
      skillGapDetails: skillGaps.slice(0, 50), // Top 50 detailed gaps
    };
  }

  /**
   * Build user skill profile from assessments
   */
  private async buildUserSkillProfile(user: User, assessments: AssessmentInstance[]): Promise<Map<string, number>> {
    const skillProfile = new Map();

    // Get user's assessment scores
    const userAssessments = assessments.filter(a => a.employeeId === user.id);

    userAssessments.forEach(assessment => {
      const skillName = assessment.template?.name || 'General';
      const score = assessment.totalScore || 0;
      skillProfile.set(skillName, score);
    });

    // Add default scores for common skills if not assessed
    const defaultSkills = [
      'Communication', 'Leadership', 'Problem Solving', 'Technical Skills',
      'Teamwork', 'Time Management', 'Adaptability', 'Critical Thinking'
    ];

    defaultSkills.forEach(skill => {
      if (!skillProfile.has(skill)) {
        skillProfile.set(skill, 70); // Default moderate score
      }
    });

    return skillProfile;
  }

  /**
   * Identify skill gaps for a user against a competency framework
   */
  private identifyUserSkillGaps(user: User, userSkillProfile: Map<string, number>, framework: CompetencyFramework): any[] {
    const gaps = [];
    const competencies = framework.competencies as any;

    if (competencies && typeof competencies === 'object') {
      Object.entries(competencies).forEach(([competencyName, requirements]: [string, any]) => {
        const requiredLevel = requirements.requiredLevel || 80;
        const userLevel = userSkillProfile.get(competencyName) || 0;

        if (userLevel < requiredLevel) {
          const gapSize = requiredLevel - userLevel;
          const severity = this.calculateGapSeverity(gapSize, requiredLevel);

          gaps.push({
            userId: user.id,
            userName: `${user.firstName} ${user.lastName}`,
            department: user.organizationalUnit?.name || 'Unknown',
            role: user.role,
            competency: competencyName,
            currentLevel: userLevel,
            requiredLevel,
            gapSize,
            severity,
            framework: framework.name,
            priority: this.calculateGapPriority(severity, requirements.importance || 'medium'),
          });
        }
      });
    }

    return gaps;
  }

  /**
   * Calculate gap severity based on gap size
   */
  private calculateGapSeverity(gapSize: number, requiredLevel: number): string {
    const gapPercentage = (gapSize / requiredLevel) * 100;

    if (gapPercentage >= 40) return 'critical';
    if (gapPercentage >= 20) return 'moderate';
    return 'minor';
  }

  /**
   * Calculate gap priority
   */
  private calculateGapPriority(severity: string, importance: string): string {
    if (severity === 'critical' && importance === 'high') return 'urgent';
    if (severity === 'critical' || importance === 'high') return 'high';
    if (severity === 'moderate') return 'medium';
    return 'low';
  }

  /**
   * Generate improvement opportunities
   */
  private generateImprovementOpportunities(topSkillGaps: any[], departmentGaps: Map<string, any>, roleGaps: Map<string, any>): any[] {
    const opportunities = [];

    // Top skill gap opportunities
    topSkillGaps.slice(0, 5).forEach(gap => {
      opportunities.push({
        type: 'skill_training',
        title: `${gap.skill} Training Program`,
        description: `Implement organization-wide training for ${gap.skill} (${gap.frequency} employees affected)`,
        impact: 'high',
        effort: 'medium',
        timeline: '3-6 months',
      });
    });

    // Department-specific opportunities
    Array.from(departmentGaps.entries()).forEach(([dept, data]) => {
      if (data.critical > 5) {
        opportunities.push({
          type: 'department_focus',
          title: `${dept} Skill Development Initiative`,
          description: `Targeted skill development program for ${dept} department`,
          impact: 'medium',
          effort: 'medium',
          timeline: '2-4 months',
        });
      }
    });

    // Role-specific opportunities
    Array.from(roleGaps.entries()).forEach(([role, data]) => {
      if (data.critical > 3) {
        opportunities.push({
          type: 'role_certification',
          title: `${role} Certification Program`,
          description: `Role-specific certification and skill development for ${role} positions`,
          impact: 'high',
          effort: 'high',
          timeline: '6-12 months',
        });
      }
    });

    return opportunities.slice(0, 10); // Top 10 opportunities
  }
}
