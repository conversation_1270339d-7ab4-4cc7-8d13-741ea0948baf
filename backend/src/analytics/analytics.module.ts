import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyticsDashboard } from './entities/analytics-dashboard.entity';
import { PerformanceMetric } from './entities/performance-metric.entity';
import { EngagementSurvey } from './entities/engagement-survey.entity';
import { SurveyResponse } from './entities/survey-response.entity';
import { User } from '../users/entities/user.entity';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import { AnalyticsCacheService } from './services/analytics-cache.service';
import { AuditModule } from '../audit/audit.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AnalyticsDashboard,
      PerformanceMetric,
      EngagementSurvey,
      SurveyResponse,
      User,
      AssessmentInstance,
      OrganizationalUnit,
    ]),
    AuditModule, // For audit logging compliance
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService, AnalyticsCacheService],
  exports: [TypeOrmModule, AnalyticsService, AnalyticsCacheService],
})
export class AnalyticsModule { }
