import {
  IsString,
  IsOptional,
  IsBoolean,
  IsObject,
  Length,
  Matches,
  IsNotEmpty,
  ValidateNested,
  IsUUID
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateDashboardDto {
  @ApiProperty({
    description: 'Dashboard name',
    example: 'My Analytics Dashboard',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 100)
  @Matches(/^[a-zA-Z0-9\s\-_\.]+$/, {
    message: 'Dashboard name can only contain letters, numbers, spaces, hyphens, underscores, and dots'
  })
  @Transform(({ value }) => value?.trim())
  name: string;

  @ApiPropertyOptional({
    description: 'Dashboard description',
    example: 'Comprehensive analytics dashboard for team performance',
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  @Transform(({ value }) => value?.trim())
  description?: string;

  @ApiPropertyOptional({
    description: 'Dashboard layout configuration',
    example: { columns: 3, rows: 4 }
  })
  @IsOptional()
  @IsObject()
  layoutConfig?: object;

  @ApiPropertyOptional({
    description: 'Widget configuration',
    example: { widgets: ['performance', 'engagement'] }
  })
  @IsOptional()
  @IsObject()
  widgetConfig?: object;

  @ApiPropertyOptional({
    description: 'Whether this is the default dashboard',
    example: false
  })
  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this dashboard is shared with others',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isShared?: boolean;
}

export class UpdateDashboardDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsObject()
  layoutConfig?: object;

  @IsOptional()
  @IsObject()
  widgetConfig?: object;

  @IsOptional()
  @IsBoolean()
  isDefault?: boolean;

  @IsOptional()
  @IsBoolean()
  isShared?: boolean;
}
