import { 
  IsNotEmpty, 
  Is<PERSON>umber, 
  IsDateString, 
  IsOptional, 
  IsString, 
  IsEnum, 
  Min, 
  Max, 
  IsInt 
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { DashboardMetricsStatus } from '../entities/manager-dashboard-metrics.entity';

export class CreateManagerDashboardMetricsDto {
  @IsNotEmpty()
  @IsNumber()
  @IsInt()
  @Min(1)
  organizationalUnitId: number;

  @IsNotEmpty()
  @IsNumber()
  @IsInt()
  @Min(1)
  managerId: number;

  @IsNotEmpty()
  @IsDateString()
  @Transform(({ value }) => {
    // Ensure we get the first day of the month for reporting period
    const date = new Date(value);
    return new Date(date.getFullYear(), date.getMonth(), 1).toISOString().split('T')[0];
  })
  reportingPeriod: string;

  // Core Metrics - all optional for initial creation
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  @Max(999.9)
  @Type(() => Number)
  fteCount?: number;

  @IsOptional()
  @IsNumber()
  @IsInt()
  @Min(0)
  @Max(999)
  @Type(() => Number)
  attritionResigned?: number;

  @IsOptional()
  @IsNumber()
  @IsInt()
  @Min(0)
  @Max(999)
  @Type(() => Number)
  attritionInvoluntary?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @Type(() => Number)
  slaPercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @Type(() => Number)
  utilizationPercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @Type(() => Number)
  axPercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @Type(() => Number)
  complianceScore?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(-999.99)
  @Max(999.99)
  @Type(() => Number)
  abVariancePercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @Type(() => Number)
  vacationLeavePercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @Type(() => Number)
  inOfficePercentage?: number;

  @IsOptional()
  @IsEnum(DashboardMetricsStatus)
  status?: DashboardMetricsStatus;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  notes?: string;
}
