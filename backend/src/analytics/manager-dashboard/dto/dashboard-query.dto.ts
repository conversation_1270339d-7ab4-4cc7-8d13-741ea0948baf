import { 
  <PERSON><PERSON><PERSON>al, 
  <PERSON>N<PERSON>ber, 
  IsDateString, 
  IsString, 
  IsEnum,
  IsInt,
  Min
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { DashboardMetricsStatus } from '../entities/manager-dashboard-metrics.entity';

export class DashboardQueryDto {
  @IsOptional()
  @IsNumber()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  managerId?: number;

  @IsOptional()
  @IsDateString()
  @Transform(({ value }) => {
    // Ensure we get the first day of the month for reporting period
    const date = new Date(value);
    return new Date(date.getFullYear(), date.getMonth(), 1).toISOString().split('T')[0];
  })
  reportingPeriod?: string;

  @IsOptional()
  @IsNumber()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  organizationalUnitId?: number;

  @IsOptional()
  @IsEnum(DashboardMetricsStatus)
  status?: DashboardMetricsStatus;

  @IsOptional()
  @IsNumber()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @IsOptional()
  @IsNumber()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit?: number = 50;
}

export class UpdateReminderSettingsDto {
  @IsOptional()
  @IsNumber()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  reminderDayOfMonth?: number;

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  })
  reminderEnabled?: boolean;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  emailTemplateId?: string;
}
