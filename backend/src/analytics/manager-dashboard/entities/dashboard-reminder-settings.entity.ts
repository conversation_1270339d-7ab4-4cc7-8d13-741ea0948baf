import { 
  <PERSON><PERSON><PERSON>, 
  PrimaryGeneratedColumn, 
  Column, 
  ManyToOne, 
  <PERSON>in<PERSON><PERSON><PERSON>n, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index
} from 'typeorm';
import { User } from '../../../users/entities/user.entity';

@Entity('dashboard_reminder_settings')
export class DashboardReminderSettings {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'manager_id', unique: true })
  @Index()
  managerId: number;

  @Column({ 
    name: 'reminder_day_of_month', 
    default: 25,
    comment: 'Day of month to send reminder (1-31)'
  })
  reminderDayOfMonth: number;

  @Column({ 
    name: 'reminder_enabled', 
    default: true,
    comment: 'Whether reminders are enabled for this manager'
  })
  @Index()
  reminderEnabled: boolean;

  @Column({ 
    name: 'email_template_id', 
    length: 50, 
    default: 'monthly_dashboard_reminder',
    comment: 'Email template identifier for reminders'
  })
  emailTemplateId: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'manager_id' })
  manager: User;

  // Helper methods
  shouldSendReminderToday(): boolean {
    if (!this.reminderEnabled) {
      return false;
    }

    const today = new Date();
    const currentDay = today.getDate();
    
    // Handle end-of-month edge cases
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0).getDate();
    const targetDay = Math.min(this.reminderDayOfMonth, lastDayOfMonth);
    
    return currentDay === targetDay;
  }

  getNextReminderDate(): Date {
    const today = new Date();
    const nextReminder = new Date(today.getFullYear(), today.getMonth(), this.reminderDayOfMonth);
    
    // If the reminder day has already passed this month, schedule for next month
    if (nextReminder <= today) {
      nextReminder.setMonth(nextReminder.getMonth() + 1);
    }
    
    // Handle end-of-month edge cases
    const lastDayOfTargetMonth = new Date(nextReminder.getFullYear(), nextReminder.getMonth() + 1, 0).getDate();
    if (this.reminderDayOfMonth > lastDayOfTargetMonth) {
      nextReminder.setDate(lastDayOfTargetMonth);
    }
    
    return nextReminder;
  }

  getFormattedReminderSchedule(): string {
    if (!this.reminderEnabled) {
      return 'Disabled';
    }
    
    const suffix = this.getReminderDaySuffix();
    return `${this.reminderDayOfMonth}${suffix} of each month`;
  }

  private getReminderDaySuffix(): string {
    const day = this.reminderDayOfMonth;
    if (day >= 11 && day <= 13) {
      return 'th';
    }
    switch (day % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  }

  isValidReminderDay(): boolean {
    return this.reminderDayOfMonth >= 1 && this.reminderDayOfMonth <= 31;
  }

  updateReminderSettings(dayOfMonth: number, enabled: boolean, templateId?: string): void {
    if (dayOfMonth < 1 || dayOfMonth > 31) {
      throw new Error('Reminder day must be between 1 and 31');
    }
    
    this.reminderDayOfMonth = dayOfMonth;
    this.reminderEnabled = enabled;
    
    if (templateId) {
      this.emailTemplateId = templateId;
    }
  }
}
