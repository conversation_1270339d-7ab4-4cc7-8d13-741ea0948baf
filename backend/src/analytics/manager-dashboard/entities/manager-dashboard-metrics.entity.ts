import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  ManyToOne, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>n, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index
} from 'typeorm';
import { OrganizationalUnit } from '../../../teams/entities/organizational-unit.entity';
import { User } from '../../../users/entities/user.entity';

export enum DashboardMetricsStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  APPROVED = 'approved'
}

@Entity('manager_dashboard_metrics')
@Index(['managerId', 'reportingPeriod', 'status'])
@Index(['organizationalUnitId', 'reportingPeriod'])
@Index(['lastUpdatedAt'])
export class ManagerDashboardMetrics {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'organizational_unit_id' })
  @Index()
  organizationalUnitId: number;

  @Column({ name: 'manager_id' })
  @Index()
  managerId: number;

  @Column({ type: 'date', name: 'reporting_period' })
  @Index()
  reportingPeriod: Date;

  // Core Metrics - following Excel dashboard structure
  @Column({ 
    type: 'decimal', 
    precision: 4, 
    scale: 1, 
    name: 'fte_count', 
    default: 0,
    comment: 'Full-Time Equivalent count'
  })
  fteCount: number;

  @Column({ 
    name: 'attrition_resigned', 
    default: 0,
    comment: 'Number of resigned employees'
  })
  attritionResigned: number;

  @Column({ 
    name: 'attrition_involuntary', 
    default: 0,
    comment: 'Number of involuntary terminations'
  })
  attritionInvoluntary: number;

  @Column({ 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    name: 'sla_percentage', 
    nullable: true,
    comment: 'Service Level Agreement percentage'
  })
  slaPercentage: number;

  @Column({ 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    name: 'utilization_percentage', 
    nullable: true,
    comment: 'Resource utilization percentage'
  })
  utilizationPercentage: number;

  @Column({ 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    name: 'ax_percentage', 
    nullable: true,
    comment: 'AX performance percentage'
  })
  axPercentage: number;

  @Column({ 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    name: 'compliance_score', 
    nullable: true,
    comment: 'Compliance score percentage'
  })
  complianceScore: number;

  @Column({ 
    type: 'decimal', 
    precision: 6, 
    scale: 2, 
    name: 'ab_variance_percentage', 
    nullable: true,
    comment: 'AB variance percentage (can be negative)'
  })
  abVariancePercentage: number;

  @Column({ 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    name: 'vacation_leave_percentage', 
    nullable: true,
    comment: 'Vacation/leave percentage'
  })
  vacationLeavePercentage: number;

  @Column({ 
    type: 'decimal', 
    precision: 5, 
    scale: 2, 
    name: 'in_office_percentage', 
    nullable: true,
    comment: 'In-office presence percentage'
  })
  inOfficePercentage: number;

  // Metadata
  @Column({ name: 'last_updated_by', nullable: true })
  lastUpdatedBy: number;

  @Column({ 
    type: 'enum', 
    enum: DashboardMetricsStatus, 
    default: DashboardMetricsStatus.DRAFT,
    comment: 'Current status of the metrics submission'
  })
  @Index()
  status: DashboardMetricsStatus;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  lastUpdatedAt: Date;

  // Relations
  @ManyToOne(() => OrganizationalUnit, { eager: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'manager_id' })
  manager: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'last_updated_by' })
  lastUpdatedByUser: User;

  // Helper methods
  getTotalAttrition(): number {
    return (this.attritionResigned || 0) + (this.attritionInvoluntary || 0);
  }

  getFormattedReportingPeriod(): string {
    return this.reportingPeriod.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  }

  isEditable(): boolean {
    return this.status === DashboardMetricsStatus.DRAFT;
  }

  getCompletionPercentage(): number {
    const fields = [
      this.fteCount,
      this.slaPercentage,
      this.utilizationPercentage,
      this.axPercentage,
      this.complianceScore,
      this.abVariancePercentage,
      this.vacationLeavePercentage,
      this.inOfficePercentage
    ];
    
    const completedFields = fields.filter(field => field !== null && field !== undefined).length;
    return Math.round((completedFields / fields.length) * 100);
  }
}
