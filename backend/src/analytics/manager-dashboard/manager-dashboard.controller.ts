import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Body, 
  Param, 
  Query, 
  UseGuards, 
  Request,
  ParseIntPipe,
  HttpStatus,
  HttpCode
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ManagerDashboardService } from './manager-dashboard.service';
import { 
  CreateManagerDashboardMetricsDto, 
  UpdateManagerDashboardMetricsDto,
  DashboardQueryDto,
  UpdateReminderSettingsDto
} from './dto';
import { UserRole } from '../../users/entities/user.entity';

@ApiTags('Manager Dashboard')
@ApiBearerAuth()
@Controller('analytics/manager-dashboard')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ManagerDashboardController {
  constructor(private readonly dashboardService: ManagerDashboardService) {}

  @Get('metrics')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Get manager dashboard metrics with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Dashboard metrics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiQuery({ name: 'managerId', required: false, type: Number })
  @ApiQuery({ name: 'reportingPeriod', required: false, type: String })
  @ApiQuery({ name: 'organizationalUnitId', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, enum: ['draft', 'submitted', 'approved'] })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getDashboardMetrics(
    @Query() query: DashboardQueryDto,
    @Request() req: any,
  ) {
    return this.dashboardService.getDashboardMetricsForManager(query, req.user.id);
  }

  @Get('metrics/:managerId')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Get dashboard metrics for a specific manager' })
  @ApiResponse({ status: 200, description: 'Dashboard metrics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'Manager not found' })
  @ApiParam({ name: 'managerId', type: Number })
  @ApiQuery({ name: 'reportingPeriod', required: false, type: String })
  async getManagerDashboard(
    @Param('managerId', ParseIntPipe) managerId: number,
    @Request() req: any,
    @Query('reportingPeriod') reportingPeriod?: string,
  ) {
    const query: DashboardQueryDto = { managerId };
    if (reportingPeriod) {
      query.reportingPeriod = reportingPeriod;
    }
    
    return this.dashboardService.getDashboardMetricsForManager(query, req.user.id);
  }

  @Post('metrics')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Create new dashboard metrics' })
  @ApiResponse({ status: 201, description: 'Dashboard metrics created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @HttpCode(HttpStatus.CREATED)
  async createMetrics(
    @Body() createDto: CreateManagerDashboardMetricsDto,
    @Request() req: any,
  ) {
    return this.dashboardService.createMetrics(createDto, req.user.id);
  }

  @Put('metrics/:id')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Update existing dashboard metrics' })
  @ApiResponse({ status: 200, description: 'Dashboard metrics updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiResponse({ status: 404, description: 'Metrics not found' })
  @ApiParam({ name: 'id', type: Number })
  async updateMetrics(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateManagerDashboardMetricsDto,
    @Request() req: any,
  ) {
    return this.dashboardService.updateMetrics(id, updateDto, req.user.id);
  }

  @Get('organizational-units/:managerId')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Get organizational units managed by a user' })
  @ApiResponse({ status: 200, description: 'Organizational units retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiParam({ name: 'managerId', type: Number })
  async getManagerOrganizationalUnits(
    @Param('managerId', ParseIntPipe) managerId: number,
    @Request() req: any,
  ) {
    return this.dashboardService.getManagerOrganizationalUnits(managerId, req.user.id);
  }

  @Get('managers')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Get list of managers for dropdown' })
  @ApiResponse({ status: 200, description: 'Managers list retrieved successfully' })
  async getManagers(@Request() req: any) {
    return this.dashboardService.getManagers(req.user.id);
  }

  @Get('summary/:managerId')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Get dashboard summary statistics for a manager' })
  @ApiResponse({ status: 200, description: 'Dashboard summary retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiParam({ name: 'managerId', type: Number })
  async getDashboardSummary(
    @Param('managerId', ParseIntPipe) managerId: number,
    @Request() req: any,
  ) {
    return this.dashboardService.getDashboardSummary(managerId, req.user.id);
  }

  @Get('reminder-settings/:managerId')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Get reminder settings for a manager' })
  @ApiResponse({ status: 200, description: 'Reminder settings retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiParam({ name: 'managerId', type: Number })
  async getReminderSettings(
    @Param('managerId', ParseIntPipe) managerId: number,
    @Request() req: any,
  ) {
    return this.dashboardService.getReminderSettings(managerId, req.user.id);
  }

  @Put('reminder-settings/:managerId')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Update reminder settings for a manager' })
  @ApiResponse({ status: 200, description: 'Reminder settings updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  @ApiParam({ name: 'managerId', type: Number })
  async updateReminderSettings(
    @Param('managerId', ParseIntPipe) managerId: number,
    @Body() updateDto: UpdateReminderSettingsDto,
    @Request() req: any,
  ) {
    return this.dashboardService.updateReminderSettings(managerId, updateDto, req.user.id);
  }

  @Get('calculate-fte/:organizationalUnitId')
  @Roles(UserRole.MANAGER, UserRole.DIRECTOR, UserRole.VP, UserRole.CEO, UserRole.HR_ADMIN)
  @ApiOperation({ summary: 'Calculate FTE count for an organizational unit' })
  @ApiResponse({ status: 200, description: 'FTE count calculated successfully' })
  @ApiParam({ name: 'organizationalUnitId', type: Number })
  async calculateFteCount(
    @Param('organizationalUnitId', ParseIntPipe) organizationalUnitId: number,
  ) {
    const fteCount = await this.dashboardService.calculateFteCount(organizationalUnitId);
    return { fteCount };
  }
}
