import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ManagerDashboardController } from './manager-dashboard.controller';
import { ManagerDashboardService } from './manager-dashboard.service';
import { DashboardReminderService } from './dashboard-reminder.service';
import { ManagerDashboardMetrics } from './entities/manager-dashboard-metrics.entity';
import { DashboardReminderSettings } from './entities/dashboard-reminder-settings.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { User } from '../../users/entities/user.entity';
import { AuditModule } from '../../audit/audit.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ManagerDashboardMetrics,
      DashboardReminderSettings,
      OrganizationalUnit,
      User,
    ]),
    ScheduleModule.forRoot(), // For cron jobs
    AuditModule, // For audit logging
  ],
  controllers: [ManagerDashboardController],
  providers: [
    ManagerDashboardService,
    DashboardReminderService,
  ],
  exports: [
    TypeOrmModule,
    ManagerDashboardService,
    DashboardReminderService,
  ],
})
export class ManagerDashboardModule {}
