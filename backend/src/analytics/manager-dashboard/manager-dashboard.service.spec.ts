import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { ManagerDashboardService } from './manager-dashboard.service';
import { ManagerDashboardMetrics, DashboardMetricsStatus } from './entities/manager-dashboard-metrics.entity';
import { DashboardReminderSettings } from './entities/dashboard-reminder-settings.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { User, UserRole } from '../../users/entities/user.entity';
import { AuditLoggingService } from '../../audit/audit-logging.service';

describe('ManagerDashboardService', () => {
  let service: ManagerDashboardService;
  let metricsRepository: Repository<ManagerDashboardMetrics>;
  let reminderSettingsRepository: Repository<DashboardReminderSettings>;
  let orgUnitRepository: Repository<OrganizationalUnit>;
  let userRepository: Repository<User>;
  let auditLoggingService: AuditLoggingService;

  const mockMetricsRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockReminderSettingsRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockOrgUnitRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
  };

  const mockUserRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    count: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockAuditLoggingService = {
    logEvent: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ManagerDashboardService,
        {
          provide: getRepositoryToken(ManagerDashboardMetrics),
          useValue: mockMetricsRepository,
        },
        {
          provide: getRepositoryToken(DashboardReminderSettings),
          useValue: mockReminderSettingsRepository,
        },
        {
          provide: getRepositoryToken(OrganizationalUnit),
          useValue: mockOrgUnitRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: AuditLoggingService,
          useValue: mockAuditLoggingService,
        },
      ],
    }).compile();

    service = module.get<ManagerDashboardService>(ManagerDashboardService);
    metricsRepository = module.get<Repository<ManagerDashboardMetrics>>(
      getRepositoryToken(ManagerDashboardMetrics),
    );
    reminderSettingsRepository = module.get<Repository<DashboardReminderSettings>>(
      getRepositoryToken(DashboardReminderSettings),
    );
    orgUnitRepository = module.get<Repository<OrganizationalUnit>>(
      getRepositoryToken(OrganizationalUnit),
    );
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    auditLoggingService = module.get<AuditLoggingService>(AuditLoggingService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getDashboardMetricsForManager', () => {
    it('should return metrics for a manager', async () => {
      const mockUser = { id: 1, role: UserRole.MANAGER };
      const mockMetrics = [
        {
          id: 1,
          managerId: 1,
          organizationalUnitId: 1,
          fteCount: 10,
          status: DashboardMetricsStatus.DRAFT,
        },
      ];

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockMetrics, 1]),
      };

      mockMetricsRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDashboardMetricsForManager(
        { managerId: 1, page: 1, limit: 50 },
        1,
      );

      expect(result.metrics).toEqual(mockMetrics);
      expect(result.total).toBe(1);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
    });

    it('should throw ForbiddenException for unauthorized access', async () => {
      const mockUser = { id: 2, role: UserRole.EMPLOYEE };
      mockUserRepository.findOne.mockResolvedValue(mockUser);

      await expect(
        service.getDashboardMetricsForManager({ managerId: 1 }, 2),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should allow admin access to any manager', async () => {
      const mockUser = { id: 2, role: UserRole.HR_ADMIN };
      const mockMetrics = [];

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      
      const mockQueryBuilder = {
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        addOrderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockMetrics, 0]),
      };

      mockMetricsRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getDashboardMetricsForManager(
        { managerId: 1 },
        2,
      );

      expect(result.metrics).toEqual(mockMetrics);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: 2 } });
    });
  });

  describe('updateMetrics', () => {
    it('should update metrics successfully', async () => {
      const mockUser = { id: 1, role: UserRole.MANAGER };
      const mockExistingMetrics = {
        id: 1,
        managerId: 1,
        organizationalUnitId: 1,
        fteCount: 10,
        status: DashboardMetricsStatus.DRAFT,
      };
      const mockUpdatedMetrics = { ...mockExistingMetrics, fteCount: 12 };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockMetricsRepository.findOne
        .mockResolvedValueOnce(mockExistingMetrics)
        .mockResolvedValueOnce(mockUpdatedMetrics);
      mockMetricsRepository.save.mockResolvedValue(mockUpdatedMetrics);

      const result = await service.updateMetrics(
        1,
        { fteCount: 12 },
        1,
      );

      expect(result).toEqual(mockUpdatedMetrics);
      expect(mockMetricsRepository.save).toHaveBeenCalled();
      expect(mockAuditLoggingService.logEvent).toHaveBeenCalled();
    });

    it('should throw NotFoundException for non-existent metrics', async () => {
      mockMetricsRepository.findOne.mockResolvedValue(null);

      await expect(
        service.updateMetrics(999, { fteCount: 12 }, 1),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getManagerOrganizationalUnits', () => {
    it('should return organizational units for a manager', async () => {
      const mockUser = { id: 1, role: UserRole.MANAGER };
      const mockOrgUnits = [
        { id: 1, name: 'Unit 1', managerId: 1, isActive: true },
        { id: 2, name: 'Unit 2', managerId: 1, isActive: true },
      ];

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockOrgUnitRepository.find.mockResolvedValue(mockOrgUnits);

      const result = await service.getManagerOrganizationalUnits(1, 1);

      expect(result).toEqual(mockOrgUnits);
      expect(mockOrgUnitRepository.find).toHaveBeenCalledWith({
        where: { managerId: 1, isActive: true },
        relations: ['parent', 'manager'],
        order: { name: 'ASC' },
      });
    });
  });

  describe('calculateFteCount', () => {
    it('should calculate FTE count correctly', async () => {
      mockUserRepository.count.mockResolvedValue(15);

      const result = await service.calculateFteCount(1);

      expect(result).toBe(15);
      expect(mockUserRepository.count).toHaveBeenCalledWith({
        where: {
          organizationalUnitId: 1,
          isActive: true,
        },
      });
    });
  });

  describe('getReminderSettings', () => {
    it('should return existing reminder settings', async () => {
      const mockUser = { id: 1, role: UserRole.MANAGER };
      const mockSettings = {
        id: 1,
        managerId: 1,
        reminderDayOfMonth: 25,
        reminderEnabled: true,
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockReminderSettingsRepository.findOne.mockResolvedValue(mockSettings);

      const result = await service.getReminderSettings(1, 1);

      expect(result).toEqual(mockSettings);
    });

    it('should create default settings if none exist', async () => {
      const mockUser = { id: 1, role: UserRole.MANAGER };
      const mockDefaultSettings = {
        managerId: 1,
        reminderDayOfMonth: 25,
        reminderEnabled: true,
        emailTemplateId: 'monthly_dashboard_reminder',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockReminderSettingsRepository.findOne.mockResolvedValue(null);
      mockReminderSettingsRepository.create.mockReturnValue(mockDefaultSettings);
      mockReminderSettingsRepository.save.mockResolvedValue(mockDefaultSettings);

      const result = await service.getReminderSettings(1, 1);

      expect(mockReminderSettingsRepository.create).toHaveBeenCalledWith({
        managerId: 1,
        reminderDayOfMonth: 25,
        reminderEnabled: true,
        emailTemplateId: 'monthly_dashboard_reminder',
      });
      expect(result).toEqual(mockDefaultSettings);
    });
  });
});
