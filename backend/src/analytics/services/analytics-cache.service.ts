import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

/**
 * 🚀 High-Performance Analytics Caching Service
 * Implements intelligent caching for analytics data with TTL and invalidation
 */
@Injectable()
export class AnalyticsCacheService {
  private readonly logger = new Logger(AnalyticsCacheService.name);
  private readonly cache = new Map<string, CacheEntry<any>>();
  private readonly defaultTtl: number;
  private readonly maxCacheSize: number;

  constructor(private readonly configService: ConfigService) {
    this.defaultTtl = this.configService.get<number>('ANALYTICS_CACHE_TTL', 300000); // 5 minutes
    this.maxCacheSize = this.configService.get<number>('ANALYTICS_CACHE_MAX_SIZE', 1000);
    
    // Cleanup expired entries every 5 minutes
    setInterval(() => this.cleanup(), 300000);
  }

  /**
   * Get cached data or execute function if not cached
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttl: number = this.defaultTtl
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      // Check if data exists and is not expired
      const cached = this.get<T>(key);
      if (cached !== null) {
        this.logger.debug(`Cache HIT for key: ${key} (${Date.now() - startTime}ms)`);
        return cached;
      }

      // Data not cached or expired, fetch new data
      this.logger.debug(`Cache MISS for key: ${key}, fetching...`);
      const data = await fetchFunction();
      
      // Cache the result
      this.set(key, data, ttl);
      
      const duration = Date.now() - startTime;
      this.logger.debug(`Cache SET for key: ${key} (${duration}ms)`);
      
      return data;
    } catch (error) {
      this.logger.error(`Cache operation failed for key: ${key}`, error);
      throw error;
    }
  }

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    // Check if expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttl: number = this.defaultTtl): void {
    // Enforce cache size limit
    if (this.cache.size >= this.maxCacheSize) {
      this.evictOldest();
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * Delete specific cache entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.logger.log('Analytics cache cleared');
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string): number {
    let count = 0;
    const regex = new RegExp(pattern);
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        count++;
      }
    }
    
    this.logger.log(`Invalidated ${count} cache entries matching pattern: ${pattern}`);
    return count;
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryUsage: string;
  } {
    const memoryUsage = process.memoryUsage();
    
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      memoryUsage: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
    };
  }

  /**
   * Generate cache key for analytics queries
   */
  generateKey(prefix: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join('|');
    
    return `analytics:${prefix}:${sortedParams}`;
  }

  /**
   * Cache key generators for common analytics queries
   */
  getMetricsSummaryKey(userId: number, role: string): string {
    return this.generateKey('metrics_summary', { userId, role });
  }

  getEngagementSummaryKey(userId: number, role: string): string {
    return this.generateKey('engagement_summary', { userId, role });
  }

  getDashboardKey(dashboardId: number, userId: number): string {
    return this.generateKey('dashboard', { dashboardId, userId });
  }

  getUserDashboardsKey(userId: number): string {
    return this.generateKey('user_dashboards', { userId });
  }

  getManagerDashboardKey(managerId: number, period: string): string {
    return this.generateKey('manager_dashboard', { managerId, period });
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up ${cleanedCount} expired cache entries`);
    }
  }

  /**
   * Evict oldest entry when cache is full
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.logger.debug(`Evicted oldest cache entry: ${oldestKey}`);
    }
  }

  /**
   * Warm up cache with frequently accessed data
   */
  async warmUp(): Promise<void> {
    this.logger.log('Starting analytics cache warm-up...');
    
    try {
      // This would typically pre-load frequently accessed analytics data
      // Implementation depends on specific use cases
      
      this.logger.log('Analytics cache warm-up completed');
    } catch (error) {
      this.logger.error('Analytics cache warm-up failed', error);
    }
  }

  /**
   * Cache invalidation strategies for data updates
   */
  invalidateUserData(userId: number): void {
    this.invalidatePattern(`.*userId:${userId}.*`);
  }

  invalidateDashboardData(dashboardId: number): void {
    this.invalidatePattern(`.*dashboardId:${dashboardId}.*`);
  }

  invalidateManagerData(managerId: number): void {
    this.invalidatePattern(`.*managerId:${managerId}.*`);
  }

  invalidateAllAnalytics(): void {
    this.invalidatePattern('analytics:.*');
  }
}
