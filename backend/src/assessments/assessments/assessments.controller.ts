import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { AssessmentsService } from './assessments.service';
import { CreateAssessmentDto } from '../dto/create-assessment.dto';
import { UpdateAssessmentDto } from '../dto/update-assessment.dto';
import { CreateAssessmentWithScoringDto, BulkUpdateAssessmentDto } from '../dto/create-assessment-with-scoring.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';

@Controller('assessments')
@UseGuards(JwtAuthGuard)
export class AssessmentsController {
  constructor(private readonly assessmentsService: AssessmentsService) { }

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  create(@Body() createAssessmentDto: CreateAssessmentDto, @Request() req) {
    return this.assessmentsService.create(createAssessmentDto, req.user.userId, req.user.role);
  }

  @Get()
  findAll(@Request() req) {
    return this.assessmentsService.findAll(req.user.userId, req.user.role);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.assessmentsService.findOne(+id, req.user.userId, req.user.role);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateAssessmentDto: UpdateAssessmentDto,
    @Request() req,
  ) {
    return this.assessmentsService.update(+id, updateAssessmentDto, req.user.userId, req.user.role);
  }

  @Post(':id/submit')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  submitAssessment(@Param('id') id: string, @Request() req) {
    return this.assessmentsService.submitAssessment(+id, req.user.userId, req.user.role);
  }



  @Post('create-with-scoring')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  createWithAdvancedScoring(
    @Body() createAssessmentWithScoringDto: CreateAssessmentWithScoringDto,
    @Request() req
  ) {
    return this.assessmentsService.createWithAdvancedScoring(
      createAssessmentWithScoringDto,
      req.user.userId,
      req.user.role
    );
  }

  @Patch(':id/bulk-update-scores')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  bulkUpdateScores(
    @Param('id') id: string,
    @Body() bulkUpdateDto: BulkUpdateAssessmentDto,
    @Request() req
  ) {
    return this.assessmentsService.bulkUpdateScores(
      +id,
      bulkUpdateDto,
      req.user.userId,
      req.user.role
    );
  }

  // Workflow Management Endpoints

  @Post(':id/start')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  startAssessment(@Param('id') id: string, @Request() req) {
    return this.assessmentsService.startAssessment(+id, req.user.userId, req.user.role);
  }

  @Post(':id/complete')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  completeAssessment(
    @Param('id') id: string,
    @Body() body: { comments?: string },
    @Request() req
  ) {
    return this.assessmentsService.completeAssessment(
      +id,
      req.user.userId,
      req.user.role,
      body.comments
    );
  }

  @Post(':id/approve')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  approveAssessment(
    @Param('id') id: string,
    @Body() body: { comments?: string },
    @Request() req
  ) {
    return this.assessmentsService.approveAssessment(
      +id,
      req.user.userId,
      req.user.role,
      body.comments
    );
  }

  @Post(':id/reject')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  rejectAssessment(
    @Param('id') id: string,
    @Body() body: { rejectionReason: string },
    @Request() req
  ) {
    return this.assessmentsService.rejectAssessment(
      +id,
      req.user.userId,
      req.user.role,
      body.rejectionReason
    );
  }

  @Post(':id/reopen')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  reopenAssessment(
    @Param('id') id: string,
    @Body() body: { reason?: string },
    @Request() req
  ) {
    return this.assessmentsService.reopenAssessment(
      +id,
      req.user.userId,
      req.user.role,
      body.reason
    );
  }

  @Get(':id/available-actions')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getAvailableActions(@Param('id') id: string, @Request() req) {
    return this.assessmentsService.getAvailableActions(+id, req.user.role);
  }

  @Get(':id/workflow-history')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  getWorkflowHistory(@Param('id') id: string, @Request() req) {
    return this.assessmentsService.getWorkflowHistory(+id, req.user.userId, req.user.role);
  }

  @Get(':id/validate-transition/:targetStatus')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  validateTransition(
    @Param('id') id: string,
    @Param('targetStatus') targetStatus: string,
    @Body() metadata?: Record<string, any>
  ) {
    return this.assessmentsService.validateAssessmentForTransition(+id, targetStatus as any, metadata);
  }

  // Query endpoints with workflow support

  @Get('status/:status')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  findByStatus(@Param('status') status: string, @Request() req) {
    return this.assessmentsService.findByStatus(status as any, req.user.userId, req.user.role);
  }

  @Get('pending-approvals')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  findPendingApprovals(@Request() req) {
    return this.assessmentsService.findPendingApprovals(req.user.userId, req.user.role);
  }

  @Get('evaluator/:evaluatorId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  findByEvaluator(
    @Param('evaluatorId') evaluatorId: string,
    @Request() req,
    @Query('status') status?: string
  ) {
    return this.assessmentsService.findByEvaluator(+evaluatorId, status as any, req.user.userId, req.user.role);
  }

  @Get('employee/:employeeId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  findByEmployee(
    @Param('employeeId') employeeId: string,
    @Request() req,
    @Query('status') status?: string
  ) {
    return this.assessmentsService.findByEmployee(+employeeId, status as any, req.user.userId, req.user.role);
  }

  @Get('workflow/stats')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getWorkflowStats(@Request() req) {
    return this.assessmentsService.getWorkflowStats(req.user.userId, req.user.role);
  }
}
