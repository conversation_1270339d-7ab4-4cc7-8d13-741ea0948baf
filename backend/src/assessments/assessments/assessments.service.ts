import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Connection } from 'typeorm';
import { AssessmentInstance, AssessmentStatus } from '../entities/assessment-instance.entity';
import { AssessmentResponse } from '../entities/assessment-response.entity';
import { AssessmentArea } from '../entities/assessment-area.entity';
import { CreateAssessmentDto } from '../dto/create-assessment.dto';
import { UpdateAssessmentDto } from '../dto/update-assessment.dto';
import { CreateAssessmentWithScoringDto, BulkUpdateAssessmentDto } from '../dto/create-assessment-with-scoring.dto';
import { TemplatesService } from '../templates/templates.service';
import { ScoringService, AssessmentInput } from '../services/scoring.service';
import { WorkflowService, WorkflowAction } from '../services/workflow.service';
import { UserRole } from '../../users/entities/user.entity';

@Injectable()
export class AssessmentsService {
  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
    @InjectRepository(AssessmentResponse)
    private responseRepository: Repository<AssessmentResponse>,
    @InjectRepository(AssessmentArea)
    private areaRepository: Repository<AssessmentArea>,
    private templatesService: TemplatesService,
    private scoringService: ScoringService,
    private workflowService: WorkflowService,
    private connection: Connection,
  ) { }

  async create(createAssessmentDto: CreateAssessmentDto, evaluatorId: number, userRole: string = 'MANAGER'): Promise<AssessmentInstance> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to create assessments');
    }
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get template for snapshot
      const template = await this.templatesService.findOne(createAssessmentDto.templateId);

      // Create assessment instance
      const assessment = this.assessmentRepository.create({
        templateId: createAssessmentDto.templateId,
        employeeId: createAssessmentDto.employeeId,
        evaluatorId: evaluatorId,
        status: createAssessmentDto.status || AssessmentStatus.DRAFT,
        assessmentDate: createAssessmentDto.assessmentDate,
        notes: createAssessmentDto.notes,
        templateSnapshot: {
          name: template.name,
          description: template.description,
          version: template.version,
        },
        totalScore: 0,
      });

      const savedAssessment = await queryRunner.manager.save(assessment);

      // Create responses if provided
      if (createAssessmentDto.responses && createAssessmentDto.responses.length > 0) {
        let totalWeightedScore = 0;

        for (const responseDto of createAssessmentDto.responses) {
          const area = await this.areaRepository.findOne({
            where: { id: responseDto.areaId }
          });

          if (!area) {
            throw new NotFoundException(`Assessment area with ID ${responseDto.areaId} not found`);
          }

          const weightedScore = (responseDto.score / area.maxScore) * area.weight;

          const response = this.responseRepository.create({
            assessmentId: savedAssessment.id,
            areaId: responseDto.areaId,
            score: responseDto.score,
            evaluatorComments: responseDto.evaluatorComments,
            employeeComments: responseDto.employeeComments,
            areaWeight: area.weight,
            weightedScore: weightedScore,
            areaSnapshot: {
              name: area.name,
              description: area.description,
              weight: area.weight,
              maxScore: area.maxScore,
            },
          });

          await queryRunner.manager.save(response);
          totalWeightedScore += weightedScore;
        }

        // Update the total score
        savedAssessment.totalScore = totalWeightedScore;
        await queryRunner.manager.save(savedAssessment);
      }

      await queryRunner.commitTransaction();

      return this.findOne(savedAssessment.id);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(userId: number, userRole: string): Promise<AssessmentInstance[]> {
    const query = this.assessmentRepository.createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.employee', 'employee')
      .leftJoinAndSelect('assessment.evaluator', 'evaluator')
      .leftJoinAndSelect('assessment.responses', 'response');

    // Apply role-based access control
    if (userRole === UserRole.HR_ADMIN) {
      // HR can see all assessments
    } else if (userRole === UserRole.MANAGER) {
      // Managers can see assessments where they're the evaluator or for employees they manage
      query.where('assessment.evaluatorId = :userId', { userId });
    } else {
      // Regular employees can only see their own assessments
      query.where('assessment.employeeId = :userId', { userId });
    }

    return query.getMany();
  }

  async findOne(id: number, userId?: number, userRole?: string): Promise<AssessmentInstance> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id },
      relations: ['employee', 'evaluator', 'responses', 'responses.area'],
    });

    if (!assessment) {
      throw new NotFoundException(`Assessment with ID ${id} not found`);
    }

    // Apply role-based access control if user info provided
    if (userId && userRole) {
      const canAccess =
        userRole === UserRole.HR_ADMIN ||
        assessment.evaluatorId === userId ||
        assessment.employeeId === userId;

      if (!canAccess) {
        throw new ForbiddenException('You do not have permission to access this assessment');
      }
    }

    return assessment;
  }

  async update(id: number, updateAssessmentDto: UpdateAssessmentDto, userId: number, userRole: string): Promise<AssessmentInstance> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const assessment = await this.findOne(id);

      // Check permissions
      const canModify =
        userRole === UserRole.HR_ADMIN ||
        (userRole === UserRole.MANAGER && assessment.evaluatorId === userId) ||
        (assessment.employeeId === userId && updateAssessmentDto.responses?.every(r => r.employeeComments !== undefined));

      if (!canModify) {
        throw new ForbiddenException('You do not have permission to modify this assessment');
      }

      // Update assessment fields
      if (updateAssessmentDto.status !== undefined) assessment.status = updateAssessmentDto.status;
      if (updateAssessmentDto.notes !== undefined) assessment.notes = updateAssessmentDto.notes;

      await queryRunner.manager.save(assessment);

      // Update responses if provided
      if (updateAssessmentDto.responses && updateAssessmentDto.responses.length > 0) {
        let totalWeightedScore = 0;

        for (const responseDto of updateAssessmentDto.responses) {
          let response: AssessmentResponse;

          if (responseDto.id) {
            // Update existing response
            response = await this.responseRepository.findOne({
              where: { id: responseDto.id, assessmentId: id }
            });

            if (!response) {
              throw new NotFoundException(`Response with ID ${responseDto.id} not found in this assessment`);
            }

            const area = await this.areaRepository.findOne({
              where: { id: response.areaId }
            });

            if (responseDto.score !== undefined) {
              response.score = responseDto.score;
              response.weightedScore = (responseDto.score / area.maxScore) * area.weight;
            }

            if (userRole === UserRole.MANAGER || userRole === UserRole.HR_ADMIN) {
              if (responseDto.evaluatorComments !== undefined) {
                response.evaluatorComments = responseDto.evaluatorComments;
              }
            }

            if (responseDto.employeeComments !== undefined) {
              response.employeeComments = responseDto.employeeComments;
            }

          } else if (responseDto.areaId) {
            // Create new response
            const area = await this.areaRepository.findOne({
              where: { id: responseDto.areaId }
            });

            if (!area) {
              throw new NotFoundException(`Assessment area with ID ${responseDto.areaId} not found`);
            }

            const weightedScore = (responseDto.score / area.maxScore) * area.weight;

            response = this.responseRepository.create({
              assessmentId: id,
              areaId: responseDto.areaId,
              score: responseDto.score,
              evaluatorComments: responseDto.evaluatorComments,
              employeeComments: responseDto.employeeComments,
              areaWeight: area.weight,
              weightedScore: weightedScore,
              areaSnapshot: {
                name: area.name,
                description: area.description,
                weight: area.weight,
                maxScore: area.maxScore,
              },
            });
          }

          await queryRunner.manager.save(response);

          // Recalculate total score
          const allResponses = await this.responseRepository.find({
            where: { assessmentId: id }
          });

          totalWeightedScore = allResponses.reduce((sum, resp) => sum + Number(resp.weightedScore), 0);

          assessment.totalScore = totalWeightedScore;
          await queryRunner.manager.save(assessment);
        }
      }

      await queryRunner.commitTransaction();

      return this.findOne(id);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async submitAssessment(id: number, userId: number, userRole: string): Promise<AssessmentInstance> {
    const assessment = await this.findOne(id);

    // Check permissions
    const canSubmit =
      userRole === UserRole.HR_ADMIN ||
      (userRole === UserRole.MANAGER && assessment.evaluatorId === userId);

    if (!canSubmit) {
      throw new ForbiddenException('You do not have permission to submit this assessment');
    }

    // Check that the assessment is in DRAFT status
    if (assessment.status !== AssessmentStatus.DRAFT) {
      throw new ForbiddenException('Only assessments in DRAFT status can be submitted');
    }

    // Update status to IN_PROGRESS
    assessment.status = AssessmentStatus.IN_PROGRESS;

    return this.assessmentRepository.save(assessment);
  }



  async createWithAdvancedScoring(
    createDto: CreateAssessmentWithScoringDto,
    evaluatorId: number,
    userRole: string = 'MANAGER'
  ): Promise<AssessmentInstance> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to create assessments with advanced scoring');
    }
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get template with areas and scoring rules
      const template = await this.templatesService.findOne(createDto.templateId);

      // Validate inputs
      const validationErrors = this.scoringService.validateInputs(
        template.areas,
        createDto.areaInputs.map(input => ({
          areaId: input.areaId,
          baseScore: input.baseScore,
          additionalData: input.additionalData
        }))
      );

      if (validationErrors.length > 0) {
        throw new BadRequestException(`Validation errors: ${validationErrors.join(', ')}`);
      }

      // Calculate scores using the scoring engine
      const scoringInputs: AssessmentInput[] = createDto.areaInputs.map(input => ({
        areaId: input.areaId,
        baseScore: input.baseScore,
        additionalData: input.additionalData || {}
      }));

      const calculationResult = await this.scoringService.calculateAssessmentScore(
        template.areas,
        scoringInputs
      );

      // Create assessment instance
      const assessment = this.assessmentRepository.create({
        templateId: createDto.templateId,
        employeeId: createDto.employeeId,
        evaluatorId: evaluatorId,
        status: AssessmentStatus.DRAFT,
        assessmentDate: new Date(createDto.assessmentDate),
        notes: createDto.notes,
        templateSnapshot: {
          name: template.name,
          description: template.description,
          version: template.version,
        },
        totalScore: calculationResult.totalScore,
        scorePercentage: calculationResult.scorePercentage,
      });

      const savedAssessment = await queryRunner.manager.save(assessment);

      // Create responses with calculated scores
      for (const areaResult of calculationResult.areaResults) {
        const inputData = createDto.areaInputs.find(input => input.areaId === areaResult.areaId);
        const area = template.areas.find(a => a.id === areaResult.areaId);

        const response = this.responseRepository.create({
          assessmentId: savedAssessment.id,
          areaId: areaResult.areaId,
          score: areaResult.finalScore,
          baseScore: areaResult.baseScore,
          evaluatorComments: inputData?.evaluatorComments,
          employeeComments: inputData?.employeeComments,
          areaWeight: areaResult.weight,
          weightedScore: areaResult.weightedScore,
          scoreAdjustments: JSON.stringify(areaResult.adjustments),
          additionalData: JSON.stringify(inputData?.additionalData || {}),
          areaSnapshot: {
            name: area.name,
            description: area.description,
            weight: area.weight,
            maxScore: area.maxScore,
          },
        });

        await queryRunner.manager.save(response);
      }

      await queryRunner.commitTransaction();

      // Return the assessment with responses
      return this.findOne(savedAssessment.id);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async bulkUpdateScores(
    assessmentId: number,
    updateDto: BulkUpdateAssessmentDto,
    userId: number,
    userRole: string
  ): Promise<AssessmentInstance> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const assessment = await this.findOne(assessmentId);

      // Check permissions
      const canModify =
        userRole === UserRole.HR_ADMIN ||
        (userRole === UserRole.MANAGER && assessment.evaluatorId === userId);

      if (!canModify) {
        throw new ForbiddenException('You do not have permission to modify this assessment');
      }

      // Get template with areas and scoring rules
      const template = await this.templatesService.findOne(assessment.templateId);

      // Prepare scoring inputs
      const scoringInputs: AssessmentInput[] = updateDto.areaUpdates.map(update => ({
        areaId: update.areaId,
        baseScore: update.baseScore,
        additionalData: update.additionalData || {}
      }));

      // Calculate new scores
      const calculationResult = await this.scoringService.calculateAssessmentScore(
        template.areas,
        scoringInputs
      );

      // Update assessment totals
      assessment.totalScore = calculationResult.totalScore;
      assessment.scorePercentage = calculationResult.scorePercentage;
      assessment.notes = updateDto.notes || assessment.notes;

      await queryRunner.manager.save(assessment);

      // Update individual responses
      for (const areaResult of calculationResult.areaResults) {
        const updateData = updateDto.areaUpdates.find(update => update.areaId === areaResult.areaId);

        const response = await this.responseRepository.findOne({
          where: { assessmentId: assessmentId, areaId: areaResult.areaId }
        });

        if (response) {
          response.score = areaResult.finalScore;
          response.baseScore = areaResult.baseScore;
          response.weightedScore = areaResult.weightedScore;
          response.scoreAdjustments = JSON.stringify(areaResult.adjustments);
          response.additionalData = JSON.stringify(updateData?.additionalData || {});

          if (updateData?.evaluatorComments !== undefined) {
            response.evaluatorComments = updateData.evaluatorComments;
          }
          if (updateData?.employeeComments !== undefined) {
            response.employeeComments = updateData.employeeComments;
          }

          await queryRunner.manager.save(response);
        }
      }

      await queryRunner.commitTransaction();

      return this.findOne(assessmentId);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // Workflow Management Methods

  async startAssessment(assessmentId: number, userId: number, userRole: UserRole): Promise<AssessmentInstance> {
    const action: WorkflowAction = {
      assessmentId,
      action: 'start',
      userId,
      userRole,
      comments: 'Assessment started'
    };

    return this.workflowService.executeTransition(action, userId, userRole);
  }

  async completeAssessment(
    assessmentId: number,
    userId: number,
    userRole: UserRole,
    comments?: string
  ): Promise<AssessmentInstance> {
    const action: WorkflowAction = {
      assessmentId,
      action: 'complete',
      userId,
      userRole,
      comments: comments || 'Assessment completed'
    };

    return this.workflowService.executeTransition(action, userId, userRole);
  }

  async approveAssessment(
    assessmentId: number,
    userId: number,
    userRole: UserRole,
    comments?: string
  ): Promise<AssessmentInstance> {
    const action: WorkflowAction = {
      assessmentId,
      action: 'approve',
      userId,
      userRole,
      comments: comments || 'Assessment approved'
    };

    return this.workflowService.executeTransition(action, userId, userRole);
  }

  async rejectAssessment(
    assessmentId: number,
    userId: number,
    userRole: UserRole,
    rejectionReason: string
  ): Promise<AssessmentInstance> {
    const action: WorkflowAction = {
      assessmentId,
      action: 'reject',
      userId,
      userRole,
      comments: rejectionReason,
      metadata: { rejectionReason }
    };

    return this.workflowService.executeTransition(action, userId, userRole);
  }

  async reopenAssessment(
    assessmentId: number,
    userId: number,
    userRole: UserRole,
    reason?: string
  ): Promise<AssessmentInstance> {
    const action: WorkflowAction = {
      assessmentId,
      action: 'reopen',
      userId,
      userRole,
      comments: reason || 'Assessment reopened for revision',
      metadata: { revisionReason: reason }
    };

    return this.workflowService.executeTransition(action, userId, userRole);
  }

  async getAvailableActions(
    assessmentId: number,
    userRole: UserRole
  ): Promise<{ action: string; label: string; requiresComment?: boolean }[]> {
    const assessment = await this.findOne(assessmentId);
    const transitions = this.workflowService.getAvailableTransitions(assessment.status, userRole);

    const actionMap = {
      'in_progress': { action: 'start', label: 'Start Assessment' },
      'completed': { action: 'complete', label: 'Complete Assessment', requiresComment: true },
      'approved': { action: 'approve', label: 'Approve Assessment', requiresComment: true },
      'rejected': { action: 'reject', label: 'Reject Assessment', requiresComment: true },
      'draft': { action: 'save_draft', label: 'Save as Draft' }
    };

    return transitions.map(transition => {
      const actionInfo = actionMap[transition.to];
      return actionInfo || { action: transition.to, label: `Move to ${transition.to}` };
    });
  }

  async getWorkflowHistory(assessmentId: number, userId: number, userRole: string): Promise<any[]> {
    // Validate user permissions - all authenticated users can view workflow history
    if (![UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view workflow history');
    }

    // For employees, ensure they can only view their own assessment history
    if (userRole === UserRole.EMPLOYEE) {
      const assessment = await this.assessmentRepository.findOne({
        where: { id: assessmentId },
        select: ['employeeId']
      });
      if (!assessment || assessment.employeeId !== userId) {
        throw new ForbiddenException('Employees can only view their own assessment workflow history');
      }
    }

    return this.workflowService.getWorkflowHistory(assessmentId);
  }

  async validateAssessmentForTransition(
    assessmentId: number,
    targetStatus: AssessmentStatus,
    metadata?: Record<string, any>
  ) {
    return this.workflowService.validateAssessmentForTransition(assessmentId, targetStatus, metadata);
  }

  async getWorkflowStats(userId: number, userRole: string) {
    // Validate user permissions - only HR_ADMIN and MANAGER can view workflow stats
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view workflow statistics');
    }

    return this.workflowService.getWorkflowStats();
  }

  // Enhanced query methods with workflow support

  async findByStatus(status: AssessmentStatus, userId: number, userRole: string): Promise<AssessmentInstance[]> {
    // Validate user permissions - only HR_ADMIN and MANAGER can query by status
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to query assessments by status');
    }

    return this.assessmentRepository.find({
      where: { status },
      relations: ['template', 'employee', 'evaluator', 'responses'],
      order: { updatedAt: 'DESC' }
    });
  }

  async findPendingApprovals(userId: number, userRole: string): Promise<AssessmentInstance[]> {
    // Validate user permissions - only HR_ADMIN can view pending approvals
    if (userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR administrators can view pending approvals');
    }

    return this.findByStatus(AssessmentStatus.COMPLETED, userId, userRole);
  }

  async findByEvaluator(evaluatorId: number, status: AssessmentStatus | undefined, userId: number, userRole: string): Promise<AssessmentInstance[]> {
    // Validate user permissions - HR_ADMIN and MANAGER can query by evaluator
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to query assessments by evaluator');
    }

    // Managers can only view assessments they are evaluating
    if (userRole === UserRole.MANAGER && evaluatorId !== userId) {
      throw new ForbiddenException('Managers can only view assessments they are evaluating');
    }

    const where: any = { evaluatorId };
    if (status) {
      where.status = status;
    }

    return this.assessmentRepository.find({
      where,
      relations: ['template', 'employee', 'responses'],
      order: { updatedAt: 'DESC' }
    });
  }

  async findByEmployee(employeeId: number, status: AssessmentStatus | undefined, userId: number, userRole: string): Promise<AssessmentInstance[]> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to query assessments by employee');
    }

    // Employees can only view their own assessments
    if (userRole === UserRole.EMPLOYEE && employeeId !== userId) {
      throw new ForbiddenException('Employees can only view their own assessments');
    }

    const where: any = { employeeId };
    if (status) {
      where.status = status;
    }

    return this.assessmentRepository.find({
      where,
      relations: ['template', 'evaluator', 'responses'],
      order: { updatedAt: 'DESC' }
    });
  }
}
