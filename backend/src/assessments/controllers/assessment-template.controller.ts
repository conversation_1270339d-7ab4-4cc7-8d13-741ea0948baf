import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { AssessmentTemplateService } from '../services/assessment-template.service';
import { TemplateLevel } from '../entities/assessment-template.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';

export class CreateTemplateDto {
  name: string;
  description: string;
  templateLevel: TemplateLevel;
  organizationalUnitId?: number;
  createdById: number;
  criteriaIds: number[];
}

export class UpdateTemplateDto {
  name?: string;
  description?: string;
  isActive?: boolean;
  criteriaUpdates?: {
    criteriaId: number;
    isRequired?: boolean;
    orderIndex?: number;
    weightOverride?: number;
  }[];
}

@Controller('assessments/templates')
@UseGuards(JwtAuthGuard)
export class AssessmentTemplateController {
  constructor(
    private readonly templateService: AssessmentTemplateService,
  ) { }

  /**
   * Get all templates organized by level
   */
  @Get()
  async getTemplatesByLevel(@Request() req) {
    try {
      const templates = await this.templateService.getTemplatesByLevel(req.user.id, req.user.role);
      return {
        success: true,
        data: templates
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get templates for a specific organizational unit
   */
  @Get('organizational-unit/:unitId')
  async getTemplatesForOrganizationalUnit(@Param('unitId') unitId: number, @Request() req) {
    try {
      const templates = await this.templateService.getTemplatesForOrganizationalUnit(unitId, req.user.id, req.user.role);
      return {
        success: true,
        data: templates
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get a specific template with its criteria
   */
  @Get(':id')
  async getTemplate(@Param('id') id: number, @Request() req) {
    try {
      const template = await this.templateService.getTemplateWithCriteria(id, req.user.id, req.user.role);
      return {
        success: true,
        data: template
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a new assessment template
   */
  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async createTemplate(@Body() createTemplateDto: CreateTemplateDto, @Request() req) {
    try {
      const template = await this.templateService.createTemplate(createTemplateDto, req.user.id, req.user.role);
      return {
        success: true,
        data: template,
        message: 'Template created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update an existing template
   */
  @Put(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async updateTemplate(@Param('id') id: number, @Body() updateTemplateDto: UpdateTemplateDto, @Request() req) {
    try {
      // Update basic template info
      if (updateTemplateDto.name || updateTemplateDto.description || updateTemplateDto.isActive !== undefined) {
        await this.templateService.updateTemplate(id, {
          name: updateTemplateDto.name,
          description: updateTemplateDto.description,
          isActive: updateTemplateDto.isActive
        }, req.user.userId, req.user.role);
      }

      // Update criteria if provided
      if (updateTemplateDto.criteriaUpdates) {
        await this.templateService.updateTemplateCriteria(id, updateTemplateDto.criteriaUpdates);
      }

      const updatedTemplate = await this.templateService.getTemplateWithCriteria(id, req.user.id, req.user.role);
      return {
        success: true,
        data: updatedTemplate,
        message: 'Template updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete/deactivate a template
   */
  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  async deleteTemplate(@Param('id') id: number, @Request() req) {
    try {
      await this.templateService.deactivateTemplate(id, req.user.userId, req.user.role);
      return {
        success: true,
        message: 'Template deactivated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Duplicate a template
   */
  @Post(':id/duplicate')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async duplicateTemplate(@Param('id') id: number, @Body() duplicateData: { name: string; createdById: number }, @Request() req) {
    try {
      const template = await this.templateService.duplicateTemplate(id, duplicateData.name, duplicateData.createdById);
      return {
        success: true,
        data: template,
        message: 'Template duplicated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Seed mock templates to database
   */
  @Post('seed-mock-data')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  async seedMockTemplates(@Request() req) {
    try {
      await this.templateService.seedMockTemplates();
      return {
        success: true,
        message: 'Mock templates seeded successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}
