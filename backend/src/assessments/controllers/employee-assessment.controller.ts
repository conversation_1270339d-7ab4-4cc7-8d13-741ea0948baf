import { Controller, Get, Post, Param, Body, UseGuards, Request } from '@nestjs/common';
import { EmployeeAssessmentService } from '../services/employee-assessment.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';

@Controller('assessments/employees')
@UseGuards(JwtAuthGuard)
export class EmployeeAssessmentController {
  constructor(
    private readonly employeeAssessmentService: EmployeeAssessmentService,
  ) { }

  /**
   * Get assessment data for a specific employee
   */
  @Get(':employeeId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async getEmployeeAssessmentData(@Param('employeeId') employeeId: number, @Request() req) {
    try {
      const data = await this.employeeAssessmentService.getEmployeeAssessmentData(employeeId, req.user.id, req.user.role);
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get assessment data for all employees in a team
   */
  @Get('team/:teamId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getTeamAssessmentData(@Param('teamId') teamId: number, @Request() req) {
    try {
      const data = await this.employeeAssessmentService.getTeamAssessmentData(teamId, req.user.id, req.user.role);
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get assessment data for all employees
   */
  @Get()
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  async getAllEmployeesAssessmentData(@Request() req) {
    try {
      const data = await this.employeeAssessmentService.getAllEmployeesAssessmentData(req.user.id, req.user.role);
      return {
        success: true,
        data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create assessment from template
   */
  @Post(':employeeId/assessments')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async createAssessmentFromTemplate(
    @Param('employeeId') employeeId: number,
    @Body() createData: { templateId: number; evaluatorId: number },
    @Request() req
  ) {
    try {
      const assessment = await this.employeeAssessmentService.createAssessmentFromTemplate(
        employeeId,
        createData.templateId,
        createData.evaluatorId,
        req.user.id,
        req.user.role
      );
      return {
        success: true,
        data: assessment,
        message: 'Assessment created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}
