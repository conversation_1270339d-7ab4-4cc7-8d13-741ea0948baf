import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { User } from '../../users/entities/user.entity';
import { AssessmentInstance } from '../entities/assessment-instance.entity';
import { AssessmentCriteriaService } from './assessment-criteria.service';
import { AssessmentScoringScale } from '../entities/assessment-scoring-scale.entity';
import { AssessmentScoringScaleLevel } from '../entities/assessment-scoring-scale-level.entity';

export interface TeamWithAssessmentData {
  id: number;
  name: string;
  description: string;
  memberCount: number;
  completedAssessments: number;
  pendingAssessments: number;
  averageScore: number;
  lastActivity: string;
  manager: string;
  assessmentCriteria: {
    hrLevel: any[];
    organizationalLevel: any[];
    teamLevel: any[];
  };
  members: any[];
}

@Injectable()
export class AssessmentDataService {
  constructor(
    @InjectRepository(OrganizationalUnit)
    private organizationalUnitRepository: Repository<OrganizationalUnit>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
    @InjectRepository(AssessmentScoringScale)
    private scoringScaleRepository: Repository<AssessmentScoringScale>,
    @InjectRepository(AssessmentScoringScaleLevel)
    private scoringScaleLevelRepository: Repository<AssessmentScoringScaleLevel>,
    private assessmentCriteriaService: AssessmentCriteriaService,
  ) { }

  /**
   * Get all teams with their assessment data for the HR Assessment Dashboard
   */
  async getTeamsWithAssessmentData(): Promise<TeamWithAssessmentData[]> {
    // Get all teams (organizational units that are teams)
    const teams = await this.organizationalUnitRepository.find({
      where: { type: 'team', isActive: true },
      relations: ['manager', 'members']
    });

    const teamsWithData: TeamWithAssessmentData[] = [];

    for (const team of teams) {
      // Get assessment criteria for this team
      const criteriaHierarchy = await this.assessmentCriteriaService.getCriteriaHierarchy(team.id);

      // Get team members
      const members = await this.userRepository.find({
        where: { organizationalUnitId: team.id },
        select: ['id', 'firstName', 'lastName', 'title', 'email']
      });

      // 🔐 NIS2-COMPLIANT: Calculate real assessment statistics from database
      const memberCount = members.length;

      // Get actual assessment data for team members
      const memberIds = members.map(m => m.id);
      const assessments = memberIds.length > 0 ? await this.assessmentRepository.find({
        where: {
          employeeId: In(memberIds),
          status: In(['COMPLETED', 'APPROVED'])
        }
      }) : [];

      const completedAssessments = assessments.length;
      const pendingAssessments = memberIds.length > 0 ? await this.assessmentRepository.count({
        where: {
          employeeId: In(memberIds),
          status: In(['DRAFT', 'IN_PROGRESS', 'PENDING_REVIEW'])
        }
      }) : 0;

      const averageScore = assessments.length > 0
        ? assessments.reduce((sum, a) => sum + (a.totalScore || 0), 0) / assessments.length
        : 0;

      teamsWithData.push({
        id: team.id,
        name: team.name,
        description: team.description || `${team.name} operations`,
        memberCount,
        completedAssessments,
        pendingAssessments,
        averageScore,
        lastActivity: new Date().toISOString().split('T')[0],
        manager: team.manager ? `${team.manager.firstName} ${team.manager.lastName}` : 'No Manager',
        assessmentCriteria: {
          hrLevel: criteriaHierarchy.hierarchy.hrLevel.criteria,
          organizationalLevel: criteriaHierarchy.hierarchy.organizationalLevel.criteria,
          teamLevel: criteriaHierarchy.hierarchy.teamLevel.criteria
        },
        members: await Promise.all(members.map(async (member) => {
          // Get real assessment data for each member
          const memberAssessments = await this.assessmentRepository.find({
            where: { employeeId: member.id },
            order: { assessmentDate: 'DESC' },
            take: 1
          });

          const hasAssessment = memberAssessments.length > 0;
          const lastAssessment = hasAssessment ? memberAssessments[0].assessmentDate : null;
          const status = hasAssessment ? memberAssessments[0].status.toLowerCase() : 'pending';

          return {
            id: member.id,
            firstName: member.firstName,
            lastName: member.lastName,
            title: member.title,
            email: member.email,
            hasAssessment,
            lastAssessment,
            status
          };
        }))
      });
    }

    return teamsWithData;
  }

  /**
   * Get scoring scales available for a team
   */
  async getScoringScalesForTeam(teamId: number) {
    const team = await this.organizationalUnitRepository.findOne({
      where: { id: teamId },
      relations: ['parent']
    });

    if (!team) {
      throw new Error(`Team with ID ${teamId} not found`);
    }

    // Get scoring scales available to this team:
    // 1. Company-wide scales (organizational_unit_id = NULL)
    // 2. Scales from parent organizational units
    // 3. Team-specific scales
    const scales = await this.scoringScaleRepository.find({
      where: [
        { organizationalUnitId: null, isActive: true }, // Company-wide
        { organizationalUnitId: team.parentId, isActive: true }, // Parent org
        { organizationalUnitId: teamId, isActive: true } // Team-specific
      ],
      relations: ['levels', 'organizationalUnit'],
      order: { id: 'ASC' }
    });

    return scales.map(scale => ({
      id: scale.id,
      name: scale.name,
      description: scale.description,
      source: scale.organizationalUnit?.name || 'Company-wide',
      levels: scale.levels.map(level => ({
        id: level.id,
        levelName: level.levelName,
        levelCode: level.levelCode,
        minScore: level.minPerformanceScore,
        maxScore: level.maxPerformanceScore,
        points: level.pointsAwarded,
        description: level.description
      }))
    }));
  }

  /**
   * Calculate final score based on performance, scale level, and manager adjustment
   */
  calculateFinalScore(
    performanceScore: number,
    scaleLevelPoints: number,
    managerAdjustment: number = 0
  ): number {
    // Final score = base points from scale + manager adjustment
    // Ensure it doesn't go below 0 or above 100
    const finalScore = scaleLevelPoints + managerAdjustment;
    return Math.max(0, Math.min(100, finalScore));
  }

  /**
   * Get scoring scale level based on performance score
   */
  async getScaleLevelForPerformance(
    scoringScaleId: number,
    performanceScore: number
  ): Promise<AssessmentScoringScaleLevel | null> {
    return this.scoringScaleLevelRepository.findOne({
      where: {
        scoringScaleId,
        minPerformanceScore: performanceScore >= 0 ? undefined : 0,
        maxPerformanceScore: performanceScore <= 100 ? undefined : 100
      },
      order: { orderIndex: 'ASC' }
    });
  }
}
