import { Injectable, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserRole } from '../../users/entities/user.entity';
import { AssessmentTemplate, TemplateLevel } from '../entities/assessment-template.entity';
import { AssessmentTemplateCriteria } from '../entities/assessment-template-criteria.entity';
import { AssessmentCriteria } from '../entities/assessment-criteria.entity';
import { CriteriaLevelType } from '../entities/assessment-criteria-level.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';

export interface TemplateWithCriteria {
  id: number;
  name: string;
  description: string;
  version: string;
  templateLevel: TemplateLevel;
  organizationalUnit?: {
    id: number;
    name: string;
    type: string;
  };
  criteria: {
    id: number;
    name: string;
    description: string;
    weight: number;
    maxScore: number;
    scoringMethod: string;
    isRequired: boolean;
    orderIndex: number;
  }[];
  isActive: boolean;
  createdAt: Date;
}

@Injectable()
export class AssessmentTemplateService {
  constructor(
    @InjectRepository(AssessmentTemplate)
    private templateRepository: Repository<AssessmentTemplate>,
    @InjectRepository(AssessmentTemplateCriteria)
    private templateCriteriaRepository: Repository<AssessmentTemplateCriteria>,
    @InjectRepository(AssessmentCriteria)
    private criteriaRepository: Repository<AssessmentCriteria>,
    @InjectRepository(OrganizationalUnit)
    private organizationalUnitRepository: Repository<OrganizationalUnit>,
  ) { }

  /**
   * Get all assessment templates organized by level
   */
  async getTemplatesByLevel(userId: number, userRole: string): Promise<{
    hrLevel: TemplateWithCriteria[];
    organizationalLevel: TemplateWithCriteria[];
    teamLevel: TemplateWithCriteria[];
  }> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view templates');
    }
    const templates = await this.templateRepository.find({
      where: { isActive: true },
      relations: ['organizationalUnit', 'templateCriteria', 'templateCriteria.criteria'],
      order: { templateLevel: 'ASC', name: 'ASC' }
    });

    const hrLevel: TemplateWithCriteria[] = [];
    const organizationalLevel: TemplateWithCriteria[] = [];
    const teamLevel: TemplateWithCriteria[] = [];

    for (const template of templates) {
      const templateWithCriteria = await this.formatTemplateWithCriteria(template);

      switch (template.templateLevel) {
        case TemplateLevel.HR_LEVEL:
          hrLevel.push(templateWithCriteria);
          break;
        case TemplateLevel.ORGANIZATIONAL_LEVEL:
          organizationalLevel.push(templateWithCriteria);
          break;
        case TemplateLevel.TEAM_LEVEL:
          teamLevel.push(templateWithCriteria);
          break;
      }
    }

    return { hrLevel, organizationalLevel, teamLevel };
  }

  /**
   * Get templates available for a specific organizational unit
   */
  async getTemplatesForOrganizationalUnit(unitId: number, userId: number, userRole: string): Promise<TemplateWithCriteria[]> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view organizational unit templates');
    }
    const unit = await this.organizationalUnitRepository.findOne({
      where: { id: unitId },
      relations: ['parent', 'parent.parent']
    });

    if (!unit) {
      throw new Error(`Organizational unit with ID ${unitId} not found`);
    }

    // Get templates available to this unit:
    // 1. HR level templates (available to all)
    // 2. Organizational level templates from parent units
    // 3. Team level templates specific to this unit
    const templates = await this.templateRepository.find({
      where: [
        { templateLevel: TemplateLevel.HR_LEVEL, isActive: true },
        { templateLevel: TemplateLevel.ORGANIZATIONAL_LEVEL, organizationalUnitId: unit.parentId, isActive: true },
        { templateLevel: TemplateLevel.TEAM_LEVEL, organizationalUnitId: unitId, isActive: true }
      ],
      relations: ['organizationalUnit', 'templateCriteria', 'templateCriteria.criteria'],
      order: { templateLevel: 'ASC', name: 'ASC' }
    });

    const result: TemplateWithCriteria[] = [];
    for (const template of templates) {
      result.push(await this.formatTemplateWithCriteria(template));
    }

    return result;
  }

  /**
   * Get a specific template with its criteria
   */
  async getTemplateWithCriteria(templateId: number, userId: number, userRole: string): Promise<TemplateWithCriteria> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view template details');
    }
    const template = await this.templateRepository.findOne({
      where: { id: templateId, isActive: true },
      relations: ['organizationalUnit', 'templateCriteria', 'templateCriteria.criteria']
    });

    if (!template) {
      throw new Error(`Template with ID ${templateId} not found`);
    }

    return this.formatTemplateWithCriteria(template);
  }

  /**
   * Create a new assessment template
   */
  async createTemplate(templateData: {
    name: string;
    description: string;
    templateLevel: TemplateLevel;
    organizationalUnitId?: number;
    createdById: number;
    criteriaIds: number[];
  }, userId: number, userRole: string): Promise<TemplateWithCriteria> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to create templates');
    }
    // Create the template
    const template = this.templateRepository.create({
      name: templateData.name,
      description: templateData.description,
      templateLevel: templateData.templateLevel,
      organizationalUnitId: templateData.organizationalUnitId,
      createdById: templateData.createdById,
      version: '1.0'
    });

    const savedTemplate = await this.templateRepository.save(template);

    // Link criteria to template
    const templateCriteria = templateData.criteriaIds.map((criteriaId, index) =>
      this.templateCriteriaRepository.create({
        templateId: savedTemplate.id,
        criteriaId,
        isRequired: true,
        orderIndex: index + 1
      })
    );

    await this.templateCriteriaRepository.save(templateCriteria);

    return this.getTemplateWithCriteria(savedTemplate.id, userId, userRole);
  }

  /**
   * Update template criteria
   */
  async updateTemplateCriteria(templateId: number, criteriaUpdates: {
    criteriaId: number;
    isRequired?: boolean;
    orderIndex?: number;
    weightOverride?: number;
  }[], userId: number = 0, userRole: string = 'SYSTEM'): Promise<TemplateWithCriteria> {
    for (const update of criteriaUpdates) {
      await this.templateCriteriaRepository.update(
        { templateId, criteriaId: update.criteriaId },
        {
          isRequired: update.isRequired,
          orderIndex: update.orderIndex,
          weightOverride: update.weightOverride
        }
      );
    }

    return this.getTemplateWithCriteria(templateId, userId, userRole);
  }

  /**
   * Update basic template information
   */
  async updateTemplate(templateId: number, updateData: {
    name?: string;
    description?: string;
    isActive?: boolean;
  }, userId: number, userRole: string): Promise<void> {
    // Validate user permissions - only HR_ADMIN and MANAGER can update templates
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to update templates');
    }
    await this.templateRepository.update(templateId, updateData);
  }

  /**
   * Deactivate a template
   */
  async deactivateTemplate(templateId: number, userId: number, userRole: string): Promise<void> {
    // Validate user permissions - only HR_ADMIN can deactivate templates
    if (userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR administrators can deactivate templates');
    }
    await this.templateRepository.update(templateId, { isActive: false });
  }

  /**
   * Duplicate a template
   */
  async duplicateTemplate(templateId: number, newName: string, createdById: number, userRole: string = 'MANAGER'): Promise<TemplateWithCriteria> {
    // Validate user permissions - only HR_ADMIN and MANAGER can duplicate templates
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to duplicate templates');
    }
    const originalTemplate = await this.templateRepository.findOne({
      where: { id: templateId },
      relations: ['templateCriteria']
    });

    if (!originalTemplate) {
      throw new Error(`Template with ID ${templateId} not found`);
    }

    // Create new template
    const newTemplate = this.templateRepository.create({
      name: newName,
      description: `Copy of ${originalTemplate.description}`,
      templateLevel: originalTemplate.templateLevel,
      organizationalUnitId: originalTemplate.organizationalUnitId,
      createdById,
      version: '1.0'
    });

    const savedTemplate = await this.templateRepository.save(newTemplate);

    // Copy criteria associations
    if (originalTemplate.templateCriteria) {
      const newTemplateCriteria = originalTemplate.templateCriteria.map(tc =>
        this.templateCriteriaRepository.create({
          templateId: savedTemplate.id,
          criteriaId: tc.criteriaId,
          isRequired: tc.isRequired,
          orderIndex: tc.orderIndex,
          weightOverride: tc.weightOverride
        })
      );

      await this.templateCriteriaRepository.save(newTemplateCriteria);
    }

    // Note: This is an internal call, we'll pass system context
    return this.getTemplateWithCriteria(savedTemplate.id, 0, 'SYSTEM');
  }

  /**
   * Add criteria to template
   */
  async addCriteriaToTemplate(templateId: number, criteriaIds: number[], userId: number, userRole: string): Promise<void> {
    // Validate user permissions - only HR_ADMIN and MANAGER can modify template criteria
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to modify template criteria');
    }
    const existingCriteria = await this.templateCriteriaRepository.find({
      where: { templateId }
    });

    const maxOrderIndex = existingCriteria.length > 0
      ? Math.max(...existingCriteria.map(tc => tc.orderIndex))
      : 0;

    const newTemplateCriteria = criteriaIds.map((criteriaId, index) =>
      this.templateCriteriaRepository.create({
        templateId,
        criteriaId,
        isRequired: true,
        orderIndex: maxOrderIndex + index + 1
      })
    );

    await this.templateCriteriaRepository.save(newTemplateCriteria);
  }

  /**
   * Remove criteria from template
   */
  async removeCriteriaFromTemplate(templateId: number, criteriaIds: number[], userId: number, userRole: string): Promise<void> {
    // Validate user permissions - only HR_ADMIN and MANAGER can modify template criteria
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to modify template criteria');
    }
    await this.templateCriteriaRepository.delete({
      templateId,
      criteriaId: criteriaIds as any
    });
  }

  /**
   * Get available criteria for a template level
   */
  async getAvailableCriteriaForLevel(templateLevel: TemplateLevel, organizationalUnitId?: number, userId: number = 0, userRole: string = 'SYSTEM'): Promise<any[]> {
    // Note: This method is called internally by other services which already validate permissions
    let criteria = [];

    switch (templateLevel) {
      case TemplateLevel.HR_LEVEL:
        criteria = await this.criteriaRepository.find({
          where: {
            criteriaLevel: { levelType: CriteriaLevelType.HR_LEVEL },
            isActive: true
          },
          relations: ['criteriaLevel']
        });
        break;

      case TemplateLevel.ORGANIZATIONAL_LEVEL:
        criteria = await this.criteriaRepository.find({
          where: {
            criteriaLevel: { levelType: CriteriaLevelType.ORGANIZATIONAL_LEVEL },
            organizationalUnitId,
            isActive: true
          },
          relations: ['criteriaLevel']
        });
        break;

      case TemplateLevel.TEAM_LEVEL:
        criteria = await this.criteriaRepository.find({
          where: {
            criteriaLevel: { levelType: CriteriaLevelType.TEAM_LEVEL },
            organizationalUnitId,
            isActive: true
          },
          relations: ['criteriaLevel']
        });
        break;
    }

    return criteria.map(c => ({
      id: c.id,
      name: c.name,
      description: c.description,
      weight: c.weight,
      maxScore: c.maxScore,
      scoringMethod: c.scoringMethod
    }));
  }

  /**
   * Seed mock templates to database
   */
  async seedMockTemplates(userId: number = 0, userRole: string = 'SYSTEM'): Promise<void> {
    // Note: This is a seeding method typically called during system initialization
    // Check if templates already exist
    const existingTemplates = await this.templateRepository.count();
    if (existingTemplates > 0) {
      throw new Error('Templates already exist in database. Use this endpoint only for initial seeding.');
    }

    // Mock template data based on the frontend mock data
    const mockTemplatesData = [
      // HR Level Templates
      {
        name: 'HR Corporate Standards Template',
        description: 'Company-wide mandatory assessment criteria template',
        templateLevel: TemplateLevel.HR_LEVEL,
        organizationalUnitId: null,
        criteriaIds: [1, 2, 3, 4, 5, 6] // HR criteria IDs
      },
      {
        name: 'Annual Performance Review Template',
        description: 'Comprehensive yearly evaluation with HR standards',
        templateLevel: TemplateLevel.HR_LEVEL,
        organizationalUnitId: null,
        criteriaIds: [1, 2, 3, 4, 5, 6] // HR criteria IDs
      },
      {
        name: 'New Employee Onboarding Assessment',
        description: 'Standard assessment for new hire evaluation',
        templateLevel: TemplateLevel.HR_LEVEL,
        organizationalUnitId: null,
        criteriaIds: [1, 2, 3, 4, 5, 6] // HR criteria IDs
      },
      // Organizational Level Templates
      {
        name: 'Technology Division Excellence Template',
        description: 'Technical assessment criteria for technology teams',
        templateLevel: TemplateLevel.ORGANIZATIONAL_LEVEL,
        organizationalUnitId: 2, // Technology Division
        criteriaIds: [7, 8, 9, 10, 11] // Tech criteria IDs
      },
      {
        name: 'Product Division Innovation Template',
        description: 'Product-focused assessment criteria',
        templateLevel: TemplateLevel.ORGANIZATIONAL_LEVEL,
        organizationalUnitId: 3, // Product Division
        criteriaIds: [12, 13, 14, 15, 16] // Product criteria IDs
      },
      // Team Level Templates
      {
        name: 'Frontend Engineering Assessment',
        description: 'Frontend development specific criteria',
        templateLevel: TemplateLevel.TEAM_LEVEL,
        organizationalUnitId: 11, // Frontend Team
        criteriaIds: [17, 18, 19, 20] // Frontend criteria IDs
      },
      {
        name: 'Backend Engineering Assessment',
        description: 'Backend development specific criteria',
        templateLevel: TemplateLevel.TEAM_LEVEL,
        organizationalUnitId: 12, // Backend Team
        criteriaIds: [21, 22, 23, 24] // Backend criteria IDs
      },
      {
        name: 'DevOps Engineering Assessment',
        description: 'DevOps and infrastructure specific criteria',
        templateLevel: TemplateLevel.TEAM_LEVEL,
        organizationalUnitId: 13, // DevOps Team
        criteriaIds: [25, 26, 27, 28] // DevOps criteria IDs
      }
    ];

    // Create templates
    for (const templateData of mockTemplatesData) {
      await this.createTemplate({
        ...templateData,
        createdById: 1 // Default admin user
      }, 1, 'HR_ADMIN'); // System seeding with admin context
    }
  }

  /**
   * Format template with criteria for response
   */
  private async formatTemplateWithCriteria(template: AssessmentTemplate): Promise<TemplateWithCriteria> {
    const criteria = template.templateCriteria
      ?.filter(tc => tc.isActive)
      ?.sort((a, b) => a.orderIndex - b.orderIndex)
      ?.map(tc => ({
        id: tc.criteria.id,
        name: tc.criteria.name,
        description: tc.criteria.description,
        weight: tc.weightOverride || tc.criteria.weight,
        maxScore: tc.criteria.maxScore,
        scoringMethod: tc.criteria.scoringMethod,
        isRequired: tc.isRequired,
        orderIndex: tc.orderIndex
      })) || [];

    return {
      id: template.id,
      name: template.name,
      description: template.description,
      version: template.version.toString(),
      templateLevel: template.templateLevel,
      organizationalUnit: template.organizationalUnit ? {
        id: template.organizationalUnit.id,
        name: template.organizationalUnit.name,
        type: template.organizationalUnit.type
      } : undefined,
      criteria,
      isActive: template.isActive,
      createdAt: template.createdAt
    };
  }
}
