import { Injectable, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole } from '../../users/entities/user.entity';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { AssessmentTemplateService } from './assessment-template.service';
import { AssessmentCriteriaService } from './assessment-criteria.service';

export interface EmployeeAssessmentData {
  employee: {
    id: number;
    firstName: string;
    lastName: string;
    title: string;
    email: string;
    organizationalUnit: {
      id: number;
      name: string;
      type: string;
    };
  };
  applicableTemplates: any[];
  assessmentCriteria: {
    hrLevel: any[];
    organizationalLevel: any[];
    teamLevel: any[];
    allCriteria: any[];
  };
  hasActiveAssessment: boolean;
  lastAssessmentDate: string | null;
  assessmentStatus: 'not_started' | 'in_progress' | 'completed' | 'overdue';
}

@Injectable()
export class EmployeeAssessmentService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(OrganizationalUnit)
    private organizationalUnitRepository: Repository<OrganizationalUnit>,
    private templateService: AssessmentTemplateService,
    private criteriaService: AssessmentCriteriaService,
  ) { }

  /**
   * Get assessment data for a specific employee
   */
  async getEmployeeAssessmentData(employeeId: number, userId: number, userRole: string): Promise<EmployeeAssessmentData> {
    // Validate user permissions - employees can only view their own data
    if (userRole === UserRole.EMPLOYEE && userId !== employeeId) {
      throw new ForbiddenException('Employees can only view their own assessment data');
    }
    if (![UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view employee assessment data');
    }
    // Get employee with organizational unit
    const employee = await this.userRepository.findOne({
      where: { id: employeeId },
      relations: ['organizationalUnit']
    });

    if (!employee) {
      throw new Error(`Employee with ID ${employeeId} not found`);
    }

    // Get applicable templates for the employee's organizational unit
    const applicableTemplates = await this.templateService.getTemplatesForOrganizationalUnit(
      employee.organizationalUnitId,
      userId,
      userRole
    );

    // Get assessment criteria hierarchy for the employee's team
    const criteriaHierarchy = await this.criteriaService.getCriteriaHierarchy(
      employee.organizationalUnitId,
      userId,
      userRole
    );

    // TODO: Check for active assessments (implement when assessment instances are ready)
    const hasActiveAssessment = false;
    const lastAssessmentDate = null;
    const assessmentStatus = 'not_started' as const;

    return {
      employee: {
        id: employee.id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        title: employee.title,
        email: employee.email,
        organizationalUnit: {
          id: employee.organizationalUnit.id,
          name: employee.organizationalUnit.name,
          type: employee.organizationalUnit.type
        }
      },
      applicableTemplates,
      assessmentCriteria: {
        hrLevel: criteriaHierarchy.hierarchy.hrLevel.criteria,
        organizationalLevel: criteriaHierarchy.hierarchy.organizationalLevel.criteria,
        teamLevel: criteriaHierarchy.hierarchy.teamLevel.criteria,
        allCriteria: criteriaHierarchy.hierarchy.hrLevel.criteria
          .concat(criteriaHierarchy.hierarchy.organizationalLevel.criteria)
          .concat(criteriaHierarchy.hierarchy.teamLevel.criteria)
      },
      hasActiveAssessment,
      lastAssessmentDate,
      assessmentStatus
    };
  }

  /**
   * Get assessment data for all employees in a team
   */
  async getTeamAssessmentData(teamId: number, userId: number, userRole: string): Promise<EmployeeAssessmentData[]> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view team assessment data');
    }
    // Get all employees in the team
    const employees = await this.userRepository.find({
      where: { organizationalUnitId: teamId },
      relations: ['organizationalUnit']
    });

    const assessmentData: EmployeeAssessmentData[] = [];

    for (const employee of employees) {
      try {
        const employeeData = await this.getEmployeeAssessmentData(employee.id, userId, userRole);
        assessmentData.push(employeeData);
      } catch (error) {
        console.error(`Error getting assessment data for employee ${employee.id}:`, error);
        // Continue with other employees
      }
    }

    return assessmentData;
  }

  /**
   * Get all employees with their assessment data
   */
  async getAllEmployeesAssessmentData(userId: number, userRole: string): Promise<EmployeeAssessmentData[]> {
    // Validate user permissions - only HR admins can view all employees
    if (userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR administrators can view all employee assessment data');
    }
    // Get all active employees
    const employees = await this.userRepository.find({
      where: { isActive: true },
      relations: ['organizationalUnit']
    });

    const assessmentData: EmployeeAssessmentData[] = [];

    for (const employee of employees) {
      try {
        const employeeData = await this.getEmployeeAssessmentData(employee.id, userId, userRole);
        assessmentData.push(employeeData);
      } catch (error) {
        console.error(`Error getting assessment data for employee ${employee.id}:`, error);
        // Continue with other employees
      }
    }

    return assessmentData;
  }

  /**
   * Create assessment instance for employee using a template
   */
  async createAssessmentFromTemplate(
    employeeId: number,
    templateId: number,
    evaluatorId: number,
    userId: number,
    userRole: string
  ): Promise<any> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to create assessments');
    }
    // TODO: Implement assessment instance creation
    // This will be implemented when we have the assessment instances ready
    throw new Error('Assessment instance creation not yet implemented');
  }
}
