import { Test, TestingModule } from '@nestjs/testing';
import { ScoringService } from './scoring.service';
import { AssessmentArea } from '../entities/assessment-area.entity';
import { ScoringRule, RuleType, ConditionOperator } from '../entities/scoring-rule.entity';

describe('ScoringService', () => {
  let service: ScoringService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ScoringService],
    }).compile();

    service = module.get<ScoringService>(ScoringService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('calculateAreaScore', () => {
    it('should calculate basic score without rules', async () => {
      const area: AssessmentArea = {
        id: 1,
        name: 'Communication',
        weight: 0.25,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: []
      };

      const input = {
        areaId: 1,
        baseScore: 85,
        additionalData: {}
      };

      const result = await service.calculateAreaScore(area, input);

      expect(result.areaId).toBe(1);
      expect(result.baseScore).toBe(85);
      expect(result.finalScore).toBe(85);
      expect(result.weightedScore).toBe(85 * 0.25);
      expect(result.adjustments).toHaveLength(0);
    });

    it('should apply addition scoring rule', async () => {
      const scoringRule: ScoringRule = {
        id: 1,
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 10,
        description: 'Bonus for training completion'
      };

      const area: AssessmentArea = {
        id: 1,
        name: 'Communication',
        weight: 0.25,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: [scoringRule]
      };

      const input = {
        areaId: 1,
        baseScore: 85,
        additionalData: {}
      };

      const result = await service.calculateAreaScore(area, input);

      expect(result.finalScore).toBe(95); // 85 + 10
      expect(result.adjustments).toHaveLength(1);
      expect(result.adjustments[0].adjustment).toBe(10);
      expect(result.adjustments[0].ruleType).toBe(RuleType.ADDITION);
    });

    it('should apply subtraction scoring rule', async () => {
      const scoringRule: ScoringRule = {
        id: 1,
        ruleType: RuleType.SUBTRACTION,
        scoreAdjustment: 5,
        description: 'Penalty for incidents'
      };

      const area: AssessmentArea = {
        id: 1,
        name: 'Safety',
        weight: 0.25,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: [scoringRule]
      };

      const input = {
        areaId: 1,
        baseScore: 85,
        additionalData: {}
      };

      const result = await service.calculateAreaScore(area, input);

      expect(result.finalScore).toBe(80); // 85 - 5
      expect(result.adjustments).toHaveLength(1);
      expect(result.adjustments[0].adjustment).toBe(-5);
    });

    it('should apply conditional scoring rule for NC incidents', async () => {
      const scoringRule: ScoringRule = {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'nc_incidents',
        scoreAdjustment: 3,
        description: 'Penalty per NC incident'
      };

      const area: AssessmentArea = {
        id: 1,
        name: 'Quality',
        weight: 0.25,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: [scoringRule]
      };

      const input = {
        areaId: 1,
        baseScore: 85,
        additionalData: {
          nc_incidents: 2
        }
      };

      const result = await service.calculateAreaScore(area, input);

      expect(result.finalScore).toBe(79); // 85 - (2 * 3)
      expect(result.adjustments).toHaveLength(1);
      expect(result.adjustments[0].adjustment).toBe(-6);
    });

    it('should apply conditional scoring rule for training completed', async () => {
      const scoringRule: ScoringRule = {
        id: 1,
        ruleType: RuleType.CONDITIONAL,
        conditionField: 'training_completed',
        scoreAdjustment: 2,
        description: 'Bonus per training completed'
      };

      const area: AssessmentArea = {
        id: 1,
        name: 'Development',
        weight: 0.25,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: [scoringRule]
      };

      const input = {
        areaId: 1,
        baseScore: 85,
        additionalData: {
          training_completed: 3
        }
      };

      const result = await service.calculateAreaScore(area, input);

      expect(result.finalScore).toBe(91); // 85 + (3 * 2)
      expect(result.adjustments).toHaveLength(1);
      expect(result.adjustments[0].adjustment).toBe(6);
    });

    it('should cap score at maxScore', async () => {
      const scoringRule: ScoringRule = {
        id: 1,
        ruleType: RuleType.ADDITION,
        scoreAdjustment: 20,
        description: 'Large bonus'
      };

      const area: AssessmentArea = {
        id: 1,
        name: 'Communication',
        weight: 0.25,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: [scoringRule]
      };

      const input = {
        areaId: 1,
        baseScore: 95,
        additionalData: {}
      };

      const result = await service.calculateAreaScore(area, input);

      expect(result.finalScore).toBe(100); // Capped at maxScore
      expect(result.adjustments[0].adjustment).toBe(20);
    });

    it('should not allow negative scores', async () => {
      const scoringRule: ScoringRule = {
        id: 1,
        ruleType: RuleType.SUBTRACTION,
        scoreAdjustment: 50,
        description: 'Large penalty'
      };

      const area: AssessmentArea = {
        id: 1,
        name: 'Safety',
        weight: 0.25,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: [scoringRule]
      };

      const input = {
        areaId: 1,
        baseScore: 30,
        additionalData: {}
      };

      const result = await service.calculateAreaScore(area, input);

      expect(result.finalScore).toBe(0); // Cannot go below 0
      expect(result.adjustments[0].adjustment).toBe(-50);
    });

    it('should apply multiple scoring rules', async () => {
      const rules: ScoringRule[] = [
        {
          id: 1,
          ruleType: RuleType.ADDITION,
          scoreAdjustment: 5,
          description: 'Training bonus'
        },
        {
          id: 2,
          ruleType: RuleType.CONDITIONAL,
          conditionField: 'nc_incidents',
          scoreAdjustment: 2,
          description: 'NC penalty'
        }
      ];

      const area: AssessmentArea = {
        id: 1,
        name: 'Quality',
        weight: 0.25,
        maxScore: 100,
        orderIndex: 1,
        scoringRules: rules
      };

      const input = {
        areaId: 1,
        baseScore: 85,
        additionalData: {
          nc_incidents: 1
        }
      };

      const result = await service.calculateAreaScore(area, input);

      expect(result.finalScore).toBe(88); // 85 + 5 - 2
      expect(result.adjustments).toHaveLength(2);
    });
  });

  describe('calculateAssessmentScore', () => {
    it('should calculate total assessment score', async () => {
      const areas: AssessmentArea[] = [
        {
          id: 1,
          name: 'Communication',
          weight: 0.4,
          maxScore: 100,
          orderIndex: 1,
          scoringRules: []
        },
        {
          id: 2,
          name: 'Technical',
          weight: 0.6,
          maxScore: 100,
          orderIndex: 2,
          scoringRules: []
        }
      ];

      const inputs = [
        { areaId: 1, baseScore: 80, additionalData: {} },
        { areaId: 2, baseScore: 90, additionalData: {} }
      ];

      const result = await service.calculateAssessmentScore(areas, inputs);

      // Weighted average: (80 * 0.4 + 90 * 0.6) / (0.4 + 0.6) = 86
      expect(result.totalScore).toBe(86);
      expect(result.maxPossibleScore).toBe(100);
      expect(result.scorePercentage).toBe(86);
      expect(result.areaResults).toHaveLength(2);
    });

    it('should throw error for missing area input', async () => {
      const areas: AssessmentArea[] = [
        {
          id: 1,
          name: 'Communication',
          weight: 0.5,
          maxScore: 100,
          orderIndex: 1,
          scoringRules: []
        },
        {
          id: 2,
          name: 'Technical',
          weight: 0.5,
          maxScore: 100,
          orderIndex: 2,
          scoringRules: []
        }
      ];

      const inputs = [
        { areaId: 1, baseScore: 80, additionalData: {} }
        // Missing input for area 2
      ];

      await expect(service.calculateAssessmentScore(areas, inputs))
        .rejects
        .toThrow('Missing input for area: Technical');
    });
  });

  describe('validateInputs', () => {
    it('should return no errors for valid inputs', () => {
      const areas: AssessmentArea[] = [
        {
          id: 1,
          name: 'Communication',
          weight: 0.5,
          maxScore: 100,
          orderIndex: 1,
          scoringRules: []
        }
      ];

      const inputs = [
        { areaId: 1, baseScore: 85, additionalData: {} }
      ];

      const errors = service.validateInputs(areas, inputs);
      expect(errors).toHaveLength(0);
    });

    it('should return error for missing input', () => {
      const areas: AssessmentArea[] = [
        {
          id: 1,
          name: 'Communication',
          weight: 0.5,
          maxScore: 100,
          orderIndex: 1,
          scoringRules: []
        }
      ];

      const inputs = []; // No inputs

      const errors = service.validateInputs(areas, inputs);
      expect(errors).toContain('Missing input for area: Communication');
    });

    it('should return error for score out of range', () => {
      const areas: AssessmentArea[] = [
        {
          id: 1,
          name: 'Communication',
          weight: 0.5,
          maxScore: 100,
          orderIndex: 1,
          scoringRules: []
        }
      ];

      const inputs = [
        { areaId: 1, baseScore: 150, additionalData: {} } // Score too high
      ];

      const errors = service.validateInputs(areas, inputs);
      expect(errors).toContain('Score for Communication must be between 0 and 100');
    });
  });
});
