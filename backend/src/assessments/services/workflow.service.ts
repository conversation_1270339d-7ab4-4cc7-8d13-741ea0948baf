import { Injectable, BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssessmentInstance, AssessmentStatus } from '../entities/assessment-instance.entity';
import { UserRole } from '../../users/entities/user.entity';

export interface WorkflowTransition {
  from: AssessmentStatus;
  to: AssessmentStatus;
  allowedRoles: UserRole[];
  requiresApproval?: boolean;
  validationRules?: string[];
}

export interface WorkflowAction {
  assessmentId: number;
  action: string;
  userId: number;
  userRole: UserRole;
  comments?: string;
  metadata?: Record<string, any>;
}

export interface WorkflowValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

@Injectable()
export class WorkflowService {
  private readonly transitions: WorkflowTransition[] = [
    // Draft transitions
    {
      from: AssessmentStatus.DRAFT,
      to: AssessmentStatus.IN_PROGRESS,
      allowedRoles: [UserRole.HR_ADMIN, UserRole.MANAGER],
      validationRules: ['has_all_scores', 'valid_assessment_date']
    },
    {
      from: AssessmentStatus.DRAFT,
      to: AssessmentStatus.COMPLETED,
      allowedRoles: [UserRole.HR_ADMIN, UserRole.MANAGER],
      validationRules: ['has_all_scores', 'valid_assessment_date', 'has_evaluator_comments']
    },

    // In Progress transitions
    {
      from: AssessmentStatus.IN_PROGRESS,
      to: AssessmentStatus.DRAFT,
      allowedRoles: [UserRole.HR_ADMIN, UserRole.MANAGER],
      validationRules: []
    },
    {
      from: AssessmentStatus.IN_PROGRESS,
      to: AssessmentStatus.COMPLETED,
      allowedRoles: [UserRole.HR_ADMIN, UserRole.MANAGER],
      validationRules: ['has_all_scores', 'has_evaluator_comments']
    },

    // Completed transitions
    {
      from: AssessmentStatus.COMPLETED,
      to: AssessmentStatus.IN_PROGRESS,
      allowedRoles: [UserRole.HR_ADMIN, UserRole.MANAGER],
      validationRules: []
    },
    {
      from: AssessmentStatus.COMPLETED,
      to: AssessmentStatus.APPROVED,
      allowedRoles: [UserRole.HR_ADMIN],
      requiresApproval: true,
      validationRules: ['has_all_scores', 'has_evaluator_comments']
    },
    {
      from: AssessmentStatus.COMPLETED,
      to: AssessmentStatus.REJECTED,
      allowedRoles: [UserRole.HR_ADMIN],
      requiresApproval: true,
      validationRules: ['has_rejection_reason']
    },

    // Approved transitions (limited)
    {
      from: AssessmentStatus.APPROVED,
      to: AssessmentStatus.IN_PROGRESS,
      allowedRoles: [UserRole.HR_ADMIN],
      validationRules: ['has_revision_reason']
    },

    // Rejected transitions
    {
      from: AssessmentStatus.REJECTED,
      to: AssessmentStatus.IN_PROGRESS,
      allowedRoles: [UserRole.HR_ADMIN, UserRole.MANAGER],
      validationRules: []
    },
    {
      from: AssessmentStatus.REJECTED,
      to: AssessmentStatus.COMPLETED,
      allowedRoles: [UserRole.HR_ADMIN, UserRole.MANAGER],
      validationRules: ['has_all_scores', 'has_evaluator_comments']
    }
  ];

  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
  ) { }

  /**
   * Get available transitions for an assessment
   */
  getAvailableTransitions(
    currentStatus: AssessmentStatus,
    userRole: UserRole
  ): WorkflowTransition[] {
    return this.transitions.filter(
      transition =>
        transition.from === currentStatus &&
        transition.allowedRoles.includes(userRole)
    );
  }

  /**
   * Check if a transition is allowed
   */
  isTransitionAllowed(
    from: AssessmentStatus,
    to: AssessmentStatus,
    userRole: UserRole
  ): boolean {
    const transition = this.transitions.find(
      t => t.from === from && t.to === to && t.allowedRoles.includes(userRole)
    );
    return !!transition;
  }

  /**
   * Validate assessment before transition
   */
  async validateAssessmentForTransition(
    assessmentId: number,
    targetStatus: AssessmentStatus,
    metadata?: Record<string, any>
  ): Promise<WorkflowValidationResult> {
    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId },
      relations: ['responses', 'template', 'template.areas']
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }

    const transition = this.transitions.find(
      t => t.from === assessment.status && t.to === targetStatus
    );

    if (!transition) {
      return {
        isValid: false,
        errors: [`Transition from ${assessment.status} to ${targetStatus} is not allowed`],
        warnings: []
      };
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // Apply validation rules
    if (transition.validationRules) {
      for (const rule of transition.validationRules) {
        const ruleResult = await this.applyValidationRule(rule, assessment, metadata);
        if (!ruleResult.isValid) {
          errors.push(...ruleResult.errors);
        }
        warnings.push(...ruleResult.warnings);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Execute workflow transition
   */
  async executeTransition(action: WorkflowAction, userId: number, userRole: string): Promise<AssessmentInstance> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to execute workflow transitions');
    }
    const assessment = await this.assessmentRepository.findOne({
      where: { id: action.assessmentId },
      relations: ['responses']
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }

    // Parse target status from action
    const targetStatus = this.parseActionToStatus(action.action);

    // Check if transition is allowed
    if (!this.isTransitionAllowed(assessment.status, targetStatus, action.userRole)) {
      throw new ForbiddenException(
        `User with role ${action.userRole} cannot transition assessment from ${assessment.status} to ${targetStatus}`
      );
    }

    // Validate assessment for transition
    const validation = await this.validateAssessmentForTransition(
      action.assessmentId,
      targetStatus,
      action.metadata
    );

    if (!validation.isValid) {
      throw new BadRequestException(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Update assessment status
    assessment.status = targetStatus;

    // Add workflow metadata
    const workflowHistory = Array.isArray(assessment.workflowHistory)
      ? assessment.workflowHistory
      : (assessment.workflowHistory ? JSON.parse(assessment.workflowHistory) : []);

    workflowHistory.push({
      from: assessment.status,
      to: targetStatus,
      userId: action.userId,
      timestamp: new Date(),
      comments: action.comments,
      metadata: action.metadata
    });

    assessment.workflowHistory = JSON.stringify(workflowHistory);

    // Set approval/rejection specific fields
    if (targetStatus === AssessmentStatus.APPROVED) {
      assessment.approvedAt = new Date();
      assessment.approvedById = action.userId;
    } else if (targetStatus === AssessmentStatus.REJECTED) {
      assessment.rejectedAt = new Date();
      assessment.rejectedById = action.userId;
      assessment.rejectionReason = action.comments;
    }

    return this.assessmentRepository.save(assessment);
  }

  /**
   * Get workflow history for an assessment
   */
  async getWorkflowHistory(assessmentId: number, userId: number = 0, userRole: string = 'SYSTEM'): Promise<any[]> {
    // Note: This method is called internally by assessments service which already validates permissions
    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId }
    });

    if (!assessment) {
      throw new NotFoundException('Assessment not found');
    }

    try {
      return assessment.workflowHistory ? JSON.parse(assessment.workflowHistory) : [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Apply individual validation rule
   */
  private async applyValidationRule(
    rule: string,
    assessment: AssessmentInstance,
    metadata?: Record<string, any>
  ): Promise<WorkflowValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    switch (rule) {
      case 'has_all_scores':
        if (!assessment.responses || assessment.responses.length === 0) {
          errors.push('Assessment must have at least one response');
        } else {
          const incompleteResponses = assessment.responses.filter(r => r.score === null || r.score === undefined);
          if (incompleteResponses.length > 0) {
            errors.push(`${incompleteResponses.length} assessment area(s) are missing scores`);
          }
        }
        break;

      case 'valid_assessment_date':
        if (!assessment.assessmentDate) {
          errors.push('Assessment date is required');
        } else if (assessment.assessmentDate > new Date()) {
          warnings.push('Assessment date is in the future');
        }
        break;

      case 'has_evaluator_comments':
        if (!assessment.responses || assessment.responses.length === 0) {
          errors.push('Assessment must have responses');
        } else {
          const responsesWithoutComments = assessment.responses.filter(
            r => !r.evaluatorComments || r.evaluatorComments.trim().length === 0
          );
          if (responsesWithoutComments.length > 0) {
            warnings.push(`${responsesWithoutComments.length} area(s) are missing evaluator comments`);
          }
        }
        break;

      case 'has_rejection_reason':
        if (!metadata?.rejectionReason || metadata.rejectionReason.trim().length === 0) {
          errors.push('Rejection reason is required');
        }
        break;

      case 'has_revision_reason':
        if (!metadata?.revisionReason || metadata.revisionReason.trim().length === 0) {
          errors.push('Revision reason is required when reopening approved assessment');
        }
        break;

      default:
        warnings.push(`Unknown validation rule: ${rule}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Parse action string to assessment status
   */
  private parseActionToStatus(action: string): AssessmentStatus {
    const actionMap: Record<string, AssessmentStatus> = {
      'start': AssessmentStatus.IN_PROGRESS,
      'complete': AssessmentStatus.COMPLETED,
      'approve': AssessmentStatus.APPROVED,
      'reject': AssessmentStatus.REJECTED,
      'reopen': AssessmentStatus.IN_PROGRESS,
      'save_draft': AssessmentStatus.DRAFT
    };

    const status = actionMap[action];
    if (!status) {
      throw new BadRequestException(`Unknown action: ${action}`);
    }

    return status;
  }

  /**
   * Get workflow statistics
   */
  async getWorkflowStats(userId: number = 0, userRole: string = 'SYSTEM'): Promise<{
    totalAssessments: number;
    byStatus: Record<AssessmentStatus, number>;
    averageCompletionTime: number;
    pendingApprovals: number;
  }> {
    // Note: This method is called internally by assessments service which already validates permissions
    const assessments = await this.assessmentRepository.find();

    const byStatus = assessments.reduce((acc, assessment) => {
      acc[assessment.status] = (acc[assessment.status] || 0) + 1;
      return acc;
    }, {} as Record<AssessmentStatus, number>);

    // Calculate average completion time (mock calculation)
    const completedAssessments = assessments.filter(a =>
      a.status === AssessmentStatus.COMPLETED ||
      a.status === AssessmentStatus.APPROVED
    );

    const averageCompletionTime = completedAssessments.length > 0
      ? completedAssessments.reduce((sum, a) => {
        const created = new Date(a.createdAt);
        const completed = new Date(a.updatedAt);
        return sum + (completed.getTime() - created.getTime());
      }, 0) / completedAssessments.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    return {
      totalAssessments: assessments.length,
      byStatus,
      averageCompletionTime,
      pendingApprovals: byStatus[AssessmentStatus.COMPLETED] || 0
    };
  }
}
