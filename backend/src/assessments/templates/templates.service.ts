import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Connection } from 'typeorm';
import { AssessmentTemplate } from '../entities/assessment-template.entity';
import { AssessmentArea } from '../entities/assessment-area.entity';
import { ScoringRule } from '../entities/scoring-rule.entity';
import { CreateTemplateDto } from '../dto/create-template.dto';
import { UpdateTemplateDto } from '../dto/update-template.dto';
import { TeamTemplateAssignmentDto } from '../dto/team-template-assignment.dto';
import { UserRole } from '../../users/entities/user.entity';

@Injectable()
export class TemplatesService {
  constructor(
    @InjectRepository(AssessmentTemplate)
    private templateRepository: Repository<AssessmentTemplate>,
    @InjectRepository(AssessmentArea)
    private areaRepository: Repository<AssessmentArea>,
    @InjectRepository(ScoringRule)
    private ruleRepository: Repository<ScoringRule>,
    private connection: Connection,
  ) { }

  async create(createTemplateDto: CreateTemplateDto, userId: number): Promise<AssessmentTemplate> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create the template
      const template = this.templateRepository.create({
        name: createTemplateDto.name,
        description: createTemplateDto.description,
        createdById: userId,
        isGlobal: createTemplateDto.isGlobal || false,
        parentTemplateId: createTemplateDto.parentTemplateId,
      });

      const savedTemplate = await queryRunner.manager.save(template);

      // Create associated assessment areas
      if (createTemplateDto.areas && createTemplateDto.areas.length > 0) {
        for (const areaDto of createTemplateDto.areas) {
          const area = this.areaRepository.create({
            templateId: savedTemplate.id,
            name: areaDto.name,
            description: areaDto.description,
            weight: areaDto.weight,
            maxScore: areaDto.maxScore,
            orderIndex: areaDto.orderIndex,
          });

          const savedArea = await queryRunner.manager.save(area);

          // Create associated scoring rules if any
          if (areaDto.scoringRules && areaDto.scoringRules.length > 0) {
            for (const ruleDto of areaDto.scoringRules) {
              const rule = this.ruleRepository.create({
                areaId: savedArea.id,
                ruleType: ruleDto.ruleType,
                conditionField: ruleDto.conditionField,
                conditionOperator: ruleDto.conditionOperator,
                conditionValue: ruleDto.conditionValue,
                scoreAdjustment: ruleDto.scoreAdjustment,
              });

              await queryRunner.manager.save(rule);
            }
          }
        }
      }

      await queryRunner.commitTransaction();

      return this.findOne(savedTemplate.id);

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(includeInactive = false, userId: number, userRole: string): Promise<AssessmentTemplate[]> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view templates');
    }
    const query = this.templateRepository.createQueryBuilder('template')
      .leftJoinAndSelect('template.areas', 'area')
      .leftJoinAndSelect('area.scoringRules', 'rule');

    if (!includeInactive) {
      query.where('template.isActive = :isActive', { isActive: true });
    }

    return query.getMany();
  }

  async findOne(id: number, userId: number = 0, userRole: string = 'SYSTEM'): Promise<AssessmentTemplate> {
    // Note: This method is called internally by other services which already validate permissions
    const template = await this.templateRepository.findOne({
      where: { id },
      relations: ['areas', 'areas.scoringRules'],
    });

    if (!template) {
      throw new NotFoundException(`Template with ID ${id} not found`);
    }

    return template;
  }

  async update(id: number, updateTemplateDto: UpdateTemplateDto, userId: number): Promise<AssessmentTemplate> {
    const template = await this.findOne(id);

    // Update basic template fields
    if (updateTemplateDto.name) template.name = updateTemplateDto.name;
    if (updateTemplateDto.description !== undefined) template.description = updateTemplateDto.description;
    if (updateTemplateDto.isActive !== undefined) template.isActive = updateTemplateDto.isActive;
    if (updateTemplateDto.isGlobal !== undefined) template.isGlobal = updateTemplateDto.isGlobal;

    // Increment version if this is a major update
    if (updateTemplateDto.incrementVersion) {
      template.version += 1;
    }

    return this.templateRepository.save(template);
  }

  async remove(id: number, userId: number, userRole: string): Promise<void> {
    // Validate user permissions - only HR_ADMIN can remove templates
    if (userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR administrators can remove templates');
    }

    const template = await this.findOne(id);

    // Soft delete by marking as inactive
    template.isActive = false;
    await this.templateRepository.save(template);
  }

  async clone(id: number, userId: number, customName?: string): Promise<AssessmentTemplate> {
    const sourceTemplate = await this.findOne(id);

    // Create a clone DTO
    const cloneDto: CreateTemplateDto = {
      name: customName || `${sourceTemplate.name} (Copy)`,
      description: sourceTemplate.description,
      isGlobal: false, // Default to non-global for clones
      parentTemplateId: sourceTemplate.id, // Reference the original
      areas: sourceTemplate.areas.map(area => ({
        name: area.name,
        description: area.description,
        weight: area.weight,
        maxScore: area.maxScore,
        orderIndex: area.orderIndex,
        scoringRules: area.scoringRules.map(rule => ({
          ruleType: rule.ruleType,
          conditionField: rule.conditionField,
          conditionOperator: rule.conditionOperator,
          conditionValue: rule.conditionValue,
          scoreAdjustment: rule.scoreAdjustment,
        })),
      })),
    };

    // Create the cloned template
    return this.create(cloneDto, userId);
  }

  async createNewVersion(templateId: number, userId: number, userRole: UserRole): Promise<AssessmentTemplate> {
    const sourceTemplate = await this.findOne(templateId);

    // Check permissions - only HR_ADMIN can create new versions of global templates
    if (sourceTemplate.isGlobal && userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR administrators can create new versions of global templates');
    }

    // Create new version with incremented version number
    const newVersionDto: CreateTemplateDto = {
      name: sourceTemplate.name,
      description: sourceTemplate.description,
      isGlobal: sourceTemplate.isGlobal,
      parentTemplateId: sourceTemplate.parentTemplateId || sourceTemplate.id,
      areas: sourceTemplate.areas.map(area => ({
        name: area.name,
        description: area.description,
        weight: area.weight,
        maxScore: area.maxScore,
        orderIndex: area.orderIndex,
        scoringRules: area.scoringRules.map(rule => ({
          ruleType: rule.ruleType,
          conditionField: rule.conditionField,
          conditionOperator: rule.conditionOperator,
          conditionValue: rule.conditionValue,
          scoreAdjustment: rule.scoreAdjustment,
        })),
      })),
    };

    const newTemplate = await this.create(newVersionDto, userId);

    // Set the version number to be one higher than the source
    newTemplate.version = sourceTemplate.version + 1;
    await this.templateRepository.save(newTemplate);

    // Optionally deactivate the old version
    sourceTemplate.isActive = false;
    await this.templateRepository.save(sourceTemplate);

    return newTemplate;
  }

  async getTemplateVersions(templateId: number, userId: number, userRole: string): Promise<AssessmentTemplate[]> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view template versions');
    }

    const template = await this.findOne(templateId);
    const rootTemplateId = template.parentTemplateId || template.id;

    return this.templateRepository.find({
      where: [
        { id: rootTemplateId },
        { parentTemplateId: rootTemplateId }
      ],
      relations: ['areas', 'areas.scoringRules'],
      order: { version: 'DESC' }
    });
  }

  async findByTeam(teamId: number, userId: number, userRole: string): Promise<AssessmentTemplate[]> {
    // Validate user permissions
    if (![UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view team templates');
    }
    // This would require a team_templates junction table
    // For now, return global templates and templates created by team managers
    return this.templateRepository.createQueryBuilder('template')
      .leftJoinAndSelect('template.areas', 'area')
      .leftJoinAndSelect('area.scoringRules', 'rule')
      .where('template.isGlobal = :isGlobal', { isGlobal: true })
      .orWhere('template.isActive = :isActive', { isActive: true })
      .getMany();
  }

  async assignToTeams(assignmentDto: any, userId: number, userRole: string): Promise<any> {
    // Implementation for assigning templates to teams
    // This would involve creating team-template relationships
    throw new Error('Method not implemented');
  }

  async customizeForTeam(customizeDto: any, userId: number, userRole: string): Promise<any> {
    // Implementation for customizing templates for specific teams
    throw new Error('Method not implemented');
  }

  async activate(id: number, userId: number, userRole: string): Promise<AssessmentTemplate> {
    const template = await this.findOne(id);
    template.isActive = true;
    return this.templateRepository.save(template);
  }

  async deactivate(id: number, userId: number, userRole: string): Promise<AssessmentTemplate> {
    const template = await this.findOne(id);
    template.isActive = false;
    return this.templateRepository.save(template);
  }

  async getUsageStats(id: number, userId: number, userRole: string): Promise<any> {
    // Validate user permissions - only HR_ADMIN and MANAGER can view usage stats
    if (![UserRole.HR_ADMIN, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('Insufficient permissions to view template usage statistics');
    }
    // Implementation for getting template usage statistics
    return {
      templateId: id,
      totalAssessments: 0,
      activeAssessments: 0,
      completedAssessments: 0
    };
  }

  async validateTemplate(id: number): Promise<any> {
    const template = await this.findOne(id);
    // Implementation for validating template structure
    return {
      isValid: true,
      errors: [],
      warnings: []
    };
  }
}
