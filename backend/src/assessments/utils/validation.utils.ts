/**
 * 🔐 NIS2-COMPLIANT: Shared Assessment Validation Utilities
 * Centralized validation functions to eliminate code duplication
 * and ensure consistent validation across assessment services.
 */

import { AssessmentArea } from '../entities/assessment-area.entity';

export interface AssessmentInput {
  areaId: number;
  baseScore: number;
  additionalData?: Record<string, any>;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Validate assessment inputs against template areas
 */
export function validateAssessmentInputs(
  areas: AssessmentArea[],
  inputs: AssessmentInput[]
): ValidationResult {
  const errors: string[] = [];

  for (const area of areas) {
    const input = inputs.find(i => i.areaId === area.id);
    if (!input) {
      errors.push(`Missing input for area: ${area.name}`);
      continue;
    }

    if (input.baseScore < 0 || input.baseScore > area.maxScore) {
      errors.push(`Score for ${area.name} must be between 0 and ${area.maxScore}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate template creation data
 */
export function validateTemplateData(templateData: any): ValidationResult {
  const errors: string[] = [];

  if (!templateData.name || templateData.name.trim().length === 0) {
    errors.push('Template name is required');
  }

  if (templateData.name && templateData.name.length > 255) {
    errors.push('Template name must be less than 255 characters');
  }

  if (!templateData.description || templateData.description.trim().length === 0) {
    errors.push('Template description is required');
  }

  if (templateData.areas && Array.isArray(templateData.areas)) {
    templateData.areas.forEach((area: any, index: number) => {
      if (!area.name || area.name.trim().length === 0) {
        errors.push(`Area ${index + 1}: Name is required`);
      }

      if (typeof area.weight !== 'number' || area.weight <= 0 || area.weight > 1) {
        errors.push(`Area ${index + 1}: Weight must be a number between 0 and 1`);
      }

      if (typeof area.maxScore !== 'number' || area.maxScore <= 0) {
        errors.push(`Area ${index + 1}: Max score must be a positive number`);
      }
    });

    // Validate total weight
    const totalWeight = templateData.areas.reduce((sum: number, area: any) => sum + (area.weight || 0), 0);
    if (Math.abs(totalWeight - 1) > 0.01) { // Allow small floating point differences
      errors.push('Total weight of all areas must equal 1.0');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate user permissions for assessment operations
 */
export function validateAssessmentPermissions(
  userRole: string,
  userId: number,
  assessment?: any,
  operation: 'create' | 'read' | 'update' | 'delete' = 'read'
): ValidationResult {
  const errors: string[] = [];

  // HR_ADMIN can perform all operations
  if (userRole === 'HR_ADMIN') {
    return { isValid: true, errors: [] };
  }

  switch (operation) {
    case 'create':
      if (userRole !== 'MANAGER') {
        errors.push('Only managers and HR admins can create assessments');
      }
      break;

    case 'update':
    case 'delete':
      if (!assessment) {
        errors.push('Assessment data is required for this operation');
        break;
      }

      if (userRole === 'MANAGER' && assessment.managerId !== userId) {
        errors.push('Managers can only modify assessments they created');
      } else if (userRole === 'EMPLOYEE' && assessment.employeeId !== userId) {
        errors.push('Employees can only view their own assessments');
      } else if (!['MANAGER', 'EMPLOYEE'].includes(userRole)) {
        errors.push('Insufficient permissions for this operation');
      }
      break;

    case 'read':
      if (!assessment) {
        break; // Reading list is generally allowed
      }

      if (userRole === 'EMPLOYEE' && assessment.employeeId !== userId) {
        errors.push('Employees can only view their own assessments');
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate assessment status transition
 */
export function validateStatusTransition(
  currentStatus: string,
  targetStatus: string,
  userRole: string
): ValidationResult {
  const errors: string[] = [];

  const validTransitions: Record<string, string[]> = {
    'DRAFT': ['IN_PROGRESS', 'COMPLETED'],
    'IN_PROGRESS': ['COMPLETED', 'DRAFT'],
    'COMPLETED': ['APPROVED', 'REJECTED'],
    'APPROVED': [], // Final state
    'REJECTED': ['IN_PROGRESS'] // Can be reopened for revision
  };

  if (!validTransitions[currentStatus]) {
    errors.push(`Invalid current status: ${currentStatus}`);
    return { isValid: false, errors };
  }

  if (!validTransitions[currentStatus].includes(targetStatus)) {
    errors.push(`Cannot transition from ${currentStatus} to ${targetStatus}`);
  }

  // Role-based transition validation
  if (targetStatus === 'APPROVED' || targetStatus === 'REJECTED') {
    if (userRole !== 'HR_ADMIN' && userRole !== 'MANAGER') {
      errors.push('Only managers and HR admins can approve or reject assessments');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate scoring rule data
 */
export function validateScoringRule(rule: any): ValidationResult {
  const errors: string[] = [];

  if (!rule.ruleType || !['addition', 'multiplication', 'conditional'].includes(rule.ruleType)) {
    errors.push('Rule type must be one of: addition, multiplication, conditional');
  }

  if (typeof rule.scoreAdjustment !== 'number') {
    errors.push('Score adjustment must be a number');
  }

  if (rule.ruleType === 'conditional') {
    if (!rule.conditionField) {
      errors.push('Conditional rules must specify a condition field');
    }
    if (!rule.conditionOperator || !['equals', 'greater_than', 'less_than', 'contains'].includes(rule.conditionOperator)) {
      errors.push('Conditional rules must specify a valid operator');
    }
    if (rule.conditionValue === undefined || rule.conditionValue === null) {
      errors.push('Conditional rules must specify a condition value');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate date ranges for reports and queries
 */
export function validateDateRange(startDate?: string, endDate?: string): ValidationResult {
  const errors: string[] = [];

  if (startDate && isNaN(Date.parse(startDate))) {
    errors.push('Start date must be a valid date');
  }

  if (endDate && isNaN(Date.parse(endDate))) {
    errors.push('End date must be a valid date');
  }

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start > end) {
      errors.push('Start date must be before end date');
    }

    // Prevent queries for excessively long periods (more than 2 years)
    const maxPeriodMs = 2 * 365 * 24 * 60 * 60 * 1000; // 2 years in milliseconds
    if (end.getTime() - start.getTime() > maxPeriodMs) {
      errors.push('Date range cannot exceed 2 years');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitize and validate text input to prevent XSS
 */
export function validateAndSanitizeText(
  text: string,
  fieldName: string,
  maxLength: number = 1000
): ValidationResult {
  const errors: string[] = [];

  if (!text || typeof text !== 'string') {
    errors.push(`${fieldName} must be a valid string`);
    return { isValid: false, errors };
  }

  if (text.length > maxLength) {
    errors.push(`${fieldName} must be less than ${maxLength} characters`);
  }

  // Check for potentially dangerous content
  const dangerousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(text)) {
      errors.push(`${fieldName} contains potentially dangerous content`);
      break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
