import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { AuditLoggingService } from './audit-logging.service';

// 🔐 NIS2-COMPLIANT: Audit interceptor
// Automatically logs all API calls for comprehensive audit trails

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuditInterceptor.name);

  constructor(private readonly auditLoggingService: AuditLoggingService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    // Extract request information
    const {
      method,
      url,
      headers,
      body,
      params,
      query,
      user,
      ip,
      sessionID
    } = request;

    const userAgent = headers['user-agent'] || '';
    const userId = user?.id || null;
    const correlationId = headers['x-correlation-id'] || this.generateCorrelationId();

    // Determine resource and action from the request
    const { resource, action } = this.extractResourceAndAction(method, url);

    // Determine if this is a sensitive operation
    const isSensitiveOperation = this.isSensitiveOperation(method, url);
    const severity = isSensitiveOperation ? 'medium' : 'low';
    const riskLevel = this.assessRiskLevel(method, url, user?.role);

    return next.handle().pipe(
      tap(async (data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        try {
          // Log successful operation
          await this.auditLoggingService.logEvent({
            userId: userId || 0,
            action,
            resource,
            resourceId: this.extractResourceId(params, query),
            details: {
              method,
              url,
              status_code: response.statusCode,
              duration_ms: duration,
              request_size: this.getRequestSize(body),
              response_size: this.getResponseSize(data),
              query_params: query,
              path_params: params,
              correlation_id: correlationId,
              timestamp: new Date().toISOString()
            },
            ipAddress: ip,
            userAgent,
            sessionId: sessionID,
            severity,
            category: this.getCategory(method, url),
            outcome: 'success',
            riskLevel
          });

          // Log data access if this is a read operation
          if (method === 'GET' && this.isDashboardDataAccess(url)) {
            await this.auditLoggingService.logDashboardAccess(
              userId || 0,
              this.extractDashboardType(url),
              this.extractDataAccessed(url, data),
              {
                correlation_id: correlationId,
                duration_ms: duration
              }
            );
          }

          // Log data modification if this is a write operation
          if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
            await this.auditLoggingService.logDataModification(
              userId || 0,
              resource,
              this.extractResourceId(params, query) || 'unknown',
              this.mapMethodToAction(method),
              method === 'PUT' || method === 'PATCH' ? body : undefined,
              data,
              {
                correlation_id: correlationId,
                duration_ms: duration
              }
            );
          }

        } catch (error) {
          this.logger.error('Failed to log audit event', error);
        }
      }),
      catchError(async (error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        try {
          // Log failed operation
          await this.auditLoggingService.logEvent({
            userId: userId || 0,
            action,
            resource,
            resourceId: this.extractResourceId(params, query),
            details: {
              method,
              url,
              error_message: error.message,
              error_stack: error.stack,
              status_code: error.status || 500,
              duration_ms: duration,
              correlation_id: correlationId,
              timestamp: new Date().toISOString()
            },
            ipAddress: ip,
            userAgent,
            sessionId: sessionID,
            severity: 'high',
            category: this.getCategory(method, url),
            outcome: 'error',
            riskLevel: 'medium'
          });

          // Log security event for unauthorized access
          if (error.status === 401 || error.status === 403) {
            await this.auditLoggingService.logSecurityEvent(
              userId,
              {
                type: 'unauthorized_access',
                description: `Unauthorized ${method} request to ${url}`,
                metadata: {
                  status_code: error.status,
                  error_message: error.message,
                  correlation_id: correlationId
                }
              },
              ip,
              {
                user_agent: userAgent,
                session_id: sessionID
              }
            );
          }

        } catch (auditError) {
          this.logger.error('Failed to log audit event for error', auditError);
        }

        throw error;
      })
    );
  }

  private extractResourceAndAction(method: string, url: string): { resource: string; action: string } {
    const pathSegments = url.split('/').filter(segment => segment && !segment.match(/^\d+$/));
    const resource = pathSegments[1] || 'unknown';
    const action = `${method.toLowerCase()}_${resource}`;

    return { resource, action };
  }

  private extractResourceId(params: any, query: any): string | undefined {
    return params?.id || params?.userId || query?.id || undefined;
  }

  private isSensitiveOperation(method: string, url: string): boolean {
    const sensitivePatterns = [
      /\/auth\//,
      /\/users\//,
      /\/admin\//,
      /\/config\//,
      /\/enterprise-config\//,
      /\/audit\//
    ];

    return sensitivePatterns.some(pattern => pattern.test(url)) || 
           ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method);
  }

  private assessRiskLevel(method: string, url: string, userRole?: string): 'low' | 'medium' | 'high' | 'critical' {
    // Critical operations
    if (url.includes('/admin/') || url.includes('/config/')) {
      return 'critical';
    }

    // High risk operations
    if (method === 'DELETE' || url.includes('/auth/')) {
      return 'high';
    }

    // Medium risk operations
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      return 'medium';
    }

    // Low risk operations
    return 'low';
  }

  private getCategory(method: string, url: string): 'authentication' | 'authorization' | 'data_access' | 'data_modification' | 'system_config' | 'security_event' {
    if (url.includes('/auth/')) {
      return 'authentication';
    }

    if (url.includes('/config/') || url.includes('/enterprise-config/')) {
      return 'system_config';
    }

    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
      return 'data_modification';
    }

    return 'data_access';
  }

  private isDashboardDataAccess(url: string): boolean {
    return url.includes('/dashboard/') || 
           url.includes('/analytics/') || 
           url.includes('/reports/');
  }

  private extractDashboardType(url: string): string {
    if (url.includes('/dashboard/')) {
      return 'dashboard';
    }
    if (url.includes('/analytics/')) {
      return 'analytics';
    }
    if (url.includes('/reports/')) {
      return 'reports';
    }
    return 'unknown';
  }

  private extractDataAccessed(url: string, data: any): string[] {
    const accessed = [];
    
    if (data?.data) {
      if (Array.isArray(data.data)) {
        accessed.push(`${data.data.length} records`);
      } else if (typeof data.data === 'object') {
        accessed.push(...Object.keys(data.data));
      }
    }

    return accessed;
  }

  private mapMethodToAction(method: string): 'create' | 'update' | 'delete' {
    switch (method) {
      case 'POST':
        return 'create';
      case 'PUT':
      case 'PATCH':
        return 'update';
      case 'DELETE':
        return 'delete';
      default:
        return 'update';
    }
  }

  private getRequestSize(body: any): number {
    if (!body) return 0;
    return JSON.stringify(body).length;
  }

  private getResponseSize(data: any): number {
    if (!data) return 0;
    return JSON.stringify(data).length;
  }

  private generateCorrelationId(): string {
    return `audit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
