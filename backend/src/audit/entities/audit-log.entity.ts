import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm';

// 🔐 NIS2-COMPLIANT: Audit log entity
// Stores comprehensive audit trails for all dashboard data access and modifications

@Entity('audit_logs')
@Index(['userId', 'timestamp'])
@Index(['resource', 'timestamp'])
@Index(['category', 'timestamp'])
@Index(['severity', 'timestamp'])
@Index(['riskLevel', 'timestamp'])
export class AuditLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', nullable: false })
  @Index()
  userId: number;

  @Column({ type: 'varchar', length: 100, nullable: false })
  @Index()
  action: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  @Index()
  resource: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  resourceId: string;

  @Column({ type: 'json', nullable: true })
  details: Record<string, any>;

  @Column({ type: 'varchar', length: 45, nullable: true })
  ipAddress: string;

  @Column({ type: 'text', nullable: true })
  userAgent: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  sessionId: string;

  @Column({
    type: 'enum',
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'low'
  })
  @Index()
  severity: 'low' | 'medium' | 'high' | 'critical';

  @Column({
    type: 'enum',
    enum: ['authentication', 'authorization', 'data_access', 'data_modification', 'system_config', 'security_event', 'compliance'],
    nullable: false
  })
  @Index()
  category: 'authentication' | 'authorization' | 'data_access' | 'data_modification' | 'system_config' | 'security_event' | 'compliance';

  @Column({
    type: 'enum',
    enum: ['success', 'failure', 'error'],
    default: 'success'
  })
  @Index()
  outcome: 'success' | 'failure' | 'error';

  @Column({
    type: 'enum',
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'low'
  })
  @Index()
  riskLevel: 'low' | 'medium' | 'high' | 'critical';

  @CreateDateColumn()
  @Index()
  timestamp: Date;

  @Column({ type: 'json', nullable: true })
  compliance: {
    nis2: boolean;
    gdpr: boolean;
    retention_period: number;
  };

  @Column({ type: 'varchar', length: 255, nullable: true })
  correlationId: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'boolean', default: false })
  archived: boolean;

  @Column({ type: 'timestamp', nullable: true })
  archivedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;
}
