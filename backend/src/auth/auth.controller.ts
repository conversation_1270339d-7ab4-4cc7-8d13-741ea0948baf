import { Controller, Request, Post, UseGuards, Body, Get, Ip, BadRequestException, HttpCode, HttpStatus } from '@nestjs/common';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { MfaLoginDto } from './dto/mfa-login.dto';
import { ValidatePasswordDto } from './dto/validate-password.dto';
import { SecurityAuditService } from './services/security-audit.service';
import { PasswordPolicyService } from './services/password-policy.service';

@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private securityAuditService: SecurityAuditService,
    private passwordPolicyService: PasswordPolicyService,
  ) { }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  async login(@Body() loginDto: LoginDto, @Request() req, @Ip() clientIp: string) {
    // LocalAuthGuard validates the user credentials before this function is called
    return this.authService.login(req.user, clientIp);
  }



  @Post('refresh')
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto, @Ip() clientIp: string) {
    return this.authService.refreshToken(refreshTokenDto.refreshToken, clientIp);
  }

  @Post('login-mfa')
  async loginWithMfa(@Body() mfaLoginDto: MfaLoginDto, @Ip() clientIp: string) {
    return this.authService.loginWithMfa(
      mfaLoginDto.email,
      mfaLoginDto.password,
      mfaLoginDto.mfaToken,
      clientIp
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  getProfile(@Request() req) {
    return req.user;
  }

  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  async changePassword(@Body() changePasswordDto: ChangePasswordDto, @Request() req) {
    const { currentPassword, newPassword, confirmPassword } = changePasswordDto;

    if (newPassword !== confirmPassword) {
      throw new BadRequestException('New password and confirmation do not match');
    }

    await this.authService.changePassword(req.user.userId, currentPassword, newPassword);
    return { message: 'Password changed successfully' };
  }

  @UseGuards(JwtAuthGuard)
  @Post('validate-password')
  async validatePassword(@Body() validatePasswordDto: ValidatePasswordDto, @Request() req) {
    const validation = await this.passwordPolicyService.validatePassword(
      validatePasswordDto.password,
      req.user.userId
    );

    const strength = this.passwordPolicyService.getPasswordStrengthDescription(validation.score);

    return {
      valid: validation.valid,
      score: validation.score,
      errors: validation.errors,
      strength: strength
    };
  }

  @Get('password-policy')
  getPasswordPolicy() {
    return {
      minLength: 12,
      requirements: [
        'At least 12 characters long',
        'At least one uppercase letter (A-Z)',
        'At least one lowercase letter (a-z)',
        'At least one number (0-9)',
        'At least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)',
        'No common passwords or dictionary words',
        'No repeating patterns or sequential characters',
        'Cannot reuse any of your last 12 passwords'
      ],
      description: 'NIS2-compliant password policy for enhanced security'
    };
  }

  @Get('health')
  healthCheck() {
    return { status: 'ok', timestamp: new Date().toISOString() };
  }

  /**
   * 🔐 Enhanced logout endpoint
   * POST /auth/logout
   */
  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async logout(@Request() req: any) {
    try {
      const userId = req.user.userId;
      const sessionId = req.user.sessionId;
      const clientIp = req.ip || req.connection.remoteAddress;

      await this.authService.logout(userId, sessionId, clientIp);

      return {
        success: true,
        message: 'Logged out successfully'
      };
    } catch (error) {
      console.error('🔐 [AUTH-CONTROLLER] Logout failed:', error);
      throw new BadRequestException('Logout failed');
    }
  }

  /**
   * 🔐 NIS2-Compliant Security Audit Endpoint
   * Receives security events from frontend for centralized logging
   */
  @Post('security-audit')
  async logSecurityEvent(@Body() auditData: any, @Request() req, @Ip() clientIp: string) {
    try {
      // Extract user info if authenticated
      const userId = req.user?.userId || null;
      const email = req.user?.email || null;

      // Log the security event
      await this.securityAuditService.logSecurityEvent({
        eventType: auditData.event,
        userId,
        email,
        ipAddress: clientIp,
        userAgent: req.headers['user-agent'],
        sessionId: auditData.sessionFingerprint,
        details: auditData.details,
        severity: this.determineSeverity(auditData.event)
      });

      return { status: 'logged' };
    } catch (error) {
      // Silent fail for security audit - don't expose system details
      return { status: 'error' };
    }
  }

  /**
   * 🔐 Determine security event severity
   */
  private determineSeverity(eventType: string): any {
    const highSeverityEvents = [
      'LOGIN_FAILED',
      'TOKEN_EXPIRED',
      'SESSION_FINGERPRINT_MISMATCH',
      'TOKEN_RETRIEVAL_RATE_LIMITED'
    ];

    const criticalSeverityEvents = [
      'INVALID_TOKEN_FORMAT',
      'TOKEN_STORAGE_FAILED',
      'SESSION_HIJACK_ATTEMPT'
    ];

    if (criticalSeverityEvents.includes(eventType)) {
      return 'CRITICAL';
    } else if (highSeverityEvents.includes(eventType)) {
      return 'HIGH';
    } else {
      return 'LOW';
    }
  }
}
