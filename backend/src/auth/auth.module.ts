import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersModule } from '../users/users.module';
import { User } from '../users/entities/user.entity';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { AuthController } from './auth.controller';
import { MfaController } from './controllers/mfa.controller';
import { SessionController } from './controllers/session.controller';
import { SecurityAuditService } from './services/security-audit.service';
import { MfaService } from './services/mfa.service';
import { PasswordPolicyService } from './services/password-policy.service';
import { TokenRotationService } from './services/token-rotation.service';
import { SessionManagementService } from './services/session-management.service';
import { SessionAnalyticsService } from './services/session-analytics.service';
import { DeviceFingerprintingService } from './services/device-fingerprinting.service';
import { AuthErrorHandlerService } from './services/auth-error-handler.service';
import { SecurityModule } from '../security/security.module';
import { SharedModule } from '../shared/shared.module';

@Module({
  imports: [
    UsersModule,
    PassportModule,
    SecurityModule,
    SharedModule,
    TypeOrmModule.forFeature([User]),
    JwtModule.registerAsync({
      useFactory: () => {
        // 🔐 NIS2-COMPLIANT: Enforce strong JWT secret requirements
        const secret = process.env.JWT_SECRET;

        if (!secret) {
          throw new Error('🔐 [SECURITY] JWT_SECRET environment variable is required for production');
        }

        if (secret.length < 32) {
          throw new Error('🔐 [SECURITY] JWT_SECRET must be at least 32 characters long for NIS2 compliance');
        }

        if (secret === 'devSecretKey' || secret.includes('dev') || secret.includes('test')) {
          console.error('🔐 [SECURITY] WARNING: Using development JWT secret in production environment');
          if (process.env.NODE_ENV === 'production') {
            throw new Error('🔐 [SECURITY] Development JWT secret detected in production environment');
          }
        }

        // 🔐 Only log configuration details in development
        if (process.env.NODE_ENV !== 'production') {
          console.log('🔐 [JWT-CONFIG] JWT Module configured with secure secret:', {
            hasEnvVar: !!secret,
            isSecure: secret.length >= 32 && !secret.includes('dev'),
            environment: process.env.NODE_ENV
          });
        }

        return {
          secret: secret,
          signOptions: {
            expiresIn: '1h',
            issuer: 'ehrx-system',
            audience: 'ehrx-users',
            algorithm: 'HS256'
          },
        };
      },
    }),
  ],
  providers: [
    AuthService,
    LocalStrategy,
    JwtStrategy,
    SecurityAuditService,
    MfaService,
    PasswordPolicyService,
    TokenRotationService,
    SessionManagementService,
    SessionAnalyticsService,
    DeviceFingerprintingService,
    AuthErrorHandlerService,
  ],
  controllers: [AuthController, MfaController, SessionController],
  exports: [AuthService, SecurityAuditService, MfaService, PasswordPolicyService, TokenRotationService, SessionManagementService, SessionAnalyticsService, DeviceFingerprintingService, AuthErrorHandlerService],
})
export class AuthModule { }
