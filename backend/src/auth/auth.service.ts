import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { UsersService } from '../users/users.service';
import { User, AccountStatus } from '../users/entities/user.entity';
import { SecurityAuditService, SecurityEventType, SecuritySeverity } from './services/security-audit.service';
import { RateLimitingService } from '../security/services/rate-limiting.service';
import { MfaService } from './services/mfa.service';
import { PasswordPolicyService } from './services/password-policy.service';
import { TokenRotationService } from './services/token-rotation.service';
import { SessionManagementService } from './services/session-management.service';
import { AuthErrorHandlerService } from './services/auth-error-handler.service';
import { TokenUtilityService } from './services/token-utility.service';
import { PasswordUtilityService } from './services/password-utility.service';

@Injectable()
export class AuthService {
  private readonly MAX_LOGIN_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds
  private readonly REFRESH_TOKEN_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private securityAuditService: SecurityAuditService,
    private rateLimitingService: RateLimitingService,
    private mfaService: MfaService,
    private passwordPolicyService: PasswordPolicyService,
    private tokenRotationService: TokenRotationService,
    private sessionManagementService: SessionManagementService,
    private authErrorHandler: AuthErrorHandlerService,
    private tokenUtilityService: TokenUtilityService,
    private passwordUtilityService: PasswordUtilityService,
  ) { }

  async validateUser(email: string, pass: string, clientIp?: string): Promise<any> {
    // 🔐 TIMING ATTACK PROTECTION: Always perform password hash operation
    const startTime = Date.now();
    let user: any = null;
    let isPasswordValid = false;

    // 🔐 NIS2-COMPLIANT: Check rate limiting before processing
    if (clientIp && this.rateLimitingService.isIPBlocked(clientIp)) {
      const error = this.authErrorHandler.createAuthError(
        'IP_BLOCKED',
        'IP address is temporarily blocked due to too many failed attempts',
        'Too many failed attempts. IP temporarily blocked.'
      );
      await this.authErrorHandler.handleAuthenticationFailure(error, {
        email,
        clientIp
      });
    }

    try {
      user = await this.usersService.findByEmail(email);
    } catch (error) {
      // Continue with timing attack protection even if user lookup fails
    }

    // 🔐 TIMING ATTACK PROTECTION: Always perform bcrypt operation
    if (user && user.password) {
      isPasswordValid = await bcrypt.compare(pass, user.password);
    } else {
      // Perform dummy bcrypt operation to maintain consistent timing
      await bcrypt.compare(pass, '$2b$10$dummyHashToPreventTimingAttacks.abcdefghijklmnopqrstuvwxyz');
    }

    if (!user) {
      // 🔐 Log failed attempt for non-existent user
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        email,
        ipAddress: clientIp,
        details: { reason: 'USER_NOT_FOUND', email },
        severity: SecuritySeverity.MEDIUM
      });
      if (clientIp) {
        this.rateLimitingService.recordFailedAttempt(clientIp, 'unknown');
      }

      // 🔐 TIMING ATTACK PROTECTION: Ensure consistent response time
      await this.ensureMinimumDelay(startTime, 100); // Minimum 100ms delay
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if account is locked
    if (user.accountStatus === AccountStatus.LOCKED ||
      (user.accountLockedUntil && new Date() < user.accountLockedUntil)) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        userId: user.id,
        email,
        ipAddress: clientIp,
        details: { reason: 'ACCOUNT_LOCKED', email },
        severity: SecuritySeverity.HIGH
      });
      // 🔐 TIMING ATTACK PROTECTION: Ensure consistent response time
      await this.ensureMinimumDelay(startTime, 100);
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if account is active
    if (user.accountStatus !== AccountStatus.ACTIVE || !user.isActive) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        userId: user.id,
        email,
        ipAddress: clientIp,
        details: { reason: 'ACCOUNT_INACTIVE', email },
        severity: SecuritySeverity.MEDIUM
      });
      // 🔐 TIMING ATTACK PROTECTION: Ensure consistent response time
      await this.ensureMinimumDelay(startTime, 100);
      throw new UnauthorizedException('Invalid credentials');
    }

    // Password validation was already performed above for timing attack protection
    if (!isPasswordValid) {
      // 🔐 Log failed password attempt
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        userId: user.id,
        email,
        ipAddress: clientIp,
        details: { reason: 'INVALID_PASSWORD', email },
        severity: SecuritySeverity.MEDIUM
      });
      if (clientIp) {
        this.rateLimitingService.recordFailedAttempt(clientIp, user.id.toString());
      }
      await this.handleFailedLogin(user);
      // 🔐 TIMING ATTACK PROTECTION: Ensure consistent response time
      await this.ensureMinimumDelay(startTime, 100);
      throw new UnauthorizedException('Invalid credentials');
    }

    // 🔐 Successful login - log and reset counters
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.LOGIN_SUCCESS,
      userId: user.id,
      email,
      ipAddress: clientIp,
      details: { email },
      severity: SecuritySeverity.LOW
    });
    if (clientIp) {
      this.rateLimitingService.recordSuccessfulAttempt(clientIp, user.id.toString());
    }
    await this.handleSuccessfulLogin(user, clientIp);

    const { password, twoFactorSecret, sessionToken, ...result } = user;
    return result;
  }

  async loginWithMfa(email: string, password: string, mfaToken?: string, clientIp?: string) {
    // First validate user credentials
    const user = await this.validateUser(email, password, clientIp);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if MFA is enabled for this user
    if (user.twoFactorEnabled) {
      if (!mfaToken) {
        // Return indication that MFA is required
        return {
          requiresMfa: true,
          message: 'MFA token required',
          user: {
            id: user.id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
          }
        };
      }

      // Verify MFA token
      const isMfaValid = await this.mfaService.verifyMfaToken(user.id, mfaToken);
      if (!isMfaValid) {
        await this.securityAuditService.logSecurityEvent({
          eventType: SecurityEventType.MFA_FAILED,
          userId: user.id,
          email: user.email,
          ipAddress: clientIp,
          details: { reason: 'INVALID_MFA_TOKEN' },
          severity: SecuritySeverity.HIGH
        });
        throw new UnauthorizedException('Invalid MFA token');
      }
    }

    // Proceed with normal login
    return this.login(user, clientIp);
  }

  async login(user: any, clientIp?: string, userAgent?: string, deviceInfo?: any) {
    const sessionId = this.tokenUtilityService.generateSessionId();
    const payload = {
      email: user.email,
      sub: user.id,
      role: user.role,
      sessionId
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.tokenUtilityService.generateRefreshToken();

    // Create comprehensive session with advanced tracking
    const sessionResult = await this.sessionManagementService.createSession(
      user.id,
      sessionId,
      clientIp || 'unknown',
      userAgent || 'unknown',
      deviceInfo
    );

    // Manage concurrent sessions
    await this.tokenRotationService.manageConcurrentSessions(user.id, sessionId);

    // Update session information with refresh token
    await this.updateUserSession(user.id, sessionId, refreshToken, clientIp);

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        mustChangePassword: user.mustChangePassword,
        twoFactorEnabled: user.twoFactorEnabled,
        lastLoginAt: user.lastLoginAt,
      },
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: 3600, // 1 hour
      refresh_expires_in: 604800, // 7 days
      session: {
        sessionId,
        riskScore: sessionResult.riskScore,
        warnings: sessionResult.warnings,
        requiresAdditionalVerification: sessionResult.requiresAdditionalVerification
      }
    };
  }



  async refreshToken(refreshToken: string, clientIp?: string): Promise<any> {
    // Find user by refresh token
    const user = await this.userRepository.findOne({
      where: { refreshToken, accountStatus: AccountStatus.ACTIVE }
    });

    if (!user || !user.refreshTokenExpiresAt || new Date() > user.refreshTokenExpiresAt) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        userId: user?.id,
        ipAddress: clientIp,
        details: { reason: 'INVALID_REFRESH_TOKEN' },
        severity: SecuritySeverity.MEDIUM
      });
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    // Generate new tokens
    const sessionId = this.tokenUtilityService.generateSessionId();
    const payload = {
      email: user.email,
      sub: user.id,
      role: user.role,
      sessionId
    };

    const newAccessToken = this.jwtService.sign(payload);
    const newRefreshToken = this.tokenUtilityService.generateRefreshToken();

    // Update session with new tokens
    await this.updateUserSession(user.id, sessionId, newRefreshToken, clientIp);

    // Log successful token refresh
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.LOGIN_SUCCESS,
      userId: user.id,
      email: user.email,
      ipAddress: clientIp,
      details: { action: 'TOKEN_REFRESH' },
      severity: SecuritySeverity.LOW
    });

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        mustChangePassword: user.mustChangePassword,
        twoFactorEnabled: user.twoFactorEnabled,
        lastLoginAt: user.lastLoginAt,
      },
      access_token: newAccessToken,
      refresh_token: newRefreshToken,
      expires_in: 3600, // 1 hour
      refresh_expires_in: 604800, // 7 days
    };
  }

  async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.PASSWORD_CHANGE_FAILED,
        userId,
        email: user.email,
        details: { reason: 'INVALID_CURRENT_PASSWORD' },
        severity: SecuritySeverity.MEDIUM
      });
      throw new BadRequestException('Current password is incorrect');
    }

    // Validate new password against policy
    const passwordValidation = await this.passwordPolicyService.validatePassword(newPassword, userId);
    if (!passwordValidation.valid) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.PASSWORD_CHANGE_FAILED,
        userId,
        email: user.email,
        details: { reason: 'POLICY_VIOLATION', errors: passwordValidation.errors },
        severity: SecuritySeverity.LOW
      });
      throw new BadRequestException(`Password does not meet security requirements: ${passwordValidation.errors.join(', ')}`);
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    // Store password in history for reuse prevention
    await this.passwordPolicyService.storePasswordHistory(userId, hashedNewPassword);

    // Update password and related fields
    await this.userRepository.update(userId, {
      password: hashedNewPassword,
      passwordChangedAt: new Date(),
      mustChangePassword: false,
    });

    // Log successful password change
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.PASSWORD_CHANGED,
      userId,
      email: user.email,
      details: { passwordScore: passwordValidation.score },
      severity: SecuritySeverity.LOW
    });
  }

  private async handleFailedLogin(user: User): Promise<void> {
    const failedAttempts = user.failedLoginAttempts + 1;
    const updateData: Partial<User> = {
      failedLoginAttempts: failedAttempts,
    };

    // Lock account if max attempts reached
    if (failedAttempts >= this.MAX_LOGIN_ATTEMPTS) {
      updateData.accountStatus = AccountStatus.LOCKED;
      updateData.accountLockedUntil = new Date(Date.now() + this.LOCKOUT_DURATION);
    }

    await this.userRepository.update(user.id, updateData);
  }

  private async handleSuccessfulLogin(user: User, clientIp?: string): Promise<void> {
    await this.userRepository.update(user.id, {
      failedLoginAttempts: 0,
      lastLoginAt: new Date(),
      lastLoginIp: clientIp,
      accountLockedUntil: null,
    });
  }

  private async updateUserSession(userId: number, sessionId: string, refreshToken: string, clientIp?: string): Promise<void> {
    const expiresAt = new Date(Date.now() + 3600 * 1000); // 1 hour from now
    const refreshExpiresAt = new Date(Date.now() + this.REFRESH_TOKEN_EXPIRY);

    await this.userRepository.update(userId, {
      sessionToken: sessionId,
      sessionExpiresAt: expiresAt,
      refreshToken: refreshToken,
      refreshTokenExpiresAt: refreshExpiresAt,
      lastLoginAt: new Date(),
      lastLoginIp: clientIp,
    });
  }

  // Token generation methods moved to TokenUtilityService for consistency

  async validateSession(token: string): Promise<User | null> {
    const user = await this.userRepository.findOne({
      where: { sessionToken: token },
    });

    if (!user || !user.sessionExpiresAt || new Date() > user.sessionExpiresAt) {
      return null;
    }

    return user;
  }

  /**
   * 🔐 Enhanced logout with comprehensive session termination
   */
  async logout(userId: number, sessionId?: string, clientIp?: string): Promise<void> {
    try {
      // Terminate specific session or all user sessions
      if (sessionId) {
        await this.sessionManagementService.terminateSession(sessionId, 'User logout');
      } else {
        await this.sessionManagementService.terminateAllUserSessions(userId, 'User logout');
      }

      // Revoke all user tokens
      await this.tokenRotationService.revokeAllUserSessions(userId, 'User logout');

      // Clear database session
      await this.userRepository.update(userId, {
        sessionToken: null,
        sessionExpiresAt: null,
        refreshToken: null,
        refreshTokenExpiresAt: null,
      });

      // Log logout event
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGOUT,
        userId,
        ipAddress: clientIp,
        details: {
          sessionId,
          logoutType: sessionId ? 'single_session' : 'all_sessions'
        },
        severity: SecuritySeverity.LOW
      });

    } catch (error) {
      console.error('🔐 [AUTH-SERVICE] Logout error:', error);
      throw error;
    }
  }

  /**
   * 🔐 TIMING ATTACK PROTECTION: Ensure minimum delay for consistent response times
   */
  private async ensureMinimumDelay(startTime: number, minimumDelayMs: number): Promise<void> {
    const elapsedTime = Date.now() - startTime;
    const remainingDelay = minimumDelayMs - elapsedTime;

    if (remainingDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, remainingDelay));
    }
  }

  // REMOVED: Duplicate password utility methods
  // Now using centralized PasswordUtilityService for:
  // - generateDefaultPassword()
  // - hashPassword()
  // This ensures consistent password handling across the application
}
