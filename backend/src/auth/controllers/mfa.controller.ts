import { Controller, Post, Get, Body, UseGuards, Request, Delete } from '@nestjs/common';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { MfaService } from '../services/mfa.service';
import { EnableMfaDto, VerifyMfaDto, DisableMfaDto, RegenerateBackupCodesDto } from '../dto/mfa.dto';

/**
 * 🔐 NIS2-Compliant Multi-Factor Authentication Controller
 * 
 * Provides endpoints for:
 * - MFA setup and configuration
 * - Token verification
 * - Backup code management
 * - MFA status checking
 */
@Controller('auth/mfa')
@UseGuards(JwtAuthGuard)
export class MfaController {
  constructor(private mfaService: MfaService) {}

  /**
   * 🔐 Generate MFA secret and QR code for setup
   */
  @Post('generate-secret')
  async generateSecret(@Request() req) {
    return this.mfaService.generateMfaSecret(req.user.userId);
  }

  /**
   * 🔐 Enable MFA after verifying setup token
   */
  @Post('enable')
  async enableMfa(@Request() req, @Body() enableMfaDto: EnableMfaDto) {
    return this.mfaService.enableMfa(req.user.userId, enableMfaDto.token);
  }

  /**
   * 🔐 Disable MFA
   */
  @Delete('disable')
  async disableMfa(@Request() req, @Body() disableMfaDto: DisableMfaDto) {
    await this.mfaService.disableMfa(req.user.userId, disableMfaDto.token);
    return { message: 'MFA disabled successfully' };
  }

  /**
   * 🔐 Verify MFA token (for testing purposes)
   */
  @Post('verify')
  async verifyToken(@Request() req, @Body() verifyMfaDto: VerifyMfaDto) {
    const isValid = await this.mfaService.verifyMfaToken(req.user.userId, verifyMfaDto.token);
    return { valid: isValid };
  }

  /**
   * 🔐 Get MFA status for current user
   */
  @Get('status')
  async getStatus(@Request() req) {
    return this.mfaService.getMfaStatus(req.user.userId);
  }

  /**
   * 🔐 Regenerate backup codes
   */
  @Post('regenerate-backup-codes')
  async regenerateBackupCodes(@Request() req, @Body() regenerateDto: RegenerateBackupCodesDto) {
    return this.mfaService.regenerateBackupCodes(req.user.userId, regenerateDto.token);
  }
}
