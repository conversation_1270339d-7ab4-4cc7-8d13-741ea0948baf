import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  Logger,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { TokenRotationService } from '../services/token-rotation.service';
import { SessionManagementService } from '../services/session-management.service';
import { SecurityAuditService, SecurityEventType, SecuritySeverity } from '../services/security-audit.service';
import { SessionInfo, SessionAnalytics, TokenRotationResult } from '../interfaces/session.interfaces';

/**
 * 🔐 NIS2-Compliant Session Management Controller
 * 
 * Provides endpoints for:
 * - Token rotation management
 * - Session monitoring and control
 * - Multi-device session management
 * - Security analytics and reporting
 */
@Controller('auth/sessions')
export class SessionController {
  private readonly logger = new Logger(SessionController.name);

  constructor(
    private readonly tokenRotationService: TokenRotationService,
    private readonly sessionManagementService: SessionManagementService,
    private readonly securityAuditService: SecurityAuditService,
  ) { }

  /**
   * 🔐 Rotate token if needed
   * POST /auth/sessions/rotate
   */
  @Post('rotate')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async rotateToken(@Request() req: any) {
    try {
      const userId = req.user.userId;
      const currentToken = req.headers.authorization?.replace('Bearer ', '');
      const clientIp = req.ip || req.connection.remoteAddress;
      const userAgent = req.headers['user-agent'];

      if (!currentToken) {
        throw new BadRequestException('No token provided');
      }

      const result = await this.tokenRotationService.rotateTokenIfNeeded(
        currentToken,
        userId,
        clientIp,
        userAgent
      );

      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.TOKEN_REFRESH,
        userId,
        ipAddress: clientIp,
        details: {
          rotated: result.rotated,
          extended: result.extended,
          reason: result.reason
        },
        severity: SecuritySeverity.LOW
      });

      return {
        success: true,
        rotated: result.rotated,
        extended: result.extended,
        access_token: result.accessToken,
        refresh_token: result.refreshToken,
        expires_in: result.expiresIn,
        reason: result.reason
      };

    } catch (error) {
      this.logger.error('🔐 [SESSION-CONTROLLER] Token rotation failed:', error);
      throw error;
    }
  }

  /**
   * 🔐 Get current user's active sessions
   * GET /auth/sessions
   */
  @Get()
  @UseGuards(JwtAuthGuard)
  async getUserSessions(@Request() req: any) {
    try {
      const userId = req.user.userId;
      const sessions = await this.sessionManagementService.getUserSessions(userId);

      // Sanitize session data for client
      const sanitizedSessions = sessions.map(session => ({
        sessionId: session.sessionId,
        deviceInfo: session.deviceInfo,
        location: session.location,
        createdAt: session.createdAt,
        lastActivityAt: session.lastActivityAt,
        riskScore: session.riskScore,
        anomalies: session.anomalies,
        isCurrent: session.sessionId === req.user.sessionId
      }));

      return {
        success: true,
        sessions: sanitizedSessions,
        totalSessions: sanitizedSessions.length
      };

    } catch (error) {
      this.logger.error('🔐 [SESSION-CONTROLLER] Failed to get user sessions:', error);
      throw error;
    }
  }

  /**
   * 🔐 Terminate specific session
   * DELETE /auth/sessions/:sessionId
   */
  @Delete(':sessionId')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async terminateSession(@Request() req: any, @Param('sessionId') sessionId: string) {
    try {
      const userId = req.user.userId;
      const clientIp = req.ip || req.connection.remoteAddress;

      // Verify session belongs to user
      const userSessions = await this.sessionManagementService.getUserSessions(userId);
      const sessionToTerminate = userSessions.find(s => s.sessionId === sessionId);

      if (!sessionToTerminate) {
        throw new BadRequestException('Session not found or does not belong to user');
      }

      await this.sessionManagementService.terminateSession(sessionId, 'User requested termination');

      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGOUT,
        userId,
        ipAddress: clientIp,
        details: {
          terminatedSessionId: sessionId,
          reason: 'User requested termination',
          deviceInfo: sessionToTerminate.deviceInfo
        },
        severity: SecuritySeverity.LOW
      });

      return {
        success: true,
        message: 'Session terminated successfully'
      };

    } catch (error) {
      this.logger.error('🔐 [SESSION-CONTROLLER] Failed to terminate session:', error);
      throw error;
    }
  }

  /**
   * 🔐 Terminate all other sessions (keep current)
   * POST /auth/sessions/terminate-others
   */
  @Post('terminate-others')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async terminateOtherSessions(@Request() req: any) {
    try {
      const userId = req.user.userId;
      const currentSessionId = req.user.sessionId;
      const clientIp = req.ip || req.connection.remoteAddress;

      const userSessions = await this.sessionManagementService.getUserSessions(userId);
      const otherSessions = userSessions.filter(s => s.sessionId !== currentSessionId);

      let terminatedCount = 0;
      for (const session of otherSessions) {
        await this.sessionManagementService.terminateSession(
          session.sessionId,
          'User terminated other sessions'
        );
        terminatedCount++;
      }

      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGOUT,
        userId,
        ipAddress: clientIp,
        details: {
          action: 'Terminate other sessions',
          terminatedSessions: terminatedCount,
          keptSession: currentSessionId
        },
        severity: SecuritySeverity.LOW
      });

      return {
        success: true,
        message: `${terminatedCount} other sessions terminated successfully`,
        terminatedCount
      };

    } catch (error) {
      this.logger.error('🔐 [SESSION-CONTROLLER] Failed to terminate other sessions:', error);
      throw error;
    }
  }

  /**
   * 🔐 Terminate all user sessions (including current)
   * POST /auth/sessions/terminate-all
   */
  @Post('terminate-all')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async terminateAllSessions(@Request() req: any) {
    try {
      const userId = req.user.userId;
      const clientIp = req.ip || req.connection.remoteAddress;

      const terminatedCount = await this.sessionManagementService.terminateAllUserSessions(
        userId,
        'User requested termination of all sessions'
      );

      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.LOGOUT,
        userId,
        ipAddress: clientIp,
        details: {
          action: 'Terminate all sessions',
          terminatedSessions: terminatedCount
        },
        severity: SecuritySeverity.MEDIUM
      });

      return {
        success: true,
        message: `All ${terminatedCount} sessions terminated successfully`,
        terminatedCount
      };

    } catch (error) {
      this.logger.error('🔐 [SESSION-CONTROLLER] Failed to terminate all sessions:', error);
      throw error;
    }
  }

  /**
   * 🔐 Validate current session
   * POST /auth/sessions/validate
   */
  @Post('validate')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  async validateSession(@Request() req: any) {
    try {
      const sessionId = req.user.sessionId;
      const clientIp = req.ip || req.connection.remoteAddress;
      const userAgent = req.headers['user-agent'];

      const validation = await this.sessionManagementService.validateSession(
        sessionId,
        clientIp,
        userAgent
      );

      if (!validation.valid) {
        throw new UnauthorizedException(validation.reason);
      }

      return {
        success: true,
        valid: validation.valid,
        riskScore: validation.riskScore,
        warnings: validation.warnings,
        session: {
          sessionId: validation.session?.sessionId,
          createdAt: validation.session?.createdAt,
          lastActivityAt: validation.session?.lastActivityAt,
          deviceInfo: validation.session?.deviceInfo,
          location: validation.session?.location
        }
      };

    } catch (error) {
      this.logger.error('🔐 [SESSION-CONTROLLER] Session validation failed:', error);
      throw error;
    }
  }

  /**
   * 🔐 Get session security analytics
   * GET /auth/sessions/analytics
   */
  @Get('analytics')
  @UseGuards(JwtAuthGuard)
  async getSessionAnalytics(@Request() req: any) {
    try {
      const userId = req.user.userId;
      const sessions = await this.sessionManagementService.getUserSessions(userId);

      const analytics = {
        totalSessions: sessions.length,
        activeSessions: sessions.filter(s => s.isActive).length,
        averageRiskScore: sessions.length > 0
          ? sessions.reduce((sum, s) => sum + s.riskScore, 0) / sessions.length
          : 0,
        deviceTypes: this.aggregateDeviceTypes(sessions),
        locations: this.aggregateLocations(sessions),
        riskDistribution: this.aggregateRiskScores(sessions),
        anomalies: this.aggregateAnomalies(sessions),
        sessionDurations: this.calculateSessionDurations(sessions)
      };

      return {
        success: true,
        analytics
      };

    } catch (error) {
      this.logger.error('🔐 [SESSION-CONTROLLER] Failed to get session analytics:', error);
      throw error;
    }
  }

  /**
   * 🔐 Utility methods for analytics
   */
  private aggregateDeviceTypes(sessions: any[]): Record<string, number> {
    const deviceTypes: Record<string, number> = {};
    sessions.forEach(session => {
      const deviceType = session.deviceInfo?.device || 'Unknown';
      deviceTypes[deviceType] = (deviceTypes[deviceType] || 0) + 1;
    });
    return deviceTypes;
  }

  private aggregateLocations(sessions: any[]): Record<string, number> {
    const locations: Record<string, number> = {};
    sessions.forEach(session => {
      const location = session.location?.country || 'Unknown';
      locations[location] = (locations[location] || 0) + 1;
    });
    return locations;
  }

  private aggregateRiskScores(sessions: any[]): { low: number; medium: number; high: number } {
    const distribution = { low: 0, medium: 0, high: 0 };
    sessions.forEach(session => {
      if (session.riskScore < 30) distribution.low++;
      else if (session.riskScore < 70) distribution.medium++;
      else distribution.high++;
    });
    return distribution;
  }

  private aggregateAnomalies(sessions: any[]): Record<string, number> {
    const anomalies: Record<string, number> = {};
    sessions.forEach(session => {
      session.anomalies?.forEach((anomaly: string) => {
        anomalies[anomaly] = (anomalies[anomaly] || 0) + 1;
      });
    });
    return anomalies;
  }

  private calculateSessionDurations(sessions: any[]): {
    average: number;
    shortest: number;
    longest: number;
  } {
    if (sessions.length === 0) {
      return { average: 0, shortest: 0, longest: 0 };
    }

    const durations = sessions.map(session => {
      const start = new Date(session.createdAt).getTime();
      const end = session.terminatedAt
        ? new Date(session.terminatedAt).getTime()
        : Date.now();
      return end - start;
    });

    return {
      average: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      shortest: Math.min(...durations),
      longest: Math.max(...durations)
    };
  }
}
