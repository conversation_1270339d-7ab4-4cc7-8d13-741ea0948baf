import { IsString, IsNotEmpty, Length, Matches } from 'class-validator';

export class EnableMfaDto {
  @IsString()
  @IsNotEmpty({ message: 'MFA token is required' })
  @Length(6, 6, { message: 'M<PERSON> token must be 6 digits' })
  @Matches(/^\d{6}$/, { message: 'MFA token must contain only digits' })
  token: string;
}

export class VerifyMfaDto {
  @IsString()
  @IsNotEmpty({ message: 'MFA token is required' })
  @Length(6, 8, { message: 'MFA token must be 6-8 characters' })
  token: string;
}

export class DisableMfaDto {
  @IsString()
  @IsNotEmpty({ message: 'MFA token is required' })
  @Length(6, 8, { message: 'MFA token must be 6-8 characters' })
  token: string;
}

export class RegenerateBackupCodesDto {
  @IsString()
  @IsNotEmpty({ message: 'MFA token is required' })
  @Length(6, 6, { message: 'MFA token must be 6 digits' })
  @Matches(/^\d{6}$/, { message: 'MFA token must contain only digits' })
  token: string;
}
