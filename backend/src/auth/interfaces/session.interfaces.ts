/**
 * 🔐 Session Management Interfaces
 * 
 * Shared interfaces for session management across the authentication system
 */

export interface DeviceInfo {
  browser: string;
  browserVersion: string;
  os: string;
  osVersion: string;
  device: string;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

export interface LocationInfo {
  country?: string;
  region?: string;
  city?: string;
  lat?: number;
  lng?: number;
  timezone?: string;
  isp?: string;
  coordinates?: { lat: number; lng: number };
}

export interface SessionInfo {
  sessionId: string;
  userId: number;
  deviceFingerprint: string;
  deviceInfo: DeviceInfo;
  ipAddress: string;
  clientIp: string;
  location?: LocationInfo;
  userAgent: string;
  createdAt: Date;
  lastActivityAt: Date;
  expiresAt: Date;
  isActive: boolean;
  riskScore: number;
  anomalies: string[];
  activityCount: number;
  dataTransferred: number;
  requiresAdditionalVerification: boolean;
  terminatedAt?: Date;
  terminationReason?: string;
}

export interface SuspiciousActivity {
  type: string;
  timestamp: Date;
  sessionId: string;
  details: any;
  riskScore: number;
}

export interface SessionValidationResult {
  isValid: boolean;
  valid: boolean;
  session?: SessionInfo;
  anomalies: string[];
  riskScore: number;
  requiresAdditionalVerification: boolean;
  reason?: string;
  warnings?: string[];
  action?: 'ALLOW' | 'DENY' | 'DENY_AND_ALERT';
}

export interface TokenRotationResult {
  rotated: boolean;
  extended: boolean;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  reason: string;
}

export interface SessionAnalytics {
  totalSessions: number;
  activeSessions: number;
  sessionsLast24h: number;
  sessionsLast7d: number;
  uniqueDevices: number;
  uniqueIPs: number;
  uniqueLocations: number;
  averageSessionDuration: number;
  riskScore: number;
  anomalies: string[];
}
