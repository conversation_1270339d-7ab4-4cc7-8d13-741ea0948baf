import { Injectable, Logger, UnauthorizedException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { SecurityAuditService, SecurityEventType, SecuritySeverity } from './security-audit.service';

/**
 * 🔐 Authentication Error Handler Service
 * 
 * Provides standardized error handling for authentication:
 * - Consistent error responses
 * - Security event logging
 * - Error classification
 * - Rate limiting integration
 */
@Injectable()
export class AuthErrorHandlerService {
  private readonly logger = new Logger(AuthErrorHandlerService.name);

  constructor(
    private readonly securityAuditService: SecurityAuditService,
  ) {}

  /**
   * 🔐 Handle authentication failure
   */
  async handleAuthenticationFailure(
    error: AuthenticationError,
    context: AuthErrorContext
  ): Promise<never> {
    // Log security event
    await this.securityAuditService.logSecurityEvent({
      eventType: this.mapErrorToEventType(error.type),
      userId: context.userId,
      email: context.email,
      ipAddress: context.clientIp,
      userAgent: context.userAgent,
      details: {
        errorType: error.type,
        reason: error.reason,
        attemptCount: context.attemptCount,
        ...error.details
      },
      severity: this.mapErrorToSeverity(error.type)
    });

    // Log for monitoring
    this.logger.warn(`🔐 [AUTH-ERROR] ${error.type}: ${error.message}`, {
      userId: context.userId,
      email: context.email,
      clientIp: context.clientIp,
      reason: error.reason,
      details: error.details
    });

    // Throw appropriate HTTP exception
    throw this.mapErrorToHttpException(error);
  }

  /**
   * 🔐 Handle session error
   */
  async handleSessionError(
    error: SessionError,
    context: AuthErrorContext
  ): Promise<never> {
    // Log security event
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.SESSION_INVALID,
      userId: context.userId,
      email: context.email,
      ipAddress: context.clientIp,
      userAgent: context.userAgent,
      sessionId: context.sessionId,
      details: {
        errorType: error.type,
        reason: error.reason,
        sessionId: context.sessionId,
        ...error.details
      },
      severity: this.mapSessionErrorToSeverity(error.type)
    });

    // Log for monitoring
    this.logger.warn(`🔐 [SESSION-ERROR] ${error.type}: ${error.message}`, {
      userId: context.userId,
      sessionId: context.sessionId,
      clientIp: context.clientIp,
      reason: error.reason
    });

    // Throw appropriate HTTP exception
    throw this.mapSessionErrorToHttpException(error);
  }

  /**
   * 🔐 Handle MFA error
   */
  async handleMfaError(
    error: MfaError,
    context: AuthErrorContext
  ): Promise<never> {
    // Log security event
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.MFA_FAILED,
      userId: context.userId,
      email: context.email,
      ipAddress: context.clientIp,
      userAgent: context.userAgent,
      details: {
        errorType: error.type,
        reason: error.reason,
        mfaMethod: error.mfaMethod,
        ...error.details
      },
      severity: SecuritySeverity.HIGH
    });

    // Log for monitoring
    this.logger.warn(`🔐 [MFA-ERROR] ${error.type}: ${error.message}`, {
      userId: context.userId,
      email: context.email,
      mfaMethod: error.mfaMethod,
      reason: error.reason
    });

    // Throw appropriate HTTP exception
    throw new UnauthorizedException(error.userMessage || 'Multi-factor authentication failed');
  }

  /**
   * 🔐 Create authentication error
   */
  createAuthError(
    type: AuthErrorType,
    reason: string,
    userMessage?: string,
    details?: any
  ): AuthenticationError {
    return {
      type,
      reason,
      message: userMessage || this.getDefaultMessage(type),
      userMessage: userMessage || this.getDefaultUserMessage(type),
      details: details || {}
    };
  }

  /**
   * 🔐 Create session error
   */
  createSessionError(
    type: SessionErrorType,
    reason: string,
    userMessage?: string,
    details?: any
  ): SessionError {
    return {
      type,
      reason,
      message: userMessage || this.getDefaultSessionMessage(type),
      userMessage: userMessage || this.getDefaultSessionUserMessage(type),
      details: details || {}
    };
  }

  /**
   * 🔐 Create MFA error
   */
  createMfaError(
    type: MfaErrorType,
    reason: string,
    mfaMethod: string,
    userMessage?: string,
    details?: any
  ): MfaError {
    return {
      type,
      reason,
      mfaMethod,
      message: userMessage || this.getDefaultMfaMessage(type),
      userMessage: userMessage || this.getDefaultMfaUserMessage(type),
      details: details || {}
    };
  }

  /**
   * 🔐 Map error type to security event type
   */
  private mapErrorToEventType(errorType: AuthErrorType): SecurityEventType {
    switch (errorType) {
      case 'INVALID_CREDENTIALS':
      case 'USER_NOT_FOUND':
      case 'INVALID_PASSWORD':
        return SecurityEventType.LOGIN_FAILED;
      case 'ACCOUNT_LOCKED':
        return SecurityEventType.ACCOUNT_LOCKED;
      case 'RATE_LIMITED':
        return SecurityEventType.RATE_LIMIT_EXCEEDED;
      case 'IP_BLOCKED':
        return SecurityEventType.IP_BLOCKED;
      default:
        return SecurityEventType.LOGIN_FAILED;
    }
  }

  /**
   * 🔐 Map error type to severity
   */
  private mapErrorToSeverity(errorType: AuthErrorType): SecuritySeverity {
    switch (errorType) {
      case 'RATE_LIMITED':
      case 'IP_BLOCKED':
      case 'ACCOUNT_LOCKED':
        return SecuritySeverity.HIGH;
      case 'INVALID_CREDENTIALS':
      case 'INVALID_PASSWORD':
        return SecuritySeverity.MEDIUM;
      default:
        return SecuritySeverity.LOW;
    }
  }

  /**
   * 🔐 Map session error type to severity
   */
  private mapSessionErrorToSeverity(errorType: SessionErrorType): SecuritySeverity {
    switch (errorType) {
      case 'SESSION_HIJACKED':
      case 'SUSPICIOUS_ACTIVITY':
        return SecuritySeverity.CRITICAL;
      case 'SESSION_EXPIRED':
      case 'INVALID_SESSION':
        return SecuritySeverity.MEDIUM;
      default:
        return SecuritySeverity.LOW;
    }
  }

  /**
   * 🔐 Map error to HTTP exception
   */
  private mapErrorToHttpException(error: AuthenticationError): Error {
    switch (error.type) {
      case 'RATE_LIMITED':
      case 'IP_BLOCKED':
        return new ForbiddenException(error.userMessage);
      case 'ACCOUNT_LOCKED':
        return new ForbiddenException(error.userMessage);
      case 'INVALID_CREDENTIALS':
      case 'USER_NOT_FOUND':
      case 'INVALID_PASSWORD':
        return new UnauthorizedException(error.userMessage);
      default:
        return new BadRequestException(error.userMessage);
    }
  }

  /**
   * 🔐 Map session error to HTTP exception
   */
  private mapSessionErrorToHttpException(error: SessionError): Error {
    switch (error.type) {
      case 'SESSION_HIJACKED':
      case 'SUSPICIOUS_ACTIVITY':
        return new ForbiddenException(error.userMessage);
      case 'SESSION_EXPIRED':
      case 'INVALID_SESSION':
        return new UnauthorizedException(error.userMessage);
      default:
        return new BadRequestException(error.userMessage);
    }
  }

  /**
   * 🔐 Get default error messages
   */
  private getDefaultMessage(type: AuthErrorType): string {
    const messages = {
      'INVALID_CREDENTIALS': 'Invalid user credentials provided',
      'USER_NOT_FOUND': 'User account not found',
      'INVALID_PASSWORD': 'Password validation failed',
      'ACCOUNT_LOCKED': 'User account is locked',
      'RATE_LIMITED': 'Rate limit exceeded for authentication attempts',
      'IP_BLOCKED': 'IP address is temporarily blocked'
    };
    return messages[type] || 'Authentication error occurred';
  }

  private getDefaultUserMessage(type: AuthErrorType): string {
    const messages = {
      'INVALID_CREDENTIALS': 'Invalid email or password',
      'USER_NOT_FOUND': 'Invalid email or password',
      'INVALID_PASSWORD': 'Invalid email or password',
      'ACCOUNT_LOCKED': 'Account is locked. Please contact your administrator.',
      'RATE_LIMITED': 'Too many failed attempts. Please try again later.',
      'IP_BLOCKED': 'Access temporarily restricted. Please try again later.'
    };
    return messages[type] || 'Authentication failed';
  }

  private getDefaultSessionMessage(type: SessionErrorType): string {
    const messages = {
      'SESSION_EXPIRED': 'User session has expired',
      'INVALID_SESSION': 'Session validation failed',
      'SESSION_HIJACKED': 'Potential session hijacking detected',
      'SUSPICIOUS_ACTIVITY': 'Suspicious session activity detected'
    };
    return messages[type] || 'Session error occurred';
  }

  private getDefaultSessionUserMessage(type: SessionErrorType): string {
    const messages = {
      'SESSION_EXPIRED': 'Your session has expired. Please log in again.',
      'INVALID_SESSION': 'Invalid session. Please log in again.',
      'SESSION_HIJACKED': 'Security alert: Please log in again immediately.',
      'SUSPICIOUS_ACTIVITY': 'Unusual activity detected. Please verify your identity.'
    };
    return messages[type] || 'Session error. Please log in again.';
  }

  private getDefaultMfaMessage(type: MfaErrorType): string {
    const messages = {
      'INVALID_TOKEN': 'MFA token validation failed',
      'TOKEN_EXPIRED': 'MFA token has expired',
      'BACKUP_CODE_USED': 'MFA backup code already used',
      'MFA_NOT_ENABLED': 'MFA is not enabled for user'
    };
    return messages[type] || 'MFA error occurred';
  }

  private getDefaultMfaUserMessage(type: MfaErrorType): string {
    const messages = {
      'INVALID_TOKEN': 'Invalid authentication code',
      'TOKEN_EXPIRED': 'Authentication code has expired',
      'BACKUP_CODE_USED': 'This backup code has already been used',
      'MFA_NOT_ENABLED': 'Multi-factor authentication is not enabled'
    };
    return messages[type] || 'Multi-factor authentication failed';
  }
}

// Error type definitions
export type AuthErrorType = 'INVALID_CREDENTIALS' | 'USER_NOT_FOUND' | 'INVALID_PASSWORD' | 'ACCOUNT_LOCKED' | 'RATE_LIMITED' | 'IP_BLOCKED';
export type SessionErrorType = 'SESSION_EXPIRED' | 'INVALID_SESSION' | 'SESSION_HIJACKED' | 'SUSPICIOUS_ACTIVITY';
export type MfaErrorType = 'INVALID_TOKEN' | 'TOKEN_EXPIRED' | 'BACKUP_CODE_USED' | 'MFA_NOT_ENABLED';

export interface AuthenticationError {
  type: AuthErrorType;
  reason: string;
  message: string;
  userMessage: string;
  details: any;
}

export interface SessionError {
  type: SessionErrorType;
  reason: string;
  message: string;
  userMessage: string;
  details: any;
}

export interface MfaError {
  type: MfaErrorType;
  reason: string;
  mfaMethod: string;
  message: string;
  userMessage: string;
  details: any;
}

export interface AuthErrorContext {
  userId?: number;
  email?: string;
  clientIp?: string;
  userAgent?: string;
  sessionId?: string;
  attemptCount?: number;
}
