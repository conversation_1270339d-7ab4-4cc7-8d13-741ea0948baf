import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';
import { DeviceInfo } from '../interfaces/session.interfaces';

/**
 * 🔐 Device Fingerprinting Service
 * 
 * Handles device identification and fingerprinting:
 * - Device fingerprint generation
 * - Device information extraction
 * - Device trust scoring
 * - Device pattern analysis
 */
@Injectable()
export class DeviceFingerprintingService {
  private readonly logger = new Logger(DeviceFingerprintingService.name);

  /**
   * 🔐 Generate device fingerprint from request information
   */
  generateDeviceFingerprint(userAgent?: string, acceptLanguage?: string, acceptEncoding?: string): string {
    const components = [
      userAgent || 'unknown',
      acceptLanguage || 'unknown',
      acceptEncoding || 'unknown'
    ];

    const fingerprint = crypto
      .createHash('sha256')
      .update(components.join('|'))
      .digest('hex')
      .substring(0, 32);

    return fingerprint;
  }

  /**
   * 🔐 Extract device information from user agent
   */
  extractDeviceInfo(userAgent?: string): DeviceInfo {
    if (!userAgent) {
      return {
        browser: 'Unknown',
        browserVersion: 'Unknown',
        os: 'Unknown',
        osVersion: 'Unknown',
        device: 'Unknown',
        isMobile: false,
        isTablet: false,
        isDesktop: true
      };
    }

    const deviceInfo: DeviceInfo = {
      browser: this.extractBrowser(userAgent),
      browserVersion: this.extractBrowserVersion(userAgent),
      os: this.extractOS(userAgent),
      osVersion: this.extractOSVersion(userAgent),
      device: this.extractDevice(userAgent),
      isMobile: this.isMobile(userAgent),
      isTablet: this.isTablet(userAgent),
      isDesktop: this.isDesktop(userAgent)
    };

    return deviceInfo;
  }

  /**
   * 🔐 Calculate device trust score
   */
  calculateDeviceTrustScore(deviceFingerprint: string, userHistory: any[]): number {
    let trustScore = 5; // Start with neutral score

    // Check if device has been seen before
    const knownDevices = userHistory.map(h => h.deviceFingerprint).filter(Boolean);
    const deviceUsageCount = knownDevices.filter(d => d === deviceFingerprint).length;

    if (deviceUsageCount === 0) {
      trustScore -= 3; // New device penalty
    } else if (deviceUsageCount > 10) {
      trustScore += 2; // Frequently used device bonus
    }

    // Check for device consistency
    const deviceSessions = userHistory.filter(h => h.deviceFingerprint === deviceFingerprint);
    if (deviceSessions.length > 0) {
      const locations = deviceSessions.map(s => s.location).filter(Boolean);
      const uniqueLocations = new Set(locations.map(l => `${l.lat},${l.lng}`));

      if (uniqueLocations.size > 5) {
        trustScore -= 1; // Device used from many locations
      }
    }

    return Math.max(0, Math.min(10, trustScore));
  }

  /**
   * 🔐 Detect device anomalies
   */
  detectDeviceAnomalies(deviceInfo: DeviceInfo, userHistory: any[]): string[] {
    const anomalies: string[] = [];

    // Check for unusual device type
    const userDeviceTypes = userHistory
      .map(h => h.deviceInfo)
      .filter(Boolean)
      .map(d => d.device);

    const commonDeviceType = this.getMostCommon(userDeviceTypes);
    if (commonDeviceType && deviceInfo.device !== commonDeviceType && userDeviceTypes.length > 5) {
      anomalies.push('UNUSUAL_DEVICE_TYPE');
    }

    // Check for unusual browser
    const userBrowsers = userHistory
      .map(h => h.deviceInfo)
      .filter(Boolean)
      .map(d => d.browser);

    const commonBrowser = this.getMostCommon(userBrowsers);
    if (commonBrowser && deviceInfo.browser !== commonBrowser && userBrowsers.length > 5) {
      anomalies.push('UNUSUAL_BROWSER');
    }

    // Check for unusual OS
    const userOSs = userHistory
      .map(h => h.deviceInfo)
      .filter(Boolean)
      .map(d => d.os);

    const commonOS = this.getMostCommon(userOSs);
    if (commonOS && deviceInfo.os !== commonOS && userOSs.length > 5) {
      anomalies.push('UNUSUAL_OPERATING_SYSTEM');
    }

    return anomalies;
  }

  /**
   * 🔐 Extract browser from user agent
   */
  private extractBrowser(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('Opera')) return 'Opera';
    return 'Unknown';
  }

  /**
   * 🔐 Extract browser version from user agent
   */
  private extractBrowserVersion(userAgent: string): string {
    const patterns = [
      /Chrome\/([0-9.]+)/,
      /Firefox\/([0-9.]+)/,
      /Safari\/([0-9.]+)/,
      /Edge\/([0-9.]+)/,
      /Opera\/([0-9.]+)/
    ];

    for (const pattern of patterns) {
      const match = userAgent.match(pattern);
      if (match) return match[1];
    }

    return 'Unknown';
  }

  /**
   * 🔐 Extract operating system from user agent
   */
  private extractOS(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac OS')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  /**
   * 🔐 Extract OS version from user agent
   */
  private extractOSVersion(userAgent: string): string {
    const patterns = [
      /Windows NT ([0-9.]+)/,
      /Mac OS X ([0-9_]+)/,
      /Android ([0-9.]+)/,
      /OS ([0-9_]+)/
    ];

    for (const pattern of patterns) {
      const match = userAgent.match(pattern);
      if (match) return match[1].replace(/_/g, '.');
    }

    return 'Unknown';
  }

  /**
   * 🔐 Extract device type from user agent
   */
  private extractDevice(userAgent: string): string {
    if (this.isMobile(userAgent)) return 'Mobile';
    if (this.isTablet(userAgent)) return 'Tablet';
    return 'Desktop';
  }

  /**
   * 🔐 Check if device is mobile
   */
  private isMobile(userAgent: string): boolean {
    return /Mobile|Android|iPhone|iPod|BlackBerry|Windows Phone/i.test(userAgent);
  }

  /**
   * 🔐 Check if device is tablet
   */
  private isTablet(userAgent: string): boolean {
    return /iPad|Android.*Tablet|Kindle|Silk/i.test(userAgent);
  }

  /**
   * 🔐 Check if device is desktop
   */
  private isDesktop(userAgent: string): boolean {
    return !this.isMobile(userAgent) && !this.isTablet(userAgent);
  }

  /**
   * 🔐 Get most common item from array
   */
  private getMostCommon(items: string[]): string | null {
    if (items.length === 0) return null;

    const counts = items.reduce((acc, item) => {
      acc[item] = (acc[item] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b);
  }
}


