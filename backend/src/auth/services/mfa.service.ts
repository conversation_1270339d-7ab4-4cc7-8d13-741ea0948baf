import { Injectable, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import { User } from '../../users/entities/user.entity';
import { SecurityAuditService, SecurityEventType, SecuritySeverity } from './security-audit.service';

/**
 * 🔐 NIS2-Compliant Multi-Factor Authentication Service
 * 
 * Implements TOTP-based MFA using industry standards:
 * - RFC 6238 (TOTP: Time-Based One-Time Password Algorithm)
 * - 30-second time windows
 * - 6-digit codes
 * - Base32 encoded secrets
 * - QR code generation for easy setup
 */
@Injectable()
export class MfaService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private securityAuditService: SecurityAuditService,
  ) {}

  /**
   * 🔐 Generate MFA secret and QR code for user setup
   */
  async generateMfaSecret(userId: number): Promise<{ secret: string; qrCodeUrl: string; manualEntryKey: string }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.twoFactorEnabled) {
      throw new BadRequestException('MFA is already enabled for this user');
    }

    // Generate secret
    const secret = speakeasy.generateSecret({
      name: `eHRx (${user.email})`,
      issuer: 'eHRx - Employee Health & Risk Management',
      length: 32,
    });

    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

    // Store the secret temporarily (not yet enabled)
    await this.userRepository.update(userId, {
      twoFactorSecret: secret.base32,
    });

    // Log security event
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.SECURITY_SETTING_CHANGED,
      userId,
      email: user.email,
      details: { action: 'MFA_SECRET_GENERATED' },
      severity: SecuritySeverity.MEDIUM
    });

    return {
      secret: secret.base32!,
      qrCodeUrl,
      manualEntryKey: secret.base32!,
    };
  }

  /**
   * 🔐 Enable MFA after verifying the initial setup token
   */
  async enableMfa(userId: number, token: string): Promise<{ backupCodes: string[] }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (user.twoFactorEnabled) {
      throw new BadRequestException('MFA is already enabled for this user');
    }

    if (!user.twoFactorSecret) {
      throw new BadRequestException('MFA secret not found. Please generate a new secret first.');
    }

    // Verify the token
    const isValid = this.verifyToken(user.twoFactorSecret, token);
    if (!isValid) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.MFA_FAILED,
        userId,
        email: user.email,
        details: { action: 'MFA_ENABLE_FAILED', reason: 'INVALID_TOKEN' },
        severity: SecuritySeverity.HIGH
      });
      throw new UnauthorizedException('Invalid MFA token');
    }

    // Generate backup codes
    const backupCodes = this.generateBackupCodes();

    // Enable MFA
    await this.userRepository.update(userId, {
      twoFactorEnabled: true,
      mfaBackupCodes: JSON.stringify(backupCodes),
    });

    // Log successful MFA enablement
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.SECURITY_SETTING_CHANGED,
      userId,
      email: user.email,
      details: { action: 'MFA_ENABLED' },
      severity: SecuritySeverity.MEDIUM
    });

    return { backupCodes };
  }

  /**
   * 🔐 Disable MFA after verifying current password and MFA token
   */
  async disableMfa(userId: number, token: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.twoFactorEnabled) {
      throw new BadRequestException('MFA is not enabled for this user');
    }

    // Verify the token (either TOTP or backup code)
    const isValid = this.verifyToken(user.twoFactorSecret!, token) || 
                   this.verifyBackupCode(user.mfaBackupCodes, token);

    if (!isValid) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.MFA_FAILED,
        userId,
        email: user.email,
        details: { action: 'MFA_DISABLE_FAILED', reason: 'INVALID_TOKEN' },
        severity: SecuritySeverity.HIGH
      });
      throw new UnauthorizedException('Invalid MFA token or backup code');
    }

    // Disable MFA
    await this.userRepository.update(userId, {
      twoFactorEnabled: false,
      twoFactorSecret: null,
      mfaBackupCodes: null,
    });

    // Log MFA disablement
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.SECURITY_SETTING_CHANGED,
      userId,
      email: user.email,
      details: { action: 'MFA_DISABLED' },
      severity: SecuritySeverity.HIGH
    });
  }

  /**
   * 🔐 Verify MFA token during login
   */
  async verifyMfaToken(userId: number, token: string): Promise<boolean> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) {
      return false;
    }

    // Try TOTP first, then backup codes
    const isValidTotp = this.verifyToken(user.twoFactorSecret, token);
    const isValidBackup = this.verifyBackupCode(user.mfaBackupCodes, token);

    const isValid = isValidTotp || isValidBackup;

    // Log the attempt
    await this.securityAuditService.logSecurityEvent({
      eventType: isValid ? SecurityEventType.MFA_SUCCESS : SecurityEventType.MFA_FAILED,
      userId,
      email: user.email,
      details: { 
        action: 'MFA_LOGIN_VERIFICATION',
        tokenType: isValidTotp ? 'TOTP' : (isValidBackup ? 'BACKUP_CODE' : 'INVALID')
      },
      severity: isValid ? SecuritySeverity.LOW : SecuritySeverity.MEDIUM
    });

    // If backup code was used, remove it from the list
    if (isValidBackup && user.mfaBackupCodes) {
      const backupCodes = JSON.parse(user.mfaBackupCodes);
      const updatedCodes = backupCodes.filter((code: string) => code !== token);
      await this.userRepository.update(userId, {
        mfaBackupCodes: JSON.stringify(updatedCodes),
      });
    }

    return isValid;
  }

  /**
   * 🔐 Verify TOTP token
   */
  private verifyToken(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2, // Allow 2 time steps (60 seconds) of tolerance
    });
  }

  /**
   * 🔐 Verify backup code
   */
  private verifyBackupCode(backupCodesJson: string | null, token: string): boolean {
    if (!backupCodesJson) {
      return false;
    }

    try {
      const backupCodes = JSON.parse(backupCodesJson);
      return backupCodes.includes(token);
    } catch {
      return false;
    }
  }

  /**
   * 🔐 Generate backup codes for MFA recovery
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      // Generate 8-character alphanumeric codes
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * 🔐 Get MFA status for user
   */
  async getMfaStatus(userId: number): Promise<{ enabled: boolean; hasBackupCodes: boolean }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    return {
      enabled: user.twoFactorEnabled || false,
      hasBackupCodes: !!(user.mfaBackupCodes && JSON.parse(user.mfaBackupCodes).length > 0),
    };
  }

  /**
   * 🔐 Regenerate backup codes
   */
  async regenerateBackupCodes(userId: number, token: string): Promise<{ backupCodes: string[] }> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    if (!user.twoFactorEnabled) {
      throw new BadRequestException('MFA is not enabled for this user');
    }

    // Verify the token
    const isValid = this.verifyToken(user.twoFactorSecret!, token);
    if (!isValid) {
      await this.securityAuditService.logSecurityEvent({
        eventType: SecurityEventType.MFA_FAILED,
        userId,
        email: user.email,
        details: { action: 'BACKUP_CODES_REGENERATION_FAILED', reason: 'INVALID_TOKEN' },
        severity: SecuritySeverity.HIGH
      });
      throw new UnauthorizedException('Invalid MFA token');
    }

    // Generate new backup codes
    const backupCodes = this.generateBackupCodes();

    // Update user with new backup codes
    await this.userRepository.update(userId, {
      mfaBackupCodes: JSON.stringify(backupCodes),
    });

    // Log backup codes regeneration
    await this.securityAuditService.logSecurityEvent({
      eventType: SecurityEventType.SECURITY_SETTING_CHANGED,
      userId,
      email: user.email,
      details: { action: 'MFA_BACKUP_CODES_REGENERATED' },
      severity: SecuritySeverity.MEDIUM
    });

    return { backupCodes };
  }
}
