import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User } from '../../users/entities/user.entity';

/**
 * 🔐 NIS2-Compliant Password Policy Service
 * 
 * Implements comprehensive password security:
 * - Minimum 12 characters (NIS2 recommendation)
 * - Mixed case letters, numbers, special characters
 * - Password history tracking (prevent reuse of last 12 passwords)
 * - Common password blacklist
 * - Password strength scoring
 * - Breach detection integration ready
 */
@Injectable()
export class PasswordPolicyService {
  // NIS2-compliant password requirements
  private readonly MIN_LENGTH = 12;
  private readonly MAX_LENGTH = 128;
  private readonly HISTORY_COUNT = 12; // Track last 12 passwords
  private readonly MIN_SCORE = 3; // Out of 5

  // Common weak passwords to reject
  private readonly COMMON_PASSWORDS = [
    'password', 'password123', '123456', '123456789', 'qwerty', 'abc123',
    'password1', 'admin', 'administrator', 'root', 'user', 'guest',
    'welcome', 'login', 'pass', 'secret', 'default', 'changeme',
    'Password123', 'Password1', 'Qwerty123', 'Admin123', 'Welcome123'
  ];

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  /**
   * 🔐 Validate password against comprehensive policy
   */
  async validatePassword(password: string, userId?: number): Promise<{ valid: boolean; errors: string[]; score: number }> {
    const errors: string[] = [];
    let score = 0;

    // Length validation
    if (password.length < this.MIN_LENGTH) {
      errors.push(`Password must be at least ${this.MIN_LENGTH} characters long`);
    } else if (password.length >= this.MIN_LENGTH) {
      score += 1;
    }

    if (password.length > this.MAX_LENGTH) {
      errors.push(`Password must not exceed ${this.MAX_LENGTH} characters`);
    }

    // Character composition validation
    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~`]/.test(password);

    if (!hasLowercase) {
      errors.push('Password must contain at least one lowercase letter');
    } else {
      score += 0.25;
    }

    if (!hasUppercase) {
      errors.push('Password must contain at least one uppercase letter');
    } else {
      score += 0.25;
    }

    if (!hasNumbers) {
      errors.push('Password must contain at least one number');
    } else {
      score += 0.25;
    }

    if (!hasSpecialChars) {
      errors.push('Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)');
    } else {
      score += 0.25;
    }

    // Complexity scoring
    const uniqueChars = new Set(password).size;
    if (uniqueChars >= password.length * 0.6) {
      score += 0.5; // Good character diversity
    }

    // Pattern detection
    if (this.hasRepeatingPatterns(password)) {
      errors.push('Password contains too many repeating patterns');
    } else {
      score += 0.5;
    }

    if (this.hasSequentialChars(password)) {
      errors.push('Password contains sequential characters (e.g., 123, abc)');
    } else {
      score += 0.5;
    }

    // Common password check
    if (this.isCommonPassword(password)) {
      errors.push('Password is too common and easily guessable');
    } else {
      score += 0.5;
    }

    // Dictionary word check (basic)
    if (this.containsDictionaryWords(password)) {
      errors.push('Password should not contain common dictionary words');
    } else {
      score += 0.5;
    }

    // Password history check
    if (userId) {
      const isReused = await this.isPasswordReused(password, userId);
      if (isReused) {
        errors.push(`Password cannot be one of your last ${this.HISTORY_COUNT} passwords`);
      } else {
        score += 0.5;
      }
    }

    // Normalize score to 0-5 scale
    score = Math.min(5, Math.max(0, score));

    return {
      valid: errors.length === 0 && score >= this.MIN_SCORE,
      errors,
      score: Math.round(score * 10) / 10 // Round to 1 decimal place
    };
  }

  /**
   * 🔐 Store password in history for reuse prevention
   */
  async storePasswordHistory(userId: number, hashedPassword: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      return;
    }

    let passwordHistory: string[] = [];
    if (user.passwordHistory) {
      try {
        passwordHistory = JSON.parse(user.passwordHistory);
      } catch {
        passwordHistory = [];
      }
    }

    // Add new password to history
    passwordHistory.unshift(hashedPassword);

    // Keep only the last HISTORY_COUNT passwords
    passwordHistory = passwordHistory.slice(0, this.HISTORY_COUNT);

    // Update user record
    await this.userRepository.update(userId, {
      passwordHistory: JSON.stringify(passwordHistory),
    });
  }

  /**
   * 🔐 Check if password was recently used
   */
  private async isPasswordReused(password: string, userId: number): Promise<boolean> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user || !user.passwordHistory) {
      return false;
    }

    try {
      const passwordHistory: string[] = JSON.parse(user.passwordHistory);
      
      // Check against stored password hashes
      for (const hashedPassword of passwordHistory) {
        const isMatch = await bcrypt.compare(password, hashedPassword);
        if (isMatch) {
          return true;
        }
      }
    } catch {
      // If parsing fails, assume no history
      return false;
    }

    return false;
  }

  /**
   * 🔐 Detect repeating patterns
   */
  private hasRepeatingPatterns(password: string): boolean {
    // Check for 3+ character repeating patterns
    for (let i = 0; i <= password.length - 6; i++) {
      const pattern = password.substring(i, i + 3);
      const nextPattern = password.substring(i + 3, i + 6);
      if (pattern === nextPattern) {
        return true;
      }
    }

    // Check for single character repetition (4+ times)
    const charCounts = new Map<string, number>();
    for (const char of password) {
      charCounts.set(char, (charCounts.get(char) || 0) + 1);
      if (charCounts.get(char)! >= 4) {
        return true;
      }
    }

    return false;
  }

  /**
   * 🔐 Detect sequential characters
   */
  private hasSequentialChars(password: string): boolean {
    const sequences = [
      'abcdefghijklmnopqrstuvwxyz',
      'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      '0123456789',
      'qwertyuiopasdfghjklzxcvbnm', // QWERTY keyboard layout
      '!@#$%^&*()_+-=[]{}|;:,.<>?'
    ];

    for (const sequence of sequences) {
      for (let i = 0; i <= sequence.length - 4; i++) {
        const subseq = sequence.substring(i, i + 4);
        if (password.toLowerCase().includes(subseq.toLowerCase())) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 🔐 Check against common passwords
   */
  private isCommonPassword(password: string): boolean {
    return this.COMMON_PASSWORDS.some(common => 
      password.toLowerCase().includes(common.toLowerCase()) ||
      common.toLowerCase().includes(password.toLowerCase())
    );
  }

  /**
   * 🔐 Basic dictionary word detection
   */
  private containsDictionaryWords(password: string): boolean {
    const commonWords = [
      'password', 'admin', 'user', 'login', 'welcome', 'company', 'system',
      'computer', 'internet', 'security', 'access', 'account', 'service',
      'manager', 'employee', 'business', 'office', 'work', 'team', 'project'
    ];

    const lowerPassword = password.toLowerCase();
    return commonWords.some(word => lowerPassword.includes(word));
  }

  /**
   * 🔐 Generate password strength description
   */
  getPasswordStrengthDescription(score: number): { level: string; color: string; description: string } {
    if (score >= 4.5) {
      return {
        level: 'Excellent',
        color: '#4caf50',
        description: 'Very strong password with excellent security'
      };
    } else if (score >= 3.5) {
      return {
        level: 'Strong',
        color: '#8bc34a',
        description: 'Strong password with good security'
      };
    } else if (score >= 2.5) {
      return {
        level: 'Moderate',
        color: '#ff9800',
        description: 'Moderate password, consider strengthening'
      };
    } else if (score >= 1.5) {
      return {
        level: 'Weak',
        color: '#f44336',
        description: 'Weak password, please strengthen'
      };
    } else {
      return {
        level: 'Very Weak',
        color: '#d32f2f',
        description: 'Very weak password, must be strengthened'
      };
    }
  }

  /**
   * 🔐 Generate secure password suggestion
   */
  generateSecurePassword(): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    let password = '';
    
    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += special[Math.floor(Math.random() * special.length)];
    
    // Fill remaining length with random characters
    const allChars = lowercase + uppercase + numbers + special;
    for (let i = 4; i < 16; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }
}
