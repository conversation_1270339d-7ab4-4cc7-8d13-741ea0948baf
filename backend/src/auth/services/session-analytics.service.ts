import { Injectable, Logger } from '@nestjs/common';
import { SessionAnalytics } from '../interfaces/session.interfaces';

/**
 * 🔐 Session Analytics Service
 * 
 * Handles session pattern analysis and security analytics:
 * - Session behavior analysis
 * - Risk scoring
 * - Pattern detection
 * - Security metrics
 */
@Injectable()
export class SessionAnalyticsService {
  private readonly logger = new Logger(SessionAnalyticsService.name);

  /**
   * 🔐 Calculate session risk score
   */
  calculateSessionRiskScore(sessionInfo: any, userHistory: any[]): number {
    let riskScore = 0;

    // Geographic risk
    if (sessionInfo.location && userHistory.length > 0) {
      const lastKnownLocation = userHistory[userHistory.length - 1]?.location;
      if (lastKnownLocation && this.calculateDistance(sessionInfo.location, lastKnownLocation) > 1000) {
        riskScore += 3; // High risk for impossible travel
      }
    }

    // Time-based risk
    const currentHour = new Date().getHours();
    if (currentHour < 6 || currentHour > 22) {
      riskScore += 1; // Moderate risk for unusual hours
    }

    // Device fingerprint risk
    if (sessionInfo.deviceFingerprint) {
      const knownDevices = userHistory.map(h => h.deviceFingerprint).filter(Boolean);
      if (!knownDevices.includes(sessionInfo.deviceFingerprint)) {
        riskScore += 2; // New device
      }
    }

    // IP address risk
    if (sessionInfo.ipAddress) {
      const knownIPs = userHistory.map(h => h.ipAddress).filter(Boolean);
      if (!knownIPs.includes(sessionInfo.ipAddress)) {
        riskScore += 1; // New IP address
      }
    }

    return Math.min(10, riskScore); // Cap at 10
  }

  /**
   * 🔐 Detect session anomalies
   */
  detectSessionAnomalies(sessionInfo: any, userHistory: any[]): string[] {
    const anomalies: string[] = [];

    // Impossible travel detection
    if (sessionInfo.location && userHistory.length > 0) {
      const lastSession = userHistory[userHistory.length - 1];
      if (lastSession?.location && lastSession.timestamp) {
        const distance = this.calculateDistance(sessionInfo.location, lastSession.location);
        const timeDiff = (Date.now() - new Date(lastSession.timestamp).getTime()) / (1000 * 60 * 60); // hours
        const maxSpeed = distance / timeDiff; // km/h

        if (maxSpeed > 1000) { // Faster than commercial aircraft
          anomalies.push('IMPOSSIBLE_TRAVEL');
        }
      }
    }

    // Unusual access patterns
    const currentHour = new Date().getHours();
    const userTypicalHours = userHistory
      .map(h => new Date(h.timestamp).getHours())
      .filter(h => !isNaN(h));

    if (userTypicalHours.length > 10) {
      const avgHour = userTypicalHours.reduce((a, b) => a + b, 0) / userTypicalHours.length;
      if (Math.abs(currentHour - avgHour) > 6) {
        anomalies.push('UNUSUAL_TIME_ACCESS');
      }
    }

    // Multiple concurrent sessions from different locations
    const activeSessions = userHistory.filter(h =>
      h.isActive && h.location && h.location !== sessionInfo.location
    );
    if (activeSessions.length > 2) {
      anomalies.push('MULTIPLE_GEOGRAPHIC_SESSIONS');
    }

    return anomalies;
  }

  /**
   * 🔐 Generate session analytics report
   */
  generateSessionAnalytics(userSessions: any[]): SessionAnalytics {
    const now = new Date();
    const last24Hours = userSessions.filter(s =>
      (now.getTime() - new Date(s.createdAt).getTime()) < 24 * 60 * 60 * 1000
    );
    const last7Days = userSessions.filter(s =>
      (now.getTime() - new Date(s.createdAt).getTime()) < 7 * 24 * 60 * 60 * 1000
    );

    return {
      totalSessions: userSessions.length,
      activeSessions: userSessions.filter(s => s.isActive).length,
      sessionsLast24h: last24Hours.length,
      sessionsLast7d: last7Days.length,
      uniqueDevices: new Set(userSessions.map(s => s.deviceFingerprint).filter(Boolean)).size,
      uniqueIPs: new Set(userSessions.map(s => s.ipAddress).filter(Boolean)).size,
      uniqueLocations: new Set(userSessions.map(s => s.location).filter(Boolean)).size,
      averageSessionDuration: this.calculateAverageSessionDuration(userSessions),
      riskScore: this.calculateOverallRiskScore(userSessions),
      anomalies: this.detectPatternAnomalies(userSessions)
    };
  }

  /**
   * 🔐 Calculate distance between two geographic points
   */
  private calculateDistance(location1: any, location2: any): number {
    if (!location1 || !location2 || !location1.lat || !location1.lng || !location2.lat || !location2.lng) {
      return 0;
    }

    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(location2.lat - location1.lat);
    const dLng = this.toRadians(location2.lng - location1.lng);
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(location1.lat)) * Math.cos(this.toRadians(location2.lat)) *
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * 🔐 Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * 🔐 Calculate average session duration
   */
  private calculateAverageSessionDuration(sessions: any[]): number {
    const completedSessions = sessions.filter(s => s.endedAt);
    if (completedSessions.length === 0) return 0;

    const totalDuration = completedSessions.reduce((sum, session) => {
      const duration = new Date(session.endedAt).getTime() - new Date(session.createdAt).getTime();
      return sum + duration;
    }, 0);

    return totalDuration / completedSessions.length / (1000 * 60); // Return in minutes
  }

  /**
   * 🔐 Calculate overall risk score for user
   */
  private calculateOverallRiskScore(sessions: any[]): number {
    if (sessions.length === 0) return 0;

    const recentSessions = sessions.filter(s =>
      (Date.now() - new Date(s.createdAt).getTime()) < 7 * 24 * 60 * 60 * 1000
    );

    const riskScores = recentSessions.map(s => s.riskScore || 0);
    const avgRisk = riskScores.reduce((a, b) => a + b, 0) / riskScores.length;

    return Math.round(avgRisk * 10) / 10;
  }

  /**
   * 🔐 Detect pattern anomalies
   */
  private detectPatternAnomalies(sessions: any[]): string[] {
    const anomalies: string[] = [];

    // Check for rapid session creation
    const recentSessions = sessions.filter(s =>
      (Date.now() - new Date(s.createdAt).getTime()) < 60 * 60 * 1000 // Last hour
    );

    if (recentSessions.length > 10) {
      anomalies.push('RAPID_SESSION_CREATION');
    }

    // Check for sessions from too many different locations
    const uniqueLocations = new Set(
      sessions
        .filter(s => s.location)
        .map(s => `${s.location.lat},${s.location.lng}`)
    );

    if (uniqueLocations.size > 5) {
      anomalies.push('MULTIPLE_LOCATIONS');
    }

    return anomalies;
  }
}
