import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, AccountStatus } from '../../users/entities/user.entity';
import { SecurityAuditService, SecurityEventType, SecuritySeverity } from './security-audit.service';
import { TokenUtilityService } from './token-utility.service';
import { DeviceInfo, LocationInfo, SessionInfo, SuspiciousActivity, SessionValidationResult } from '../interfaces/session.interfaces';
import * as crypto from 'crypto';

/**
 * 🔐 NIS2-Compliant Advanced Session Management Service
 * 
 * Implements enterprise-grade session management:
 * - Multi-device session tracking
 * - Session anomaly detection
 * - Geographic session monitoring
 * - Device fingerprinting
 * - Session hijacking detection
 * - Automatic session cleanup
 * - Session analytics and reporting
 */
@Injectable()
export class SessionManagementService {
  private readonly logger = new Logger(SessionManagementService.name);

  // Session configuration
  private readonly MAX_SESSIONS_PER_USER = 5;
  private readonly SESSION_TIMEOUT = 8 * 60 * 60 * 1000; // 8 hours
  private readonly IDLE_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private readonly SUSPICIOUS_ACTIVITY_THRESHOLD = 3;

  // In-memory session store (in production, use Redis)
  private activeSessions = new Map<string, SessionInfo>();
  private userSessions = new Map<number, Set<string>>();
  private suspiciousActivities = new Map<string, SuspiciousActivity[]>();

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly securityAuditService: SecurityAuditService,
    private readonly tokenUtilityService: TokenUtilityService,
  ) {
    // Clean up expired sessions every 5 minutes
    setInterval(() => this.cleanupExpiredSessions(), 5 * 60 * 1000);

    // Analyze session patterns every hour
    setInterval(() => this.analyzeSessionPatterns(), 60 * 60 * 1000);
  }

  /**
   * 🔐 Create new session with comprehensive tracking
   * Consolidated to use User entity session fields only
   */
  async createSession(
    userId: number,
    sessionId: string,
    clientIp: string,
    userAgent: string,
    deviceInfo?: any
  ): Promise<SessionCreationResult> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId, accountStatus: AccountStatus.ACTIVE }
      });

      if (!user) {
        throw new Error('User not found or inactive');
      }

      // Generate device fingerprint using centralized service
      const deviceFingerprint = this.tokenUtilityService.generateDeviceFingerprint(clientIp, userAgent, deviceInfo);

      // Check for session limits (simplified for User entity approach)
      await this.enforceSessionLimitsUserEntity(userId, sessionId);

      // Detect potential session anomalies
      const anomalyDetection = await this.detectSessionAnomalies(userId, clientIp, userAgent, deviceFingerprint);

      // Create session info
      const sessionInfo: SessionInfo = {
        sessionId,
        userId,
        clientIp,
        ipAddress: clientIp,
        userAgent,
        deviceFingerprint,
        deviceInfo: deviceInfo || this.parseUserAgent(userAgent),
        createdAt: new Date(),
        lastActivityAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        isActive: true,
        location: await this.getLocationFromIP(clientIp),
        riskScore: anomalyDetection.riskScore,
        anomalies: anomalyDetection.anomalies,
        activityCount: 0,
        dataTransferred: 0,
        requiresAdditionalVerification: anomalyDetection.riskScore > 7,
      };

      // Store session
      this.activeSessions.set(sessionId, sessionInfo);

      // Update user sessions tracking
      if (!this.userSessions.has(userId)) {
        this.userSessions.set(userId, new Set());
      }
      this.userSessions.get(userId)!.add(sessionId);

      // Log session creation
      await this.logSessionEvent('SESSION_CREATED', sessionInfo, {
        riskScore: anomalyDetection.riskScore,
        anomalies: anomalyDetection.anomalies,
        deviceInfo: sessionInfo.deviceInfo
      });

      return {
        success: true,
        sessionId,
        riskScore: anomalyDetection.riskScore,
        warnings: anomalyDetection.anomalies,
        requiresAdditionalVerification: anomalyDetection.riskScore > 70
      };

    } catch (error) {
      this.logger.error('🔐 [SESSION-MGMT] Error creating session:', error);
      throw error;
    }
  }

  /**
   * 🔐 Update session activity
   */
  async updateSessionActivity(
    sessionId: string,
    activityType: string,
    dataSize?: number
  ): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      this.logger.warn('🔐 [SESSION-MGMT] Attempted to update non-existent session:', sessionId);
      return;
    }

    // Update activity tracking
    session.lastActivityAt = new Date();
    session.activityCount++;
    if (dataSize) {
      session.dataTransferred += dataSize;
    }

    // Check for suspicious activity patterns
    await this.checkSuspiciousActivity(session, activityType);

    // Update session in store
    this.activeSessions.set(sessionId, session);
  }

  /**
   * 🔐 Validate session and check for anomalies
   */
  async validateSession(sessionId: string, clientIp: string, userAgent: string): Promise<SessionValidationResult> {
    const session = this.activeSessions.get(sessionId);

    if (!session) {
      return {
        isValid: false,
        valid: false,
        anomalies: ['session_not_found'],
        riskScore: 10,
        requiresAdditionalVerification: true,
        reason: 'Session not found',
        action: 'DENY'
      };
    }

    // Check if session is expired
    if (!session.isActive || this.isSessionExpired(session)) {
      await this.terminateSession(sessionId, 'Session expired');
      return {
        isValid: false,
        valid: false,
        anomalies: ['session_expired'],
        riskScore: 5,
        requiresAdditionalVerification: false,
        reason: 'Session expired',
        action: 'DENY'
      };
    }

    // Check for session hijacking indicators
    const hijackingDetection = this.detectSessionHijacking(session, clientIp, userAgent);
    if (hijackingDetection.suspected) {
      await this.handleSuspectedHijacking(session, hijackingDetection);
      return {
        isValid: false,
        valid: false,
        anomalies: ['session_hijacking_suspected'],
        riskScore: 10,
        requiresAdditionalVerification: true,
        reason: 'Session hijacking suspected',
        action: 'DENY_AND_ALERT'
      };
    }

    // Update last activity
    await this.updateSessionActivity(sessionId, 'validation');

    return {
      isValid: true,
      valid: true,
      session,
      anomalies: session.anomalies,
      riskScore: session.riskScore,
      requiresAdditionalVerification: session.requiresAdditionalVerification,
      warnings: session.anomalies
    };
  }

  /**
   * 🔐 Get user's active sessions
   */
  async getUserSessions(userId: number): Promise<SessionInfo[]> {
    const sessionIds = this.userSessions.get(userId) || new Set();
    const sessions: SessionInfo[] = [];

    for (const sessionId of sessionIds) {
      const session = this.activeSessions.get(sessionId);
      if (session && session.isActive && !this.isSessionExpired(session)) {
        sessions.push(session);
      }
    }

    return sessions;
  }

  /**
   * 🔐 Terminate specific session
   */
  async terminateSession(sessionId: string, reason: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    // Mark session as inactive
    session.isActive = false;
    session.terminatedAt = new Date();
    session.terminationReason = reason;

    // Remove from active sessions
    this.activeSessions.delete(sessionId);

    // Remove from user sessions
    const userSessions = this.userSessions.get(session.userId);
    if (userSessions) {
      userSessions.delete(sessionId);
    }

    // Update database
    await this.userRepository.update(session.userId, {
      sessionToken: null,
      sessionExpiresAt: null
    });

    // Log session termination
    await this.logSessionEvent('SESSION_TERMINATED', session, {
      reason,
      duration: Date.now() - session.createdAt.getTime(),
      activityCount: session.activityCount
    });
  }

  /**
   * 🔐 Simplified session limits for User entity approach
   */
  private async enforceSessionLimitsUserEntity(userId: number, newSessionId: string): Promise<void> {
    // For User entity approach, we only allow one active session per user
    // This simplifies the session management while maintaining security
    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (user && user.sessionToken && user.sessionExpiresAt && user.sessionExpiresAt > new Date()) {
      // Log session replacement
      await this.logSecurityEvent('SESSION_REPLACED', userId, null, {
        oldSessionExpiry: user.sessionExpiresAt,
        newSessionId,
        reason: 'New login replaced existing session'
      });
    }
  }

  /**
   * 🔐 Terminate all user sessions (simplified for User entity)
   */
  async terminateAllUserSessions(userId: number, reason: string): Promise<number> {
    // Clear session data from user entity
    await this.userRepository.update(userId, {
      sessionToken: null,
      sessionExpiresAt: null,
      refreshToken: null,
      refreshTokenExpiresAt: null
    });

    // Clear in-memory tracking
    this.userSessions.delete(userId);

    await this.logSecurityEvent('ALL_USER_SESSIONS_TERMINATED', userId, null, {
      reason,
      terminatedSessions: 1 // Only one session per user in this approach
    });

    return 1;
  }

  /**
   * 🔐 Detect session anomalies
   */
  private async detectSessionAnomalies(
    userId: number,
    clientIp: string,
    userAgent: string,
    deviceFingerprint: string
  ): Promise<AnomalyDetectionResult> {
    const anomalies: string[] = [];
    let riskScore = 0;

    // Get user's session history
    const userSessions = await this.getUserSessions(userId);
    const user = await this.userRepository.findOne({ where: { id: userId } });

    // Check for new device
    const knownDevices = userSessions.map(s => s.deviceFingerprint);
    if (!knownDevices.includes(deviceFingerprint)) {
      anomalies.push('New device detected');
      riskScore += 30;
    }

    // Check for unusual location
    const location = await this.getLocationFromIP(clientIp);
    const knownLocations = userSessions.map(s => s.location?.country).filter(Boolean);
    if (location && !knownLocations.includes(location.country)) {
      anomalies.push('New geographic location');
      riskScore += 40;
    }

    // Check for unusual time
    const hour = new Date().getHours();
    if (hour < 6 || hour > 22) {
      anomalies.push('Unusual login time');
      riskScore += 20;
    }

    // Check for rapid successive logins
    const recentSessions = userSessions.filter(
      s => Date.now() - s.createdAt.getTime() < 5 * 60 * 1000 // Last 5 minutes
    );
    if (recentSessions.length > 2) {
      anomalies.push('Multiple rapid login attempts');
      riskScore += 50;
    }

    // Check against user's typical patterns
    if (user?.lastLoginIp && user.lastLoginIp !== clientIp) {
      const timeSinceLastLogin = Date.now() - (user.lastLoginAt?.getTime() || 0);
      if (timeSinceLastLogin < 60 * 1000) { // Less than 1 minute
        anomalies.push('Rapid IP change detected');
        riskScore += 60;
      }
    }

    return {
      riskScore: Math.min(100, riskScore),
      anomalies,
      requiresVerification: riskScore > 70
    };
  }

  /**
   * 🔐 Detect session hijacking
   */
  private detectSessionHijacking(
    session: SessionInfo,
    currentIp: string,
    currentUserAgent: string
  ): HijackingDetectionResult {
    const indicators: string[] = [];
    let suspicionLevel = 0;

    // Check IP address change
    if (session.clientIp !== currentIp) {
      indicators.push('IP address changed');
      suspicionLevel += 40;
    }

    // Check user agent change
    if (session.userAgent !== currentUserAgent) {
      indicators.push('User agent changed');
      suspicionLevel += 30;
    }

    // Check for impossible travel
    if (session.location && session.clientIp !== currentIp) {
      const timeDiff = Date.now() - session.lastActivityAt.getTime();
      if (timeDiff < 30 * 60 * 1000) { // Less than 30 minutes
        indicators.push('Impossible travel detected');
        suspicionLevel += 80;
      }
    }

    // Check activity patterns
    if (session.activityCount > 1000) { // Unusually high activity
      indicators.push('Unusual activity volume');
      suspicionLevel += 20;
    }

    return {
      suspected: suspicionLevel > 50,
      suspicionLevel,
      indicators,
      action: suspicionLevel > 80 ? 'IMMEDIATE_TERMINATION' : 'MONITOR'
    };
  }

  /**
   * 🔐 Handle suspected session hijacking
   */
  private async handleSuspectedHijacking(
    session: SessionInfo,
    detection: HijackingDetectionResult
  ): Promise<void> {
    // Terminate session immediately
    await this.terminateSession(session.sessionId, 'Suspected session hijacking');

    // Log critical security event
    await this.logSecurityEvent('SESSION_HIJACKING_SUSPECTED', session.userId, session.clientIp, {
      sessionId: session.sessionId,
      suspicionLevel: detection.suspicionLevel,
      indicators: detection.indicators,
      action: 'Session terminated'
    });

    // Notify user (in production, send email/SMS)
    this.logger.error('🔐 [SECURITY-ALERT] Session hijacking suspected:', {
      userId: session.userId,
      sessionId: session.sessionId,
      indicators: detection.indicators,
      suspicionLevel: detection.suspicionLevel
    });
  }

  // REMOVED: generateDeviceFingerprint() - now using centralized TokenUtilityService

  private parseUserAgent(userAgent: string): DeviceInfo {
    // Basic user agent parsing (in production, use a proper library)
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
    const isTablet = /iPad|Android.*Tablet/.test(userAgent);
    return {
      browser: this.extractBrowser(userAgent),
      browserVersion: 'Unknown',
      os: this.extractOS(userAgent),
      osVersion: 'Unknown',
      device: this.extractDevice(userAgent),
      isMobile,
      isTablet,
      isDesktop: !isMobile && !isTablet
    };
  }

  private extractBrowser(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private extractOS(userAgent: string): string {
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac OS')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private extractDevice(userAgent: string): string {
    if (userAgent.includes('iPhone')) return 'iPhone';
    if (userAgent.includes('iPad')) return 'iPad';
    if (userAgent.includes('Android')) return 'Android Device';
    return 'Desktop';
  }

  private async getLocationFromIP(ip: string): Promise<LocationInfo | null> {
    // In production, use a geolocation service
    return {
      country: 'Unknown',
      city: 'Unknown',
      region: 'Unknown',
      coordinates: { lat: 0, lng: 0 }
    };
  }

  private isSessionExpired(session: SessionInfo): boolean {
    const now = Date.now();
    const sessionAge = now - session.createdAt.getTime();
    const idleTime = now - session.lastActivityAt.getTime();

    return sessionAge > this.SESSION_TIMEOUT || idleTime > this.IDLE_TIMEOUT;
  }

  private async enforceSessionLimits(userId: number, newSessionId: string): Promise<void> {
    const userSessions = this.userSessions.get(userId) || new Set();

    if (userSessions.size >= this.MAX_SESSIONS_PER_USER) {
      // Find oldest session to terminate
      let oldestSession: SessionInfo | null = null;
      let oldestTime = Date.now();

      for (const sessionId of userSessions) {
        const session = this.activeSessions.get(sessionId);
        if (session && session.createdAt.getTime() < oldestTime) {
          oldestTime = session.createdAt.getTime();
          oldestSession = session;
        }
      }

      if (oldestSession) {
        await this.terminateSession(oldestSession.sessionId, 'Session limit exceeded');
      }
    }
  }

  private async checkSuspiciousActivity(session: SessionInfo, activityType: string): Promise<void> {
    const userId = session.userId.toString();
    const activities = this.suspiciousActivities.get(userId) || [];

    // Add current activity
    activities.push({
      type: activityType,
      timestamp: new Date(),
      sessionId: session.sessionId,
      details: { activityType },
      riskScore: 1
    });

    // Keep only recent activities (last hour)
    const recentActivities = activities.filter(
      a => Date.now() - a.timestamp.getTime() < 60 * 60 * 1000
    );

    this.suspiciousActivities.set(userId, recentActivities);

    // Check for suspicious patterns
    if (recentActivities.length > this.SUSPICIOUS_ACTIVITY_THRESHOLD) {
      await this.logSecurityEvent('SUSPICIOUS_ACTIVITY_DETECTED', session.userId, session.clientIp, {
        activityCount: recentActivities.length,
        activities: recentActivities.map(a => a.type),
        sessionId: session.sessionId
      });
    }
  }

  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (this.isSessionExpired(session)) {
        expiredSessions.push(sessionId);
      }
    }

    for (const sessionId of expiredSessions) {
      this.terminateSession(sessionId, 'Session expired (cleanup)');
    }

    this.logger.log(`🔐 [SESSION-CLEANUP] Cleaned up ${expiredSessions.length} expired sessions`);
  }

  private analyzeSessionPatterns(): void {
    // Implement session pattern analysis for security insights
    const totalSessions = this.activeSessions.size;
    const userCount = this.userSessions.size;

    this.logger.log('🔐 [SESSION-ANALYTICS] Session statistics:', {
      totalActiveSessions: totalSessions,
      activeUsers: userCount,
      averageSessionsPerUser: userCount > 0 ? (totalSessions / userCount).toFixed(2) : 0,
      timestamp: new Date().toISOString()
    });
  }

  private async logSessionEvent(eventType: string, session: SessionInfo, details?: any): Promise<void> {
    await this.securityAuditService.logSecurityEvent({
      eventType: eventType as SecurityEventType,
      userId: session.userId,
      ipAddress: session.clientIp,
      details: {
        sessionId: session.sessionId,
        userAgent: session.userAgent,
        deviceInfo: session.deviceInfo,
        ...details
      },
      severity: SecuritySeverity.LOW
    });
  }

  private async logSecurityEvent(eventType: string, userId: number, clientIp?: string, details?: any): Promise<void> {
    await this.securityAuditService.logSecurityEvent({
      eventType: eventType as SecurityEventType,
      userId,
      ipAddress: clientIp,
      details,
      severity: SecuritySeverity.MEDIUM
    });
  }
}

// Additional local interfaces
interface SessionCreationResult {
  success: boolean;
  sessionId: string;
  riskScore: number;
  warnings: string[];
  requiresAdditionalVerification: boolean;
}

interface AnomalyDetectionResult {
  riskScore: number;
  anomalies: string[];
  requiresVerification: boolean;
}

interface HijackingDetectionResult {
  suspected: boolean;
  suspicionLevel: number;
  indicators: string[];
  action: 'MONITOR' | 'IMMEDIATE_TERMINATION';
}
