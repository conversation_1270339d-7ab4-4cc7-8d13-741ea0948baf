import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, AccountStatus } from '../../users/entities/user.entity';
import { SecurityAuditService, SecurityEventType, SecuritySeverity } from './security-audit.service';
import { TokenRotationResult } from '../interfaces/session.interfaces';
import { TokenUtilityService } from './token-utility.service';

/**
 * 🔐 NIS2-Compliant Token Rotation Service
 * 
 * Implements advanced token rotation and session management:
 * - Automatic token rotation on usage
 * - Sliding session expiration
 * - Token family tracking for security
 * - Concurrent session management
 * - Token blacklisting and revocation
 * - Session fingerprinting
 * - Anomaly detection for token usage
 */
@Injectable()
export class TokenRotationService {
  private readonly logger = new Logger(TokenRotationService.name);

  // Token rotation configuration
  private readonly TOKEN_ROTATION_THRESHOLD = 15 * 60 * 1000; // 15 minutes
  private readonly MAX_CONCURRENT_SESSIONS = 3;
  private readonly SESSION_EXTENSION_THRESHOLD = 30 * 60 * 1000; // 30 minutes
  private readonly TOKEN_FAMILY_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

  // In-memory token blacklist (in production, use Redis)
  private tokenBlacklist = new Set<string>();
  private tokenFamilies = new Map<string, TokenFamily>();

  constructor(
    private readonly jwtService: JwtService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly securityAuditService: SecurityAuditService,
    private readonly tokenUtilityService: TokenUtilityService,
  ) {
    // Clean up expired token families every hour
    setInterval(() => this.cleanupExpiredTokenFamilies(), 60 * 60 * 1000);
  }

  /**
   * 🔐 Rotate token if needed based on usage patterns
   */
  async rotateTokenIfNeeded(
    currentToken: string,
    userId: number,
    clientIp?: string,
    userAgent?: string
  ): Promise<TokenRotationResult> {
    try {
      const user = await this.userRepository.findOne({
        where: { id: userId, accountStatus: AccountStatus.ACTIVE }
      });

      if (!user) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Decode current token to get metadata
      const tokenPayload = this.jwtService.decode(currentToken) as any;
      if (!tokenPayload) {
        throw new UnauthorizedException('Invalid token format');
      }

      // Check if token is blacklisted
      if (this.isTokenBlacklisted(currentToken)) {
        await this.logSecurityEvent('TOKEN_BLACKLISTED', userId, clientIp, {
          reason: 'Attempted use of blacklisted token'
        });
        throw new UnauthorizedException('Token has been revoked');
      }

      // Get token family information
      const tokenFamily = this.getTokenFamily(tokenPayload.jti || tokenPayload.sessionId);

      // Check for token reuse (security violation)
      if (tokenFamily && this.isTokenReused(currentToken, tokenFamily)) {
        await this.handleTokenReuseViolation(userId, tokenFamily, clientIp);
        throw new UnauthorizedException('Token reuse detected - security violation');
      }

      // Determine if rotation is needed
      const rotationNeeded = this.shouldRotateToken(tokenPayload, user);

      if (rotationNeeded.rotate) {
        return await this.performTokenRotation(user, tokenPayload, clientIp, userAgent, rotationNeeded.reason);
      }

      // Extend session if needed
      const sessionExtended = await this.extendSessionIfNeeded(user, tokenPayload);

      return {
        rotated: false,
        extended: sessionExtended,
        accessToken: currentToken,
        refreshToken: user.refreshToken,
        expiresIn: this.getTokenRemainingTime(tokenPayload),
        reason: sessionExtended ? 'Session extended' : 'No rotation needed'
      };

    } catch (error) {
      this.logger.error('🔐 [TOKEN-ROTATION] Error in token rotation:', error);
      throw error;
    }
  }

  /**
   * 🔐 Perform token rotation
   */
  private async performTokenRotation(
    user: User,
    oldTokenPayload: any,
    clientIp?: string,
    userAgent?: string,
    reason?: string
  ): Promise<TokenRotationResult> {
    // Generate new session ID and token family ID
    const newSessionId = this.generateSessionId();
    const tokenFamilyId = oldTokenPayload.jti || oldTokenPayload.sessionId || this.generateTokenFamilyId();

    // Create new token payload
    const newPayload = {
      email: user.email,
      sub: user.id,
      role: user.role,
      sessionId: newSessionId,
      jti: tokenFamilyId,
      iat: Math.floor(Date.now() / 1000),
      fingerprint: this.generateSessionFingerprint(clientIp, userAgent)
    };

    // Generate new tokens
    const newAccessToken = this.jwtService.sign(newPayload);
    const newRefreshToken = this.generateRefreshToken();

    // Update token family tracking
    this.updateTokenFamily(tokenFamilyId, {
      id: tokenFamilyId,
      userId: user.id,
      currentToken: newAccessToken,
      previousTokens: [oldTokenPayload.sessionId],
      createdAt: new Date(),
      lastRotatedAt: new Date(),
      clientIp,
      userAgent,
      rotationCount: (this.tokenFamilies.get(tokenFamilyId)?.rotationCount || 0) + 1
    });

    // Blacklist old token
    this.blacklistToken(this.extractTokenFromPayload(oldTokenPayload));

    // Update user session in database
    await this.updateUserSession(user.id, newAccessToken, newRefreshToken, clientIp);

    // Log token rotation event
    await this.logSecurityEvent('TOKEN_ROTATED', user.id, clientIp, {
      reason,
      oldSessionId: oldTokenPayload.sessionId,
      newSessionId,
      tokenFamilyId
    });

    return {
      rotated: true,
      extended: false,
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      expiresIn: 3600, // 1 hour
      reason: reason || 'Token rotated for security'
    };
  }

  /**
   * 🔐 Determine if token should be rotated
   */
  private shouldRotateToken(tokenPayload: any, user: User): { rotate: boolean; reason?: string } {
    const now = Date.now();
    const tokenIssuedAt = (tokenPayload.iat || 0) * 1000;
    const tokenAge = now - tokenIssuedAt;

    // Rotate if token is older than threshold
    if (tokenAge > this.TOKEN_ROTATION_THRESHOLD) {
      return { rotate: true, reason: 'Token age exceeded threshold' };
    }

    // Rotate if session fingerprint doesn't match
    const currentFingerprint = this.generateSessionFingerprint();
    if (tokenPayload.fingerprint && tokenPayload.fingerprint !== currentFingerprint) {
      return { rotate: true, reason: 'Session fingerprint mismatch' };
    }

    // Rotate if user has too many concurrent sessions
    if (this.hasExcessiveConcurrentSessions(user.id)) {
      return { rotate: true, reason: 'Excessive concurrent sessions' };
    }

    // Rotate if suspicious activity detected
    if (this.detectSuspiciousActivity(tokenPayload, user)) {
      return { rotate: true, reason: 'Suspicious activity detected' };
    }

    return { rotate: false };
  }

  /**
   * 🔐 Extend session if needed
   */
  private async extendSessionIfNeeded(user: User, tokenPayload: any): Promise<boolean> {
    if (!user.sessionExpiresAt) return false;

    const now = new Date();
    const timeUntilExpiry = user.sessionExpiresAt.getTime() - now.getTime();

    // Extend session if it's close to expiry
    if (timeUntilExpiry < this.SESSION_EXTENSION_THRESHOLD) {
      const newExpiryTime = new Date(now.getTime() + 3600 * 1000); // Extend by 1 hour

      await this.userRepository.update(user.id, {
        sessionExpiresAt: newExpiryTime
      });

      await this.logSecurityEvent('SESSION_EXTENDED', user.id, user.lastLoginIp, {
        oldExpiry: user.sessionExpiresAt.toISOString(),
        newExpiry: newExpiryTime.toISOString(),
        sessionId: tokenPayload.sessionId
      });

      return true;
    }

    return false;
  }

  /**
   * 🔐 Handle token reuse violation
   */
  private async handleTokenReuseViolation(
    userId: number,
    tokenFamily: TokenFamily,
    clientIp?: string
  ): Promise<void> {
    // Revoke entire token family
    this.revokeTokenFamily(tokenFamily.id);

    // Invalidate all user sessions
    await this.invalidateAllUserSessions(userId);

    // Log critical security event
    await this.logSecurityEvent('TOKEN_REUSE_VIOLATION', userId, clientIp, {
      tokenFamilyId: tokenFamily.id,
      severity: 'CRITICAL',
      action: 'All sessions revoked'
    });

    this.logger.error('🔐 [SECURITY-VIOLATION] Token reuse detected for user:', {
      userId,
      tokenFamilyId: tokenFamily.id,
      clientIp,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 🔐 Manage concurrent sessions
   */
  async manageConcurrentSessions(userId: number, newSessionId: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) return;

    // Get all active token families for user
    const userTokenFamilies = Array.from(this.tokenFamilies.values())
      .filter(family => family.userId === userId);

    if (userTokenFamilies.length >= this.MAX_CONCURRENT_SESSIONS) {
      // Remove oldest session
      const oldestFamily = userTokenFamilies
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())[0];

      this.revokeTokenFamily(oldestFamily.id);

      await this.logSecurityEvent('SESSION_LIMIT_EXCEEDED', userId, user.lastLoginIp, {
        maxSessions: this.MAX_CONCURRENT_SESSIONS,
        revokedSession: oldestFamily.id,
        newSession: newSessionId
      });
    }
  }

  /**
   * 🔐 Revoke all user sessions
   */
  async revokeAllUserSessions(userId: number, reason?: string): Promise<void> {
    // Revoke all token families for user
    const userTokenFamilies = Array.from(this.tokenFamilies.entries())
      .filter(([_, family]) => family.userId === userId);

    for (const [familyId, _] of userTokenFamilies) {
      this.revokeTokenFamily(familyId);
    }

    // Clear user session in database
    await this.invalidateAllUserSessions(userId);

    await this.logSecurityEvent('ALL_SESSIONS_REVOKED', userId, null, {
      reason: reason || 'Administrative action',
      revokedFamilies: userTokenFamilies.length
    });
  }

  /**
   * 🔐 Utility methods - now using centralized TokenUtilityService
   */
  private generateSessionId(): string {
    return this.tokenUtilityService.generateSessionId();
  }

  private generateTokenFamilyId(): string {
    return this.tokenUtilityService.generateTokenFamilyId();
  }

  private generateRefreshToken(): string {
    return this.tokenUtilityService.generateRefreshToken();
  }

  private generateSessionFingerprint(clientIp?: string, userAgent?: string): string {
    return this.tokenUtilityService.generateSessionFingerprint(clientIp, userAgent);
  }

  private blacklistToken(token: string): void {
    this.tokenUtilityService.blacklistToken(token);
  }

  private isTokenBlacklisted(token: string): boolean {
    return this.tokenUtilityService.isTokenBlacklisted(token);
  }

  private getTokenFamily(familyId: string): TokenFamily | undefined {
    return this.tokenFamilies.get(familyId);
  }

  private updateTokenFamily(familyId: string, family: TokenFamily): void {
    this.tokenFamilies.set(familyId, { ...family, id: familyId });
  }

  private revokeTokenFamily(familyId: string): void {
    const family = this.tokenFamilies.get(familyId);
    if (family) {
      // Blacklist all tokens in family
      this.blacklistToken(family.currentToken);
      family.previousTokens.forEach(token => this.blacklistToken(token));
      this.tokenFamilies.delete(familyId);
    }
  }

  private isTokenReused(token: string, family: TokenFamily): boolean {
    return family.previousTokens.includes(this.extractSessionIdFromToken(token));
  }

  private extractTokenFromPayload(payload: any): string {
    // This would need to be implemented based on how you store the original token
    return payload.originalToken || '';
  }

  private extractSessionIdFromToken(token: string): string {
    try {
      const payload = this.jwtService.decode(token) as any;
      return payload?.sessionId || '';
    } catch {
      return '';
    }
  }

  private getTokenRemainingTime(payload: any): number {
    const exp = payload.exp * 1000;
    const now = Date.now();
    return Math.max(0, Math.floor((exp - now) / 1000));
  }

  private hasExcessiveConcurrentSessions(userId: number): boolean {
    const userSessions = Array.from(this.tokenFamilies.values())
      .filter(family => family.userId === userId);
    return userSessions.length > this.MAX_CONCURRENT_SESSIONS;
  }

  private detectSuspiciousActivity(payload: any, user: User): boolean {
    // Implement suspicious activity detection logic
    // For example: unusual login times, geographic anomalies, etc.
    return false;
  }

  private async updateUserSession(
    userId: number,
    accessToken: string,
    refreshToken: string,
    clientIp?: string
  ): Promise<void> {
    const expiresAt = new Date(Date.now() + 3600 * 1000);
    const refreshExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

    await this.userRepository.update(userId, {
      sessionToken: accessToken,
      sessionExpiresAt: expiresAt,
      refreshToken,
      refreshTokenExpiresAt: refreshExpiresAt,
      lastLoginIp: clientIp,
    });
  }

  private async invalidateAllUserSessions(userId: number): Promise<void> {
    await this.userRepository.update(userId, {
      sessionToken: null,
      sessionExpiresAt: null,
      refreshToken: null,
      refreshTokenExpiresAt: null,
    });
  }

  private cleanupExpiredTokenFamilies(): void {
    const now = Date.now();
    for (const [familyId, family] of this.tokenFamilies.entries()) {
      if (now - family.createdAt.getTime() > this.TOKEN_FAMILY_EXPIRY) {
        this.revokeTokenFamily(familyId);
      }
    }
  }

  private async logSecurityEvent(
    eventType: string,
    userId: number,
    clientIp?: string,
    details?: any
  ): Promise<void> {
    await this.securityAuditService.logSecurityEvent({
      eventType: eventType as SecurityEventType,
      userId,
      ipAddress: clientIp,
      details,
      severity: SecuritySeverity.MEDIUM
    });
  }
}

// Local types
interface TokenFamily {
  id: string;
  userId: number;
  currentToken: string;
  previousTokens: string[];
  createdAt: Date;
  lastRotatedAt: Date;
  clientIp?: string;
  userAgent?: string;
  rotationCount: number;
}
