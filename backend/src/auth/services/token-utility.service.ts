import { Injectable, Logger } from '@nestjs/common';
import * as crypto from 'crypto';

/**
 * 🔐 Centralized Token Utility Service
 * 
 * Consolidates all token generation methods to ensure consistency
 * and eliminate duplication across the authentication system.
 * 
 * Features:
 * - Secure random token generation
 * - Consistent session ID format
 * - Cryptographically secure refresh tokens
 * - Device fingerprinting
 * - Token blacklist management (Redis-ready)
 */
@Injectable()
export class TokenUtilityService {
  private readonly logger = new Logger(TokenUtilityService.name);
  
  // In-memory blacklist for development (should use Redis in production)
  private readonly tokenBlacklist = new Set<string>();
  
  // Token configuration constants
  private readonly SESSION_ID_LENGTH = 32; // 16 bytes = 32 hex chars
  private readonly REFRESH_TOKEN_LENGTH = 128; // 64 bytes = 128 hex chars
  private readonly TOKEN_FAMILY_ID_LENGTH = 16; // 8 bytes = 16 hex chars
  private readonly FINGERPRINT_LENGTH = 32; // 16 bytes = 32 hex chars

  /**
   * 🔐 Generate cryptographically secure session ID
   */
  generateSessionId(): string {
    return crypto.randomBytes(this.SESSION_ID_LENGTH / 2).toString('hex');
  }

  /**
   * 🔐 Generate cryptographically secure refresh token
   */
  generateRefreshToken(): string {
    return crypto.randomBytes(this.REFRESH_TOKEN_LENGTH / 2).toString('hex');
  }

  /**
   * 🔐 Generate token family ID for rotation tracking
   */
  generateTokenFamilyId(): string {
    return crypto.randomBytes(this.TOKEN_FAMILY_ID_LENGTH / 2).toString('hex');
  }

  /**
   * 🔐 Generate device fingerprint for session security
   */
  generateDeviceFingerprint(clientIp?: string, userAgent?: string, deviceInfo?: any): string {
    const data = JSON.stringify({
      ip: clientIp || 'unknown',
      userAgent: userAgent || 'unknown',
      deviceInfo: deviceInfo || {},
      timestamp: Math.floor(Date.now() / (1000 * 60 * 60)) // Hour-based for stability
    });
    
    return crypto
      .createHash('sha256')
      .update(data)
      .digest('hex')
      .substring(0, this.FINGERPRINT_LENGTH);
  }

  /**
   * 🔐 Generate session fingerprint for token rotation
   */
  generateSessionFingerprint(clientIp?: string, userAgent?: string): string {
    const data = `${clientIp || 'unknown'}-${userAgent || 'unknown'}`;
    return crypto
      .createHash('sha256')
      .update(data)
      .digest('hex')
      .substring(0, 16);
  }

  /**
   * 🔐 Generate unique incident ID for security events
   */
  generateIncidentId(): string {
    const timestamp = Date.now().toString(36);
    const random = crypto.randomBytes(4).toString('hex');
    return `INC-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * 🔐 Generate audit session ID for frontend logging
   */
  generateAuditSessionId(): string {
    const timestamp = Date.now();
    const random = crypto.randomBytes(4).toString('hex');
    return `audit-${timestamp}-${random}`;
  }

  /**
   * 🔐 Token blacklist management
   */
  blacklistToken(token: string): void {
    this.tokenBlacklist.add(token);
    this.logger.log(`🔐 [TOKEN-BLACKLIST] Token blacklisted: ${token.substring(0, 10)}...`);
    
    // TODO: In production, store in Redis with TTL
    // await this.redisService.setex(`blacklist:${token}`, 3600, 'true');
  }

  /**
   * 🔐 Check if token is blacklisted
   */
  isTokenBlacklisted(token: string): boolean {
    const isBlacklisted = this.tokenBlacklist.has(token);
    
    if (isBlacklisted) {
      this.logger.warn(`🔐 [TOKEN-BLACKLIST] Blacklisted token access attempt: ${token.substring(0, 10)}...`);
    }
    
    return isBlacklisted;
    
    // TODO: In production, check Redis
    // return await this.redisService.exists(`blacklist:${token}`);
  }

  /**
   * 🔐 Clear expired tokens from blacklist (cleanup)
   */
  cleanupBlacklist(): void {
    // For in-memory implementation, we'll clear periodically
    // In production with Redis, TTL handles this automatically
    const currentSize = this.tokenBlacklist.size;
    
    if (currentSize > 10000) { // Prevent memory bloat
      this.tokenBlacklist.clear();
      this.logger.log(`🔐 [TOKEN-BLACKLIST] Cleared blacklist cache (was ${currentSize} entries)`);
    }
  }

  /**
   * 🔐 Get blacklist statistics
   */
  getBlacklistStats(): { size: number; memoryUsage: string } {
    const size = this.tokenBlacklist.size;
    const memoryUsage = `${(size * 100 / 1024).toFixed(2)} KB`; // Rough estimate
    
    return { size, memoryUsage };
  }

  /**
   * 🔐 Validate token format
   */
  validateTokenFormat(token: string, expectedLength?: number): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // Check if it's a valid hex string
    const hexPattern = /^[a-f0-9]+$/i;
    if (!hexPattern.test(token)) {
      return false;
    }

    // Check length if specified
    if (expectedLength && token.length !== expectedLength) {
      return false;
    }

    return true;
  }

  /**
   * 🔐 Extract token metadata safely
   */
  extractTokenMetadata(token: string): { 
    length: number; 
    format: string; 
    entropy: number;
    isSecure: boolean;
  } {
    return {
      length: token.length,
      format: /^[a-f0-9]+$/i.test(token) ? 'hex' : 'unknown',
      entropy: new Set(token).size / token.length, // Character diversity
      isSecure: token.length >= 32 && /^[a-f0-9]+$/i.test(token)
    };
  }
}
