import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, AccountStatus } from '../../users/entities/user.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    // 🔐 NIS2-COMPLIANT: Enforce secure JWT configuration
    const secret = process.env.JWT_SECRET;

    if (!secret) {
      throw new Error('🔐 [SECURITY] JWT_SECRET environment variable is required');
    }

    if (secret.length < 32) {
      throw new Error('🔐 [SECURITY] JWT_SECRET must be at least 32 characters for NIS2 compliance');
    }

    // 🔐 Only log basic configuration status in production
    if (process.env.NODE_ENV !== 'production') {
      console.log('🔐 [JWT-STRATEGY] JWT Strategy configured securely:', {
        hasEnvVar: !!secret,
        isSecure: secret.length >= 32,
        environment: process.env.NODE_ENV
      });
    }

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secret,
      issuer: 'ehrx-system',
      audience: 'ehrx-users',
      algorithms: ['HS256'],
    });
  }

  async validate(payload: any) {
    // 🔐 Only log detailed payload in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 [JWT-DEBUG] JWT Strategy validate called with payload:', {
        sub: payload.sub,
        email: payload.email,
        role: payload.role,
        iat: payload.iat,
        exp: payload.exp,
        sessionId: payload.sessionId
      });
    }

    // 🔐 NIS2-COMPLIANT: Enhanced token validation

    // Check token expiration (additional check beyond JWT library)
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && now >= payload.exp) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔐 [JWT-DEBUG] Token expired:', { exp: payload.exp, now });
      }
      throw new UnauthorizedException('Token expired');
    }

    // Validate that the user still exists and is active
    const user = await this.userRepository.findOne({
      where: { id: payload.sub, accountStatus: AccountStatus.ACTIVE }
    });

    if (!user) {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔐 [JWT-DEBUG] User not found or inactive:', payload.sub);
      }
      throw new UnauthorizedException('User not found or inactive');
    }

    // 🔐 NIS2-COMPLIANT: Validate session token against database
    if (payload.sessionId) {
      if (!user.sessionToken || !user.sessionExpiresAt) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔐 [JWT-DEBUG] No valid session in database for user:', user.id);
        }
        throw new UnauthorizedException('Invalid session');
      }

      // Check if session has expired
      if (new Date() > user.sessionExpiresAt) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔐 [JWT-DEBUG] Session expired in database for user:', user.id);
        }
        throw new UnauthorizedException('Session expired');
      }

      // 🔐 SECURITY FIX: Use exact match instead of includes() to prevent partial matches
      const sessionTokens = user.sessionToken.split(',').map(token => token.trim());
      const sessionValid = sessionTokens.includes(payload.sessionId);
      if (!sessionValid) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔐 [JWT-DEBUG] Session ID mismatch for user:', user.id);
        }
        throw new UnauthorizedException('Invalid session');
      }
    }

    // 🔐 NIS2-COMPLIANT: Check for account security flags
    if (user.mustChangePassword) {
      console.log('🔐 [JWT-DEBUG] User must change password:', user.id);
      // Allow access but flag for password change requirement
    }

    const result = {
      userId: user.id,
      email: user.email,
      role: user.role,
      mustChangePassword: user.mustChangePassword,
      sessionId: payload.sessionId
    };

    // 🔧 FIXED: Only log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 [JWT-DEBUG] Returning user object:', result);
    }
    return result;
  }
}
