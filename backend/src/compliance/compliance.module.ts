import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { AuditLog } from '../audit/entities/audit-log.entity';
import { GdprComplianceService } from './gdpr-compliance.service';
import { Soc2ComplianceService } from './soc2-compliance.service';
import { GdprService } from './services/gdpr.service';
import { Soc2Service } from './services/soc2.service';
import { ComplianceController } from './compliance.controller';
import { AuditModule } from '../audit/audit.module';
import { SecurityModule } from '../security/security.module';

/**
 * 🔐 Enterprise Compliance Module
 * Provides comprehensive compliance services for:
 * - GDPR (General Data Protection Regulation)
 * - SOC2 Type II (Trust Service Criteria)
 * - NIS2 (Network and Information Security Directive)
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([User, AuditLog]),
    AuditModule,
    SecurityModule
  ],
  controllers: [ComplianceController],
  providers: [
    GdprComplianceService,
    Soc2ComplianceService,
    GdprService,
    Soc2Service
  ],
  exports: [
    GdprComplianceService,
    Soc2ComplianceService,
    GdprService,
    Soc2Service
  ],
})
export class ComplianceModule { }
