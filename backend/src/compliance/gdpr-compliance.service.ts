import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { AuditLog } from '../audit/entities/audit-log.entity';

/**
 * 🔐 GDPR Compliance Service
 * Implements GDPR requirements for data protection and privacy
 */
@Injectable()
export class GdprComplianceService {
  private readonly logger = new Logger(GdprComplianceService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AuditLog)
    private readonly auditRepository: Repository<AuditLog>,
  ) { }

  /**
   * 🔐 GDPR Article 17 - Right to Erasure (Right to be Forgotten)
   */
  async requestDataDeletion(userId: number, requestedBy: number): Promise<void> {
    this.logger.log(`Processing GDPR data deletion request for user ${userId}`);

    try {
      // 1. Anonymize user data instead of hard delete (for audit trail)
      await this.anonymizeUserData(userId);

      // 2. Log the deletion request for compliance
      await this.logGdprAction('data_deletion', userId, requestedBy, {
        action: 'User data anonymized per GDPR Article 17',
        timestamp: new Date().toISOString(),
      });

      this.logger.log(`GDPR data deletion completed for user ${userId}`);
    } catch (error) {
      this.logger.error(`GDPR data deletion failed for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * 🔐 GDPR Article 15 - Right of Access
   */
  async exportUserData(userId: number, requestedBy: number): Promise<any> {
    this.logger.log(`Processing GDPR data export request for user ${userId}`);

    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new Error('User not found');
      }

      // Prepare user data export
      const userData = {
        personalData: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        accountData: {
          isActive: user.isActive,
          accountStatus: user.accountStatus,
          lastLoginAt: user.lastLoginAt,
          mustChangePassword: user.mustChangePassword,
        },
        exportMetadata: {
          exportDate: new Date().toISOString(),
          requestedBy: requestedBy,
          dataRetentionPeriod: '7 years (NIS2 compliance)',
          legalBasis: 'GDPR Article 15 - Right of Access',
        },
      };

      // Log the export request
      await this.logGdprAction('data_export', userId, requestedBy, {
        action: 'User data exported per GDPR Article 15',
        dataTypes: Object.keys(userData),
      });

      return userData;
    } catch (error) {
      this.logger.error(`GDPR data export failed for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * 🔐 GDPR Article 16 - Right to Rectification
   */
  async updateUserData(userId: number, updates: any, requestedBy: number): Promise<void> {
    this.logger.log(`Processing GDPR data rectification for user ${userId}`);

    try {
      await this.userRepository.update(userId, updates);

      // Log the rectification
      await this.logGdprAction('data_rectification', userId, requestedBy, {
        action: 'User data updated per GDPR Article 16',
        updatedFields: Object.keys(updates),
        timestamp: new Date().toISOString(),
      });

      this.logger.log(`GDPR data rectification completed for user ${userId}`);
    } catch (error) {
      this.logger.error(`GDPR data rectification failed for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * 🔐 Data Retention Policy Enforcement
   */
  async enforceDataRetention(): Promise<void> {
    this.logger.log('Enforcing GDPR data retention policies');

    const retentionPeriodDays = 2555; // 7 years for NIS2 compliance
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionPeriodDays);

    try {
      // Find users that exceed retention period and are inactive
      const expiredUsers = await this.userRepository
        .createQueryBuilder('user')
        .where('user.isActive = :isActive', { isActive: false })
        .andWhere('user.updatedAt < :cutoffDate', { cutoffDate })
        .getMany();

      for (const user of expiredUsers) {
        await this.anonymizeUserData(user.id);
        this.logger.log(`Anonymized expired user data for user ${user.id}`);
      }

      this.logger.log(`Data retention enforcement completed. Processed ${expiredUsers.length} users`);
    } catch (error) {
      this.logger.error('Data retention enforcement failed:', error);
      throw error;
    }
  }

  /**
   * 🔐 Anonymize user data while preserving audit trail
   */
  private async anonymizeUserData(userId: number): Promise<void> {
    const anonymizedData = {
      email: `anonymized_${userId}@deleted.local`,
      firstName: 'ANONYMIZED',
      lastName: 'USER',
      password: 'ANONYMIZED',
      passwordHistory: null,
      sessionToken: null,
      mfaSecret: null,
      backupCodes: null,
      isActive: false,
      accountStatus: 'deleted' as any,
    };

    await this.userRepository.update(userId, anonymizedData);
  }

  /**
   * 🔐 Log GDPR compliance actions
   */
  private async logGdprAction(
    action: string,
    userId: number,
    requestedBy: number,
    details: any,
  ): Promise<void> {
    const auditLog = this.auditRepository.create({
      userId: requestedBy,
      action: `GDPR_${action.toUpperCase()}`,
      resource: 'User',
      resourceId: userId.toString(),
      details: details,
      ipAddress: 'system',
      userAgent: 'GDPR Compliance Service',
      category: 'data_access',
      severity: 'medium',
      outcome: 'success',
      riskLevel: 'medium',
      compliance: {
        nis2: true,
        gdpr: true,
        retention_period: 2555
      }
    });

    await this.auditRepository.save(auditLog);
  }

  /**
   * 🔐 Generate GDPR compliance report
   */
  async generateComplianceReport(): Promise<any> {
    const report = {
      reportDate: new Date().toISOString(),
      dataRetentionPolicy: '7 years (NIS2 compliance)',
      activeUsers: await this.userRepository.count({ where: { isActive: true } }),
      anonymizedUsers: await this.userRepository.count({
        where: { accountStatus: 'deleted' as any }
      }),
      gdprRequests: {
        dataExports: await this.auditRepository.count({
          where: { action: 'GDPR_DATA_EXPORT' }
        }),
        dataDeletions: await this.auditRepository.count({
          where: { action: 'GDPR_DATA_DELETION' }
        }),
        dataRectifications: await this.auditRepository.count({
          where: { action: 'GDPR_DATA_RECTIFICATION' }
        }),
      },
      complianceStatus: 'COMPLIANT',
      lastRetentionEnforcement: new Date().toISOString(),
    };

    return report;
  }
}
