import { Module, Global } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { EnvironmentConfigService } from './environment.config';

/**
 * 🔐 Global Configuration Module
 * 
 * Provides centralized configuration management
 * for security, encryption, and compliance settings
 */
@Global()
@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true,
    }),
  ],
  providers: [EnvironmentConfigService],
  exports: [EnvironmentConfigService, NestConfigModule],
})
export class ConfigModule {}
