import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 🔐 Environment Configuration Service
 * 
 * Centralized configuration management with validation
 * for security, encryption, and compliance settings
 */
@Injectable()
export class EnvironmentConfigService {
  constructor(private readonly configService: ConfigService) {
    this.validateCriticalConfig();
  }

  // ==================== DATABASE CONFIGURATION ====================
  get database() {
    return {
      host: this.configService.get<string>('DB_HOST', '127.0.0.1'),
      port: this.configService.get<number>('DB_PORT', 5432),
      username: this.configService.get<string>('DB_USERNAME'),
      password: this.configService.get<string>('DB_PASSWORD'),
      database: this.configService.get<string>('DB_NAME', 'ehrx'),
    };
  }

  // ==================== JWT CONFIGURATION ====================
  get jwt() {
    return {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: this.configService.get<number>('JWT_EXPIRATION', 3600),
    };
  }

  // ==================== SERVER CONFIGURATION ====================
  get server() {
    return {
      port: this.configService.get<number>('PORT', 4000),
      nodeEnv: this.configService.get<string>('NODE_ENV', 'development'),
      frontendUrl: this.configService.get<string>('FRONTEND_URL', 'http://localhost:3080'),
    };
  }

  // ==================== ENCRYPTION CONFIGURATION ====================
  get encryption() {
    return {
      masterKey: this.configService.get<string>('ENCRYPTION_MASTER_KEY'),
      salt: this.configService.get<string>('ENCRYPTION_SALT'),
      iterations: this.configService.get<number>('ENCRYPTION_ITERATIONS', 100000),
    };
  }

  // ==================== SECURITY CONFIGURATION ====================
  get security() {
    return {
      sessionTimeout: this.configService.get<number>('SESSION_TIMEOUT', 1800000), // 30 minutes
      rateLimitWindowMs: this.configService.get<number>('RATE_LIMIT_WINDOW_MS', 60000),
      rateLimitMaxRequests: this.configService.get<number>('RATE_LIMIT_MAX_REQUESTS', 100),
      hstsMaxAge: this.configService.get<number>('SECURITY_HSTS_MAX_AGE', 31536000),
      cspEnabled: this.configService.get<boolean>('SECURITY_CSP_ENABLED', true),
    };
  }

  // ==================== COMPLIANCE CONFIGURATION ====================
  get gdpr() {
    return {
      dataRetentionDays: this.configService.get<number>('GDPR_DATA_RETENTION_DAYS', 2555),
      anonymizationEnabled: this.configService.get<boolean>('GDPR_ANONYMIZATION_ENABLED', true),
      auditRetentionDays: this.configService.get<number>('GDPR_AUDIT_RETENTION_DAYS', 3650),
    };
  }

  get soc2() {
    return {
      auditEnabled: this.configService.get<boolean>('SOC2_AUDIT_ENABLED', true),
      monitoringEnabled: this.configService.get<boolean>('SOC2_MONITORING_ENABLED', true),
      incidentReporting: this.configService.get<boolean>('SOC2_INCIDENT_REPORTING', true),
    };
  }

  get nis2() {
    return {
      securityMonitoring: this.configService.get<boolean>('NIS2_SECURITY_MONITORING', true),
      incidentResponse: this.configService.get<boolean>('NIS2_INCIDENT_RESPONSE', true),
      vulnerabilityManagement: this.configService.get<boolean>('NIS2_VULNERABILITY_MANAGEMENT', true),
    };
  }

  // ==================== AUDIT LOGGING ====================
  get auditLog() {
    return {
      retentionDays: this.configService.get<number>('AUDIT_LOG_RETENTION_DAYS', 3650),
      level: this.configService.get<string>('AUDIT_LOG_LEVEL', 'info'),
      encryption: this.configService.get<boolean>('AUDIT_LOG_ENCRYPTION', true),
    };
  }

  // ==================== EMAIL CONFIGURATION ====================
  get email() {
    return {
      host: this.configService.get<string>('EMAIL_HOST'),
      port: this.configService.get<number>('EMAIL_PORT', 587),
      secure: this.configService.get<boolean>('EMAIL_SECURE', false),
      user: this.configService.get<string>('EMAIL_USER'),
      password: this.configService.get<string>('EMAIL_PASSWORD'),
      complianceAlertEmail: this.configService.get<string>('COMPLIANCE_ALERT_EMAIL'),
      securityAlertEmail: this.configService.get<string>('SECURITY_ALERT_EMAIL'),
    };
  }

  // ==================== DEVELOPMENT SETTINGS ====================
  get development() {
    return {
      debugMode: this.configService.get<boolean>('DEBUG_MODE', false),
      verboseLogging: this.configService.get<boolean>('VERBOSE_LOGGING', false),
      mockDataEnabled: this.configService.get<boolean>('MOCK_DATA_ENABLED', false),
    };
  }

  // ==================== UTILITY METHODS ====================
  get isProduction(): boolean {
    return this.server.nodeEnv === 'production';
  }

  get isDevelopment(): boolean {
    return this.server.nodeEnv === 'development';
  }

  get isTest(): boolean {
    return this.server.nodeEnv === 'test';
  }

  // ==================== VALIDATION ====================
  private validateCriticalConfig(): void {
    const errors: string[] = [];

    // Validate JWT secret
    if (!this.jwt.secret || this.jwt.secret.length < 32) {
      errors.push('JWT_SECRET must be at least 32 characters long');
    }

    // Validate encryption configuration
    if (!this.encryption.masterKey) {
      errors.push('ENCRYPTION_MASTER_KEY is required for data encryption');
    }

    if (!this.encryption.salt) {
      errors.push('ENCRYPTION_SALT is required for key derivation');
    }

    if (this.encryption.iterations < 10000) {
      errors.push('ENCRYPTION_ITERATIONS should be at least 10,000 for security');
    }

    // Validate database configuration
    if (!this.database.username || !this.database.password) {
      errors.push('Database credentials (DB_USERNAME, DB_PASSWORD) are required');
    }

    // Production-specific validations
    if (this.isProduction) {
      if (this.jwt.secret && (this.jwt.secret.includes('dev') || this.jwt.secret.includes('test'))) {
        errors.push('Production JWT_SECRET should not contain "dev" or "test"');
      }

      if (this.encryption.masterKey && (this.encryption.masterKey.includes('dev') || this.encryption.masterKey.includes('test'))) {
        errors.push('Production ENCRYPTION_MASTER_KEY should not contain "dev" or "test"');
      }

      if (this.server.frontendUrl && !this.server.frontendUrl.startsWith('https://')) {
        errors.push('Production FRONTEND_URL must use HTTPS');
      }
    }

    // Log warnings for missing optional configurations
    const warnings: string[] = [];

    if (!this.email.host) {
      warnings.push('EMAIL_HOST not configured - compliance notifications will be disabled');
    }

    if (!this.email.complianceAlertEmail) {
      warnings.push('COMPLIANCE_ALERT_EMAIL not configured - compliance alerts will not be sent');
    }

    if (!this.email.securityAlertEmail) {
      warnings.push('SECURITY_ALERT_EMAIL not configured - security alerts will not be sent');
    }

    // Throw errors if critical configuration is missing
    if (errors.length > 0) {
      throw new Error(`Critical configuration errors:\n${errors.join('\n')}`);
    }

    // Log warnings
    if (warnings.length > 0) {
      console.warn('⚠️  Configuration warnings:');
      warnings.forEach(warning => console.warn(`   - ${warning}`));
    }

    // Log successful validation
    console.log('✅ Environment configuration validated successfully');
    console.log(`🔐 Security mode: ${this.isProduction ? 'PRODUCTION' : 'DEVELOPMENT'}`);
    console.log(`🔐 Encryption: ${this.encryption.masterKey ? 'ENABLED' : 'DISABLED'}`);
    console.log(`🔐 Compliance monitoring: ${this.soc2.monitoringEnabled ? 'ENABLED' : 'DISABLED'}`);
  }

  /**
   * Get configuration summary for health checks
   */
  getConfigSummary() {
    return {
      environment: this.server.nodeEnv,
      security: {
        encryptionEnabled: !!this.encryption.masterKey,
        sessionTimeout: this.security.sessionTimeout,
        rateLimitEnabled: this.security.rateLimitMaxRequests > 0,
        hstsEnabled: this.security.hstsMaxAge > 0,
        cspEnabled: this.security.cspEnabled,
      },
      compliance: {
        gdprEnabled: this.gdpr.anonymizationEnabled,
        soc2Enabled: this.soc2.auditEnabled,
        nis2Enabled: this.nis2.securityMonitoring,
        auditLogEncryption: this.auditLog.encryption,
      },
      database: {
        host: this.database.host,
        port: this.database.port,
        database: this.database.database,
      },
      server: {
        port: this.server.port,
        frontendUrl: this.server.frontendUrl,
      },
    };
  }

  /**
   * Generate secure configuration for production deployment
   */
  generateProductionSecrets(): Record<string, string> {
    const crypto = require('crypto');

    return {
      JWT_SECRET: crypto.randomBytes(32).toString('base64'),
      ENCRYPTION_MASTER_KEY: crypto.randomBytes(32).toString('base64'),
      ENCRYPTION_SALT: crypto.randomBytes(16).toString('base64'),
    };
  }
}
