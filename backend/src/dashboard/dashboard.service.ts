import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssessmentInstance, AssessmentStatus } from '../assessments/entities/assessment-instance.entity';
import { User, UserRole } from '../users/entities/user.entity';
import { AssessmentResponse } from '../assessments/entities/assessment-response.entity';
import { AuditLoggingService } from '../audit/audit-logging.service';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
    @InjectRepository(AssessmentResponse)
    private responseRepository: Repository<AssessmentResponse>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private auditLoggingService: AuditLoggingService,
  ) { }

  async getOverviewStats(userId: number, userRole: string): Promise<any> {
    let assessmentQuery = this.assessmentRepository.createQueryBuilder('assessment');

    // Apply role-based filtering
    if (userRole === UserRole.HR_ADMIN) {
      // HR can see all assessments
    } else if (userRole === UserRole.MANAGER) {
      // Managers can only see their team's assessments
      assessmentQuery = assessmentQuery.where('assessment.evaluatorId = :userId', { userId });
    } else {
      // Regular employees can only see their own assessments
      assessmentQuery = assessmentQuery.where('assessment.employeeId = :userId', { userId });
    }

    // Get total assessments count
    const totalAssessments = await assessmentQuery.getCount();

    // Get assessment counts by status
    const statusCounts = await Promise.all(
      Object.values(AssessmentStatus).map(async (status) => {
        const count = await assessmentQuery
          .clone()
          .where('assessment.status = :status', { status })
          .getCount();

        return { status, count };
      })
    );

    // Get average assessment score
    const averageScoreResult = await assessmentQuery
      .select('AVG(assessment.totalScore)', 'avgScore')
      .getRawOne();

    const averageScore = averageScoreResult ? parseFloat(averageScoreResult.avgScore) || 0 : 0;

    return {
      totalAssessments,
      statusCounts,
      averageScore,
    };
  }

  async getEmployeePerformanceStats(userId: number, userRole: string): Promise<any> {
    let query = this.assessmentRepository
      .createQueryBuilder('assessment')
      .leftJoinAndSelect('assessment.employee', 'employee')
      .select([
        'employee.id as employeeId',
        'employee.first_name as firstName',
        'employee.last_name as lastName',
        'AVG(assessment.totalScore) as averageScore',
        'COUNT(assessment.id) as assessmentCount'
      ])
      .groupBy('employee.id');

    // Apply role-based filtering
    if (userRole === UserRole.HR_ADMIN) {
      // HR can see all employees
    } else if (userRole === UserRole.MANAGER) {
      // Managers can only see their team's employees
      query = query.where('assessment.evaluatorId = :userId', { userId });
    } else {
      // Regular employees can only see their own data
      query = query.where('assessment.employeeId = :userId', { userId });
    }

    const employeeStats = await query.getRawMany();

    return employeeStats;
  }

  async getAssessmentAreaStats(): Promise<any> {
    // Get average scores by assessment area
    const areaScores = await this.responseRepository
      .createQueryBuilder('response')
      .leftJoinAndSelect('response.area', 'area')
      .select([
        'area.id as areaId',
        'area.name as areaName',
        'AVG(response.score) as averageScore',
        'AVG(response.weightedScore) as averageWeightedScore',
      ])
      .groupBy('area.id')
      .orderBy('averageScore', 'DESC')
      .getRawMany();

    return areaScores;
  }

  async getAssessmentTrendsByMonth(userId: number, userRole: string): Promise<any> {
    let query = this.assessmentRepository
      .createQueryBuilder('assessment')
      .select([
        'EXTRACT(YEAR FROM assessment.assessmentDate) as year',
        'EXTRACT(MONTH FROM assessment.assessmentDate) as month',
        'COUNT(assessment.id) as count',
        'AVG(assessment.totalScore) as averageScore',
      ])
      .groupBy('year, month')
      .orderBy('year, month');

    // Apply role-based filtering
    if (userRole === UserRole.HR_ADMIN) {
      // HR can see all trends
    } else if (userRole === UserRole.MANAGER) {
      // Managers can only see their team's trends
      query = query.where('assessment.evaluatorId = :userId', { userId });
    } else {
      // Regular employees can only see their own trends
      query = query.where('assessment.employeeId = :userId', { userId });
    }

    const trends = await query.getRawMany();

    return trends;
  }

  async getUserDashboardData(userId: number, userRole: string): Promise<any> {
    // For employees - focused on their own performance
    if (userRole === UserRole.EMPLOYEE) {
      const assessments = await this.assessmentRepository
        .createQueryBuilder('assessment')
        .leftJoinAndSelect('assessment.responses', 'response')
        .leftJoinAndSelect('response.area', 'area')
        .where('assessment.employeeId = :userId', { userId })
        .getMany();

      const pendingAssessments = assessments.filter(
        a => a.status === AssessmentStatus.IN_PROGRESS || a.status === AssessmentStatus.DRAFT
      ).length;

      const completedAssessments = assessments.filter(
        a => a.status === AssessmentStatus.COMPLETED || a.status === AssessmentStatus.APPROVED
      ).length;

      const latestAssessment = assessments.sort(
        (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      )[0] || null;

      // Get area strengths and weaknesses
      const areaScores = [];

      for (const assessment of assessments) {
        for (const response of assessment.responses) {
          const existingArea = areaScores.find(a => a.areaId === response.areaId);

          if (existingArea) {
            existingArea.scores.push(response.score);
          } else {
            areaScores.push({
              areaId: response.areaId,
              areaName: response.area.name,
              scores: [response.score],
            });
          }
        }
      }

      // Calculate averages
      const areaAverages = areaScores.map(area => ({
        areaId: area.areaId,
        areaName: area.areaName,
        averageScore: area.scores.reduce((sum, score) => sum + score, 0) / area.scores.length,
      }));

      // Sort by average score
      const strengths = [...areaAverages].sort((a, b) => b.averageScore - a.averageScore).slice(0, 3);
      const weaknesses = [...areaAverages].sort((a, b) => a.averageScore - b.averageScore).slice(0, 3);

      return {
        pendingAssessments,
        completedAssessments,
        latestAssessment,
        strengths,
        weaknesses,
      };
    }
    // For managers & HR - focused on team performance
    else {
      return this.getOverviewStats(userId, userRole);
    }
  }
}
