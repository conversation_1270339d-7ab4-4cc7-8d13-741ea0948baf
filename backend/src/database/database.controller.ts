import { Controller, Get, UseGuards, Logger } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { DatabaseService } from './database.service';

/**
 * 🔐 SECURITY-HARDENED Database Controller
 *
 * REMOVED: Generic database access endpoints that posed security risks
 * REPLACED: With specific, controlled database health monitoring only
 *
 * Previous endpoints removed for security:
 * - GET /database/tables/:tableName (generic table access)
 * - POST /database/tables/:tableName (generic record creation)
 * - PUT /database/tables/:tableName/:id (generic record updates)
 * - DELETE /database/tables/:tableName/:id (generic record deletion)
 *
 * These operations should be handled by specific entity controllers
 * with proper validation, authorization, and business logic.
 */
@Controller('database')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.HR_ADMIN) // Only HR admins can access database monitoring
export class DatabaseController {
  private readonly logger = new Logger(DatabaseController.name);

  constructor(private readonly databaseService: DatabaseService) { }

  /**
   * 🔐 Get database health status (safe monitoring endpoint)
   */
  @Get('health')
  async getDatabaseHealth() {
    this.logger.log('🔐 [DATABASE-CONTROLLER] Database health check requested');
    return this.databaseService.getDatabaseHealth();
  }

  @Get('tables')
  async getAvailableTables() {
    return this.databaseService.getAvailableTables();
  }
}
