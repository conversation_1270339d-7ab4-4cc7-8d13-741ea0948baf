import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseController } from './database.controller';
import { DatabaseService } from './database.service';
import { User } from '../users/entities/user.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';
import { AssessmentTemplate } from '../assessments/entities/assessment-template.entity';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { Skillset } from '../teams/entities/skillset.entity';
import { TeamMember } from '../teams/entities/team-member.entity';


@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      OrganizationalUnit,
      AssessmentTemplate,
      AssessmentInstance,
      Skillset,
      TeamMember,

    ]),
  ],
  controllers: [DatabaseController],
  providers: [DatabaseService],
  exports: [DatabaseService],
})
export class DatabaseModule { }
