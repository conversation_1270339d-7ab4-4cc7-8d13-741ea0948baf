import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';
import { AssessmentTemplate } from '../assessments/entities/assessment-template.entity';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { Skillset } from '../teams/entities/skillset.entity';
import { TeamMember } from '../teams/entities/team-member.entity';

/**
 * 🔐 SECURITY-HARDENED Database Service
 * 
 * REMOVED: All generic database access methods that posed security risks
 * REPLACED: With safe database health monitoring and statistics only
 * 
 * This service now provides only:
 * - Database health monitoring
 * - Safe table statistics (counts only, no data access)
 * - Connection status information
 * 
 * All data access should go through specific entity services with
 * proper validation, authorization, and business logic.
 */
@Injectable()
export class DatabaseService {
  private readonly logger = new Logger(DatabaseService.name);

  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(OrganizationalUnit)
    private orgUnitsRepository: Repository<OrganizationalUnit>,
    @InjectRepository(AssessmentTemplate)
    private templatesRepository: Repository<AssessmentTemplate>,
    @InjectRepository(AssessmentInstance)
    private assessmentInstancesRepository: Repository<AssessmentInstance>,
    @InjectRepository(Skillset)
    private skillsetsRepository: Repository<Skillset>,
    @InjectRepository(TeamMember)
    private teamMembersRepository: Repository<TeamMember>,
  ) { }

  /**
   * 🔐 Get database health status (safe monitoring)
   */
  async getDatabaseHealth() {
    try {
      const stats = await this.getTableStatistics();
      
      return {
        success: true,
        status: 'healthy',
        timestamp: new Date().toISOString(),
        data: {
          tables: stats,
          totalRecords: stats.reduce((sum, table) => sum + table.recordCount, 0),
          connectionStatus: 'active'
        }
      };
    } catch (error) {
      this.logger.error('🔐 [DATABASE-SERVICE] Health check failed:', error);
      return {
        success: false,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Database connection failed'
      };
    }
  }

  /**
   * 🔐 Get available table names (safe metadata only)
   */
  async getAvailableTables() {
    const tables = [
      { name: 'users', description: 'User accounts and profiles' },
      { name: 'organizational_units', description: 'Organizational structure' },
      { name: 'assessment_templates', description: 'Assessment templates' },
      { name: 'assessment_instances', description: 'Assessment instances' },
      { name: 'skillsets', description: 'Skill definitions' },
      { name: 'team_members', description: 'Team membership data' }
    ];

    return {
      success: true,
      data: { tables }
    };
  }

  /**
   * 🔐 Get safe table statistics (counts only, no data)
   */
  private async getTableStatistics() {
    const stats = [];

    try {
      stats.push({
        name: 'users',
        recordCount: await this.usersRepository.count(),
        lastUpdated: new Date().toISOString()
      });

      stats.push({
        name: 'organizational_units',
        recordCount: await this.orgUnitsRepository.count(),
        lastUpdated: new Date().toISOString()
      });

      stats.push({
        name: 'assessment_templates',
        recordCount: await this.templatesRepository.count(),
        lastUpdated: new Date().toISOString()
      });

      stats.push({
        name: 'assessment_instances',
        recordCount: await this.assessmentInstancesRepository.count(),
        lastUpdated: new Date().toISOString()
      });

      stats.push({
        name: 'skillsets',
        recordCount: await this.skillsetsRepository.count(),
        lastUpdated: new Date().toISOString()
      });

      stats.push({
        name: 'team_members',
        recordCount: await this.teamMembersRepository.count(),
        lastUpdated: new Date().toISOString()
      });

    } catch (error) {
      this.logger.error('🔐 [DATABASE-SERVICE] Error getting table statistics:', error);
    }

    return stats;
  }

  // REMOVED: All unsafe database manipulation methods
  // - updateRecord() - Generic record updates pose security risks
  // - deleteRecord() - Generic record deletion pose security risks  
  // - createRecord() - Generic record creation pose security risks
  // - getRepository() - Generic repository access poses security risks
  // - getTableColumns() - Generic schema access poses security risks
  // 
  // These operations should be handled by specific entity services with:
  // - Proper input validation
  // - Business logic enforcement  
  // - Authorization checks
  // - Audit logging
  // - Data integrity constraints
}
