/**
 * 🔐 NIS2-COMPLIANT: Enterprise Configuration DTOs
 * Data transfer objects for enterprise configuration management
 */

import { IsString, IsOptional, IsObject, IsNotEmpty } from 'class-validator';

export class CreateEnterpriseConfigDto {
  @IsString()
  @IsNotEmpty()
  organizationName: string;

  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;

  @IsOptional()
  @IsObject()
  features?: Record<string, boolean>;

  @IsOptional()
  @IsObject()
  branding?: Record<string, any>;

  @IsOptional()
  @IsObject()
  compliance?: Record<string, any>;
}

export class UpdateEnterpriseConfigDto {
  @IsOptional()
  @IsString()
  organizationName?: string;

  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;

  @IsOptional()
  @IsObject()
  features?: Record<string, boolean>;

  @IsOptional()
  @IsObject()
  branding?: Record<string, any>;

  @IsOptional()
  @IsObject()
  compliance?: Record<string, any>;
}

export class CreateOrganizationConfigDto {
  @IsString()
  @IsNotEmpty()
  organizationName: string;

  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;

  @IsOptional()
  @IsObject()
  branding?: Record<string, any>;
}

export class UpdateOrganizationConfigDto {
  @IsOptional()
  @IsString()
  organizationName?: string;

  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;

  @IsOptional()
  @IsObject()
  branding?: Record<string, any>;
}

export class CreateUserPreferencesDto {
  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsObject()
  preferences: Record<string, any>;
}

export class UpdateUserPreferencesDto {
  @IsOptional()
  @IsObject()
  preferences?: Record<string, any>;
}

export class CreateComplianceRuleDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsNotEmpty()
  ruleType: string;

  @IsObject()
  configuration: Record<string, any>;
}
