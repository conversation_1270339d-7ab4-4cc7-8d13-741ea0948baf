import { Controller, Get, Put, Post, Delete, Body, Param, UseGuards, Request, HttpStatus, HttpException } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { EnterpriseConfigService } from './enterprise-config.service';
import {
  CreateOrganizationConfigDto,
  UpdateOrganizationConfigDto,
  CreateUserPreferencesDto,
  UpdateUserPreferencesDto,
  CreateComplianceRuleDto
} from './dto/enterprise-config.dto';

// 🔐 NIS2-COMPLIANT: Enterprise configuration controller
// Provides organization-specific dashboard customization and role-based views

@Controller('enterprise-config')
@UseGuards(JwtAuthGuard, RolesGuard)
export class EnterpriseConfigController {
  constructor(private readonly enterpriseConfigService: EnterpriseConfigService) { }

  // Organization Configuration Endpoints

  @Get('organization')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async getOrganizationConfig(@Request() req) {
    try {
      const config = await this.enterpriseConfigService.getOrganizationConfig();

      // Log access for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'organization_config',
        'read'
      );

      return {
        success: true,
        data: config
      };
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve organization configuration',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('organization')
  @Roles(UserRole.HR_ADMIN)
  async updateOrganizationConfig(
    @Body() updateDto: UpdateOrganizationConfigDto,
    @Request() req
  ) {
    try {
      const updatedConfig = await this.enterpriseConfigService.updateOrganizationConfig(updateDto);

      // Log configuration change for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'organization_config',
        'update',
        { changes: updateDto }
      );

      return {
        success: true,
        data: updatedConfig,
        message: 'Organization configuration updated successfully'
      };
    } catch (error) {
      throw new HttpException(
        'Failed to update organization configuration',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('organization')
  @Roles(UserRole.HR_ADMIN)
  async createOrganizationConfig(
    @Body() createDto: CreateOrganizationConfigDto,
    @Request() req
  ) {
    try {
      const config = await this.enterpriseConfigService.createOrganizationConfig(createDto);

      // Log configuration creation for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'organization_config',
        'create',
        { config: createDto }
      );

      return {
        success: true,
        data: config,
        message: 'Organization configuration created successfully'
      };
    } catch (error) {
      throw new HttpException(
        'Failed to create organization configuration',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // User Preferences Endpoints

  @Get('users/:userId/preferences')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async getUserPreferences(@Param('userId') userId: string, @Request() req) {
    try {
      // Check if user can access these preferences
      if (req.user.id !== parseInt(userId) && !['admin', 'manager'].includes(req.user.role)) {
        throw new HttpException('Unauthorized access to user preferences', HttpStatus.FORBIDDEN);
      }

      const preferences = await this.enterpriseConfigService.getUserPreferences(userId);

      // Log access for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'user_preferences',
        'read',
        { targetUserId: userId }
      );

      return {
        success: true,
        data: preferences
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to retrieve user preferences',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('users/:userId/preferences')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async updateUserPreferences(
    @Param('userId') userId: string,
    @Body() updateDto: UpdateUserPreferencesDto,
    @Request() req
  ) {
    try {
      // Check if user can update these preferences
      if (req.user.id !== parseInt(userId) && !['admin'].includes(req.user.role)) {
        throw new HttpException('Unauthorized to update user preferences', HttpStatus.FORBIDDEN);
      }

      const updatedPreferences = await this.enterpriseConfigService.updateUserPreferences(
        userId,
        updateDto.preferences || {},
        req.user.id
      );

      // Log preference change for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'user_preferences',
        'update',
        { targetUserId: userId, changes: updateDto }
      );

      return {
        success: true,
        data: updatedPreferences,
        message: 'User preferences updated successfully'
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to update user preferences',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('users/:userId/preferences')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async createUserPreferences(
    @Param('userId') userId: string,
    @Body() createDto: CreateUserPreferencesDto,
    @Request() req
  ) {
    try {
      const preferences = await this.enterpriseConfigService.createUserPreferences(
        userId,
        createDto.preferences || {},
        req.user.id
      );

      // Log preference creation for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'user_preferences',
        'create',
        { targetUserId: userId, preferences: createDto }
      );

      return {
        success: true,
        data: preferences,
        message: 'User preferences created successfully'
      };
    } catch (error) {
      throw new HttpException(
        'Failed to create user preferences',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Role-based Configuration Endpoints

  @Get('roles/:role/config')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async getRoleBasedConfig(@Param('role') role: string, @Request() req) {
    try {
      // Validate role access
      if (req.user.role !== role && !['admin'].includes(req.user.role)) {
        throw new HttpException('Unauthorized access to role configuration', HttpStatus.FORBIDDEN);
      }

      const roleConfig = await this.enterpriseConfigService.getRoleBasedConfig(role);

      // Log access for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'role_config',
        'read',
        { targetRole: role }
      );

      return {
        success: true,
        data: roleConfig
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to retrieve role configuration',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Compliance and Audit Endpoints

  @Get('compliance/rules')
  @Roles(UserRole.HR_ADMIN)
  async getComplianceRules(@Request() req) {
    try {
      const rules = await this.enterpriseConfigService.getComplianceRules();

      // Log access for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'compliance_rules',
        'read'
      );

      return {
        success: true,
        data: rules
      };
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve compliance rules',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('compliance/rules')
  @Roles(UserRole.HR_ADMIN)
  async createComplianceRule(
    @Body() createDto: CreateComplianceRuleDto,
    @Request() req
  ) {
    try {
      const rule = await this.enterpriseConfigService.createComplianceRule(
        createDto,
        req.user.id
      );

      // Log rule creation for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'compliance_rules',
        'create',
        { rule: createDto }
      );

      return {
        success: true,
        data: rule,
        message: 'Compliance rule created successfully'
      };
    } catch (error) {
      throw new HttpException(
        'Failed to create compliance rule',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Feature Flags Endpoint

  @Get('features')
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async getFeatureFlags(@Request() req) {
    try {
      const features = await this.enterpriseConfigService.getFeatureFlags();

      // Log access for audit purposes
      await this.enterpriseConfigService.logConfigAccess(
        req.user.id,
        'feature_flags',
        'read'
      );

      return {
        success: true,
        data: features
      };
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve feature flags',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Audit Log Endpoint

  @Get('audit-logs')
  @Roles(UserRole.HR_ADMIN)
  async getAuditLogs(@Request() req) {
    try {
      const logs = await this.enterpriseConfigService.getConfigAuditLogs();

      return {
        success: true,
        data: logs
      };
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve audit logs',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
