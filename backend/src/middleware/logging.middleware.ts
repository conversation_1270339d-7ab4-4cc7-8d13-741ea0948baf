import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
// import { LoggingService } from '../modules/logging/logging.service';

@Injectable()
export class LoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(LoggingMiddleware.name);

  constructor(/* private readonly loggingService: LoggingService */) { }

  use(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();
    const { method, originalUrl, ip, headers } = req;
    const userAgent = headers['user-agent'] || '';

    // Get user ID from request if available
    const userId = (req as any).user?.id;

    // Log request start
    this.logger.log(`${method} ${originalUrl} - ${ip}`);

    // Override res.end to capture response
    const originalEnd = res.end;
    res.end = function (chunk?: any, encoding?: any): any {
      const responseTime = Date.now() - startTime;
      const { statusCode } = res;

      // Log the API request
      // Note: loggingService would need to be injected properly
      // loggingService.logApiRequest(
      //   method,
      //   originalUrl,
      //   statusCode,
      //   responseTime,
      //   userId,
      //   ip,
      //   userAgent,
      // );

      // Call original end method
      originalEnd.call(this, chunk, encoding);
    };

    next();
  }
}

// Export for use in app module
export default LoggingMiddleware;
