import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Delete,
} from '@nestjs/common';
import { LoggingService } from './logging.service';
import { LogCleanupService } from './log-cleanup.service';
import {
  BrowserError,
  LogQuery,
  LogLevel,
  LogSource,
  LogEntry
} from './interfaces/log.interface';
import { Request } from 'express';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../users/entities/user.entity';

@Controller('logs')
@UseGuards(JwtAuthGuard, RolesGuard)
export class LoggingController {
  constructor(
    private readonly loggingService: LoggingService,
    private readonly logCleanupService: LogCleanupService,
  ) { }

  @Post('browser-error')
  @HttpCode(HttpStatus.NO_CONTENT)
  async logBrowserError(
    @Body() browserError: BrowserError,
    @Req() req: Request,
  ): Promise<void> {
    // Extract user info from request if available
    const userId = (req as any).user?.id;
    const ip = req.ip || req.connection.remoteAddress;

    // Enhance browser error with server-side info
    const enhancedError: BrowserError = {
      ...browserError,
      timestamp: new Date(browserError.timestamp),
      userId: userId || browserError.userId,
    };

    await this.loggingService.logBrowserError(enhancedError, userId);
  }

  @Post('custom')
  @HttpCode(HttpStatus.NO_CONTENT)
  async logCustomEntry(
    @Body() logData: {
      level: LogLevel;
      source: LogSource;
      message: string;
      details?: any;
      component?: string;
      action?: string;
    },
    @Req() req: Request,
  ): Promise<void> {
    const userId = (req as any).user?.id;
    const ip = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];

    await this.loggingService.logEntry({
      ...logData,
      userId,
      ip,
      userAgent,
      url: req.url,
      method: req.method,
    });
  }

  @Get('query')
  async queryLogs(
    @Query('level') level?: LogLevel,
    @Query('source') source?: LogSource,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('userId') userId?: string,
    @Query('component') component?: string,
    @Query('search') search?: string,
    @Query('limit') limit?: string,
    @Query('offset') offset?: string,
  ): Promise<LogEntry[]> {
    const query: LogQuery = {
      level,
      source,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      userId,
      component,
      search,
      limit: limit ? parseInt(limit, 10) : undefined,
      offset: offset ? parseInt(offset, 10) : undefined,
    };

    return this.loggingService.queryLogs(query);
  }

  @Get('stats')
  async getLogStats() {
    return this.loggingService.getLogStats();
  }

  @Get('directory-stats')
  async getDirectoryStats() {
    return this.logCleanupService.getLogDirectoryStats();
  }

  @Post('cleanup')
  async manualCleanup() {
    return this.logCleanupService.manualCleanup();
  }

  @Delete('old')
  async deleteOldLogs(
    @Query('days') days?: string,
  ): Promise<{ deletedCount: number; message: string }> {
    const maxAge = days ? parseInt(days, 10) : 30;

    // This would need to be implemented in the cleanup service
    // For now, trigger manual cleanup
    const result = await this.logCleanupService.manualCleanup();

    return {
      deletedCount: result.deletedFiles.length,
      message: `Deleted ${result.deletedFiles.length} old log files`,
    };
  }

  @Get('health')
  async getLoggingHealth() {
    const stats = await this.logCleanupService.getLogDirectoryStats();
    const logStats = await this.loggingService.getLogStats();

    return {
      status: 'healthy',
      logDirectory: {
        totalFiles: stats.totalFiles,
        totalSizeMB: Math.round(stats.totalSize / (1024 * 1024) * 100) / 100,
        oldestFile: stats.oldestFile,
        newestFile: stats.newestFile,
      },
      recentActivity: {
        totalLogs: logStats.totalLogs,
        errorCount: logStats.errorCount,
        warnCount: logStats.warnCount,
      },
      timestamp: new Date(),
    };
  }
}
