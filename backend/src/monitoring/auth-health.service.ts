import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, AccountStatus } from '../users/entities/user.entity';
import { SecurityAuditLog, SecurityEventType, SecuritySeverity } from '../security/entities/security-audit-log.entity';

/**
 * 🔐 AUTHENTICATION HEALTH MONITORING SERVICE
 * 
 * Continuously monitors the authentication system health and alerts
 * on any issues that could indicate system compromise or failure.
 * 
 * Features:
 * - Real-time authentication metrics
 * - Anomaly detection
 * - Security incident alerting
 * - Performance monitoring
 * - Health status reporting
 */

export interface AuthHealthMetrics {
  timestamp: Date;
  totalUsers: number;
  activeUsers: number;
  lockedAccounts: number;
  failedLogins24h: number;
  successfulLogins24h: number;
  averageLoginTime: number;
  securityIncidents24h: number;
  systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  alerts: string[];
}

@Injectable()
export class AuthHealthService {
  private readonly logger = new Logger(AuthHealthService.name);
  private lastMetrics: AuthHealthMetrics | null = null;

  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(SecurityAuditLog)
    private auditRepository: Repository<SecurityAuditLog>,
  ) { }

  /**
   * 🔐 Get current authentication system health metrics
   */
  async getHealthMetrics(): Promise<AuthHealthMetrics> {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    try {
      // Basic user metrics
      const totalUsers = await this.usersRepository.count();
      const activeUsers = await this.usersRepository.count({
        where: { isActive: true }
      });
      const lockedAccounts = await this.usersRepository.count({
        where: { accountStatus: AccountStatus.LOCKED }
      });

      // Authentication metrics from audit logs
      const failedLogins24h = await this.auditRepository.count({
        where: {
          eventType: SecurityEventType.LOGIN_FAILURE,
          timestamp: { $gte: yesterday } as any
        }
      });

      const successfulLogins24h = await this.auditRepository.count({
        where: {
          eventType: SecurityEventType.LOGIN_SUCCESS,
          timestamp: { $gte: yesterday } as any
        }
      });

      const securityIncidents24h = await this.auditRepository.count({
        where: {
          severity: SecuritySeverity.HIGH,
          timestamp: { $gte: yesterday } as any
        }
      });

      // Calculate average login time (mock for now)
      const averageLoginTime = await this.calculateAverageLoginTime();

      // Determine system health and generate alerts
      const { systemHealth, alerts } = this.assessSystemHealth({
        totalUsers,
        activeUsers,
        lockedAccounts,
        failedLogins24h,
        successfulLogins24h,
        securityIncidents24h,
        averageLoginTime
      });

      const metrics: AuthHealthMetrics = {
        timestamp: now,
        totalUsers,
        activeUsers,
        lockedAccounts,
        failedLogins24h,
        successfulLogins24h,
        averageLoginTime,
        securityIncidents24h,
        systemHealth,
        alerts
      };

      this.lastMetrics = metrics;
      return metrics;

    } catch (error) {
      this.logger.error('🔐 [AUTH-HEALTH] Failed to collect metrics:', error);
      return {
        timestamp: now,
        totalUsers: 0,
        activeUsers: 0,
        lockedAccounts: 0,
        failedLogins24h: 0,
        successfulLogins24h: 0,
        averageLoginTime: 0,
        securityIncidents24h: 0,
        systemHealth: 'CRITICAL',
        alerts: ['Failed to collect authentication metrics']
      };
    }
  }

  /**
   * 🔐 Assess system health based on metrics
   */
  private assessSystemHealth(metrics: Partial<AuthHealthMetrics>): {
    systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL';
    alerts: string[];
  } {
    const alerts: string[] = [];
    let systemHealth: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';

    // Critical alerts
    if (metrics.securityIncidents24h > 5) {
      alerts.push(`High security incidents: ${metrics.securityIncidents24h} in 24h`);
      systemHealth = 'CRITICAL';
    }

    if (metrics.averageLoginTime > 5000) {
      alerts.push(`Slow login performance: ${metrics.averageLoginTime}ms average`);
      systemHealth = 'CRITICAL';
    }

    // Warning alerts
    if (metrics.failedLogins24h > metrics.successfulLogins24h * 0.5) {
      alerts.push(`High failed login ratio: ${metrics.failedLogins24h} failed vs ${metrics.successfulLogins24h} successful`);
      if (systemHealth === 'HEALTHY') systemHealth = 'WARNING';
    }

    if (metrics.lockedAccounts > metrics.totalUsers * 0.1) {
      alerts.push(`High locked account ratio: ${metrics.lockedAccounts}/${metrics.totalUsers} accounts locked`);
      if (systemHealth === 'HEALTHY') systemHealth = 'WARNING';
    }

    if (metrics.activeUsers < metrics.totalUsers * 0.8) {
      alerts.push(`Low active user ratio: ${metrics.activeUsers}/${metrics.totalUsers} users active`);
      if (systemHealth === 'HEALTHY') systemHealth = 'WARNING';
    }

    return { systemHealth, alerts };
  }

  /**
   * 🔐 Calculate average login time from recent audit logs
   */
  private async calculateAverageLoginTime(): Promise<number> {
    // This would typically analyze login request timing
    // For now, return a mock value
    return Math.random() * 1000 + 500; // 500-1500ms
  }

  /**
   * 🔐 Automated health check every 5 minutes
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async performHealthCheck(): Promise<void> {
    try {
      const metrics = await this.getHealthMetrics();

      if (metrics.systemHealth === 'CRITICAL') {
        this.logger.error('🔐 [AUTH-HEALTH] CRITICAL: Authentication system health is critical!', {
          alerts: metrics.alerts,
          metrics: metrics
        });

        // In production, trigger alerts (email, Slack, etc.)
        await this.triggerCriticalAlert(metrics);

      } else if (metrics.systemHealth === 'WARNING') {
        this.logger.warn('🔐 [AUTH-HEALTH] WARNING: Authentication system has warnings', {
          alerts: metrics.alerts
        });

      } else {
        this.logger.log('🔐 [AUTH-HEALTH] System healthy', {
          successfulLogins: metrics.successfulLogins24h,
          failedLogins: metrics.failedLogins24h
        });
      }

    } catch (error) {
      this.logger.error('🔐 [AUTH-HEALTH] Health check failed:', error);
    }
  }

  /**
   * 🔐 Trigger critical alert (implement based on your alerting system)
   */
  private async triggerCriticalAlert(metrics: AuthHealthMetrics): Promise<void> {
    // Implement your alerting mechanism here:
    // - Send email to administrators
    // - Post to Slack channel
    // - Create incident in monitoring system
    // - Send SMS alerts

    this.logger.error('🔐 [CRITICAL-ALERT] Authentication system requires immediate attention!', {
      timestamp: metrics.timestamp,
      systemHealth: metrics.systemHealth,
      alerts: metrics.alerts,
      metrics: {
        failedLogins24h: metrics.failedLogins24h,
        securityIncidents24h: metrics.securityIncidents24h,
        lockedAccounts: metrics.lockedAccounts,
        averageLoginTime: metrics.averageLoginTime
      }
    });
  }

  /**
   * 🔐 Get health status for API endpoints
   */
  async getHealthStatus(): Promise<{
    status: string;
    timestamp: Date;
    uptime: number;
    metrics?: AuthHealthMetrics;
  }> {
    const metrics = await this.getHealthMetrics();

    return {
      status: metrics.systemHealth,
      timestamp: new Date(),
      uptime: process.uptime(),
      metrics: metrics
    };
  }

  /**
   * 🔐 Get authentication system statistics
   */
  async getAuthStats(): Promise<any> {
    const metrics = await this.getHealthMetrics();

    return {
      users: {
        total: metrics.totalUsers,
        active: metrics.activeUsers,
        locked: metrics.lockedAccounts,
        activePercentage: Math.round((metrics.activeUsers / metrics.totalUsers) * 100)
      },
      authentication: {
        successfulLogins24h: metrics.successfulLogins24h,
        failedLogins24h: metrics.failedLogins24h,
        successRate: Math.round((metrics.successfulLogins24h / (metrics.successfulLogins24h + metrics.failedLogins24h)) * 100),
        averageLoginTime: metrics.averageLoginTime
      },
      security: {
        incidents24h: metrics.securityIncidents24h,
        systemHealth: metrics.systemHealth,
        alerts: metrics.alerts
      }
    };
  }
}
