import { Controller, Get, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { ProductionOptimizationService } from './production-optimization.service';

/**
 * 🔐 Production Optimization Controller
 * Provides endpoints for system optimization and production readiness validation
 * Restricted to CEO and HR_ADMIN roles only
 */
@Controller('optimization')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.CEO, UserRole.HR_ADMIN)
export class OptimizationController {
  constructor(
    private readonly optimizationService: ProductionOptimizationService,
  ) { }

  /**
   * 🔐 Get production readiness validation
   */
  @Get('production-readiness')
  async getProductionReadiness() {
    return await this.optimizationService.validateProductionReadiness();
  }

  /**
   * 🔐 Get environment-specific optimizations
   */
  @Get('environment')
  getEnvironmentOptimizations() {
    return this.optimizationService.getEnvironmentOptimizations();
  }

  /**
   * 🔐 Get optimization status summary
   */
  @Get('status')
  async getOptimizationStatus() {
    const readiness = await this.optimizationService.validateProductionReadiness();
    const environment = this.optimizationService.getEnvironmentOptimizations();

    return {
      timestamp: new Date().toISOString(),
      environment: environment.environment,
      overallStatus: readiness.overallStatus,
      optimizationScore: readiness.score,
      summary: {
        totalValidations: Object.keys(readiness.validations).length,
        passedValidations: Object.values(readiness.validations).filter((v: any) => v.status === 'COMPLIANT').length,
        criticalIssues: Object.values(readiness.validations).reduce((acc: number, v: any) => acc + (v.issues?.length || 0), 0),
        warnings: Object.values(readiness.validations).reduce((acc: number, v: any) => acc + (v.warnings?.length || 0), 0)
      },
      recommendations: readiness.recommendations.slice(0, 5), // Top 5 recommendations
      nextSteps: this.getNextSteps(readiness.overallStatus)
    };
  }

  /**
   * 🔐 Get next steps based on optimization status
   */
  private getNextSteps(status: string): string[] {
    if (status === 'PRODUCTION_READY') {
      return [
        'System is production ready',
        'Continue monitoring performance metrics',
        'Regular security assessments recommended',
        'Maintain compliance documentation',
        'Schedule next optimization review'
      ];
    } else {
      return [
        'Address critical issues before production deployment',
        'Review and implement warning recommendations',
        'Validate environment configuration',
        'Test security configurations',
        'Re-run production readiness validation'
      ];
    }
  }
}
