import { Module } from '@nestjs/common';
import { ProductionOptimizationService } from './production-optimization.service';
import { OptimizationController } from './optimization.controller';

/**
 * 🔐 Production Optimization Module
 * Provides optimization services for both development and production environments
 */
@Module({
  providers: [ProductionOptimizationService],
  controllers: [OptimizationController],
  exports: [ProductionOptimizationService],
})
export class OptimizationModule { }
