import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 🔐 Production Optimization Service
 * Ensures optimal performance and security for both development and production environments
 */
@Injectable()
export class ProductionOptimizationService {
  private readonly logger = new Logger(ProductionOptimizationService.name);
  private readonly isProduction: boolean;
  private readonly isDevelopment: boolean;

  constructor(private readonly configService: ConfigService) {
    this.isProduction = this.configService.get('NODE_ENV') === 'production';
    this.isDevelopment = this.configService.get('NODE_ENV') === 'development';
  }

  /**
   * 🔐 Validate production readiness
   */
  async validateProductionReadiness(): Promise<any> {
    this.logger.log('🔐 [OPTIMIZATION] Validating production readiness...');

    const validations = {
      environment: this.validateEnvironmentConfiguration(),
      security: this.validateSecurityConfiguration(),
      performance: this.validatePerformanceConfiguration(),
      monitoring: this.validateMonitoringConfiguration(),
      database: this.validateDatabaseConfiguration(),
      logging: this.validateLoggingConfiguration()
    };

    const overallStatus = Object.values(validations).every(v => v.status === 'COMPLIANT') 
      ? 'PRODUCTION_READY' 
      : 'NEEDS_ATTENTION';

    const report = {
      timestamp: new Date().toISOString(),
      environment: this.configService.get('NODE_ENV'),
      overallStatus,
      validations,
      recommendations: this.generateRecommendations(validations),
      score: this.calculateOptimizationScore(validations)
    };

    this.logger.log(`🔐 [OPTIMIZATION] Production readiness: ${overallStatus}`);
    return report;
  }

  /**
   * 🔐 Validate environment configuration
   */
  private validateEnvironmentConfiguration(): any {
    const issues = [];
    const warnings = [];

    // Check required environment variables
    const requiredVars = [
      'NODE_ENV',
      'PORT',
      'FRONTEND_URL',
      'DB_HOST',
      'DB_USERNAME',
      'DB_PASSWORD',
      'DB_NAME',
      'JWT_SECRET'
    ];

    for (const varName of requiredVars) {
      if (!this.configService.get(varName)) {
        issues.push(`Missing required environment variable: ${varName}`);
      }
    }

    // Production-specific checks
    if (this.isProduction) {
      if (!this.configService.get('FRONTEND_URL')?.startsWith('https://')) {
        issues.push('FRONTEND_URL must use HTTPS in production');
      }

      if (this.configService.get('JWT_SECRET')?.length < 32) {
        issues.push('JWT_SECRET must be at least 32 characters in production');
      }

      if (!this.configService.get('FORCE_HTTPS')) {
        warnings.push('Consider enabling FORCE_HTTPS in production');
      }
    }

    return {
      status: issues.length === 0 ? 'COMPLIANT' : 'NON_COMPLIANT',
      issues,
      warnings,
      description: 'Environment variables and configuration validation'
    };
  }

  /**
   * 🔐 Validate security configuration
   */
  private validateSecurityConfiguration(): any {
    const issues = [];
    const warnings = [];

    // Security headers validation
    const securityFeatures = {
      httpsEnforcement: this.isProduction || !!this.configService.get('FORCE_HTTPS'),
      secureHeaders: true, // Always enabled
      rateLimiting: true, // Always enabled
      auditLogging: true, // Always enabled
      mfaSupport: true, // Always enabled
      passwordPolicy: true, // Always enabled
      sessionSecurity: true, // Always enabled
      csrfProtection: true, // Always enabled
      corsRestriction: true // Always enabled
    };

    // Check for security misconfigurations
    if (this.isProduction && !securityFeatures.httpsEnforcement) {
      issues.push('HTTPS enforcement must be enabled in production');
    }

    if (this.configService.get('DEBUG_MODE') === 'true' && this.isProduction) {
      issues.push('DEBUG_MODE must be disabled in production');
    }

    if (this.configService.get('DISABLE_RATE_LIMITING') === 'true') {
      issues.push('Rate limiting must not be disabled');
    }

    return {
      status: issues.length === 0 ? 'COMPLIANT' : 'NON_COMPLIANT',
      issues,
      warnings,
      features: securityFeatures,
      description: 'Security configuration and features validation'
    };
  }

  /**
   * 🔐 Validate performance configuration
   */
  private validatePerformanceConfiguration(): any {
    const issues = [];
    const warnings = [];

    // Database performance settings
    const dbConnectionLimit = parseInt(this.configService.get('DB_CONNECTION_LIMIT', '10'));
    const dbPoolSize = parseInt(this.configService.get('DB_POOL_SIZE', '5'));

    if (this.isProduction) {
      if (dbConnectionLimit < 10) {
        warnings.push('Consider increasing DB_CONNECTION_LIMIT for production (recommended: 20+)');
      }

      if (dbPoolSize < 5) {
        warnings.push('Consider increasing DB_POOL_SIZE for production (recommended: 10+)');
      }

      if (!this.configService.get('DB_CACHE_DURATION')) {
        warnings.push('Consider enabling database query caching for production');
      }
    }

    // Memory and clustering
    const clusterEnabled = this.configService.get('CLUSTER_ENABLED') === 'true';
    const maxMemoryUsage = parseInt(this.configService.get('MAX_MEMORY_USAGE', '512'));

    if (this.isProduction && !clusterEnabled) {
      warnings.push('Consider enabling clustering for production scalability');
    }

    if (this.isProduction && maxMemoryUsage < 1024) {
      warnings.push('Consider increasing MAX_MEMORY_USAGE for production (recommended: 1024MB+)');
    }

    return {
      status: issues.length === 0 ? 'COMPLIANT' : 'NON_COMPLIANT',
      issues,
      warnings,
      configuration: {
        dbConnectionLimit,
        dbPoolSize,
        clusterEnabled,
        maxMemoryUsage,
        cacheEnabled: !!this.configService.get('DB_CACHE_DURATION')
      },
      description: 'Performance optimization and scaling configuration'
    };
  }

  /**
   * 🔐 Validate monitoring configuration
   */
  private validateMonitoringConfiguration(): any {
    const issues = [];
    const warnings = [];

    const monitoringFeatures = {
      securityEvents: true, // Always enabled
      performanceMetrics: true, // Always enabled
      errorTracking: true, // Always enabled
      auditTrails: true, // Always enabled
      healthChecks: true // Always enabled
    };

    // Production monitoring requirements
    if (this.isProduction) {
      if (!this.configService.get('SECURITY_LOG_ENABLED')) {
        warnings.push('Consider enabling SECURITY_LOG_ENABLED for production');
      }

      if (!this.configService.get('MEMORY_MONITORING_ENABLED')) {
        warnings.push('Consider enabling MEMORY_MONITORING_ENABLED for production');
      }
    }

    return {
      status: issues.length === 0 ? 'COMPLIANT' : 'NON_COMPLIANT',
      issues,
      warnings,
      features: monitoringFeatures,
      description: 'Monitoring and alerting configuration validation'
    };
  }

  /**
   * 🔐 Validate database configuration
   */
  private validateDatabaseConfiguration(): any {
    const issues = [];
    const warnings = [];

    // Database security settings
    if (this.isProduction) {
      if (this.configService.get('DB_SSL_REJECT_UNAUTHORIZED') !== 'true') {
        warnings.push('Consider enabling DB_SSL_REJECT_UNAUTHORIZED for production');
      }

      if (!this.configService.get('DB_PASSWORD') || this.configService.get('DB_PASSWORD').length < 12) {
        issues.push('Database password must be at least 12 characters in production');
      }
    }

    // Database optimization settings
    const retryAttempts = parseInt(this.configService.get('DB_RETRY_ATTEMPTS', '3'));
    const maxQueryTime = parseInt(this.configService.get('DB_MAX_QUERY_TIME', '5000'));

    if (retryAttempts < 3) {
      warnings.push('Consider setting DB_RETRY_ATTEMPTS to at least 3');
    }

    if (maxQueryTime > 10000) {
      warnings.push('DB_MAX_QUERY_TIME is high, consider optimizing queries');
    }

    return {
      status: issues.length === 0 ? 'COMPLIANT' : 'NON_COMPLIANT',
      issues,
      warnings,
      configuration: {
        retryAttempts,
        maxQueryTime,
        sslEnabled: this.configService.get('DB_SSL_REJECT_UNAUTHORIZED') === 'true',
        cacheEnabled: !!this.configService.get('DB_CACHE_DURATION')
      },
      description: 'Database configuration and optimization validation'
    };
  }

  /**
   * 🔐 Validate logging configuration
   */
  private validateLoggingConfiguration(): any {
    const issues = [];
    const warnings = [];

    const logLevel = this.configService.get('LOG_LEVEL', 'info');
    const auditRetentionDays = parseInt(this.configService.get('AUDIT_LOG_RETENTION_DAYS', '2555'));

    // Production logging requirements
    if (this.isProduction) {
      if (logLevel === 'debug' || logLevel === 'trace') {
        warnings.push('Consider using info or warn log level in production');
      }

      if (auditRetentionDays < 2555) { // 7 years for NIS2 compliance
        issues.push('AUDIT_LOG_RETENTION_DAYS must be at least 2555 days (7 years) for compliance');
      }
    }

    return {
      status: issues.length === 0 ? 'COMPLIANT' : 'NON_COMPLIANT',
      issues,
      warnings,
      configuration: {
        logLevel,
        auditRetentionDays,
        compressionEnabled: this.configService.get('LOG_COMPRESSION_ENABLED') === 'true',
        maxFileSize: this.configService.get('LOG_MAX_FILE_SIZE', '10485760'),
        maxFiles: this.configService.get('LOG_MAX_FILES', '30')
      },
      description: 'Logging configuration and retention policy validation'
    };
  }

  /**
   * 🔐 Generate optimization recommendations
   */
  private generateRecommendations(validations: any): string[] {
    const recommendations = [];

    // Collect all warnings and convert to recommendations
    Object.values(validations).forEach((validation: any) => {
      if (validation.warnings) {
        recommendations.push(...validation.warnings);
      }
    });

    // Add general recommendations
    if (this.isProduction) {
      recommendations.push('Regularly review security configurations');
      recommendations.push('Monitor system performance metrics');
      recommendations.push('Keep audit logs for compliance requirements');
      recommendations.push('Implement automated backup procedures');
    } else {
      recommendations.push('Test production configurations in staging environment');
      recommendations.push('Validate all environment variables before deployment');
      recommendations.push('Review security settings for production readiness');
    }

    return recommendations.slice(0, 10); // Top 10 recommendations
  }

  /**
   * 🔐 Calculate optimization score
   */
  private calculateOptimizationScore(validations: any): number {
    let totalChecks = 0;
    let passedChecks = 0;

    Object.values(validations).forEach((validation: any) => {
      totalChecks++;
      if (validation.status === 'COMPLIANT') {
        passedChecks++;
      }
    });

    return Math.round((passedChecks / totalChecks) * 100);
  }

  /**
   * 🔐 Get environment-specific optimizations
   */
  getEnvironmentOptimizations(): any {
    return {
      environment: this.configService.get('NODE_ENV'),
      optimizations: {
        development: {
          logging: 'verbose',
          caching: 'disabled',
          compression: 'disabled',
          sourceMap: 'enabled',
          hotReload: 'enabled'
        },
        production: {
          logging: 'minimal',
          caching: 'enabled',
          compression: 'enabled',
          sourceMap: 'disabled',
          clustering: 'enabled'
        }
      },
      currentSettings: {
        isProduction: this.isProduction,
        isDevelopment: this.isDevelopment,
        logLevel: this.configService.get('LOG_LEVEL', 'info'),
        cacheEnabled: !!this.configService.get('DB_CACHE_DURATION'),
        clusterEnabled: this.configService.get('CLUSTER_ENABLED') === 'true'
      }
    };
  }
}
