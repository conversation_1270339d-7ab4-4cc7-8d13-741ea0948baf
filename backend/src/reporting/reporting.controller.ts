import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  Res,
  StreamableFile,
} from '@nestjs/common';
import { Response } from 'express';
import { ReportingService } from './reporting.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('reports')
@UseGuards(JwtAuthGuard)
export class ReportingController {
  constructor(private readonly reportingService: ReportingService) {}

  @Get('employee/:employeeId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async getEmployeeReport(
    @Param('employeeId') employeeId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
  ) {
    return this.reportingService.generateEmployeeReport(
      +employeeId,
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );
  }

  @Get('team/:teamId')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getTeamReport(
    @Param('teamId') teamId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
  ) {
    return this.reportingService.generateTeamReport(
      +teamId,
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );
  }

  @Get('organization')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  async getOrganizationReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
  ) {
    return this.reportingService.generateOrganizationReport(
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );
  }

  @Get('dashboard')
  async getDashboardMetrics(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('teamId') teamId: string,
    @Request() req,
  ) {
    return this.reportingService.getDashboardMetrics(
      { startDate, endDate, teamId: teamId ? +teamId : undefined },
      req.user.userId,
      req.user.role,
    );
  }

  @Get('trends')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getPerformanceTrends(
    @Query('period') period: 'monthly' | 'quarterly' | 'yearly',
    @Query('count') count: string,
    @Query('teamId') teamId: string,
    @Request() req,
  ) {
    return this.reportingService.getPerformanceTrends(
      { period: period || 'monthly', count: count ? +count : 12, teamId: teamId ? +teamId : undefined },
      req.user.userId,
      req.user.role,
    );
  }

  @Get('benchmarks')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async getTeamBenchmarks(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
  ) {
    // For now, return mock benchmark data
    // In a real implementation, this would calculate actual team benchmarks
    const mockBenchmarks = [
      {
        teamId: 1,
        teamName: 'Development Team',
        teamSize: 8,
        averageScore: 85.2,
        completionRate: 92.5,
        assessmentCount: 24,
      },
      {
        teamId: 2,
        teamName: 'Marketing Team',
        teamSize: 6,
        averageScore: 78.9,
        completionRate: 88.3,
        assessmentCount: 18,
      },
      {
        teamId: 3,
        teamName: 'Sales Team',
        teamSize: 10,
        averageScore: 82.1,
        completionRate: 95.0,
        assessmentCount: 30,
      },
    ];

    return mockBenchmarks;
  }

  @Post('export/csv')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  async exportAssessmentsCsv(
    @Body() exportParams: { 
      startDate?: string;
      endDate?: string;
      employeeIds?: number[];
      teamIds?: number[];
      statuses?: string[];
    },
    @Res({ passthrough: true }) res: Response,
    @Request() req,
  ): Promise<StreamableFile> {
    const { file, filename } = await this.reportingService.exportAssessmentsCsv(
      exportParams,
      req.user.userId,
      req.user.role,
    );

    res.set({
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="${filename}"`,
    });

    return new StreamableFile(file);
  }

  // PDF Export endpoints (simplified implementation)
  @Get('employee/:employeeId/export/pdf')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER, UserRole.EMPLOYEE)
  async exportEmployeeReportPdf(
    @Param('employeeId') employeeId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    // Get the report data
    const reportData = await this.reportingService.generateEmployeeReport(
      +employeeId,
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );

    // Create a simple PDF (in a real implementation, you'd use a proper PDF library)
    const pdfContent = `Employee Report
Employee ID: ${reportData.employeeId}
Assessment Count: ${reportData.assessmentCount}
Average Score: ${reportData.averageScore}
Generated: ${new Date().toISOString()}`;

    const filename = `employee_report_${employeeId}_${new Date().toISOString().split('T')[0]}.pdf`;

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
    });

    return new StreamableFile(Buffer.from(pdfContent));
  }

  @Get('team/:teamId/export/pdf')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  async exportTeamReportPdf(
    @Param('teamId') teamId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    // Get the report data
    const reportData = await this.reportingService.generateTeamReport(
      +teamId,
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );

    // Create a simple PDF
    const pdfContent = `Team Report
Team ID: ${reportData.teamId}
Assessment Count: ${reportData.assessmentCount}
Average Score: ${reportData.averageScore}
Team Members: ${reportData.employeeScores.length}
Generated: ${new Date().toISOString()}`;

    const filename = `team_report_${teamId}_${new Date().toISOString().split('T')[0]}.pdf`;

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
    });

    return new StreamableFile(Buffer.from(pdfContent));
  }

  @Get('organization/export/pdf')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  async exportOrganizationReportPdf(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Request() req,
    @Res({ passthrough: true }) res: Response,
  ): Promise<StreamableFile> {
    // Get the report data
    const reportData = await this.reportingService.generateOrganizationReport(
      startDate,
      endDate,
      req.user.userId,
      req.user.role,
    );

    // Create a simple PDF
    const pdfContent = `Organization Report
Total Assessments: ${reportData.assessmentCount}
Average Score: ${reportData.averageScore}
Completion Rate: ${reportData.completionRate}%
Generated: ${new Date().toISOString()}`;

    const filename = `organization_report_${new Date().toISOString().split('T')[0]}.pdf`;

    res.set({
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${filename}"`,
    });

    return new StreamableFile(Buffer.from(pdfContent));
  }
}
