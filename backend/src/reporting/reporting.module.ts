import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReportingController } from './reporting.controller';
import { ReportingService } from './reporting.service';
import { AssessmentInstance } from '../assessments/entities/assessment-instance.entity';
import { AssessmentResponse } from '../assessments/entities/assessment-response.entity';
import { User } from '../users/entities/user.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AssessmentInstance,
      AssessmentResponse,
      User,
      OrganizationalUnit,
    ]),
  ],
  controllers: [ReportingController],
  providers: [ReportingService],
  exports: [ReportingService, TypeOrmModule],
})
export class ReportingModule {}
