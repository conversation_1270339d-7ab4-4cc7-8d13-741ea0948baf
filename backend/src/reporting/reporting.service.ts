import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { AssessmentInstance, AssessmentStatus } from '../assessments/entities/assessment-instance.entity';
import { AssessmentResponse } from '../assessments/entities/assessment-response.entity';
import { User, UserRole } from '../users/entities/user.entity';
import { OrganizationalUnit } from '../teams/entities/organizational-unit.entity';

@Injectable()
export class ReportingService {
  constructor(
    @InjectRepository(AssessmentInstance)
    private assessmentRepository: Repository<AssessmentInstance>,
    @InjectRepository(AssessmentResponse)
    private responseRepository: Repository<AssessmentResponse>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(OrganizationalUnit)
    private orgUnitRepository: Repository<OrganizationalUnit>,
  ) {}

  // Generate employee report with assessment summaries and trends
  async generateEmployeeReport(
    employeeId: number,
    startDate?: string,
    endDate?: string,
    userId?: number,
    userRole?: string,
  ) {
    // Check permissions
    const hasPermission = 
      userRole === UserRole.HR_ADMIN || 
      userRole === UserRole.MANAGER ||
      userId === employeeId; // Allow self-access
    
    if (!hasPermission) {
      throw new ForbiddenException('You do not have permission to access this report');
    }

    const dateFilter = this.getDateFilter(startDate, endDate);
    
    const assessments = await this.assessmentRepository.find({
      where: {
        employeeId,
        ...dateFilter,
      },
      relations: ['responses'],
      order: { assessmentDate: 'DESC' },
    });

    if (!assessments.length) {
      return {
        employeeId,
        assessmentCount: 0,
        averageScore: 0,
        scoreHistory: [],
        latestAssessment: null,
      };
    }

    // Calculate average score
    const totalScore = assessments.reduce((sum, assessment) => {
      return sum + (assessment.scorePercentage || assessment.totalScore || 0);
    }, 0);
    const averageScore = totalScore / assessments.length;

    // Create score history
    const scoreHistory = assessments.map(assessment => ({
      id: assessment.id,
      date: assessment.assessmentDate?.toISOString() || new Date().toISOString(),
      score: assessment.scorePercentage || assessment.totalScore || 0,
      status: assessment.status,
    }));

    const latestAssessment = assessments.length > 0 ? {
      id: assessments[0].id,
      date: assessments[0].assessmentDate?.toISOString() || new Date().toISOString(),
      score: assessments[0].scorePercentage || assessments[0].totalScore || 0,
      status: assessments[0].status,
    } : null;

    return {
      employeeId,
      assessmentCount: assessments.length,
      averageScore: Math.round(averageScore * 100) / 100,
      scoreHistory,
      latestAssessment,
    };
  }

  // Generate team report with team performance metrics
  async generateTeamReport(
    teamId: number,
    startDate?: string,
    endDate?: string,
    userId?: number,
    userRole?: string,
  ) {
    const hasPermission = 
      userRole === UserRole.HR_ADMIN || 
      userRole === UserRole.MANAGER;
    
    if (!hasPermission) {
      throw new ForbiddenException('You do not have permission to access this report');
    }

    const dateFilter = this.getDateFilter(startDate, endDate);
    
    // Get team members
    const teamMembers = await this.userRepository.find({
      where: { organizationalUnitId: teamId },
    });

    if (!teamMembers.length) {
      return {
        teamId,
        assessmentCount: 0,
        averageScore: 0,
        employeeScores: [],
        performanceDistribution: this.getEmptyPerformanceDistribution(),
      };
    }

    const employeeIds = teamMembers.map(member => member.id);
    
    const assessments = await this.assessmentRepository.find({
      where: {
        employeeId: In(employeeIds),
        ...dateFilter,
      },
      relations: ['responses'],
      order: { assessmentDate: 'DESC' },
    });

    // Calculate team average
    const totalScore = assessments.reduce((sum, assessment) => {
      return sum + (assessment.scorePercentage || assessment.totalScore || 0);
    }, 0);
    const averageScore = assessments.length > 0 ? totalScore / assessments.length : 0;

    // Calculate employee scores
    const employeeScores = employeeIds.map(employeeId => {
      const employeeAssessments = assessments.filter(a => a.employeeId === employeeId);
      const employeeTotal = employeeAssessments.reduce((sum, a) => sum + (a.scorePercentage || a.totalScore || 0), 0);
      const employeeAverage = employeeAssessments.length > 0 ? employeeTotal / employeeAssessments.length : 0;
      
      return {
        employeeId,
        averageScore: Math.round(employeeAverage * 100) / 100,
        assessmentCount: employeeAssessments.length,
      };
    });

    // Calculate performance distribution
    const performanceDistribution = this.calculatePerformanceDistribution(assessments);

    return {
      teamId,
      assessmentCount: assessments.length,
      averageScore: Math.round(averageScore * 100) / 100,
      employeeScores,
      performanceDistribution,
    };
  }

  // Generate organization-wide report (HR Admin only)
  async generateOrganizationReport(
    startDate?: string,
    endDate?: string,
    userId?: number,
    userRole?: string,
  ) {
    if (userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR Administrators can access organization reports');
    }

    const dateFilter = this.getDateFilter(startDate, endDate);
    
    const assessments = await this.assessmentRepository.find({
      where: dateFilter,
      relations: ['responses'],
      order: { assessmentDate: 'DESC' },
    });

    const totalAssessments = assessments.length;
    const completedAssessments = assessments.filter(a => a.status === AssessmentStatus.COMPLETED);
    const completionRate = totalAssessments > 0 ? (completedAssessments.length / totalAssessments) * 100 : 0;

    // Calculate average score
    const totalScore = completedAssessments.reduce((sum, assessment) => {
      return sum + (assessment.scorePercentage || assessment.totalScore || 0);
    }, 0);
    const averageScore = completedAssessments.length > 0 ? totalScore / completedAssessments.length : 0;

    // Status breakdown
    const statusBreakdown = {
      draft: assessments.filter(a => a.status === AssessmentStatus.DRAFT).length,
      inProgress: assessments.filter(a => a.status === AssessmentStatus.IN_PROGRESS).length,
      completed: assessments.filter(a => a.status === AssessmentStatus.COMPLETED).length,
      approved: assessments.filter(a => a.status === AssessmentStatus.APPROVED).length,
      rejected: assessments.filter(a => a.status === AssessmentStatus.REJECTED).length,
    };

    // Performance distribution
    const performanceDistribution = this.calculatePerformanceDistribution(completedAssessments);

    return {
      assessmentCount: totalAssessments,
      averageScore: Math.round(averageScore * 100) / 100,
      completionRate: Math.round(completionRate * 100) / 100,
      statusBreakdown,
      performanceDistribution,
    };
  }

  // Get dashboard metrics
  async getDashboardMetrics(
    filters: { startDate?: string; endDate?: string; teamId?: number },
    userId?: number,
    userRole?: string,
  ) {
    const dateFilter = this.getDateFilter(filters.startDate, filters.endDate);
    
    let whereClause: any = dateFilter;
    
    // If teamId is specified, filter by team members
    if (filters.teamId) {
      const teamMembers = await this.userRepository.find({
        where: { organizationalUnitId: filters.teamId },
      });
      const employeeIds = teamMembers.map(member => member.id);
      whereClause.employeeId = In(employeeIds);
    }

    const assessments = await this.assessmentRepository.find({
      where: whereClause,
      relations: ['responses'],
      order: { assessmentDate: 'DESC' },
      take: 10, // Recent assessments
    });

    const totalAssessments = assessments.length;
    const pendingAssessments = assessments.filter(a => 
      a.status === AssessmentStatus.DRAFT || a.status === AssessmentStatus.IN_PROGRESS
    ).length;
    const completedAssessments = assessments.filter(a => 
      a.status === AssessmentStatus.COMPLETED
    ).length;

    const totalScore = assessments.reduce((sum, assessment) => {
      return sum + (assessment.scorePercentage || assessment.totalScore || 0);
    }, 0);
    const averageScore = totalAssessments > 0 ? totalScore / totalAssessments : 0;

    const recentAssessments = assessments.slice(0, 5).map(assessment => ({
      id: assessment.id,
      employeeId: assessment.employeeId,
      status: assessment.status,
      score: assessment.scorePercentage || assessment.totalScore || 0,
      date: assessment.assessmentDate?.toISOString() || new Date().toISOString(),
    }));

    const performanceDistribution = this.calculatePerformanceDistribution(assessments);
    
    const statusBreakdown = {
      draft: assessments.filter(a => a.status === AssessmentStatus.DRAFT).length,
      inProgress: assessments.filter(a => a.status === AssessmentStatus.IN_PROGRESS).length,
      completed: assessments.filter(a => a.status === AssessmentStatus.COMPLETED).length,
      approved: assessments.filter(a => a.status === AssessmentStatus.APPROVED).length,
      rejected: assessments.filter(a => a.status === AssessmentStatus.REJECTED).length,
    };

    return {
      totalAssessments,
      pendingAssessments,
      completedAssessments,
      averageScore: Math.round(averageScore * 100) / 100,
      recentAssessments,
      performanceDistribution,
      statusBreakdown,
    };
  }

  // Get performance trends
  async getPerformanceTrends(
    options: { period: 'monthly' | 'quarterly' | 'yearly'; count: number; teamId?: number },
    userId?: number,
    userRole?: string,
  ) {
    const hasPermission = 
      userRole === UserRole.HR_ADMIN || 
      userRole === UserRole.MANAGER;
    
    if (!hasPermission) {
      throw new ForbiddenException('You do not have permission to access performance trends');
    }

    // For now, return mock trend data - this would need more complex date calculations
    const trends = [];
    const now = new Date();
    
    for (let i = 0; i < options.count; i++) {
      const date = new Date(now);
      if (options.period === 'monthly') {
        date.setMonth(date.getMonth() - i);
      } else if (options.period === 'quarterly') {
        date.setMonth(date.getMonth() - (i * 3));
      } else {
        date.setFullYear(date.getFullYear() - i);
      }
      
      trends.unshift({
        period: date.toISOString().substring(0, 7), // YYYY-MM format
        averageScore: Math.random() * 40 + 60, // Mock data between 60-100
        completionRate: Math.random() * 30 + 70, // Mock data between 70-100
        assessmentCount: Math.floor(Math.random() * 50) + 10, // Mock data between 10-60
      });
    }

    return trends;
  }

  // Helper method to get date filter
  private getDateFilter(startDate?: string, endDate?: string) {
    if (!startDate && !endDate) {
      return {};
    }

    const filter: any = {};
    
    if (startDate && endDate) {
      filter.assessmentDate = Between(new Date(startDate), new Date(endDate));
    } else if (startDate) {
      filter.assessmentDate = Between(new Date(startDate), new Date());
    } else if (endDate) {
      // If only endDate, go back 1 year
      const oneYearAgo = new Date(endDate);
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      filter.assessmentDate = Between(oneYearAgo, new Date(endDate));
    }

    return filter;
  }

  // Helper method to calculate performance distribution
  private calculatePerformanceDistribution(assessments: AssessmentInstance[]) {
    const distribution = {
      excellent: 0,
      good: 0,
      satisfactory: 0,
      needsImprovement: 0,
      poor: 0,
    };

    assessments.forEach(assessment => {
      const score = assessment.scorePercentage || assessment.totalScore || 0;
      if (score >= 90) {
        distribution.excellent++;
      } else if (score >= 80) {
        distribution.good++;
      } else if (score >= 70) {
        distribution.satisfactory++;
      } else if (score >= 60) {
        distribution.needsImprovement++;
      } else {
        distribution.poor++;
      }
    });

    return distribution;
  }

  // Helper method to get empty performance distribution
  private getEmptyPerformanceDistribution() {
    return {
      excellent: 0,
      good: 0,
      satisfactory: 0,
      needsImprovement: 0,
      poor: 0,
    };
  }

  // Export assessments as CSV
  async exportAssessmentsCsv(
    filters: {
      startDate?: string;
      endDate?: string;
      employeeIds?: number[];
      teamIds?: number[];
      statuses?: string[];
    },
    userId?: number,
    userRole?: string,
  ) {
    const hasPermission = userRole === UserRole.HR_ADMIN;
    
    if (!hasPermission) {
      throw new ForbiddenException('Only HR Administrators can export assessment data');
    }

    // This is a simplified implementation
    // In a real implementation, you would generate actual CSV data
    const csvData = 'Employee ID,Assessment Date,Score,Status\n';
    const filename = `assessments_export_${new Date().toISOString().split('T')[0]}.csv`;
    
    return {
      file: Buffer.from(csvData),
      filename,
    };
  }
}
