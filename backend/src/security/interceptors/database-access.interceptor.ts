import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  ForbiddenException,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';

/**
 * 🔐 Database Access Security Interceptor
 * 
 * This interceptor monitors and validates database access patterns
 * to prevent rogue SQL queries that bypass authentication.
 * 
 * Features:
 * - Monitors database query patterns
 * - Validates user context in database operations
 * - Logs suspicious database access attempts
 * - Prevents unauthorized data access
 */
@Injectable()
export class DatabaseAccessInterceptor implements NestInterceptor {
  private readonly logger = new Logger(DatabaseAccessInterceptor.name);
  
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const handler = context.getHandler();
    const className = context.getClass().name;
    const methodName = handler.name;
    
    // Skip auth-related operations
    if (this.isAuthRelatedOperation(className, methodName)) {
      return next.handle();
    }

    // Validate user context exists
    if (!this.hasValidUserContext(request)) {
      this.logger.error(`🚨 [DB-SECURITY] Database access without user context: ${className}.${methodName}`);
      throw new ForbiddenException('Database access requires authenticated user context');
    }

    // Log database access for monitoring
    this.logDatabaseAccess(request, className, methodName);

    return next.handle().pipe(
      tap({
        next: (data) => {
          // Monitor for suspicious data patterns
          this.validateResponseData(data, request, className, methodName);
        },
        error: (error) => {
          this.logger.error(`🚨 [DB-SECURITY] Database operation failed: ${className}.${methodName}`, error.message);
        }
      })
    );
  }

  /**
   * 🔐 Check if operation is auth-related (allowed to bypass user context)
   */
  private isAuthRelatedOperation(className: string, methodName: string): boolean {
    const authRelatedClasses = [
      'AuthController',
      'AuthService', 
      'SessionController',
      'SessionManagementService',
      'SecurityAuditService',
      'PasswordUtilityService',
      'TokenUtilityService'
    ];

    const authRelatedMethods = [
      'validateUser',
      'login',
      'refreshToken',
      'logout',
      'hashPassword',
      'generateToken'
    ];

    return authRelatedClasses.includes(className) || 
           authRelatedMethods.includes(methodName) ||
           className.includes('Auth') ||
           className.includes('Security');
  }

  /**
   * 🔐 Validate user context exists in request
   */
  private hasValidUserContext(request: any): boolean {
    return request.user && 
           request.user.userId && 
           request.user.role &&
           typeof request.user.userId === 'number';
  }

  /**
   * 🔐 Log database access for security monitoring
   */
  private logDatabaseAccess(request: any, className: string, methodName: string): void {
    const userInfo = {
      userId: request.user?.userId,
      role: request.user?.role,
      email: request.user?.email
    };

    this.logger.log(`🔐 [DB-ACCESS] ${className}.${methodName} - User: ${userInfo.userId} (${userInfo.role})`);

    // Log to security audit if available
    if (process.env.NODE_ENV === 'production') {
      // In production, we'd send this to a security monitoring system
      console.log(JSON.stringify({
        timestamp: new Date().toISOString(),
        type: 'DATABASE_ACCESS',
        className,
        methodName,
        user: userInfo,
        ip: request.ip,
        userAgent: request.get('User-Agent')
      }));
    }
  }

  /**
   * 🔐 Validate response data for suspicious patterns
   */
  private validateResponseData(data: any, request: any, className: string, methodName: string): void {
    if (!data) return;

    // Check for large data dumps (potential data exfiltration)
    if (Array.isArray(data) && data.length > 1000) {
      this.logger.warn(`🚨 [DB-SECURITY] Large data response detected: ${className}.${methodName} returned ${data.length} records`);
    }

    // Check for sensitive data exposure
    if (this.containsSensitiveData(data)) {
      this.logger.warn(`🚨 [DB-SECURITY] Sensitive data in response: ${className}.${methodName}`);
    }

    // Validate user can access this data
    if (!this.validateUserDataAccess(data, request.user)) {
      this.logger.error(`🚨 [DB-SECURITY] Unauthorized data access attempt: ${className}.${methodName}`);
      // In production, this could trigger additional security measures
    }
  }

  /**
   * 🔐 Check if response contains sensitive data
   */
  private containsSensitiveData(data: any): boolean {
    const sensitiveFields = ['password', 'hash', 'secret', 'token', 'key'];
    const dataStr = JSON.stringify(data).toLowerCase();
    
    return sensitiveFields.some(field => dataStr.includes(field));
  }

  /**
   * 🔐 Validate user can access the returned data
   */
  private validateUserDataAccess(data: any, user: any): boolean {
    if (!data || !user) return true;

    // For arrays of data, check if user should have access
    if (Array.isArray(data)) {
      // Sample check on first few items
      const sampleSize = Math.min(data.length, 5);
      for (let i = 0; i < sampleSize; i++) {
        if (!this.validateSingleRecordAccess(data[i], user)) {
          return false;
        }
      }
    } else {
      return this.validateSingleRecordAccess(data, user);
    }

    return true;
  }

  /**
   * 🔐 Validate user can access a single record
   */
  private validateSingleRecordAccess(record: any, user: any): boolean {
    if (!record || typeof record !== 'object') return true;

    // If record has userId, user should only access their own data (unless admin/manager)
    if (record.userId && user.role === 'EMPLOYEE') {
      return record.userId === user.userId;
    }

    // If record has createdBy, similar logic applies
    if (record.createdBy && user.role === 'EMPLOYEE') {
      return record.createdBy === user.userId;
    }

    // Managers and HR admins have broader access
    return true;
  }
}

/**
 * 🔐 Decorator to mark methods as requiring database access validation
 */
export const RequiresDatabaseAccess = () => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    // This decorator can be used to explicitly mark methods that need database access
    // The interceptor will automatically validate these
    Reflect.defineMetadata('requires-database-access', true, descriptor.value);
  };
};
