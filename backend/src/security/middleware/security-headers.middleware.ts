import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { SecurityHeadersService } from '../services/security-headers.service';

/**
 * 🔐 NIS2-Compliant Security Headers Middleware
 * 
 * Applies comprehensive security headers to all responses:
 * - Content Security Policy with nonce support
 * - HSTS for HTTPS enforcement
 * - CORS configuration
 * - Frame protection
 * - XSS protection
 * - Content type protection
 * - Permissions policy
 * - Cross-origin policies
 */
@Injectable()
export class SecurityHeadersMiddleware implements NestMiddleware {
  private readonly logger = new Logger(SecurityHeadersMiddleware.name);

  constructor(private readonly securityHeadersService: SecurityHeadersService) {}

  use(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();

    try {
      // Apply comprehensive security headers
      this.securityHeadersService.applySecurityHeaders(req, res);

      // Log security-sensitive requests
      if (this.securityHeadersService.isSensitiveEndpoint(req.path)) {
        this.logSecurityRequest(req);
      }

      // Handle preflight OPTIONS requests
      if (req.method === 'OPTIONS') {
        res.status(204).end();
        return;
      }

      // Add security audit trail for sensitive operations
      this.addSecurityAuditHeaders(req, res);

      // Monitor response time for security analysis
      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        if (responseTime > 5000) { // Log slow responses that might indicate attacks
          this.logger.warn(`🔐 [SECURITY] Slow response detected: ${req.method} ${req.path} - ${responseTime}ms`, {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            responseTime
          });
        }
      });

    } catch (error) {
      this.logger.error('🔐 [SECURITY] Error applying security headers:', error);
    }

    next();
  }

  /**
   * 🔐 Log security-sensitive requests
   */
  private logSecurityRequest(req: Request): void {
    const securityContext = {
      timestamp: new Date().toISOString(),
      method: req.method,
      path: req.path,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      referer: req.get('Referer'),
      origin: req.get('Origin'),
      contentType: req.get('Content-Type'),
      contentLength: req.get('Content-Length'),
      authorization: req.get('Authorization') ? 'Bearer [REDACTED]' : 'None',
      xForwardedFor: req.get('X-Forwarded-For'),
      xRealIp: req.get('X-Real-IP'),
      sessionId: req.get('X-Session-ID'),
      requestId: req.get('X-Request-ID'),
    };

    this.logger.log('🔐 [SECURITY-AUDIT] Sensitive endpoint access:', securityContext);
  }

  /**
   * 🔐 Add security audit headers
   */
  private addSecurityAuditHeaders(req: Request, res: Response): void {
    // Add request tracking headers
    const requestId = this.generateRequestId();
    res.setHeader('X-Request-ID', requestId);
    res.setHeader('X-Security-Policy', 'NIS2-Compliant');
    res.setHeader('X-Content-Security-Policy-Version', '1.0');
    
    // Add security timestamp
    res.setHeader('X-Security-Timestamp', new Date().toISOString());
    
    // Add rate limiting headers if applicable
    if (req.path.startsWith('/api/auth/')) {
      res.setHeader('X-RateLimit-Policy', 'auth-strict');
    }
  }

  /**
   * 🔐 Generate unique request ID for tracking
   */
  private generateRequestId(): string {
    const crypto = require('crypto');
    return crypto.randomBytes(8).toString('hex');
  }
}
