import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { RateLimitingService } from './services/rate-limiting.service';
import { SecurityHeadersService } from './services/security-headers.service';
import { HttpsEnforcementService } from './services/https-enforcement.service';
import { ProductionSecurityService } from './services/production-security.service';
import { EncryptionService } from './services/encryption.service';
import { SecurityHeadersMiddleware } from './middleware/security-headers.middleware';
import { HttpsEnforcementMiddleware } from './middleware/https-enforcement.middleware';
import { SecurityController } from './controllers/security.controller';

/**
 * 🔐 NIS2-Compliant Security Module
 * 
 * Implements enterprise-grade security measures including:
 * - Rate limiting and DDoS protection
 * - Security headers (HSTS, CSP, etc.)
 * - Audit logging for security events
 * - Input validation and sanitization
 * - Session security management
 */
@Module({
  imports: [
    // 🔐 NIS2 Requirement: Rate limiting for authentication endpoints
    ThrottlerModule.forRoot([{
      name: 'default',
      ttl: 60000, // 1 minute
      limit: 100, // 100 requests per minute
    }]),
  ],
  providers: [
    RateLimitingService,
    SecurityHeadersService,
    HttpsEnforcementService,
    ProductionSecurityService,
    EncryptionService
  ],
  controllers: [SecurityController],
  exports: [
    RateLimitingService,
    SecurityHeadersService,
    HttpsEnforcementService,
    ProductionSecurityService,
    EncryptionService,
    ThrottlerModule
  ],
})
export class SecurityModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // 🔐 Apply HTTPS enforcement middleware first (highest priority)
    consumer
      .apply(HttpsEnforcementMiddleware)
      .forRoutes('*');

    // 🔐 Apply security headers middleware to all routes
    consumer
      .apply(SecurityHeadersMiddleware)
      .forRoutes('*');
  }
}
