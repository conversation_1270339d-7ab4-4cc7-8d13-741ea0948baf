import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/typeorm';
import { Connection } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';

/**
 * 🔐 Database Security Monitoring Service
 * 
 * This service monitors database access patterns and detects
 * potential security threats or rogue SQL operations.
 */
@Injectable()
export class DatabaseSecurityMonitorService {
  private readonly logger = new Logger(DatabaseSecurityMonitorService.name);
  private suspiciousQueries: Map<string, number> = new Map();
  private lastSecurityScan: Date = new Date();

  constructor(
    @InjectConnection() private connection: Connection
  ) {}

  /**
   * 🔐 Monitor database query patterns
   */
  async monitorQueryPattern(query: string, userId?: number, context?: string): Promise<void> {
    // Detect suspicious patterns
    const suspiciousPatterns = [
      /SELECT\s+\*\s+FROM\s+users/i,
      /DELETE\s+FROM\s+\w+\s+WHERE\s+1=1/i,
      /UPDATE\s+\w+\s+SET\s+.*\s+WHERE\s+1=1/i,
      /UNION\s+SELECT/i,
      /DROP\s+TABLE/i,
      /ALTER\s+TABLE/i,
      /CREATE\s+TABLE/i,
      /GRANT\s+ALL/i,
      /--\s*$/m, // SQL comments (potential injection)
      /;\s*DROP/i, // SQL injection attempt
    ];

    for (const pattern of suspiciousPatterns) {
      if (pattern.test(query)) {
        await this.logSecurityThreat('SUSPICIOUS_SQL_PATTERN', {
          query: query.substring(0, 200), // Limit query length in logs
          pattern: pattern.toString(),
          userId,
          context,
          timestamp: new Date().toISOString()
        });
        break;
      }
    }

    // Track query frequency
    const queryHash = this.hashQuery(query);
    const currentCount = this.suspiciousQueries.get(queryHash) || 0;
    this.suspiciousQueries.set(queryHash, currentCount + 1);

    // Alert on high frequency queries (potential data exfiltration)
    if (currentCount > 100) {
      await this.logSecurityThreat('HIGH_FREQUENCY_QUERY', {
        queryHash,
        count: currentCount,
        userId,
        context
      });
    }
  }

  /**
   * 🔐 Validate database operation has proper user context
   */
  validateDatabaseOperation(operation: string, userId?: number, userRole?: string): boolean {
    // Operations that require user context
    const userContextRequired = [
      'SELECT', 'INSERT', 'UPDATE', 'DELETE'
    ];

    const operationType = operation.toUpperCase().split(' ')[0];
    
    if (userContextRequired.includes(operationType)) {
      if (!userId || !userRole) {
        this.logSecurityThreat('MISSING_USER_CONTEXT', {
          operation,
          operationType,
          hasUserId: !!userId,
          hasUserRole: !!userRole
        });
        return false;
      }
    }

    return true;
  }

  /**
   * 🔐 Scan for unauthorized database access patterns
   */
  @Cron(CronExpression.EVERY_HOUR)
  async performSecurityScan(): Promise<void> {
    this.logger.log('🔐 [DB-SECURITY] Starting hourly security scan...');

    try {
      // Check for unusual connection patterns
      await this.checkConnectionPatterns();
      
      // Check for data access anomalies
      await this.checkDataAccessAnomalies();
      
      // Check for privilege escalation attempts
      await this.checkPrivilegeEscalation();
      
      // Clear old suspicious query tracking
      this.cleanupSuspiciousQueries();
      
      this.lastSecurityScan = new Date();
      this.logger.log('🔐 [DB-SECURITY] Security scan completed successfully');
      
    } catch (error) {
      this.logger.error('🚨 [DB-SECURITY] Security scan failed', error);
    }
  }

  /**
   * 🔐 Check for unusual database connection patterns
   */
  private async checkConnectionPatterns(): Promise<void> {
    try {
      // Check active connections
      const connections = await this.connection.query('SHOW PROCESSLIST');
      
      // Look for suspicious connection patterns
      const suspiciousConnections = connections.filter((conn: any) => {
        return conn.Command === 'Query' && 
               (conn.Info?.includes('SELECT * FROM') || 
                conn.Info?.includes('SHOW TABLES') ||
                conn.Time > 300); // Long-running queries
      });

      if (suspiciousConnections.length > 0) {
        await this.logSecurityThreat('SUSPICIOUS_CONNECTIONS', {
          count: suspiciousConnections.length,
          connections: suspiciousConnections.map((c: any) => ({
            id: c.Id,
            user: c.User,
            host: c.Host,
            command: c.Command,
            time: c.Time,
            info: c.Info?.substring(0, 100)
          }))
        });
      }
    } catch (error) {
      this.logger.warn('Could not check connection patterns', error.message);
    }
  }

  /**
   * 🔐 Check for data access anomalies
   */
  private async checkDataAccessAnomalies(): Promise<void> {
    try {
      // Check for unusual data access patterns in the last hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      
      // This would typically check audit logs or security events
      // For now, we'll check for basic patterns
      
      const recentLogins = await this.connection.query(`
        SELECT COUNT(*) as count, account_status 
        FROM users 
        WHERE last_login > ? 
        GROUP BY account_status
      `, [oneHourAgo]);

      // Alert if there are logins from inactive accounts
      const inactiveLogins = recentLogins.find((r: any) => r.account_status !== 'active');
      if (inactiveLogins && inactiveLogins.count > 0) {
        await this.logSecurityThreat('INACTIVE_ACCOUNT_ACCESS', {
          count: inactiveLogins.count,
          status: inactiveLogins.account_status
        });
      }
    } catch (error) {
      this.logger.warn('Could not check data access anomalies', error.message);
    }
  }

  /**
   * 🔐 Check for privilege escalation attempts
   */
  private async checkPrivilegeEscalation(): Promise<void> {
    try {
      // Check for recent role changes
      const recentRoleChanges = await this.connection.query(`
        SELECT COUNT(*) as count 
        FROM users 
        WHERE updated_at > ? AND role IN ('HR_ADMIN', 'MANAGER')
      `, [new Date(Date.now() - 60 * 60 * 1000)]);

      if (recentRoleChanges[0]?.count > 0) {
        await this.logSecurityThreat('RECENT_PRIVILEGE_CHANGES', {
          count: recentRoleChanges[0].count,
          timeframe: '1 hour'
        });
      }
    } catch (error) {
      this.logger.warn('Could not check privilege escalation', error.message);
    }
  }

  /**
   * 🔐 Log security threat
   */
  private async logSecurityThreat(threatType: string, details: any): Promise<void> {
    const securityEvent = {
      timestamp: new Date().toISOString(),
      type: 'DATABASE_SECURITY_THREAT',
      threatType,
      details,
      severity: this.getThreatSeverity(threatType)
    };

    // Log to console (in production, this would go to security monitoring system)
    this.logger.error(`🚨 [DB-SECURITY] ${threatType}`, JSON.stringify(securityEvent));

    // In production, send to security monitoring system
    if (process.env.NODE_ENV === 'production') {
      // await this.sendToSecurityMonitoring(securityEvent);
    }
  }

  /**
   * 🔐 Get threat severity level
   */
  private getThreatSeverity(threatType: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const severityMap: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {
      'SUSPICIOUS_SQL_PATTERN': 'HIGH',
      'HIGH_FREQUENCY_QUERY': 'MEDIUM',
      'MISSING_USER_CONTEXT': 'HIGH',
      'SUSPICIOUS_CONNECTIONS': 'MEDIUM',
      'INACTIVE_ACCOUNT_ACCESS': 'HIGH',
      'RECENT_PRIVILEGE_CHANGES': 'CRITICAL'
    };

    return severityMap[threatType] || 'MEDIUM';
  }

  /**
   * 🔐 Hash query for tracking
   */
  private hashQuery(query: string): string {
    // Simple hash for query tracking (remove dynamic values)
    const normalizedQuery = query
      .replace(/\d+/g, '?')
      .replace(/'[^']*'/g, '?')
      .replace(/\s+/g, ' ')
      .trim()
      .toLowerCase();
    
    return Buffer.from(normalizedQuery).toString('base64').substring(0, 32);
  }

  /**
   * 🔐 Cleanup old suspicious query tracking
   */
  private cleanupSuspiciousQueries(): void {
    // Reset suspicious query tracking every hour
    this.suspiciousQueries.clear();
  }

  /**
   * 🔐 Get security monitoring status
   */
  getSecurityStatus(): any {
    return {
      lastScan: this.lastSecurityScan,
      suspiciousQueriesTracked: this.suspiciousQueries.size,
      monitoringActive: true,
      scanInterval: 'hourly'
    };
  }
}
