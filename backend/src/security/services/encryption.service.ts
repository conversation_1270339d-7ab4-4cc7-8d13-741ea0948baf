import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { EnvironmentConfigService } from '../../config/environment.config';

/**
 * 🔐 NIS2-Compliant Data Encryption Service
 * 
 * Implements enterprise-grade encryption for data at rest:
 * - AES-256-GCM encryption for sensitive data
 * - Key derivation using PBKDF2
 * - Secure random IV generation
 * - GDPR-compliant data protection
 * - SOC2-compliant encryption standards
 */
@Injectable()
export class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly keyLength = 32; // 256 bits
  private readonly ivLength = 16; // 128 bits
  private readonly saltLength = 32; // 256 bits
  private readonly tagLength = 16; // 128 bits
  private readonly iterations = 100000; // PBKDF2 iterations

  private readonly masterKey: string;

  constructor(private readonly envConfig: EnvironmentConfigService) {
    this.masterKey = this.envConfig.encryption.masterKey || this.generateMasterKey();
    this.validateEncryptionConfig();
  }

  /**
   * 🔐 Validate encryption configuration
   */
  private validateEncryptionConfig(): void {
    if (!this.envConfig.encryption.masterKey) {
      console.warn('🔐 [SECURITY-WARNING] No ENCRYPTION_MASTER_KEY found in environment. Using generated key.');
      console.warn('🔐 [SECURITY-WARNING] Set ENCRYPTION_MASTER_KEY in production environment.');
    }

    if (this.envConfig.encryption.iterations < 10000) {
      console.warn('🔐 [SECURITY-WARNING] ENCRYPTION_ITERATIONS should be at least 10,000 for security.');
    }

    if (!this.envConfig.encryption.salt) {
      console.warn('🔐 [SECURITY-WARNING] No ENCRYPTION_SALT found in environment.');
    }
  }

  /**
   * 🔐 Encrypt sensitive data for storage
   */
  async encryptData(plaintext: string, context?: string): Promise<string> {
    try {
      // Generate random salt and IV
      const salt = crypto.randomBytes(this.saltLength);
      const iv = crypto.randomBytes(this.ivLength);

      // Derive key from master key and salt
      const key = crypto.pbkdf2Sync(this.masterKey, salt, this.envConfig.encryption.iterations, this.keyLength, 'sha256');

      // Create cipher
      const cipher = crypto.createCipher(this.algorithm, key);

      // Add additional authenticated data (context)
      if (context) {
        cipher.setAAD(Buffer.from(context, 'utf8'));
      }

      // Encrypt the data
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Get authentication tag
      const tag = cipher.getAuthTag();

      // Combine all components: salt + iv + tag + encrypted
      const combined = Buffer.concat([
        salt,
        iv,
        tag,
        Buffer.from(encrypted, 'hex')
      ]);

      return combined.toString('base64');

    } catch (error) {
      console.error('🔐 [SECURITY-ERROR] Encryption failed:', error);
      throw new Error('Data encryption failed');
    }
  }

  /**
   * 🔐 Decrypt sensitive data from storage
   */
  async decryptData(encryptedData: string, context?: string): Promise<string> {
    try {
      // Decode from base64
      const combined = Buffer.from(encryptedData, 'base64');

      // Extract components
      const salt = combined.subarray(0, this.saltLength);
      const iv = combined.subarray(this.saltLength, this.saltLength + this.ivLength);
      const tag = combined.subarray(this.saltLength + this.ivLength, this.saltLength + this.ivLength + this.tagLength);
      const encrypted = combined.subarray(this.saltLength + this.ivLength + this.tagLength);

      // Derive key from master key and salt
      const key = crypto.pbkdf2Sync(this.masterKey, salt, this.envConfig.encryption.iterations, this.keyLength, 'sha256');

      // Create decipher
      const decipher = crypto.createDecipher(this.algorithm, key);
      decipher.setAuthTag(tag);

      // Add additional authenticated data (context)
      if (context) {
        decipher.setAAD(Buffer.from(context, 'utf8'));
      }

      // Decrypt the data
      let decrypted = decipher.update(encrypted, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;

    } catch (error) {
      console.error('🔐 [SECURITY-ERROR] Decryption failed:', error);
      throw new Error('Data decryption failed');
    }
  }

  /**
   * 🔐 Encrypt personal data for GDPR compliance
   */
  async encryptPersonalData(data: any): Promise<string> {
    const jsonData = JSON.stringify(data);
    return this.encryptData(jsonData, 'personal_data');
  }

  /**
   * 🔐 Decrypt personal data for GDPR compliance
   */
  async decryptPersonalData(encryptedData: string): Promise<any> {
    const jsonData = await this.decryptData(encryptedData, 'personal_data');
    return JSON.parse(jsonData);
  }

  /**
   * 🔐 Hash sensitive data for indexing (one-way)
   */
  hashForIndex(data: string): string {
    return crypto.createHash('sha256').update(data + this.masterKey).digest('hex');
  }

  /**
   * 🔐 Generate secure random token
   */
  generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 🔐 Generate secure random password
   */
  generateSecurePassword(length: number = 16): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';

    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }

    return password;
  }

  /**
   * 🔐 Encrypt database field
   */
  async encryptField(value: string, fieldName: string): Promise<string> {
    if (!value) return value;
    return this.encryptData(value, `field:${fieldName}`);
  }

  /**
   * 🔐 Decrypt database field
   */
  async decryptField(encryptedValue: string, fieldName: string): Promise<string> {
    if (!encryptedValue) return encryptedValue;
    return this.decryptData(encryptedValue, `field:${fieldName}`);
  }

  /**
   * 🔐 Generate master key (for development only)
   */
  private generateMasterKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 🔐 Validate encryption configuration
   */
  validateConfiguration(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!this.masterKey || this.masterKey.length < 32) {
      issues.push('Master key is too short or missing');
    }

    if (!process.env.ENCRYPTION_MASTER_KEY) {
      issues.push('ENCRYPTION_MASTER_KEY environment variable not set');
    }

    if (process.env.NODE_ENV === 'production' && !process.env.ENCRYPTION_MASTER_KEY) {
      issues.push('Production environment requires ENCRYPTION_MASTER_KEY');
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  /**
   * 🔐 Get encryption metrics for monitoring
   */
  getEncryptionMetrics(): any {
    return {
      algorithm: this.algorithm,
      keyLength: this.keyLength,
      ivLength: this.ivLength,
      saltLength: this.saltLength,
      tagLength: this.tagLength,
      iterations: this.iterations,
      configurationValid: this.validateConfiguration().isValid
    };
  }
}
