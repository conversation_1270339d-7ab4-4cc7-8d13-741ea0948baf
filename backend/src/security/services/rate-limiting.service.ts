import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';

/**
 * 🔐 NIS2-Compliant Rate Limiting Service
 * 
 * Implements advanced rate limiting strategies:
 * - Authentication endpoint protection
 * - API endpoint throttling
 * - User-specific rate limits
 * - IP-based blocking
 * - Adaptive rate limiting based on threat level
 */
@Injectable()
export class RateLimitingService implements OnModuleInit, OnModuleDestroy {
  private readonly suspiciousIPs = new Map<string, { attempts: number, lastAttempt: Date }>();
  private readonly userAttempts = new Map<string, { attempts: number, lastAttempt: Date }>();
  private readonly blockedIPs = new Map<string, Date>();
  private cleanupInterval: NodeJS.Timeout;

  // Configuration constants
  private readonly MAX_FAILED_ATTEMPTS = 5;
  private readonly BLOCK_DURATION_MINUTES = 15;
  private readonly CLEANUP_INTERVAL_MINUTES = 30;
  private readonly ATTEMPT_WINDOW_MINUTES = 15;

  onModuleInit() {
    // Start cleanup interval to prevent memory leaks
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldEntries();
    }, this.CLEANUP_INTERVAL_MINUTES * 60 * 1000);

    console.log('🔐 [RATE-LIMITING] Service initialized with security parameters:', {
      maxFailedAttempts: this.MAX_FAILED_ATTEMPTS,
      blockDurationMinutes: this.BLOCK_DURATION_MINUTES,
      cleanupIntervalMinutes: this.CLEANUP_INTERVAL_MINUTES
    });
  }

  onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }

  /**
   * 🔐 Check if IP should be blocked due to suspicious activity
   */
  isIPBlocked(ip: string): boolean {
    // Check if IP is explicitly blocked
    const blockExpiry = this.blockedIPs.get(ip);
    if (blockExpiry) {
      if (new Date() < blockExpiry) {
        return true;
      } else {
        // Block expired, remove it
        this.blockedIPs.delete(ip);
      }
    }

    // Check if IP has exceeded attempt threshold
    const ipData = this.suspiciousIPs.get(ip);
    if (ipData && ipData.attempts >= this.MAX_FAILED_ATTEMPTS) {
      // Check if attempts are within the time window
      const timeDiff = (new Date().getTime() - ipData.lastAttempt.getTime()) / (1000 * 60);
      if (timeDiff < this.ATTEMPT_WINDOW_MINUTES) {
        // Block the IP
        const blockUntil = new Date(Date.now() + this.BLOCK_DURATION_MINUTES * 60 * 1000);
        this.blockedIPs.set(ip, blockUntil);
        
        console.log('🔐 [SECURITY-ALERT] IP blocked due to excessive failed attempts:', {
          ip,
          attempts: ipData.attempts,
          blockUntil: blockUntil.toISOString()
        });
        
        return true;
      }
    }

    return false;
  }

  /**
   * 🔐 Record failed authentication attempt
   */
  recordFailedAttempt(ip: string, userId?: string): void {
    const now = new Date();

    // Track IP-based attempts
    const ipData = this.suspiciousIPs.get(ip) || { attempts: 0, lastAttempt: now };
    
    // Reset counter if last attempt was outside the window
    const timeDiff = (now.getTime() - ipData.lastAttempt.getTime()) / (1000 * 60);
    if (timeDiff > this.ATTEMPT_WINDOW_MINUTES) {
      ipData.attempts = 1;
    } else {
      ipData.attempts += 1;
    }
    
    ipData.lastAttempt = now;
    this.suspiciousIPs.set(ip, ipData);

    // Track user-based attempts if user ID available
    if (userId) {
      const userData = this.userAttempts.get(userId) || { attempts: 0, lastAttempt: now };
      
      const userTimeDiff = (now.getTime() - userData.lastAttempt.getTime()) / (1000 * 60);
      if (userTimeDiff > this.ATTEMPT_WINDOW_MINUTES) {
        userData.attempts = 1;
      } else {
        userData.attempts += 1;
      }
      
      userData.lastAttempt = now;
      this.userAttempts.set(userId, userData);
    }

    // Log security event
    console.log('🔐 [SECURITY-AUDIT] Failed authentication attempt recorded:', {
      ip,
      userId,
      ipAttempts: ipData.attempts,
      userAttempts: userId ? this.userAttempts.get(userId)?.attempts : 0,
      timestamp: now.toISOString(),
      willBlock: ipData.attempts >= this.MAX_FAILED_ATTEMPTS
    });
  }

  /**
   * 🔐 Record successful authentication (reset counters)
   */
  recordSuccessfulAttempt(ip: string, userId: string): void {
    // Clear IP and user attempt counters on successful login
    this.suspiciousIPs.delete(ip);
    this.userAttempts.delete(userId);
    this.blockedIPs.delete(ip);

    console.log('🔐 [SECURITY-AUDIT] Successful authentication, counters reset:', {
      ip,
      userId,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * 🔐 Get current threat level for IP
   */
  getThreatLevel(ip: string): 'low' | 'medium' | 'high' | 'critical' {
    if (this.isIPBlocked(ip)) {
      return 'critical';
    }

    const ipData = this.suspiciousIPs.get(ip);
    if (!ipData) {
      return 'low';
    }

    const attempts = ipData.attempts;
    if (attempts >= 4) return 'high';
    if (attempts >= 3) return 'medium';
    if (attempts >= 1) return 'medium';
    return 'low';
  }

  /**
   * 🔐 Apply adaptive rate limiting based on threat level
   */
  getAdaptiveRateLimit(ip: string): { ttl: number; limit: number } {
    const threatLevel = this.getThreatLevel(ip);
    
    switch (threatLevel) {
      case 'critical':
        return { ttl: 3600000, limit: 1 }; // 1 request per hour
      case 'high':
        return { ttl: 600000, limit: 5 }; // 5 requests per 10 minutes
      case 'medium':
        return { ttl: 300000, limit: 10 }; // 10 requests per 5 minutes
      default:
        return { ttl: 60000, limit: 20 }; // 20 requests per minute
    }
  }

  /**
   * 🔐 Get rate limiting status for monitoring
   */
  getStatus(): any {
    return {
      suspiciousIPs: this.suspiciousIPs.size,
      blockedIPs: this.blockedIPs.size,
      userAttempts: this.userAttempts.size,
      configuration: {
        maxFailedAttempts: this.MAX_FAILED_ATTEMPTS,
        blockDurationMinutes: this.BLOCK_DURATION_MINUTES,
        attemptWindowMinutes: this.ATTEMPT_WINDOW_MINUTES
      }
    };
  }

  /**
   * 🔐 Clean up old entries to prevent memory leaks
   */
  private cleanupOldEntries(): void {
    const now = new Date();
    const cutoffTime = new Date(now.getTime() - this.ATTEMPT_WINDOW_MINUTES * 60 * 1000);

    // Clean up old IP attempts
    for (const [ip, data] of this.suspiciousIPs.entries()) {
      if (data.lastAttempt < cutoffTime) {
        this.suspiciousIPs.delete(ip);
      }
    }

    // Clean up old user attempts
    for (const [userId, data] of this.userAttempts.entries()) {
      if (data.lastAttempt < cutoffTime) {
        this.userAttempts.delete(userId);
      }
    }

    // Clean up expired blocks
    for (const [ip, blockExpiry] of this.blockedIPs.entries()) {
      if (now >= blockExpiry) {
        this.blockedIPs.delete(ip);
      }
    }

    console.log('🔐 [RATE-LIMITING] Cleanup completed:', {
      suspiciousIPs: this.suspiciousIPs.size,
      blockedIPs: this.blockedIPs.size,
      userAttempts: this.userAttempts.size,
      timestamp: now.toISOString()
    });
  }

  /**
   * 🔐 Manually unblock an IP (for admin use)
   */
  unblockIP(ip: string): boolean {
    const wasBlocked = this.blockedIPs.has(ip) || this.suspiciousIPs.has(ip);
    this.blockedIPs.delete(ip);
    this.suspiciousIPs.delete(ip);
    
    if (wasBlocked) {
      console.log('🔐 [SECURITY-ADMIN] IP manually unblocked:', { ip, timestamp: new Date().toISOString() });
    }
    
    return wasBlocked;
  }
}
