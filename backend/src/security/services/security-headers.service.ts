import { Injectable } from '@nestjs/common';
import { Request, Response } from 'express';

/**
 * 🔐 NIS2-Compliant Security Headers Service
 * 
 * Implements comprehensive security headers beyond basic helmet.js:
 * - Content Security Policy (CSP) with nonce support
 * - HTTP Strict Transport Security (HSTS)
 * - Cross-Origin Resource Policy (CORP)
 * - Cross-Origin Embedder Policy (COEP)
 * - Cross-Origin Opener Policy (COOP)
 * - Permissions Policy (Feature Policy)
 * - Referrer Policy
 * - X-Frame-Options
 * - X-Content-Type-Options
 * - X-XSS-Protection
 */
@Injectable()
export class SecurityHeadersService {
  private readonly isProduction = process.env.NODE_ENV === 'production';
  private readonly frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3080';

  /**
   * 🔐 Apply comprehensive security headers
   */
  applySecurityHeaders(req: Request, res: Response): void {
    // Generate nonce for CSP
    const nonce = this.generateNonce();
    req['nonce'] = nonce;

    // Apply all security headers
    this.setHSTSHeaders(res);
    this.setCSPHeaders(res, nonce);
    this.setCORSHeaders(res, req);
    this.setFrameProtectionHeaders(res);
    this.setContentTypeHeaders(res);
    this.setReferrerPolicyHeaders(res);
    this.setPermissionsPolicyHeaders(res);
    this.setCrossOriginHeaders(res);
    this.setXSSProtectionHeaders(res);
    this.removeServerHeaders(res);
    this.setSecurityMiscHeaders(res);
  }

  /**
   * 🔐 HTTP Strict Transport Security (HSTS)
   */
  private setHSTSHeaders(res: Response): void {
    if (this.isProduction) {
      // 2 years max-age with includeSubDomains and preload
      res.setHeader('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
    } else {
      // Shorter duration for development
      res.setHeader('Strict-Transport-Security', 'max-age=86400; includeSubDomains');
    }
  }

  /**
   * 🔐 Content Security Policy (CSP)
   */
  private setCSPHeaders(res: Response, nonce: string): void {
    const cspDirectives = [
      "default-src 'self'",
      `script-src 'self' 'nonce-${nonce}' 'strict-dynamic'`,
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com data:",
      "img-src 'self' data: https: blob:",
      "media-src 'self' data:",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      `connect-src 'self' ${this.frontendUrl} wss: ws:`,
      "worker-src 'self' blob:",
      "manifest-src 'self'",
      "upgrade-insecure-requests"
    ];

    // More restrictive CSP for production
    if (this.isProduction) {
      cspDirectives[1] = `script-src 'self' 'nonce-${nonce}' 'strict-dynamic'`; // Remove unsafe-eval
      cspDirectives[2] = "style-src 'self' https://fonts.googleapis.com"; // Remove unsafe-inline
    }

    res.setHeader('Content-Security-Policy', cspDirectives.join('; '));
    
    // Also set report-only header for monitoring
    res.setHeader('Content-Security-Policy-Report-Only', cspDirectives.join('; ') + '; report-uri /api/security/csp-report');
  }

  /**
   * 🔐 CORS Headers
   */
  private setCORSHeaders(res: Response, req: Request): void {
    const allowedOrigins = [
      this.frontendUrl,
      'https://dev.trusthansen.dk',
      'http://localhost:3080'
    ];

    const origin = req.headers.origin;
    if (origin && allowedOrigins.includes(origin)) {
      res.setHeader('Access-Control-Allow-Origin', origin);
    }

    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-CSRF-Token');
    res.setHeader('Access-Control-Expose-Headers', 'X-Total-Count, X-Page-Count, X-Per-Page');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
  }

  /**
   * 🔐 Frame Protection Headers
   */
  private setFrameProtectionHeaders(res: Response): void {
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('Frame-Options', 'DENY'); // Additional protection
  }

  /**
   * 🔐 Content Type Headers
   */
  private setContentTypeHeaders(res: Response): void {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Content-Type-Options', 'nosniff'); // Additional protection
  }

  /**
   * 🔐 Referrer Policy Headers
   */
  private setReferrerPolicyHeaders(res: Response): void {
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  }

  /**
   * 🔐 Permissions Policy (Feature Policy)
   */
  private setPermissionsPolicyHeaders(res: Response): void {
    const permissions = [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
      'usb=()',
      'magnetometer=()',
      'gyroscope=()',
      'accelerometer=()',
      'ambient-light-sensor=()',
      'autoplay=()',
      'encrypted-media=()',
      'fullscreen=(self)',
      'picture-in-picture=()'
    ];

    res.setHeader('Permissions-Policy', permissions.join(', '));
  }

  /**
   * 🔐 Cross-Origin Headers
   */
  private setCrossOriginHeaders(res: Response): void {
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    res.setHeader('Cross-Origin-Embedder-Policy', 'unsafe-none'); // Can be 'require-corp' for stricter security
    res.setHeader('Cross-Origin-Opener-Policy', 'same-origin-allow-popups');
  }

  /**
   * 🔐 XSS Protection Headers
   */
  private setXSSProtectionHeaders(res: Response): void {
    res.setHeader('X-XSS-Protection', '1; mode=block');
  }

  /**
   * 🔐 Remove Server Information Headers
   */
  private removeServerHeaders(res: Response): void {
    res.removeHeader('X-Powered-By');
    res.removeHeader('Server');
    res.setHeader('Server', 'eHRx-Security'); // Custom server header
  }

  /**
   * 🔐 Additional Security Headers
   */
  private setSecurityMiscHeaders(res: Response): void {
    // Prevent DNS prefetching
    res.setHeader('X-DNS-Prefetch-Control', 'off');
    
    // Download options for IE
    res.setHeader('X-Download-Options', 'noopen');
    
    // Prevent MIME type confusion attacks
    res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
    
    // Cache control for sensitive data
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');
    
    // Additional security headers
    res.setHeader('X-Robots-Tag', 'noindex, nofollow, nosnippet, noarchive');
    
    // Timing attack protection
    res.setHeader('Timing-Allow-Origin', '*');
  }

  /**
   * 🔐 Generate cryptographically secure nonce
   */
  private generateNonce(): string {
    const crypto = require('crypto');
    return crypto.randomBytes(16).toString('base64');
  }

  /**
   * 🔐 Check if endpoint is sensitive
   */
  isSensitiveEndpoint(path: string): boolean {
    const sensitivePatterns = [
      '/api/auth/',
      '/api/users/',
      '/api/admin/',
      '/api/security/',
      '/api/database/',
      '/api/mfa/',
      '/api/audit/'
    ];
    
    return sensitivePatterns.some(pattern => path.includes(pattern));
  }

  /**
   * 🔐 Get security headers configuration
   */
  getSecurityConfig(): any {
    return {
      hsts: {
        enabled: true,
        maxAge: this.isProduction ? 63072000 : 86400,
        includeSubDomains: true,
        preload: this.isProduction
      },
      csp: {
        enabled: true,
        reportOnly: true,
        reportUri: '/api/security/csp-report'
      },
      cors: {
        enabled: true,
        allowedOrigins: [this.frontendUrl, 'https://dev.trusthansen.dk'],
        credentials: true
      },
      frameProtection: {
        enabled: true,
        policy: 'DENY'
      },
      contentTypeOptions: {
        enabled: true,
        nosniff: true
      },
      xssProtection: {
        enabled: true,
        mode: 'block'
      },
      referrerPolicy: {
        enabled: true,
        policy: 'strict-origin-when-cross-origin'
      },
      permissionsPolicy: {
        enabled: true,
        restrictedFeatures: ['camera', 'microphone', 'geolocation', 'payment']
      }
    };
  }
}
