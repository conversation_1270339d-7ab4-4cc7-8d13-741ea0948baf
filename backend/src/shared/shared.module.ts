import { Module } from '@nestjs/common';
import { PasswordUtilityService } from '../auth/services/password-utility.service';
import { TokenUtilityService } from '../auth/services/token-utility.service';

/**
 * 🔐 Shared Utilities Module
 * 
 * Provides common utility services that can be used across modules
 * without creating circular dependencies. This includes:
 * - Password utility services
 * - Token utility services
 * - Other shared authentication utilities
 */
@Module({
  providers: [
    PasswordUtilityService,
    TokenUtilityService,
  ],
  exports: [
    PasswordUtilityService,
    TokenUtilityService,
  ],
})
export class SharedModule {}
