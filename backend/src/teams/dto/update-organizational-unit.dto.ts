import {
  Is<PERSON>ptional,
  IsString,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>,
  IsPos<PERSON>,
  <PERSON>,
  <PERSON>,
  Length,
  Matches
} from 'class-validator';
import { Transform } from 'class-transformer';
import { OrganizationalUnitType } from '../entities/organizational-unit.entity';

export class UpdateOrganizationalUnitDto {
  @IsOptional()
  @IsString({ message: 'Organization name must be a string' })
  @Length(2, 100, { message: 'Organization name must be between 2 and 100 characters' })
  @Matches(/^[a-zA-Z0-9\s\-_&.()]+$/, {
    message: 'Organization name can only contain letters, numbers, spaces, and basic punctuation (-, _, &, ., ())'
  })
  @Transform(({ value }) => value?.trim())
  name?: string;

  @IsOptional()
  @IsEnum(OrganizationalUnitType, {
    message: 'Organization type must be one of: organization, division, department, team, squad, unit'
  })
  type?: OrganizationalUnitType;

  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  @Length(0, 500, { message: 'Description cannot exceed 500 characters' })
  @Transform(({ value }) => value?.trim())
  description?: string;

  @IsOptional()
  @IsNumber({}, { message: 'Manager ID must be a number' })
  @IsPositive({ message: 'Manager ID must be a positive number' })
  @Min(1, { message: 'Manager ID must be at least 1' })
  managerId?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 }, { message: 'Budget must be a valid decimal number with up to 2 decimal places' })
  @Min(0, { message: 'Budget cannot be negative' })
  @Max(999999999.99, { message: 'Budget cannot exceed 999,999,999.99' })
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  budget?: number;
}
