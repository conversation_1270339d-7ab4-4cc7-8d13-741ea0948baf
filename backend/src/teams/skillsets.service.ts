import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Connection } from 'typeorm';
import { Skillset, SkillsetCategory, SkillLevel } from './entities/skillset.entity';
import { UserSkillset } from './entities/user-skillset.entity';
import { User, UserRole } from '../users/entities/user.entity';

export interface CreateSkillsetDto {
  name: string;
  category: SkillsetCategory;
  description?: string;
  levelRequired?: SkillLevel;
  isCoreSkill?: boolean;
}

export interface UpdateSkillsetDto {
  name?: string;
  category?: SkillsetCategory;
  description?: string;
  levelRequired?: SkillLevel;
  isCoreSkill?: boolean;
  isActive?: boolean;
}

export interface AssignSkillToUserDto {
  userId: number;
  skillsetId: number;
  proficiencyLevel: SkillLevel;
  yearsExperience?: number;
  isCertified?: boolean;
  certificationName?: string;
  certificationDate?: Date;
  lastUsedDate?: Date;
  notes?: string;
}

export interface UpdateUserSkillDto {
  proficiencyLevel?: SkillLevel;
  yearsExperience?: number;
  isCertified?: boolean;
  certificationName?: string;
  certificationDate?: Date;
  lastUsedDate?: Date;
  notes?: string;
}

@Injectable()
export class SkillsetsService {
  constructor(
    @InjectRepository(Skillset)
    private skillsetRepository: Repository<Skillset>,
    @InjectRepository(UserSkillset)
    private userSkillsetRepository: Repository<UserSkillset>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private _connection: Connection,
  ) { }

  async createSkillset(createDto: CreateSkillsetDto, _userId: number, userRole: string): Promise<Skillset> {
    // Only HR_ADMIN and management roles can create skillsets
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('You do not have permission to create skillsets');
    }

    // Check if skillset with same name already exists
    const existingSkillset = await this.skillsetRepository.findOne({ where: { name: createDto.name } });
    if (existingSkillset) {
      throw new ConflictException(`Skillset with name "${createDto.name}" already exists`);
    }

    const skillset = this.skillsetRepository.create({
      name: createDto.name,
      category: createDto.category,
      description: createDto.description,
      levelRequired: createDto.levelRequired || SkillLevel.INTERMEDIATE,
      isCoreSkill: createDto.isCoreSkill || false,
    });

    return this.skillsetRepository.save(skillset);
  }

  async findAllSkillsets(): Promise<Skillset[]> {
    return this.skillsetRepository.find({
      where: { isActive: true },
      order: { category: 'ASC', name: 'ASC' },
    });
  }

  async findSkillsetsByCategory(category: SkillsetCategory): Promise<Skillset[]> {
    return this.skillsetRepository.find({
      where: { category, isActive: true },
      order: { name: 'ASC' },
    });
  }

  async findCoreSkillsets(): Promise<Skillset[]> {
    return this.skillsetRepository.find({
      where: { isCoreSkill: true, isActive: true },
      order: { category: 'ASC', name: 'ASC' },
    });
  }

  async updateSkillset(id: number, updateDto: UpdateSkillsetDto, _userId: number, userRole: string): Promise<Skillset> {
    // Only HR_ADMIN and management roles can update skillsets
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR, UserRole.MANAGER].includes(userRole as UserRole)) {
      throw new ForbiddenException('You do not have permission to update skillsets');
    }

    const skillset = await this.skillsetRepository.findOne({ where: { id } });
    if (!skillset) {
      throw new NotFoundException(`Skillset with ID ${id} not found`);
    }

    // Check for name conflicts if name is being updated
    if (updateDto.name && updateDto.name !== skillset.name) {
      const existingSkillset = await this.skillsetRepository.findOne({ where: { name: updateDto.name } });
      if (existingSkillset) {
        throw new ConflictException(`Skillset with name "${updateDto.name}" already exists`);
      }
    }

    // Update fields
    if (updateDto.name) skillset.name = updateDto.name;
    if (updateDto.category) skillset.category = updateDto.category;
    if (updateDto.description !== undefined) skillset.description = updateDto.description;
    if (updateDto.levelRequired) skillset.levelRequired = updateDto.levelRequired;
    if (updateDto.isCoreSkill !== undefined) skillset.isCoreSkill = updateDto.isCoreSkill;
    if (updateDto.isActive !== undefined) skillset.isActive = updateDto.isActive;

    return this.skillsetRepository.save(skillset);
  }

  async deleteSkillset(id: number, _userId: number, userRole: string): Promise<void> {
    // Only HR_ADMIN can delete skillsets
    if (userRole !== UserRole.HR_ADMIN) {
      throw new ForbiddenException('Only HR admins can delete skillsets');
    }

    const skillset = await this.skillsetRepository.findOne({ where: { id } });
    if (!skillset) {
      throw new NotFoundException(`Skillset with ID ${id} not found`);
    }

    // Check if skillset is assigned to any users
    const userSkillsets = await this.userSkillsetRepository.find({ where: { skillsetId: id } });
    if (userSkillsets.length > 0) {
      throw new ForbiddenException('Cannot delete skillset that is assigned to users. Please remove all user assignments first.');
    }

    await this.skillsetRepository.remove(skillset);
  }

  async assignSkillToUser(assignDto: AssignSkillToUserDto, requestUserId: number, userRole: string): Promise<UserSkillset> {
    // Check permissions
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR, UserRole.MANAGER].includes(userRole as UserRole)) {
      // Users can only assign skills to themselves
      if (assignDto.userId !== requestUserId) {
        throw new ForbiddenException('You can only assign skills to yourself');
      }
    }

    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: assignDto.userId } });
    if (!user) {
      throw new NotFoundException(`User with ID ${assignDto.userId} not found`);
    }

    // Check if skillset exists
    const skillset = await this.skillsetRepository.findOne({ where: { id: assignDto.skillsetId } });
    if (!skillset) {
      throw new NotFoundException(`Skillset with ID ${assignDto.skillsetId} not found`);
    }

    // Check if user already has this skill
    const existingUserSkill = await this.userSkillsetRepository.findOne({
      where: { userId: assignDto.userId, skillsetId: assignDto.skillsetId }
    });
    if (existingUserSkill) {
      throw new ConflictException('User already has this skill assigned');
    }

    const userSkillset = this.userSkillsetRepository.create({
      userId: assignDto.userId,
      skillsetId: assignDto.skillsetId,
      proficiencyLevel: assignDto.proficiencyLevel,
      yearsExperience: assignDto.yearsExperience || 0,
      isCertified: assignDto.isCertified || false,
      certificationName: assignDto.certificationName,
      certificationDate: assignDto.certificationDate,
      lastUsedDate: assignDto.lastUsedDate,
      notes: assignDto.notes,
    });

    return this.userSkillsetRepository.save(userSkillset);
  }

  async updateUserSkill(
    userId: number,
    skillsetId: number,
    updateDto: UpdateUserSkillDto,
    requestUserId: number,
    userRole: string
  ): Promise<UserSkillset> {
    // Check permissions
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR, UserRole.MANAGER].includes(userRole as UserRole)) {
      // Users can only update their own skills
      if (userId !== requestUserId) {
        throw new ForbiddenException('You can only update your own skills');
      }
    }

    const userSkillset = await this.userSkillsetRepository.findOne({
      where: { userId, skillsetId },
      relations: ['user', 'skillset']
    });

    if (!userSkillset) {
      throw new NotFoundException(`User skill assignment not found`);
    }

    // Update fields
    if (updateDto.proficiencyLevel) userSkillset.proficiencyLevel = updateDto.proficiencyLevel;
    if (updateDto.yearsExperience !== undefined) userSkillset.yearsExperience = updateDto.yearsExperience;
    if (updateDto.isCertified !== undefined) userSkillset.isCertified = updateDto.isCertified;
    if (updateDto.certificationName !== undefined) userSkillset.certificationName = updateDto.certificationName;
    if (updateDto.certificationDate !== undefined) userSkillset.certificationDate = updateDto.certificationDate;
    if (updateDto.lastUsedDate !== undefined) userSkillset.lastUsedDate = updateDto.lastUsedDate;
    if (updateDto.notes !== undefined) userSkillset.notes = updateDto.notes;

    return this.userSkillsetRepository.save(userSkillset);
  }

  async removeSkillFromUser(userId: number, skillsetId: number, requestUserId: number, userRole: string): Promise<void> {
    // Check permissions
    if (![UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR, UserRole.MANAGER].includes(userRole as UserRole)) {
      // Users can only remove their own skills
      if (userId !== requestUserId) {
        throw new ForbiddenException('You can only remove your own skills');
      }
    }

    const userSkillset = await this.userSkillsetRepository.findOne({
      where: { userId, skillsetId }
    });

    if (!userSkillset) {
      throw new NotFoundException(`User skill assignment not found`);
    }

    await this.userSkillsetRepository.remove(userSkillset);
  }

  async getUserSkills(userId: number): Promise<UserSkillset[]> {
    return this.userSkillsetRepository.find({
      where: { userId },
      relations: ['skillset']
    });
  }

  async getUserSkillsByCategory(userId: number, category: SkillsetCategory): Promise<UserSkillset[]> {
    return this.userSkillsetRepository.find({
      where: { userId, skillset: { category } },
      relations: ['skillset']
    });
  }

  async getSkillsetUsers(skillsetId: number): Promise<UserSkillset[]> {
    return this.userSkillsetRepository.find({
      where: { skillsetId },
      relations: ['user'],
      order: { proficiencyLevel: 'DESC', yearsExperience: 'DESC' }
    });
  }

  async getSkillsMatrix(): Promise<any> {
    const skillsets = await this.skillsetRepository.find({
      where: { isActive: true },
      order: { category: 'ASC', name: 'ASC' }
    });

    const matrix: Record<string, any> = {};
    for (const skillset of skillsets) {
      const userSkills = await this.userSkillsetRepository.find({
        where: { skillsetId: skillset.id },
        relations: ['user']
      });

      matrix[skillset.name] = {
        skillset,
        users: userSkills.map(us => ({
          user: us.user,
          proficiencyLevel: us.proficiencyLevel,
          yearsExperience: us.yearsExperience,
          isCertified: us.isCertified
        }))
      };
    }

    return matrix;
  }
}
