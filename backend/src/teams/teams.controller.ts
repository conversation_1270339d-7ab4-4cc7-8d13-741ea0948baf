import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request
} from '@nestjs/common';
import { TeamsService } from './teams.service';
import { OrganizationalUnitsService } from './organizational-units.service';
import { CreateOrganizationalUnitDto } from './dto/create-organizational-unit.dto';
import { UpdateOrganizationalUnitDto } from './dto/update-organizational-unit.dto';
import { SkillsetsService, CreateSkillsetDto, UpdateSkillsetDto, AssignSkillToUserDto, UpdateUserSkillDto } from './skillsets.service';
import { CreateTeamDto } from './dto/create-team.dto';
import { UpdateTeamDto } from './dto/update-team.dto';
import { AddTeamMemberDto, UpdateTeamMemberRoleDto } from './dto/team-member.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { Request as ExpressRequest } from 'express';

interface AuthenticatedRequest extends ExpressRequest {
  user: {
    id: number;
    role: string;
  };
}
import { SkillsetCategory } from './entities/skillset.entity';

@Controller('teams')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TeamsController {
  constructor(
    private readonly teamsService: TeamsService,
    private readonly orgUnitsService: OrganizationalUnitsService,
    private readonly skillsetsService: SkillsetsService
  ) { }

  @Post()
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  create(@Body() createTeamDto: CreateTeamDto, @Request() req: AuthenticatedRequest) {
    return this.teamsService.create(createTeamDto, req.user.id, req.user.role);
  }

  @Get()
  findAll(@Request() req: AuthenticatedRequest) {
    return this.teamsService.findAll(req.user.id, req.user.role);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req: AuthenticatedRequest) {
    return this.teamsService.findOne(+id, req.user.id, req.user.role);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateTeamDto: UpdateTeamDto,
    @Request() req: AuthenticatedRequest
  ) {
    return this.teamsService.update(+id, updateTeamDto, req.user.id, req.user.role);
  }

  @Delete(':id')
  @Roles(UserRole.HR_ADMIN)
  remove(@Param('id') id: string, @Request() req: AuthenticatedRequest) {
    return this.teamsService.remove(+id, req.user.id, req.user.role);
  }

  @Get(':id/members')
  getTeamMembers(@Param('id') id: string, @Request() req: AuthenticatedRequest) {
    return this.teamsService.getTeamMembers(+id, req.user.id, req.user.role);
  }

  @Post(':id/members')
  addMember(
    @Param('id') id: string,
    @Body() addMemberDto: AddTeamMemberDto,
    @Request() req: AuthenticatedRequest
  ) {
    return this.teamsService.addMember(+id, addMemberDto, req.user.id, req.user.role);
  }

  @Patch(':id/members/:memberId')
  updateMemberRole(
    @Param('id') id: string,
    @Param('memberId') memberId: string,
    @Body() updateRoleDto: UpdateTeamMemberRoleDto,
    @Request() req: AuthenticatedRequest
  ) {
    return this.teamsService.updateMemberRole(
      +id,
      +memberId,
      updateRoleDto,
      req.user.id,
      req.user.role
    );
  }

  @Delete(':id/members/:memberId')
  removeMember(
    @Param('id') id: string,
    @Param('memberId') memberId: string,
    @Request() req: AuthenticatedRequest
  ) {
    return this.teamsService.removeMember(+id, +memberId, req.user.id, req.user.role);
  }

  // Organizational Units Endpoints
  @Get('organizational-units')
  findAllOrganizationalUnits(@Request() req: AuthenticatedRequest) {
    return this.orgUnitsService.findAll(req.user.id, req.user.role);
  }

  @Get('organizational-units/tree')
  getOrganizationalTree() {
    return this.orgUnitsService.getOrganizationalTree();
  }

  @Get('organizational-units/:id')
  findOneOrganizationalUnit(@Param('id') id: string, @Request() req: AuthenticatedRequest) {
    return this.orgUnitsService.findOne(+id, req.user.id, req.user.role);
  }

  @Post('organizational-units')
  @Roles(UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR)
  createOrganizationalUnit(@Body() createDto: CreateOrganizationalUnitDto, @Request() req: AuthenticatedRequest) {
    return this.orgUnitsService.create(createDto, req.user.id, req.user.role);
  }

  @Patch('organizational-units/:id')
  @Roles(UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR)
  updateOrganizationalUnit(
    @Param('id') id: string,
    @Body() updateDto: UpdateOrganizationalUnitDto,
    @Request() req: AuthenticatedRequest
  ) {
    return this.orgUnitsService.update(+id, updateDto, req.user.id, req.user.role);
  }

  @Delete('organizational-units/:id')
  @Roles(UserRole.HR_ADMIN, UserRole.CEO)
  removeOrganizationalUnit(@Param('id') id: string, @Request() req: AuthenticatedRequest) {
    return this.orgUnitsService.remove(+id, req.user.id, req.user.role);
  }

  @Patch('organizational-units/:id/move')
  @Roles(UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR)
  moveOrganizationalUnit(
    @Param('id') id: string,
    @Body('newParentId') newParentId: number,
    @Request() req: AuthenticatedRequest
  ) {
    return this.orgUnitsService.moveUnit(+id, newParentId, req.user.id, req.user.role);
  }

  // Skillsets Endpoints
  @Get('skillsets')
  findAllSkillsets() {
    return this.skillsetsService.findAllSkillsets();
  }

  @Get('skillsets/category/:category')
  findSkillsetsByCategory(@Param('category') category: SkillsetCategory) {
    return this.skillsetsService.findSkillsetsByCategory(category);
  }

  @Get('skillsets/core')
  findCoreSkillsets() {
    return this.skillsetsService.findCoreSkillsets();
  }

  @Get('skillsets/matrix')
  getSkillsMatrix() {
    return this.skillsetsService.getSkillsMatrix();
  }

  @Post('skillsets')
  @Roles(UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR, UserRole.MANAGER)
  createSkillset(@Body() createDto: CreateSkillsetDto, @Request() req: AuthenticatedRequest) {
    return this.skillsetsService.createSkillset(createDto, req.user.id, req.user.role);
  }

  @Patch('skillsets/:id')
  @Roles(UserRole.HR_ADMIN, UserRole.CEO, UserRole.VP, UserRole.DIRECTOR, UserRole.MANAGER)
  updateSkillset(
    @Param('id') id: string,
    @Body() updateDto: UpdateSkillsetDto,
    @Request() req: AuthenticatedRequest
  ) {
    return this.skillsetsService.updateSkillset(+id, updateDto, req.user.id, req.user.role);
  }

  @Delete('skillsets/:id')
  @Roles(UserRole.HR_ADMIN)
  deleteSkillset(@Param('id') id: string, @Request() req: AuthenticatedRequest) {
    return this.skillsetsService.deleteSkillset(+id, req.user.id, req.user.role);
  }

  // User Skills Endpoints
  @Get('users/:userId/skills')
  getUserSkills(@Param('userId') userId: string) {
    return this.skillsetsService.getUserSkills(+userId);
  }

  @Get('users/:userId/skills/category/:category')
  getUserSkillsByCategory(
    @Param('userId') userId: string,
    @Param('category') category: SkillsetCategory
  ) {
    return this.skillsetsService.getUserSkillsByCategory(+userId, category);
  }

  @Post('users/skills')
  assignSkillToUser(@Body() assignDto: AssignSkillToUserDto, @Request() req: AuthenticatedRequest) {
    return this.skillsetsService.assignSkillToUser(assignDto, req.user.id, req.user.role);
  }

  @Patch('users/:userId/skills/:skillsetId')
  updateUserSkill(
    @Param('userId') userId: string,
    @Param('skillsetId') skillsetId: string,
    @Body() updateDto: UpdateUserSkillDto,
    @Request() req: AuthenticatedRequest
  ) {
    return this.skillsetsService.updateUserSkill(+userId, +skillsetId, updateDto, req.user.id, req.user.role);
  }

  @Delete('users/:userId/skills/:skillsetId')
  removeSkillFromUser(
    @Param('userId') userId: string,
    @Param('skillsetId') skillsetId: string,
    @Request() req: AuthenticatedRequest
  ) {
    return this.skillsetsService.removeSkillFromUser(+userId, +skillsetId, req.user.id, req.user.role);
  }

  @Get('skillsets/:id/users')
  getSkillsetUsers(@Param('id') id: string) {
    return this.skillsetsService.getSkillsetUsers(+id);
  }
}
