import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { OrganizationalUnit } from '../../teams/entities/organizational-unit.entity';
import { UserSkillset } from '../../teams/entities/user-skillset.entity';

export enum UserRole {
  CEO = 'ceo',
  VP = 'vp',
  DIRECTOR = 'director',
  MANAGER = 'manager',
  SENIOR_ENGINEER = 'senior_engineer',
  ENGINEER = 'engineer',
  JUNIOR_ENGINEER = 'junior_engineer',
  INTERN = 'intern',
  HR_ADMIN = 'hr_admin',
  GUEST = 'guest',
  EMPLOYEE = 'employee', // General employee role
}

export enum EmploymentType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract',
  INTERN = 'intern',
}

export enum AccountStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  LOCKED = 'locked',
  PENDING_ACTIVATION = 'pending_activation',
  SUSPENDED = 'suspended',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column({ name: 'first_name' })
  firstName: string;

  @Column({ name: 'last_name' })
  lastName: string;

  @Column({ nullable: true })
  title: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.ENGINEER
  })
  role: UserRole;

  @Column({ name: 'organizational_unit_id', nullable: true })
  organizationalUnitId: number | null;

  @Column({ name: 'manager_id', nullable: true })
  managerId: number;

  @Column({ name: 'hire_date', type: 'date', nullable: true })
  hireDate: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  salary: number;

  @Column({
    name: 'employment_type',
    type: 'enum',
    enum: EmploymentType,
    default: EmploymentType.FULL_TIME,
  })
  employmentType: EmploymentType;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ name: 'emergency_contact_name', nullable: true })
  emergencyContactName: string;

  @Column({ name: 'emergency_contact_phone', nullable: true })
  emergencyContactPhone: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  // Enhanced Security Fields
  @Column({
    name: 'account_status',
    type: 'enum',
    enum: AccountStatus,
    default: AccountStatus.ACTIVE,
  })
  accountStatus: AccountStatus;

  @Column({ name: 'failed_login_attempts', default: 0 })
  failedLoginAttempts: number;

  @Column({ name: 'last_login_at', type: 'timestamp', nullable: true })
  lastLoginAt: Date;

  @Column({ name: 'last_login_ip', nullable: true })
  lastLoginIp: string;

  @Column({ name: 'password_changed_at', type: 'timestamp', nullable: true })
  passwordChangedAt: Date;

  @Column({ name: 'must_change_password', default: true })
  mustChangePassword: boolean;

  @Column({ name: 'account_locked_until', type: 'timestamp', nullable: true })
  accountLockedUntil: Date;

  @Column({ name: 'two_factor_enabled', default: false })
  twoFactorEnabled: boolean;

  @Column({ name: 'two_factor_secret', nullable: true })
  twoFactorSecret: string;

  @Column({ name: 'session_token', nullable: true, length: 1000 })
  sessionToken: string;

  @Column({ name: 'session_expires_at', type: 'timestamp', nullable: true })
  sessionExpiresAt: Date;

  @Column({ name: 'refresh_token', nullable: true, length: 1000 })
  refreshToken: string;

  @Column({ name: 'refresh_token_expires_at', type: 'timestamp', nullable: true })
  refreshTokenExpiresAt: Date;

  @Column({ name: 'mfa_backup_codes', type: 'text', nullable: true })
  mfaBackupCodes: string;

  @Column({ name: 'password_history', type: 'text', nullable: true })
  passwordHistory: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => OrganizationalUnit, (unit) => unit.members, { nullable: true })
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'manager_id' })
  manager: User;

  @OneToMany(() => User, (user) => user.manager)
  directReports: User[];

  @OneToMany(() => UserSkillset, (userSkillset) => userSkillset.user)
  userSkillsets: UserSkillset[];

  @OneToMany(() => OrganizationalUnit, (unit) => unit.manager)
  managedUnits: OrganizationalUnit[];
}
