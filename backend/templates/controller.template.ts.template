import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { {{SERVICE_NAME}} } from './{{SERVICE_FILE}}';
import { Create{{ENTITY_NAME}}Dto } from './dto/create-{{ENTITY_LOWER}}.dto';
import { Update{{ENTITY_NAME}}Dto } from './dto/update-{{ENTITY_LOWER}}.dto';

/**
 * 🔐 {{ENTITY_NAME}} Controller
 * 
 * This controller follows the SQL API authentication patterns:
 * - All endpoints protected with JwtAuthGuard
 * - Role-based access control where appropriate
 * - User context passed through @Request() req
 * - Comprehensive security audit logging
 */
@Controller('{{ROUTE_NAME}}')
@UseGuards(JwtAuthGuard)
export class {{ENTITY_NAME}}Controller {
  constructor(private readonly {{SERVICE_INSTANCE}}: {{SERVICE_NAME}}) {}

  /**
   * 🔐 Create new {{ENTITY_LOWER}}
   * Requires: HR_ADMIN or MANAGER role
   */
  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  create(@Body() create{{ENTITY_NAME}}Dto: Create{{ENTITY_NAME}}Dto, @Request() req) {
    return this.{{SERVICE_INSTANCE}}.create(create{{ENTITY_NAME}}Dto, req.user.userId, req.user.role);
  }

  /**
   * 🔐 Get all {{ROUTE_NAME}}
   * User context applied for data filtering
   */
  @Get()
  findAll(@Request() req, @Query() filters?: any) {
    return this.{{SERVICE_INSTANCE}}.findAll(req.user.userId, req.user.role, filters);
  }

  /**
   * 🔐 Get specific {{ENTITY_LOWER}}
   * User context applied for access control
   */
  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.{{SERVICE_INSTANCE}}.findOne(+id, req.user.userId, req.user.role);
  }

  /**
   * 🔐 Update {{ENTITY_LOWER}}
   * User context applied for permission validation
   */
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() update{{ENTITY_NAME}}Dto: Update{{ENTITY_NAME}}Dto,
    @Request() req
  ) {
    return this.{{SERVICE_INSTANCE}}.update(+id, update{{ENTITY_NAME}}Dto, req.user.userId, req.user.role);
  }

  /**
   * 🔐 Delete {{ENTITY_LOWER}}
   * Requires: HR_ADMIN role only
   */
  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  remove(@Param('id') id: string, @Request() req) {
    return this.{{SERVICE_INSTANCE}}.remove(+id, req.user.userId, req.user.role);
  }

  /**
   * 🔐 Get {{ENTITY_LOWER}} statistics
   * Requires: HR_ADMIN or MANAGER role
   */
  @Get('statistics/overview')
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  getStatistics(@Request() req) {
    return this.{{SERVICE_INSTANCE}}.getStatistics(req.user.userId, req.user.role);
  }
}

/**
 * 🔐 SECURITY COMPLIANCE CHECKLIST:
 * 
 * ✅ @UseGuards(JwtAuthGuard) applied to controller
 * ✅ @UseGuards(RolesGuard) applied to role-protected endpoints
 * ✅ @Roles() decorator used for access control
 * ✅ @Request() req parameter for user context
 * ✅ User ID and role passed to service methods
 * ✅ Comprehensive security documentation
 * 
 * NEVER:
 * ❌ Remove authentication guards
 * ❌ Bypass user context validation
 * ❌ Allow direct database access without user permissions
 * ❌ Expose sensitive data without proper filtering
 */
