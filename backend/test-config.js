#!/usr/bin/env node

/**
 * 🔐 Environment Configuration Test
 * 
 * Tests the environment configuration setup
 */

const fs = require('fs');
const path = require('path');

console.log('🔐 Testing Environment Configuration...\n');

// Test 1: Check if .env file exists and has required variables
function testEnvFile() {
  console.log('📋 Test 1: Environment File Validation');
  
  const envPath = path.join(__dirname, '.env');
  if (!fs.existsSync(envPath)) {
    console.log('❌ .env file not found');
    return false;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredVars = [
    'JWT_SECRET',
    'ENCRYPTION_MASTER_KEY',
    'ENCRYPTION_SALT',
    'DB_HOST',
    'DB_USERNAME',
    'DB_PASSWORD'
  ];
  
  let allPresent = true;
  requiredVars.forEach(varName => {
    if (!envContent.includes(varName + '=')) {
      console.log(`❌ Missing required variable: ${varName}`);
      allPresent = false;
    } else {
      console.log(`✅ Found: ${varName}`);
    }
  });
  
  return allPresent;
}

// Test 2: Check if production secrets were generated
function testProductionSecrets() {
  console.log('\n📋 Test 2: Production Secrets Generation');
  
  const secretsDir = path.join(__dirname, 'production-secrets');
  if (!fs.existsSync(secretsDir)) {
    console.log('❌ Production secrets directory not found');
    return false;
  }
  
  const requiredFiles = [
    '.env.production',
    'secrets-manifest.json',
    'jwt_secret.secret',
    'encryption_master_key.secret'
  ];
  
  let allPresent = true;
  requiredFiles.forEach(fileName => {
    const filePath = path.join(secretsDir, fileName);
    if (!fs.existsSync(filePath)) {
      console.log(`❌ Missing file: ${fileName}`);
      allPresent = false;
    } else {
      console.log(`✅ Found: ${fileName}`);
    }
  });
  
  return allPresent;
}

// Test 3: Validate secret strength
function testSecretStrength() {
  console.log('\n📋 Test 3: Secret Strength Validation');
  
  const secretsDir = path.join(__dirname, 'production-secrets');
  const jwtSecretPath = path.join(secretsDir, 'jwt_secret.secret');
  const encryptionKeyPath = path.join(secretsDir, 'encryption_master_key.secret');
  
  let allValid = true;
  
  if (fs.existsSync(jwtSecretPath)) {
    const jwtSecret = fs.readFileSync(jwtSecretPath, 'utf8').trim();
    if (jwtSecret.length >= 32) {
      console.log('✅ JWT secret length is adequate (>= 32 characters)');
    } else {
      console.log('❌ JWT secret is too short (< 32 characters)');
      allValid = false;
    }
  }
  
  if (fs.existsSync(encryptionKeyPath)) {
    const encryptionKey = fs.readFileSync(encryptionKeyPath, 'utf8').trim();
    if (encryptionKey.length >= 32) {
      console.log('✅ Encryption key length is adequate (>= 32 characters)');
    } else {
      console.log('❌ Encryption key is too short (< 32 characters)');
      allValid = false;
    }
  }
  
  return allValid;
}

// Test 4: Check configuration files
function testConfigFiles() {
  console.log('\n📋 Test 4: Configuration Files');
  
  const configFiles = [
    'src/config/config.module.ts',
    'src/config/environment.config.ts',
    '.env.example'
  ];
  
  let allPresent = true;
  configFiles.forEach(fileName => {
    const filePath = path.join(__dirname, fileName);
    if (!fs.existsSync(filePath)) {
      console.log(`❌ Missing file: ${fileName}`);
      allPresent = false;
    } else {
      console.log(`✅ Found: ${fileName}`);
    }
  });
  
  return allPresent;
}

// Run all tests
function runTests() {
  const test1 = testEnvFile();
  const test2 = testProductionSecrets();
  const test3 = testSecretStrength();
  const test4 = testConfigFiles();
  
  console.log('\n🔐 Environment Configuration Test Results:');
  console.log('==========================================');
  console.log(`Environment File: ${test1 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Production Secrets: ${test2 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Secret Strength: ${test3 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Configuration Files: ${test4 ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = test1 && test2 && test3 && test4;
  console.log(`\nOverall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 Environment configuration is ready for production!');
    console.log('\n📋 Next Steps:');
    console.log('1. Review the .env.production file');
    console.log('2. Update database and email configuration');
    console.log('3. Deploy secrets to your production environment');
    console.log('4. Test the application with production configuration');
  } else {
    console.log('\n⚠️  Please fix the failing tests before proceeding to production.');
  }
  
  return allPassed;
}

// Run if called directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
