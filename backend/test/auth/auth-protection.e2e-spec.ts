import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { UsersService } from '../../src/users/users.service';
import { AuthService } from '../../src/auth/auth.service';
import { UserRole } from '../../src/users/entities/user.entity';

/**
 * 🔐 AUTHENTICATION PROTECTION TESTS
 * 
 * These tests ensure the authentication system remains secure and functional.
 * They must ALL PASS before any code changes can be deployed.
 * 
 * Coverage:
 * - Login/logout flows
 * - JWT token validation
 * - Session management
 * - Security features (rate limiting, account locking)
 * - Role-based access control
 * - Password security
 * - MFA functionality
 */

describe('Authentication Protection Suite (e2e)', () => {
  let app: INestApplication;
  let usersService: UsersService;
  let authService: AuthService;

  // Test user credentials
  const testUser = {
    email: '<EMAIL>',
    password: 'SecureTest123!',
    firstName: 'Test',
    lastName: 'User',
    role: UserRole.EMPLOYEE
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    usersService = moduleFixture.get<UsersService>(UsersService);
    authService = moduleFixture.get<AuthService>(AuthService);

    await app.init();

    // Create test user
    await usersService.create(testUser);
  });

  afterAll(async () => {
    // Cleanup test user
    const user = await usersService.findByEmail(testUser.email);
    if (user) {
      await usersService.remove(user.id);
    }
    await app.close();
  });

  describe('🔐 Core Authentication Flow', () => {
    it('should successfully login with valid credentials', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(201);

      expect(response.body).toHaveProperty('access_token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(testUser.email);
      expect(response.body.user).not.toHaveProperty('password');
    });

    it('should reject login with invalid credentials', async () => {
      await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: 'WrongPassword123!'
        })
        .expect(401);
    });

    it('should reject login with non-existent user', async () => {
      await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'AnyPassword123!'
        })
        .expect(401);
    });
  });

  describe('🔐 JWT Token Security', () => {
    let validToken: string;

    beforeAll(async () => {
      const loginResponse = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });
      validToken = loginResponse.body.access_token;
    });

    it('should accept valid JWT token', async () => {
      await request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', `Bearer ${validToken}`)
        .expect(200);
    });

    it('should reject invalid JWT token', async () => {
      await request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', 'Bearer invalid.jwt.token')
        .expect(401);
    });

    it('should reject requests without token', async () => {
      await request(app.getHttpServer())
        .get('/auth/profile')
        .expect(401);
    });

    it('should reject malformed authorization header', async () => {
      await request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', 'InvalidFormat')
        .expect(401);
    });
  });

  describe('🔐 Session Management', () => {
    it('should create session on login', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });

      expect(response.body).toHaveProperty('sessionId');
      expect(response.body.sessionId).toBeTruthy();
    });

    it('should invalidate session on logout', async () => {
      // Login first
      const loginResponse = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });

      const token = loginResponse.body.access_token;

      // Logout
      await request(app.getHttpServer())
        .post('/auth/logout')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      // Token should be invalid after logout
      await request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(401);
    });
  });

  describe('🔐 Security Features', () => {
    it('should enforce rate limiting on failed attempts', async () => {
      const promises = [];

      // Make multiple failed login attempts
      for (let i = 0; i < 6; i++) {
        promises.push(
          request(app.getHttpServer())
            .post('/auth/login')
            .send({
              email: testUser.email,
              password: 'WrongPassword'
            })
        );
      }

      const responses = await Promise.all(promises);

      // Should have some rate limiting responses
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should hash passwords securely', async () => {
      const user = await usersService.findByEmail(testUser.email);

      // Password should be hashed (bcrypt format)
      expect(user.password).toMatch(/^\$2[aby]\$\d+\$/);
      expect(user.password).not.toBe(testUser.password);
    });
  });

  describe('🔐 Role-Based Access Control', () => {
    it('should enforce role-based access to protected endpoints', async () => {
      const loginResponse = await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });

      const token = loginResponse.body.access_token;

      // Employee should not access admin endpoints
      await request(app.getHttpServer())
        .get('/users') // Admin-only endpoint
        .set('Authorization', `Bearer ${token}`)
        .expect(403);
    });
  });

  describe('🔐 Input Validation & Sanitization', () => {
    it('should reject malicious login attempts', async () => {
      const maliciousInputs = [
        { email: '<script>alert("xss")</script>', password: 'test' },
        { email: '<EMAIL>', password: '"; DROP TABLE users; --' },
        { email: '../../etc/passwd', password: 'test' },
        { email: '<EMAIL>', password: '=cmd|calc.exe' }
      ];

      for (const input of maliciousInputs) {
        await request(app.getHttpServer())
          .post('/auth/login')
          .send(input)
          .expect(401); // Should reject, not crash
      }
    });
  });

  describe('🔐 Timing Attack Protection', () => {
    it('should have consistent response times for invalid users vs invalid passwords', async () => {
      const startTime1 = Date.now();
      await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'AnyPassword123!'
        });
      const time1 = Date.now() - startTime1;

      const startTime2 = Date.now();
      await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: 'WrongPassword123!'
        });
      const time2 = Date.now() - startTime2;

      // Response times should be similar (within 50ms)
      const timeDifference = Math.abs(time1 - time2);
      expect(timeDifference).toBeLessThan(50);
    });
  });
});
