-- Complete eHRx Database Schema for PostgreSQL
-- Converted from MySQL to PostgreSQL syntax

-- Drop existing tables if they exist (in reverse dependency order)
DROP TABLE IF EXISTS monthly_dashboard_team_targets CASCADE;
DROP TABLE IF EXISTS monthly_dashboard_kpi_values CASCADE;
DROP TABLE IF EXISTS monthly_dashboard_submissions CASCADE;
DROP TABLE IF EXISTS monthly_dashboard_kpis CASCADE;
DROP TABLE IF EXISTS assessment_criteria_responses CASCADE;
DROP TABLE IF EXISTS assessment_criteria_scoring CASCADE;
DROP TABLE IF EXISTS assessment_scoring_scale_levels CASCADE;
DROP TABLE IF EXISTS assessment_scoring_scales CASCADE;
DROP TABLE IF EXISTS assessment_template_criteria CASCADE;
DROP TABLE IF EXISTS assessment_criteria_inheritance CASCADE;
DROP TABLE IF EXISTS assessment_criteria CASCADE;
DROP TABLE IF EXISTS assessment_criteria_levels CASCADE;
DROP TABLE IF EXISTS assessment_responses CASCADE;
DROP TABLE IF EXISTS assessment_instances CASCADE;
DROP TABLE IF EXISTS scoring_rules CASCADE;
DROP TABLE IF EXISTS assessment_areas CASCADE;
DROP TABLE IF EXISTS assessment_templates CASCADE;
DROP TABLE IF EXISTS project_assignments CASCADE;
DROP TABLE IF EXISTS projects CASCADE;
DROP TABLE IF EXISTS user_skillsets CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS skillsets CASCADE;
DROP TABLE IF EXISTS organizational_units CASCADE;

-- Create ENUM types for PostgreSQL
CREATE TYPE organizational_unit_type AS ENUM ('organization', 'division', 'department', 'team', 'squad', 'unit');
CREATE TYPE skillset_category AS ENUM ('programming', 'infrastructure', 'database', 'cloud', 'security', 'devops', 'networking', 'frontend', 'backend', 'mobile', 'data', 'ai_ml', 'project_management', 'soft_skills');
CREATE TYPE skill_level AS ENUM ('beginner', 'intermediate', 'advanced', 'expert');
CREATE TYPE user_role AS ENUM ('ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest', 'employee');
CREATE TYPE employment_type AS ENUM ('full_time', 'part_time', 'contract', 'intern');
CREATE TYPE account_status AS ENUM ('active', 'inactive', 'locked', 'pending_activation', 'suspended');
CREATE TYPE project_status AS ENUM ('planning', 'active', 'on_hold', 'completed', 'cancelled');
CREATE TYPE project_priority AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE assignment_role AS ENUM ('lead', 'developer', 'tester', 'analyst', 'designer', 'consultant');
CREATE TYPE assessment_status AS ENUM ('draft', 'active', 'archived');
CREATE TYPE assessment_type AS ENUM ('performance', 'skills', 'feedback', 'goal_setting');
CREATE TYPE rule_type AS ENUM ('bonus', 'penalty', 'threshold', 'conditional');
CREATE TYPE instance_status AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');
CREATE TYPE criteria_level_type AS ENUM ('hr_level', 'organizational_level', 'team_level');
CREATE TYPE criteria_type AS ENUM ('performance', 'behavior', 'skill', 'goal');
CREATE TYPE kpi_type AS ENUM ('percentage', 'number', 'currency', 'ratio', 'boolean');
CREATE TYPE traffic_light_status AS ENUM ('green', 'yellow', 'red');
CREATE TYPE submission_status AS ENUM ('draft', 'submitted', 'approved', 'rejected');

-- 1. Create organizational_units table
CREATE TABLE organizational_units (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type organizational_unit_type NOT NULL DEFAULT 'team',
    description TEXT,
    parent_id INTEGER,
    level INTEGER NOT NULL DEFAULT 0,
    manager_id INTEGER,
    budget DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 2. Create skillsets table
CREATE TABLE skillsets (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    category skillset_category NOT NULL,
    description TEXT,
    level_required skill_level NOT NULL DEFAULT 'intermediate',
    is_core_skill BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 3. Users table already exists - skipping creation

-- 4. Create user_skillsets table
CREATE TABLE user_skillsets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    skillset_id INTEGER NOT NULL,
    proficiency_level skill_level NOT NULL DEFAULT 'intermediate',
    years_experience DECIMAL(3,1) NOT NULL DEFAULT 0.0,
    is_certified BOOLEAN NOT NULL DEFAULT FALSE,
    certification_name VARCHAR(255),
    certification_date DATE,
    last_used_date DATE,
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, skillset_id)
);

-- 5. Create projects table
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    client_name VARCHAR(255),
    start_date DATE,
    end_date DATE,
    estimated_hours INTEGER,
    actual_hours INTEGER DEFAULT 0,
    budget DECIMAL(10,2),
    status project_status NOT NULL DEFAULT 'planning',
    priority project_priority NOT NULL DEFAULT 'medium',
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 6. Create project_assignments table
CREATE TABLE project_assignments (
    id SERIAL PRIMARY KEY,
    project_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    role assignment_role NOT NULL DEFAULT 'developer',
    allocated_hours INTEGER,
    hourly_rate DECIMAL(8,2),
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, user_id, role)
);

-- 7. Create assessment_templates table
CREATE TABLE assessment_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status assessment_status NOT NULL DEFAULT 'draft',
    assessment_type assessment_type NOT NULL DEFAULT 'performance',
    is_anonymous BOOLEAN NOT NULL DEFAULT FALSE,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 8. Create assessment_areas table
CREATE TABLE assessment_areas (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    weight DECIMAL(5,2) NOT NULL DEFAULT 1.00,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 9. Create scoring_rules table
CREATE TABLE scoring_rules (
    id SERIAL PRIMARY KEY,
    area_id INTEGER NOT NULL,
    rule_type rule_type NOT NULL,
    condition_field VARCHAR(255),
    condition_operator VARCHAR(10),
    condition_value VARCHAR(255),
    score_modifier DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 10. Create assessment_instances table
CREATE TABLE assessment_instances (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL,
    employee_id INTEGER NOT NULL,
    assessor_id INTEGER NOT NULL,
    status instance_status NOT NULL DEFAULT 'pending',
    scheduled_date DATE,
    completed_date DATE,
    total_score DECIMAL(5,2),
    max_possible_score DECIMAL(5,2),
    percentage_score DECIMAL(5,2),
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 11. Create assessment_responses table
CREATE TABLE assessment_responses (
    id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    area_id INTEGER NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL DEFAULT 5.00,
    comments TEXT,
    evidence_links TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(assessment_id, area_id)
);

-- 12. Create assessment_criteria_levels table
CREATE TABLE assessment_criteria_levels (
    id SERIAL PRIMARY KEY,
    level_type criteria_level_type NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 13. Create assessment_criteria table
CREATE TABLE assessment_criteria (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    criteria_type criteria_type NOT NULL DEFAULT 'performance',
    level_id INTEGER NOT NULL,
    parent_id INTEGER,
    weight DECIMAL(5,2) NOT NULL DEFAULT 1.00,
    is_required BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 14. Create assessment_criteria_inheritance table
CREATE TABLE assessment_criteria_inheritance (
    id SERIAL PRIMARY KEY,
    criteria_id INTEGER NOT NULL,
    target_organizational_unit_id INTEGER NOT NULL,
    inherited_from_unit_id INTEGER,
    is_customized BOOLEAN NOT NULL DEFAULT FALSE,
    custom_weight DECIMAL(5,2),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(criteria_id, target_organizational_unit_id)
);

-- 15. Create assessment_template_criteria table
CREATE TABLE assessment_template_criteria (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL,
    criteria_id INTEGER NOT NULL,
    weight DECIMAL(5,2) NOT NULL DEFAULT 1.00,
    is_required BOOLEAN NOT NULL DEFAULT FALSE,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(template_id, criteria_id)
);

-- 16. Create assessment_scoring_scales table
CREATE TABLE assessment_scoring_scales (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    min_score DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    max_score DECIMAL(5,2) NOT NULL DEFAULT 5.00,
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 17. Create assessment_scoring_scale_levels table
CREATE TABLE assessment_scoring_scale_levels (
    id SERIAL PRIMARY KEY,
    scoring_scale_id INTEGER NOT NULL,
    level_name VARCHAR(100) NOT NULL,
    level_description TEXT,
    min_score DECIMAL(5,2) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL,
    points DECIMAL(5,2) NOT NULL,
    color_code VARCHAR(7),
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 18. Create assessment_criteria_scoring table
CREATE TABLE assessment_criteria_scoring (
    id SERIAL PRIMARY KEY,
    criteria_id INTEGER NOT NULL,
    scoring_scale_id INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(criteria_id, scoring_scale_id)
);

-- 19. Create assessment_criteria_responses table
CREATE TABLE assessment_criteria_responses (
    id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    criteria_id INTEGER NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    max_score DECIMAL(5,2) NOT NULL DEFAULT 5.00,
    scoring_scale_level_id INTEGER,
    comments TEXT,
    evidence_links TEXT,
    assessor_notes TEXT,
    employee_self_score DECIMAL(5,2),
    employee_comments TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(assessment_id, criteria_id)
);

-- 20. Create monthly_dashboard_kpis table
CREATE TABLE monthly_dashboard_kpis (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    kpi_type kpi_type NOT NULL DEFAULT 'number',
    unit VARCHAR(50),
    calculation_method TEXT,
    target_value DECIMAL(10,2),
    green_threshold DECIMAL(10,2),
    yellow_threshold DECIMAL(10,2),
    red_threshold DECIMAL(10,2),
    is_higher_better BOOLEAN NOT NULL DEFAULT TRUE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 21. Create monthly_dashboard_submissions table
CREATE TABLE monthly_dashboard_submissions (
    id SERIAL PRIMARY KEY,
    organizational_unit_id INTEGER NOT NULL,
    submission_year INTEGER NOT NULL,
    submission_month INTEGER NOT NULL,
    submitted_by INTEGER NOT NULL,
    submission_date TIMESTAMP,
    status submission_status NOT NULL DEFAULT 'draft',
    overall_traffic_light traffic_light_status,
    summary_comments TEXT,
    action_items TEXT,
    approved_by INTEGER,
    approved_date TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organizational_unit_id, submission_year, submission_month)
);

-- 22. Create monthly_dashboard_kpi_values table
CREATE TABLE monthly_dashboard_kpi_values (
    id SERIAL PRIMARY KEY,
    submission_id INTEGER NOT NULL,
    kpi_id INTEGER NOT NULL,
    actual_value DECIMAL(10,2),
    target_value DECIMAL(10,2),
    traffic_light_status traffic_light_status,
    comments TEXT,
    data_source VARCHAR(255),
    calculation_details TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(submission_id, kpi_id)
);

-- 23. Create monthly_dashboard_team_targets table
CREATE TABLE monthly_dashboard_team_targets (
    id SERIAL PRIMARY KEY,
    organizational_unit_id INTEGER NOT NULL,
    kpi_id INTEGER NOT NULL,
    target_year INTEGER NOT NULL,
    target_month INTEGER,
    target_value DECIMAL(10,2) NOT NULL,
    green_threshold DECIMAL(10,2),
    yellow_threshold DECIMAL(10,2),
    red_threshold DECIMAL(10,2),
    notes TEXT,
    set_by INTEGER NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add Foreign Key Constraints
ALTER TABLE organizational_units ADD CONSTRAINT fk_org_units_parent
    FOREIGN KEY (parent_id) REFERENCES organizational_units(id) ON DELETE CASCADE;
ALTER TABLE organizational_units ADD CONSTRAINT fk_org_units_manager
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE user_skillsets ADD CONSTRAINT fk_user_skillsets_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE user_skillsets ADD CONSTRAINT fk_user_skillsets_skillset
    FOREIGN KEY (skillset_id) REFERENCES skillsets(id) ON DELETE CASCADE;

ALTER TABLE projects ADD CONSTRAINT fk_projects_created_by
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT;

ALTER TABLE project_assignments ADD CONSTRAINT fk_project_assignments_project
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE;
ALTER TABLE project_assignments ADD CONSTRAINT fk_project_assignments_user
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE assessment_templates ADD CONSTRAINT fk_assessment_templates_created_by
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT;

ALTER TABLE assessment_areas ADD CONSTRAINT fk_assessment_areas_template
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE CASCADE;

ALTER TABLE scoring_rules ADD CONSTRAINT fk_scoring_rules_area
    FOREIGN KEY (area_id) REFERENCES assessment_areas(id) ON DELETE CASCADE;

ALTER TABLE assessment_instances ADD CONSTRAINT fk_assessment_instances_template
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE RESTRICT;
ALTER TABLE assessment_instances ADD CONSTRAINT fk_assessment_instances_employee
    FOREIGN KEY (employee_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE assessment_instances ADD CONSTRAINT fk_assessment_instances_assessor
    FOREIGN KEY (assessor_id) REFERENCES users(id) ON DELETE RESTRICT;

ALTER TABLE assessment_responses ADD CONSTRAINT fk_assessment_responses_assessment
    FOREIGN KEY (assessment_id) REFERENCES assessment_instances(id) ON DELETE CASCADE;
ALTER TABLE assessment_responses ADD CONSTRAINT fk_assessment_responses_area
    FOREIGN KEY (area_id) REFERENCES assessment_areas(id) ON DELETE CASCADE;

ALTER TABLE assessment_criteria ADD CONSTRAINT fk_assessment_criteria_level
    FOREIGN KEY (level_id) REFERENCES assessment_criteria_levels(id) ON DELETE RESTRICT;
ALTER TABLE assessment_criteria ADD CONSTRAINT fk_assessment_criteria_parent
    FOREIGN KEY (parent_id) REFERENCES assessment_criteria(id) ON DELETE CASCADE;

ALTER TABLE assessment_criteria_inheritance ADD CONSTRAINT fk_criteria_inheritance_criteria
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id) ON DELETE CASCADE;
ALTER TABLE assessment_criteria_inheritance ADD CONSTRAINT fk_criteria_inheritance_target_unit
    FOREIGN KEY (target_organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE;
ALTER TABLE assessment_criteria_inheritance ADD CONSTRAINT fk_criteria_inheritance_inherited_unit
    FOREIGN KEY (inherited_from_unit_id) REFERENCES organizational_units(id) ON DELETE SET NULL;

ALTER TABLE assessment_template_criteria ADD CONSTRAINT fk_template_criteria_template
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE CASCADE;
ALTER TABLE assessment_template_criteria ADD CONSTRAINT fk_template_criteria_criteria
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id) ON DELETE CASCADE;

ALTER TABLE assessment_scoring_scale_levels ADD CONSTRAINT fk_scale_levels_scale
    FOREIGN KEY (scoring_scale_id) REFERENCES assessment_scoring_scales(id) ON DELETE CASCADE;

ALTER TABLE assessment_criteria_scoring ADD CONSTRAINT fk_criteria_scoring_criteria
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id) ON DELETE CASCADE;
ALTER TABLE assessment_criteria_scoring ADD CONSTRAINT fk_criteria_scoring_scale
    FOREIGN KEY (scoring_scale_id) REFERENCES assessment_scoring_scales(id) ON DELETE CASCADE;

ALTER TABLE assessment_criteria_responses ADD CONSTRAINT fk_criteria_responses_assessment
    FOREIGN KEY (assessment_id) REFERENCES assessment_instances(id) ON DELETE CASCADE;
ALTER TABLE assessment_criteria_responses ADD CONSTRAINT fk_criteria_responses_criteria
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id) ON DELETE CASCADE;
ALTER TABLE assessment_criteria_responses ADD CONSTRAINT fk_criteria_responses_scale_level
    FOREIGN KEY (scoring_scale_level_id) REFERENCES assessment_scoring_scale_levels(id) ON DELETE SET NULL;

ALTER TABLE monthly_dashboard_submissions ADD CONSTRAINT fk_dashboard_submissions_unit
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE;
ALTER TABLE monthly_dashboard_submissions ADD CONSTRAINT fk_dashboard_submissions_submitted_by
    FOREIGN KEY (submitted_by) REFERENCES users(id) ON DELETE RESTRICT;
ALTER TABLE monthly_dashboard_submissions ADD CONSTRAINT fk_dashboard_submissions_approved_by
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE monthly_dashboard_kpi_values ADD CONSTRAINT fk_kpi_values_submission
    FOREIGN KEY (submission_id) REFERENCES monthly_dashboard_submissions(id) ON DELETE CASCADE;
ALTER TABLE monthly_dashboard_kpi_values ADD CONSTRAINT fk_kpi_values_kpi
    FOREIGN KEY (kpi_id) REFERENCES monthly_dashboard_kpis(id) ON DELETE CASCADE;

ALTER TABLE monthly_dashboard_team_targets ADD CONSTRAINT fk_team_targets_unit
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE;
ALTER TABLE monthly_dashboard_team_targets ADD CONSTRAINT fk_team_targets_kpi
    FOREIGN KEY (kpi_id) REFERENCES monthly_dashboard_kpis(id) ON DELETE CASCADE;
ALTER TABLE monthly_dashboard_team_targets ADD CONSTRAINT fk_team_targets_set_by
    FOREIGN KEY (set_by) REFERENCES users(id) ON DELETE RESTRICT;

-- Create Indexes for Performance
CREATE INDEX idx_org_units_parent_id ON organizational_units(parent_id);
CREATE INDEX idx_org_units_type ON organizational_units(type);
CREATE INDEX idx_org_units_level ON organizational_units(level);
CREATE INDEX idx_org_units_manager_id ON organizational_units(manager_id);
CREATE INDEX idx_org_units_is_active ON organizational_units(is_active);

CREATE INDEX idx_skillsets_category ON skillsets(category);
CREATE INDEX idx_skillsets_level_required ON skillsets(level_required);
CREATE INDEX idx_skillsets_is_core_skill ON skillsets(is_core_skill);
CREATE INDEX idx_skillsets_is_active ON skillsets(is_active);

CREATE INDEX idx_user_skillsets_user_id ON user_skillsets(user_id);
CREATE INDEX idx_user_skillsets_skillset_id ON user_skillsets(skillset_id);
CREATE INDEX idx_user_skillsets_proficiency ON user_skillsets(proficiency_level);

CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_priority ON projects(priority);
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_dates ON projects(start_date, end_date);

CREATE INDEX idx_project_assignments_project_id ON project_assignments(project_id);
CREATE INDEX idx_project_assignments_user_id ON project_assignments(user_id);
CREATE INDEX idx_project_assignments_role ON project_assignments(role);
CREATE INDEX idx_project_assignments_is_active ON project_assignments(is_active);

CREATE INDEX idx_assessment_templates_status ON assessment_templates(status);
CREATE INDEX idx_assessment_templates_type ON assessment_templates(assessment_type);
CREATE INDEX idx_assessment_templates_created_by ON assessment_templates(created_by);

CREATE INDEX idx_assessment_areas_template_id ON assessment_areas(template_id);
CREATE INDEX idx_assessment_areas_sort_order ON assessment_areas(sort_order);

CREATE INDEX idx_scoring_rules_area_id ON scoring_rules(area_id);
CREATE INDEX idx_scoring_rules_type ON scoring_rules(rule_type);

CREATE INDEX idx_assessment_instances_template_id ON assessment_instances(template_id);
CREATE INDEX idx_assessment_instances_employee_id ON assessment_instances(employee_id);
CREATE INDEX idx_assessment_instances_assessor_id ON assessment_instances(assessor_id);
CREATE INDEX idx_assessment_instances_status ON assessment_instances(status);
CREATE INDEX idx_assessment_instances_dates ON assessment_instances(scheduled_date, completed_date);

CREATE INDEX idx_assessment_responses_assessment_id ON assessment_responses(assessment_id);
CREATE INDEX idx_assessment_responses_area_id ON assessment_responses(area_id);

CREATE INDEX idx_criteria_levels_type ON assessment_criteria_levels(level_type);
CREATE INDEX idx_criteria_levels_is_active ON assessment_criteria_levels(is_active);

CREATE INDEX idx_assessment_criteria_level_id ON assessment_criteria(level_id);
CREATE INDEX idx_assessment_criteria_parent_id ON assessment_criteria(parent_id);
CREATE INDEX idx_assessment_criteria_type ON assessment_criteria(criteria_type);
CREATE INDEX idx_assessment_criteria_is_active ON assessment_criteria(is_active);

CREATE INDEX idx_criteria_inheritance_criteria_id ON assessment_criteria_inheritance(criteria_id);
CREATE INDEX idx_criteria_inheritance_target_unit ON assessment_criteria_inheritance(target_organizational_unit_id);
CREATE INDEX idx_criteria_inheritance_is_active ON assessment_criteria_inheritance(is_active);

CREATE INDEX idx_template_criteria_template_id ON assessment_template_criteria(template_id);
CREATE INDEX idx_template_criteria_criteria_id ON assessment_template_criteria(criteria_id);

CREATE INDEX idx_scoring_scales_is_default ON assessment_scoring_scales(is_default);
CREATE INDEX idx_scoring_scales_is_active ON assessment_scoring_scales(is_active);

CREATE INDEX idx_scale_levels_scoring_scale_id ON assessment_scoring_scale_levels(scoring_scale_id);
CREATE INDEX idx_scale_levels_sort_order ON assessment_scoring_scale_levels(sort_order);

CREATE INDEX idx_criteria_scoring_criteria_id ON assessment_criteria_scoring(criteria_id);
CREATE INDEX idx_criteria_scoring_scale_id ON assessment_criteria_scoring(scoring_scale_id);
CREATE INDEX idx_criteria_scoring_is_active ON assessment_criteria_scoring(is_active);

CREATE INDEX idx_criteria_responses_assessment_id ON assessment_criteria_responses(assessment_id);
CREATE INDEX idx_criteria_responses_criteria_id ON assessment_criteria_responses(criteria_id);

CREATE INDEX idx_kpis_type ON monthly_dashboard_kpis(kpi_type);
CREATE INDEX idx_kpis_is_active ON monthly_dashboard_kpis(is_active);
CREATE INDEX idx_kpis_sort_order ON monthly_dashboard_kpis(sort_order);

CREATE INDEX idx_dashboard_submissions_unit_id ON monthly_dashboard_submissions(organizational_unit_id);
CREATE INDEX idx_dashboard_submissions_year_month ON monthly_dashboard_submissions(submission_year, submission_month);
CREATE INDEX idx_dashboard_submissions_status ON monthly_dashboard_submissions(status);
CREATE INDEX idx_dashboard_submissions_submitted_by ON monthly_dashboard_submissions(submitted_by);

CREATE INDEX idx_kpi_values_submission_id ON monthly_dashboard_kpi_values(submission_id);
CREATE INDEX idx_kpi_values_kpi_id ON monthly_dashboard_kpi_values(kpi_id);
CREATE INDEX idx_kpi_values_traffic_light ON monthly_dashboard_kpi_values(traffic_light_status);

CREATE INDEX idx_team_targets_unit_id ON monthly_dashboard_team_targets(organizational_unit_id);
CREATE INDEX idx_team_targets_kpi_id ON monthly_dashboard_team_targets(kpi_id);
CREATE INDEX idx_team_targets_year_month ON monthly_dashboard_team_targets(target_year, target_month);

-- Create trigger function for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_organizational_units_updated_at BEFORE UPDATE ON organizational_units FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_skillsets_updated_at BEFORE UPDATE ON skillsets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_skillsets_updated_at BEFORE UPDATE ON user_skillsets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_assignments_updated_at BEFORE UPDATE ON project_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_templates_updated_at BEFORE UPDATE ON assessment_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_areas_updated_at BEFORE UPDATE ON assessment_areas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_instances_updated_at BEFORE UPDATE ON assessment_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_responses_updated_at BEFORE UPDATE ON assessment_responses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_criteria_levels_updated_at BEFORE UPDATE ON assessment_criteria_levels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_criteria_updated_at BEFORE UPDATE ON assessment_criteria FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_criteria_inheritance_updated_at BEFORE UPDATE ON assessment_criteria_inheritance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_template_criteria_updated_at BEFORE UPDATE ON assessment_template_criteria FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_scoring_scales_updated_at BEFORE UPDATE ON assessment_scoring_scales FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_scoring_scale_levels_updated_at BEFORE UPDATE ON assessment_scoring_scale_levels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_criteria_scoring_updated_at BEFORE UPDATE ON assessment_criteria_scoring FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_criteria_responses_updated_at BEFORE UPDATE ON assessment_criteria_responses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_monthly_dashboard_kpis_updated_at BEFORE UPDATE ON monthly_dashboard_kpis FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_monthly_dashboard_submissions_updated_at BEFORE UPDATE ON monthly_dashboard_submissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_monthly_dashboard_kpi_values_updated_at BEFORE UPDATE ON monthly_dashboard_kpi_values FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_monthly_dashboard_team_targets_updated_at BEFORE UPDATE ON monthly_dashboard_team_targets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
