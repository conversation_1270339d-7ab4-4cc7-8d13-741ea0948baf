-- Create users table for PostgreSQL
CREATE TYPE user_role AS ENUM ('ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest', 'employee');
CREATE TYPE employment_type AS ENUM ('full_time', 'part_time', 'contract', 'intern');
CREATE TYPE account_status AS ENUM ('active', 'inactive', 'locked', 'pending_activation', 'suspended');

CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    title VARCHAR(255),
    role user_role NOT NULL DEFAULT 'engineer',
    organizational_unit_id INTEGER,
    manager_id INTEGER,
    hire_date DATE,
    salary DECIMAL(10,2),
    employment_type employment_type NOT NULL DEFAULT 'full_time',
    location VARCHAR(255),
    phone VARCHAR(255),
    emergency_contact_name VA<PERSON><PERSON><PERSON>(255),
    emergency_contact_phone VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT true,
    account_status account_status NOT NULL DEFAULT 'active',
    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(255),
    password_changed_at TIMESTAMP,
    must_change_password BOOLEAN NOT NULL DEFAULT true,
    account_locked_until TIMESTAMP,
    two_factor_enabled BOOLEAN NOT NULL DEFAULT false,
    two_factor_secret VARCHAR(255),
    session_token VARCHAR(1000),
    session_expires_at TIMESTAMP,
    refresh_token VARCHAR(1000),
    refresh_token_expires_at TIMESTAMP,
    mfa_backup_codes TEXT,
    password_history TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Insert test user with password 'password123'
INSERT INTO users (
    email, password, first_name, last_name, role, is_active, 
    account_status, must_change_password, failed_login_attempts
) VALUES (
    '<EMAIL>',
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'Admin',
    'User',
    'hr_admin',
    true,
    'active',
    false,
    0
);

-- Insert test user
INSERT INTO users (
    email, password, first_name, last_name, role, is_active, 
    account_status, must_change_password, failed_login_attempts
) VALUES (
    '<EMAIL>',
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    'Test',
    'User',
    'engineer',
    true,
    'active',
    false,
    0
);
