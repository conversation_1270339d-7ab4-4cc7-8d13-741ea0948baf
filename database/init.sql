-- EHRX Enhanced Database Initialization Script (PostgreSQL)
-- This script creates the complete, enhanced database schema with all security features
-- Consolidates all previous migration scripts into a single, authoritative source
-- MIGRATED FROM MARIADB TO POSTGRESQL

-- Note: Database creation should be done separately in PostgreSQL
-- CREATE DATABASE ehrx WITH ENCODING 'UTF8' LC_COLLATE='en_US.UTF-8' LC_CTYPE='en_US.UTF-8';

-- =====================================================
-- ORGANIZATIONAL STRUCTURE TABLES
-- =====================================================

-- Create ENUM types for PostgreSQL
CREATE TYPE organizational_unit_type AS ENUM ('organization', 'division', 'department', 'team', 'squad', 'unit');

-- Organizational Units table
CREATE TABLE IF NOT EXISTS organizational_units (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type organizational_unit_type NOT NULL DEFAULT 'team',
    description TEXT,
    parent_id INTEGER NULL,
    level INTEGER NOT NULL DEFAULT 0,
    manager_id INTEGER NULL,
    budget DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_organizational_units_parent FOREIGN KEY (parent_id) REFERENCES organizational_units(id) ON DELETE SET NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_organizational_units_parent_id ON organizational_units(parent_id);
CREATE INDEX IF NOT EXISTS idx_organizational_units_type ON organizational_units(type);
CREATE INDEX IF NOT EXISTS idx_organizational_units_level ON organizational_units(level);
CREATE INDEX IF NOT EXISTS idx_organizational_units_manager_id ON organizational_units(manager_id);

-- =====================================================
-- ENHANCED USERS TABLE (NIS2 COMPLIANT)
-- =====================================================

-- Create ENUM types for users table
CREATE TYPE user_role AS ENUM ('ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest', 'employee');
CREATE TYPE employment_type AS ENUM ('full_time', 'part_time', 'contract', 'intern');
CREATE TYPE account_status AS ENUM ('active', 'inactive', 'locked', 'pending_activation', 'suspended');

CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    title VARCHAR(255),
    role user_role NOT NULL DEFAULT 'engineer',
    organizational_unit_id INTEGER NULL,
    manager_id INTEGER NULL,
    hire_date DATE,
    salary DECIMAL(10,2),
    employment_type employment_type NOT NULL DEFAULT 'full_time',
    location VARCHAR(255),
    phone VARCHAR(255),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(255),

    -- Basic status fields
    is_active BOOLEAN NOT NULL DEFAULT TRUE,

    -- Enhanced Security Fields (NIS2 Compliance)
    account_status account_status NOT NULL DEFAULT 'active',
    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(255),
    password_changed_at TIMESTAMP NULL,
    must_change_password BOOLEAN NOT NULL DEFAULT TRUE,
    account_locked_until TIMESTAMP NULL,

    -- Two-Factor Authentication
    two_factor_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    two_factor_secret VARCHAR(255) NULL,

    -- Session Management (Standardized with TypeORM Entity)
    session_token VARCHAR(1000) NULL,
    session_expires_at TIMESTAMP NULL,

    -- Refresh Token Management
    refresh_token VARCHAR(1000) NULL,
    refresh_token_expires_at TIMESTAMP NULL,

    -- MFA and Security Features
    mfa_backup_codes TEXT NULL,
    password_history TEXT NULL,

    -- Timestamps
    created_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- Foreign Keys
    CONSTRAINT fk_users_organizational_unit FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE SET NULL,
    CONSTRAINT fk_users_manager FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Create indexes for performance and security
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_account_status ON users(account_status);
CREATE INDEX IF NOT EXISTS idx_users_session_token ON users(session_token);
CREATE INDEX IF NOT EXISTS idx_users_refresh_token ON users(refresh_token);
CREATE INDEX IF NOT EXISTS idx_users_manager_id ON users(manager_id);
CREATE INDEX IF NOT EXISTS idx_users_organizational_unit_id ON users(organizational_unit_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_last_login_at ON users(last_login_at);
CREATE INDEX IF NOT EXISTS idx_users_failed_login_attempts ON users(failed_login_attempts);
CREATE INDEX IF NOT EXISTS idx_users_two_factor_enabled ON users(two_factor_enabled);

-- =====================================================
-- SESSION MANAGEMENT TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token TEXT NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT fk_user_sessions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for user_sessions
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);

-- =====================================================
-- TEAMS AND SKILLSETS
-- =====================================================

-- Teams table
CREATE TABLE IF NOT EXISTS teams (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_by INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_teams_created_by FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Create indexes for teams
CREATE INDEX IF NOT EXISTS idx_teams_created_by ON teams(created_by);

-- Create ENUM type for team member roles
CREATE TYPE team_member_role AS ENUM ('team_lead', 'member', 'guest');

-- Team Membership table
CREATE TABLE IF NOT EXISTS team_members (
    id SERIAL PRIMARY KEY,
    team_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    role team_member_role NOT NULL DEFAULT 'member',
    added_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_team_members_team FOREIGN KEY (team_id) REFERENCES teams(id) ON DELETE CASCADE,
    CONSTRAINT fk_team_members_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT unique_team_user UNIQUE (team_id, user_id)
);

-- Create indexes for team_members
CREATE INDEX IF NOT EXISTS idx_team_members_team_id ON team_members(team_id);
CREATE INDEX IF NOT EXISTS idx_team_members_user_id ON team_members(user_id);

-- Create ENUM types for skillsets
CREATE TYPE skillset_category AS ENUM ('technical', 'soft_skills', 'leadership', 'domain_expertise', 'certifications', 'languages');
CREATE TYPE skill_level AS ENUM ('beginner', 'intermediate', 'advanced', 'expert');

-- Skillsets table
CREATE TABLE IF NOT EXISTS skillsets (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    category skillset_category,
    level_required skill_level DEFAULT 'intermediate',
    is_core_skill BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for skillsets
CREATE INDEX IF NOT EXISTS idx_skillsets_category ON skillsets(category);
CREATE INDEX IF NOT EXISTS idx_skillsets_level_required ON skillsets(level_required);
CREATE INDEX IF NOT EXISTS idx_skillsets_is_core_skill ON skillsets(is_core_skill);
CREATE INDEX IF NOT EXISTS idx_skillsets_is_active ON skillsets(is_active);

-- User Skillsets table
CREATE TABLE IF NOT EXISTS user_skillsets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    skillset_id INTEGER NOT NULL,
    proficiency_level skill_level NOT NULL,
    certification_name VARCHAR(255),
    certification_date DATE,
    expiry_date DATE,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_skillsets_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_skillsets_skillset FOREIGN KEY (skillset_id) REFERENCES skillsets(id) ON DELETE CASCADE,
    CONSTRAINT unique_user_skillset UNIQUE (user_id, skillset_id)
);

-- Create indexes for user_skillsets
CREATE INDEX IF NOT EXISTS idx_user_skillsets_user_id ON user_skillsets(user_id);
CREATE INDEX IF NOT EXISTS idx_user_skillsets_skillset_id ON user_skillsets(skillset_id);
CREATE INDEX IF NOT EXISTS idx_user_skillsets_proficiency_level ON user_skillsets(proficiency_level);

-- =====================================================
-- ASSESSMENT SYSTEM TABLES
-- =====================================================

-- Create ENUM types for assessment system
CREATE TYPE template_level AS ENUM ('global', 'organizational', 'team_level');
CREATE TYPE assessment_status AS ENUM ('draft', 'in_progress', 'completed', 'approved', 'rejected');

-- Assessment Templates table
CREATE TABLE IF NOT EXISTS assessment_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by_id INTEGER NOT NULL,
    organizational_unit_id INTEGER NULL,
    template_level template_level NOT NULL DEFAULT 'team_level',
    is_active BOOLEAN DEFAULT TRUE,
    version VARCHAR(10) DEFAULT '1.0',
    parent_template_id INTEGER NULL,
    is_global BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_assessment_templates_created_by FOREIGN KEY (created_by_id) REFERENCES users(id),
    CONSTRAINT fk_assessment_templates_organizational_unit FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id),
    CONSTRAINT fk_assessment_templates_parent FOREIGN KEY (parent_template_id) REFERENCES assessment_templates(id)
);

-- Create indexes for assessment_templates
CREATE INDEX IF NOT EXISTS idx_assessment_templates_created_by ON assessment_templates(created_by_id);
CREATE INDEX IF NOT EXISTS idx_assessment_templates_organizational_unit ON assessment_templates(organizational_unit_id);
CREATE INDEX IF NOT EXISTS idx_assessment_templates_is_active ON assessment_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_assessment_templates_template_level ON assessment_templates(template_level);

-- Assessment Areas table
CREATE TABLE IF NOT EXISTS assessment_areas (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    weight DECIMAL(5,2) DEFAULT 1.00,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_assessment_areas_template FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE CASCADE
);

-- Create indexes for assessment_areas
CREATE INDEX IF NOT EXISTS idx_assessment_areas_template_id ON assessment_areas(template_id);
CREATE INDEX IF NOT EXISTS idx_assessment_areas_order_index ON assessment_areas(order_index);

-- Assessment Criteria table (if needed for more detailed assessments)
CREATE TABLE IF NOT EXISTS assessment_criteria (
    id SERIAL PRIMARY KEY,
    area_id INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    weight DECIMAL(5,2) DEFAULT 1.00,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_assessment_criteria_area FOREIGN KEY (area_id) REFERENCES assessment_areas(id) ON DELETE CASCADE
);

-- Create indexes for assessment_criteria
CREATE INDEX IF NOT EXISTS idx_assessment_criteria_area_id ON assessment_criteria(area_id);
CREATE INDEX IF NOT EXISTS idx_assessment_criteria_order_index ON assessment_criteria(order_index);

-- Assessment Instances table
CREATE TABLE IF NOT EXISTS assessment_instances (
    id SERIAL PRIMARY KEY,
    template_id INTEGER NOT NULL,
    employee_id INTEGER NOT NULL,
    evaluator_id INTEGER NOT NULL,
    status assessment_status DEFAULT 'draft',
    template_snapshot JSONB NULL,
    assessment_date DATE,
    notes TEXT,
    total_score DECIMAL(10,2) DEFAULT 0,
    score_percentage DECIMAL(5,2) NULL,
    approved_at TIMESTAMP NULL,
    approved_by_id INTEGER NULL,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_assessment_instances_template FOREIGN KEY (template_id) REFERENCES assessment_templates(id),
    CONSTRAINT fk_assessment_instances_employee FOREIGN KEY (employee_id) REFERENCES users(id),
    CONSTRAINT fk_assessment_instances_evaluator FOREIGN KEY (evaluator_id) REFERENCES users(id),
    CONSTRAINT fk_assessment_instances_approved_by FOREIGN KEY (approved_by_id) REFERENCES users(id)
);

-- Create indexes for assessment_instances
CREATE INDEX IF NOT EXISTS idx_assessment_instances_template_id ON assessment_instances(template_id);
CREATE INDEX IF NOT EXISTS idx_assessment_instances_employee_id ON assessment_instances(employee_id);
CREATE INDEX IF NOT EXISTS idx_assessment_instances_evaluator_id ON assessment_instances(evaluator_id);
CREATE INDEX IF NOT EXISTS idx_assessment_instances_status ON assessment_instances(status);
CREATE INDEX IF NOT EXISTS idx_assessment_instances_assessment_date ON assessment_instances(assessment_date);

-- Assessment Responses table
CREATE TABLE IF NOT EXISTS assessment_responses (
    id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL,
    area_id INTEGER NOT NULL,
    area_snapshot JSONB NULL,
    score DECIMAL(10,2),
    evaluator_comments TEXT,
    employee_comments TEXT,
    area_weight DECIMAL(5,2) DEFAULT 1.0,
    weighted_score DECIMAL(10,2),
    base_score DECIMAL(10,2) NULL,
    score_adjustments JSONB NULL,
    additional_data JSONB NULL,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_assessment_responses_assessment FOREIGN KEY (assessment_id) REFERENCES assessment_instances(id) ON DELETE CASCADE,
    CONSTRAINT fk_assessment_responses_area FOREIGN KEY (area_id) REFERENCES assessment_areas(id)
);

-- Create indexes for assessment_responses
CREATE INDEX IF NOT EXISTS idx_assessment_responses_assessment_id ON assessment_responses(assessment_id);
CREATE INDEX IF NOT EXISTS idx_assessment_responses_area_id ON assessment_responses(area_id);
-- =====================================================
-- PERFORMANCE MANAGEMENT TABLES
-- =====================================================

-- Create ENUM types for performance management
CREATE TYPE metric_type AS ENUM ('productivity', 'quality', 'collaboration', 'innovation', 'leadership');
CREATE TYPE badge_type AS ENUM ('achievement', 'appreciation', 'milestone', 'skill');

-- Analytics Dashboards table
CREATE TABLE IF NOT EXISTS analytics_dashboards (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    user_id INTEGER NOT NULL,
    dashboard_config JSONB,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_analytics_dashboards_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for analytics_dashboards
CREATE INDEX IF NOT EXISTS idx_analytics_dashboards_user_id ON analytics_dashboards(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_dashboards_is_public ON analytics_dashboards(is_public);

-- Performance Metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    metric_type metric_type NOT NULL,
    measurement_period_start DATE NOT NULL,
    measurement_period_end DATE NOT NULL,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_performance_metrics_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for performance_metrics
CREATE INDEX IF NOT EXISTS idx_performance_metrics_user_id ON performance_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_metric_type ON performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_measurement_period ON performance_metrics(measurement_period_start, measurement_period_end);

-- Recognition Badges table
CREATE TABLE IF NOT EXISTS recognition_badges (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    icon_url VARCHAR(500),
    badge_type badge_type NOT NULL,
    point_value INTEGER NOT NULL DEFAULT 0,
    criteria JSONB,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by_id INTEGER NOT NULL,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_recognition_badges_created_by FOREIGN KEY (created_by_id) REFERENCES users(id)
);

-- Create indexes for recognition_badges
CREATE INDEX IF NOT EXISTS idx_recognition_badges_badge_type ON recognition_badges(badge_type);
CREATE INDEX IF NOT EXISTS idx_recognition_badges_point_value ON recognition_badges(point_value);
CREATE INDEX IF NOT EXISTS idx_recognition_badges_created_by_id ON recognition_badges(created_by_id);

-- Recognition Instances table
CREATE TABLE IF NOT EXISTS recognition_instances (
    id SERIAL PRIMARY KEY,
    badge_id INTEGER NOT NULL,
    recipient_user_id INTEGER NOT NULL,
    awarded_by_user_id INTEGER NOT NULL,
    reason TEXT,
    awarded_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_recognition_instances_badge FOREIGN KEY (badge_id) REFERENCES recognition_badges(id) ON DELETE CASCADE,
    CONSTRAINT fk_recognition_instances_recipient FOREIGN KEY (recipient_user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_recognition_instances_awarded_by FOREIGN KEY (awarded_by_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for recognition_instances
CREATE INDEX IF NOT EXISTS idx_recognition_instances_badge_id ON recognition_instances(badge_id);
CREATE INDEX IF NOT EXISTS idx_recognition_instances_recipient_user_id ON recognition_instances(recipient_user_id);
CREATE INDEX IF NOT EXISTS idx_recognition_instances_awarded_by_user_id ON recognition_instances(awarded_by_user_id);
CREATE INDEX IF NOT EXISTS idx_recognition_instances_awarded_at ON recognition_instances(awarded_at);

-- User Gamification table
CREATE TABLE IF NOT EXISTS user_gamification (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    total_points INTEGER DEFAULT 0,
    level_name VARCHAR(100) DEFAULT 'Beginner',
    achievements_count INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user_gamification_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT unique_user_gamification UNIQUE (user_id)
);

-- Create indexes for user_gamification
CREATE INDEX IF NOT EXISTS idx_user_gamification_total_points ON user_gamification(total_points);
CREATE INDEX IF NOT EXISTS idx_user_gamification_level_name ON user_gamification(level_name);

-- =====================================================
-- POSTGRESQL FUNCTIONS AND TRIGGERS
-- =====================================================

-- Create function to clean up expired sessions (PostgreSQL equivalent)
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS void AS $$
BEGIN
    DELETE FROM user_sessions WHERE expires_at < NOW();
    UPDATE users SET session_token = NULL, session_expires_at = NULL
    WHERE session_expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER update_organizational_units_updated_at BEFORE UPDATE ON organizational_units FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_skillsets_updated_at BEFORE UPDATE ON skillsets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_skillsets_updated_at BEFORE UPDATE ON user_skillsets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_templates_updated_at BEFORE UPDATE ON assessment_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_areas_updated_at BEFORE UPDATE ON assessment_areas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_instances_updated_at BEFORE UPDATE ON assessment_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_assessment_responses_updated_at BEFORE UPDATE ON assessment_responses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert organizational units
INSERT INTO organizational_units (id, name, type, description) VALUES
(1, 'Engineering', 'department', 'Software development and technical teams'),
(2, 'Human Resources', 'department', 'HR and people operations'),
(3, 'Management', 'department', 'Executive and management team'),
(4, 'Quality Assurance', 'department', 'Testing and quality control')
ON CONFLICT (id) DO NOTHING;

-- Insert sample users with enhanced security
INSERT INTO users (
    email, password, first_name, last_name, title, role, organizational_unit_id,
    is_active, account_status, must_change_password, failed_login_attempts
) VALUES
-- Admin user
('<EMAIL>', '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV',
 'System', 'Administrator', 'System Administrator', 'hr_admin', 2, TRUE, 'active', TRUE, 0),

-- Management users
('<EMAIL>', '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV',
 'John', 'Doe', 'Engineering Manager', 'manager', 1, TRUE, 'active', TRUE, 0),

-- Engineering users
('<EMAIL>', '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV',
 'Jane', 'Smith', 'Senior Software Engineer', 'senior_engineer', 1, TRUE, 'active', TRUE, 0),

('<EMAIL>', '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV',
 'Mike', 'Johnson', 'Software Engineer', 'engineer', 1, TRUE, 'active', TRUE, 0),

-- Test user for authentication testing
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlG.',
 'Test', 'User', 'Test Engineer', 'engineer', 1, TRUE, 'active', FALSE, 0)
ON CONFLICT (email) DO NOTHING;

-- Insert sample skillsets
INSERT INTO skillsets (name, description, category) VALUES
('JavaScript', 'JavaScript programming language', 'technical'),
('TypeScript', 'TypeScript programming language', 'technical'),
('React', 'React frontend framework', 'technical'),
('Node.js', 'Node.js backend runtime', 'technical'),
('PostgreSQL', 'PostgreSQL database management', 'technical'),
('Project Management', 'Project planning and execution', 'leadership'),
('Team Leadership', 'Leading and managing teams', 'leadership')
ON CONFLICT (name) DO NOTHING;

-- Insert sample teams
INSERT INTO teams (name, description, created_by) VALUES
('Frontend Team', 'Responsible for user interface development', 2),
('Backend Team', 'Responsible for server-side development', 2),
('DevOps Team', 'Responsible for deployment and infrastructure', 2)
ON CONFLICT DO NOTHING;

-- Insert sample recognition badges
INSERT INTO recognition_badges (name, description, badge_type, point_value, created_by_id) VALUES
('Code Quality Champion', 'Awarded for exceptional code quality', 'achievement', 100, 1),
('Team Player', 'Awarded for outstanding collaboration', 'appreciation', 75, 1),
('Innovation Leader', 'Awarded for innovative solutions', 'skill', 150, 1),
('Milestone Master', 'Awarded for meeting project milestones', 'milestone', 50, 1)
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- FINAL NOTES - POSTGRESQL VERSION
-- =====================================================
-- This script creates a complete, enhanced PostgreSQL database schema with:
-- 1. NIS2-compliant security features
-- 2. Comprehensive user management with ENUM types
-- 3. Performance tracking and analytics
-- 4. Recognition and gamification systems
-- 5. Assessment and evaluation tools
-- 6. Sample data for immediate testing
-- 7. PostgreSQL-specific features (JSONB, SERIAL, proper constraints)
-- 8. Automatic updated_at triggers
--
-- MIGRATION NOTES:
-- - Converted from MariaDB/MySQL to PostgreSQL
-- - Uses SERIAL instead of AUTO_INCREMENT
-- - Uses BOOLEAN instead of TINYINT
-- - Uses JSONB instead of JSON for better performance
-- - Uses proper ENUM types instead of string enums
-- - Uses ON CONFLICT instead of INSERT IGNORE
-- - Uses PostgreSQL functions instead of stored procedures
--
-- Default password for all test users: "TestPassword123"
-- All users are set to must_change_password = TRUE for security
