-- Drop and create the database
CREATE DATABASE IF NOT EXISTS ehrx CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ehrx;

-- Create organizational_units table (infinite hierarchy)
CREATE TABLE IF NOT EXISTS organizational_units (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type ENUM('organization', 'division', 'department', 'team', 'squad', 'unit') NOT NULL DEFAULT 'team',
    description TEXT,
    parent_id INT NULL,
    level INT NOT NULL DEFAULT 0,
    manager_id INT NULL,
    budget DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    INDEX idx_parent_id (parent_id),
    INDEX idx_type (type),
    INDEX idx_level (level),
    INDEX idx_manager_id (manager_id),
    INDEX idx_is_active (is_active)
);

-- Create skillsets table (for IT Outsourcing/Server Hosting)
CREATE TABLE IF NOT EXISTS skillsets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    category ENUM('programming', 'infrastructure', 'database', 'cloud', 'security', 'devops', 'networking', 'frontend', 'backend', 'mobile', 'data', 'ai_ml', 'project_management', 'soft_skills') NOT NULL,
    description TEXT,
    level_required ENUM('beginner', 'intermediate', 'advanced', 'expert') NOT NULL DEFAULT 'intermediate',
    is_core_skill BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category (category),
    INDEX idx_level_required (level_required),
    INDEX idx_is_core_skill (is_core_skill),
    INDEX idx_is_active (is_active)
);

-- Create users table (enhanced for IT company with security features)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    title VARCHAR(255),
    role ENUM('ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest') NOT NULL DEFAULT 'engineer',
    organizational_unit_id INT NULL,
    manager_id INT NULL,
    hire_date DATE,
    salary DECIMAL(10,2),
    employment_type ENUM('full_time', 'part_time', 'contract', 'intern') NOT NULL DEFAULT 'full_time',
    location VARCHAR(255),
    phone VARCHAR(50),
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(50),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,

    -- Enhanced Security Fields (NIS2 Compliant)
    account_status ENUM('active', 'inactive', 'locked', 'pending_activation', 'suspended') NOT NULL DEFAULT 'active',
    failed_login_attempts INT NOT NULL DEFAULT 0,
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(45) NULL, -- IPv6 compatible
    password_changed_at TIMESTAMP NULL,
    must_change_password BOOLEAN NOT NULL DEFAULT TRUE,
    account_locked_until TIMESTAMP NULL,
    two_factor_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    two_factor_secret VARCHAR(255) NULL,
    session_token VARCHAR(255) NULL,
    session_expires_at TIMESTAMP NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE SET NULL,
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_organizational_unit_id (organizational_unit_id),
    INDEX idx_manager_id (manager_id),
    INDEX idx_is_active (is_active),
    INDEX idx_account_status (account_status),
    INDEX idx_session_token (session_token),
    INDEX idx_last_login_at (last_login_at)
);

-- Add foreign key constraint for organizational_units.manager_id after users table is created
ALTER TABLE organizational_units ADD CONSTRAINT fk_org_manager
FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL;

-- Create user_skillsets table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS user_skillsets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    skillset_id INT NOT NULL,
    proficiency_level ENUM('beginner', 'intermediate', 'advanced', 'expert') NOT NULL DEFAULT 'intermediate',
    years_experience DECIMAL(3,1) DEFAULT 0.0,
    is_certified BOOLEAN NOT NULL DEFAULT FALSE,
    certification_name VARCHAR(255),
    certification_date DATE,
    last_used_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skillset_id) REFERENCES skillsets(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_skill (user_id, skillset_id),
    INDEX idx_user_id (user_id),
    INDEX idx_skillset_id (skillset_id),
    INDEX idx_proficiency_level (proficiency_level),
    INDEX idx_is_certified (is_certified)
);

-- Create projects table (for tracking project assignments)
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    client_name VARCHAR(255),
    organizational_unit_id INT,
    project_manager_id INT,
    status ENUM('planning', 'active', 'on_hold', 'completed', 'cancelled') NOT NULL DEFAULT 'planning',
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    priority ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE SET NULL,
    FOREIGN KEY (project_manager_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_organizational_unit_id (organizational_unit_id),
    INDEX idx_project_manager_id (project_manager_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
);

-- Create project_assignments table (many-to-many for users and projects)
CREATE TABLE IF NOT EXISTS project_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    user_id INT NOT NULL,
    role VARCHAR(255) NOT NULL,
    allocation_percentage DECIMAL(5,2) DEFAULT 100.00,
    start_date DATE,
    end_date DATE,
    hourly_rate DECIMAL(8,2),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_user (project_id, user_id),
    INDEX idx_project_id (project_id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active)
);

-- Create assessment_templates table
CREATE TABLE IF NOT EXISTS assessment_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(10) NOT NULL DEFAULT '1.0',
    created_by_id INT NOT NULL,
    organizational_unit_id INT NULL,
    template_level ENUM('hr_level', 'organizational_level', 'team_level') NOT NULL DEFAULT 'team_level',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by_id) REFERENCES users(id),
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    INDEX idx_created_by (created_by_id),
    INDEX idx_organizational_unit (organizational_unit_id),
    INDEX idx_template_level (template_level),
    INDEX idx_is_active (is_active)
);

-- Create assessment_areas table
CREATE TABLE IF NOT EXISTS assessment_areas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    weight DECIMAL(5,2) NOT NULL DEFAULT 1.0,
    max_score INT NOT NULL DEFAULT 10,
    order_index INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE CASCADE,
    INDEX idx_template_id (template_id)
);

-- Create scoring_rules table
CREATE TABLE IF NOT EXISTS scoring_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    area_id INT NOT NULL,
    rule_type ENUM('bonus', 'penalty', 'threshold', 'conditional') NOT NULL,
    condition_field VARCHAR(100),
    condition_operator VARCHAR(20),
    condition_value VARCHAR(255),
    score_adjustment DECIMAL(5,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (area_id) REFERENCES assessment_areas(id) ON DELETE CASCADE,
    INDEX idx_area_id (area_id)
);

-- Create assessment_instances table
CREATE TABLE IF NOT EXISTS assessment_instances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    employee_id INT NOT NULL,
    evaluator_id INT,
    status ENUM('draft', 'in_progress', 'completed', 'approved', 'rejected') NOT NULL DEFAULT 'draft',
    assessment_date DATE NOT NULL,
    notes TEXT,
    total_score DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id),
    FOREIGN KEY (employee_id) REFERENCES users(id),
    FOREIGN KEY (evaluator_id) REFERENCES users(id),
    INDEX idx_employee_id (employee_id),
    INDEX idx_evaluator_id (evaluator_id),
    INDEX idx_status (status),
    INDEX idx_assessment_date (assessment_date)
);

-- Create assessment_responses table
CREATE TABLE IF NOT EXISTS assessment_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assessment_id INT NOT NULL,
    area_id INT NOT NULL,
    score DECIMAL(5,2) NOT NULL DEFAULT 0,
    evaluator_comments TEXT,
    employee_comments TEXT,
    area_weight DECIMAL(5,2) NOT NULL DEFAULT 1.0,
    weighted_score DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assessment_id) REFERENCES assessment_instances(id) ON DELETE CASCADE,
    FOREIGN KEY (area_id) REFERENCES assessment_areas(id),
    INDEX idx_assessment_id (assessment_id),
    INDEX idx_area_id (area_id)
);

-- Create assessment_criteria_levels table (defines the three levels of assessment criteria)
CREATE TABLE IF NOT EXISTS assessment_criteria_levels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level_type ENUM('hr_level', 'organizational_level', 'team_level') NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    priority_order INT NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_level_priority (level_type, priority_order),
    INDEX idx_level_type (level_type),
    INDEX idx_priority_order (priority_order)
);

-- Create assessment_criteria table (hierarchical assessment criteria)
CREATE TABLE IF NOT EXISTS assessment_criteria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    criteria_level_id INT NOT NULL,
    organizational_unit_id INT NULL, -- NULL for HR level, specific unit for org/team level
    created_by_id INT NOT NULL,
    weight DECIMAL(5,2) NOT NULL DEFAULT 1.0,
    max_score INT NOT NULL DEFAULT 100,
    scoring_method ENUM('numeric', 'percentage', 'scale_1_5', 'scale_1_10', 'boolean') NOT NULL DEFAULT 'percentage',
    is_mandatory BOOLEAN NOT NULL DEFAULT TRUE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    effective_from DATE NOT NULL,
    effective_until DATE NULL,
    order_index INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (criteria_level_id) REFERENCES assessment_criteria_levels(id),
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES users(id),
    INDEX idx_criteria_level (criteria_level_id),
    INDEX idx_organizational_unit (organizational_unit_id),
    INDEX idx_created_by (created_by_id),
    INDEX idx_effective_dates (effective_from, effective_until),
    INDEX idx_is_active (is_active)
);

-- Create assessment_criteria_inheritance table (tracks which criteria apply to which teams)
CREATE TABLE IF NOT EXISTS assessment_criteria_inheritance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    criteria_id INT NOT NULL,
    target_organizational_unit_id INT NOT NULL,
    inherited_from_unit_id INT NULL, -- NULL for HR level, parent unit for organizational level
    inheritance_type ENUM('hr_mandated', 'organizational_inherited', 'team_specific') NOT NULL,
    is_overridable BOOLEAN NOT NULL DEFAULT FALSE,
    local_weight_override DECIMAL(5,2) NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id) ON DELETE CASCADE,
    FOREIGN KEY (target_organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    FOREIGN KEY (inherited_from_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    UNIQUE KEY unique_criteria_target (criteria_id, target_organizational_unit_id),
    INDEX idx_criteria_id (criteria_id),
    INDEX idx_target_unit (target_organizational_unit_id),
    INDEX idx_inherited_from (inherited_from_unit_id),
    INDEX idx_inheritance_type (inheritance_type)
);

-- Create assessment_template_criteria table (links templates to criteria)
CREATE TABLE IF NOT EXISTS assessment_template_criteria (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    criteria_id INT NOT NULL,
    is_required BOOLEAN NOT NULL DEFAULT TRUE,
    local_weight DECIMAL(5,2) NULL, -- Override criteria default weight
    local_max_score INT NULL, -- Override criteria default max score
    order_index INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES assessment_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id) ON DELETE CASCADE,
    UNIQUE KEY unique_template_criteria (template_id, criteria_id),
    INDEX idx_template_id (template_id),
    INDEX idx_criteria_id (criteria_id)
);

-- Create assessment_scoring_scales table (defines scoring scales for criteria)
CREATE TABLE IF NOT EXISTS assessment_scoring_scales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    organizational_unit_id INT NULL, -- NULL for company-wide scales
    created_by_id INT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_id) REFERENCES users(id),
    INDEX idx_organizational_unit (organizational_unit_id),
    INDEX idx_created_by (created_by_id)
);

-- Create assessment_scoring_scale_levels table (defines score ranges and points)
CREATE TABLE IF NOT EXISTS assessment_scoring_scale_levels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    scoring_scale_id INT NOT NULL,
    level_name VARCHAR(100) NOT NULL, -- e.g., 'Exceeds Expectations', 'Meets Expectations'
    level_code VARCHAR(10) NOT NULL, -- e.g., 'A', 'B', 'C', 'D', 'E'
    min_performance_score DECIMAL(5,2) NOT NULL, -- Minimum performance to achieve this level
    max_performance_score DECIMAL(5,2) NOT NULL, -- Maximum performance for this level
    points_awarded INT NOT NULL, -- Points awarded for this level (25, 50, 75, 100)
    description TEXT,
    order_index INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (scoring_scale_id) REFERENCES assessment_scoring_scales(id) ON DELETE CASCADE,
    INDEX idx_scoring_scale (scoring_scale_id),
    INDEX idx_performance_range (min_performance_score, max_performance_score),
    UNIQUE KEY unique_scale_level_code (scoring_scale_id, level_code)
);

-- Create assessment_criteria_scoring table (links criteria to scoring scales)
CREATE TABLE IF NOT EXISTS assessment_criteria_scoring (
    id INT AUTO_INCREMENT PRIMARY KEY,
    criteria_id INT NOT NULL,
    scoring_scale_id INT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id) ON DELETE CASCADE,
    FOREIGN KEY (scoring_scale_id) REFERENCES assessment_scoring_scales(id) ON DELETE CASCADE,
    UNIQUE KEY unique_criteria_scoring (criteria_id, scoring_scale_id),
    INDEX idx_criteria_id (criteria_id),
    INDEX idx_scoring_scale_id (scoring_scale_id)
);

-- Create assessment_criteria_responses table (stores responses for each criteria)
CREATE TABLE IF NOT EXISTS assessment_criteria_responses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    assessment_id INT NOT NULL,
    criteria_id INT NOT NULL,
    base_performance_score DECIMAL(5,2) NOT NULL DEFAULT 0, -- Initial performance assessment
    scoring_scale_level_id INT NOT NULL, -- Which scale level was achieved
    base_points INT NOT NULL, -- Points from the scoring scale level
    manager_adjustment_points INT NOT NULL DEFAULT 0, -- Manager's adjustment (+/- points)
    final_score DECIMAL(5,2) NOT NULL DEFAULT 0, -- Final calculated score
    max_possible_score DECIMAL(5,2) NOT NULL,
    weight_applied DECIMAL(5,2) NOT NULL,
    weighted_score DECIMAL(5,2) NOT NULL DEFAULT 0,
    evaluator_comments TEXT,
    manager_adjustment_reason TEXT, -- Required when manager adjusts score
    employee_comments TEXT,
    evidence_links TEXT, -- JSON array of evidence/document links
    improvement_suggestions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assessment_id) REFERENCES assessment_instances(id) ON DELETE CASCADE,
    FOREIGN KEY (criteria_id) REFERENCES assessment_criteria(id),
    FOREIGN KEY (scoring_scale_level_id) REFERENCES assessment_scoring_scale_levels(id),
    UNIQUE KEY unique_assessment_criteria (assessment_id, criteria_id),
    INDEX idx_assessment_id (assessment_id),
    INDEX idx_criteria_id (criteria_id),
    INDEX idx_scoring_scale_level (scoring_scale_level_id)
);

-- =====================================================
-- MONTHLY DASHBOARDS TABLES
-- =====================================================

-- Create monthly_dashboard_kpis table (configurable KPI definitions)
CREATE TABLE IF NOT EXISTS monthly_dashboard_kpis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    help_text TEXT,
    data_type ENUM('number', 'percentage', 'currency', 'count', 'boolean') NOT NULL DEFAULT 'number',
    unit VARCHAR(50), -- e.g., '%', 'DKK', 'hours', 'count'
    calculation_method ENUM('manual', 'auto_calculated', 'imported') NOT NULL DEFAULT 'manual',
    calculation_formula TEXT, -- For auto-calculated KPIs
    green_threshold_min DECIMAL(10,2),
    green_threshold_max DECIMAL(10,2),
    yellow_threshold_min DECIMAL(10,2),
    yellow_threshold_max DECIMAL(10,2),
    red_threshold_min DECIMAL(10,2),
    red_threshold_max DECIMAL(10,2),
    is_higher_better BOOLEAN NOT NULL DEFAULT TRUE, -- TRUE if higher values are better
    display_order INT NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_display_order (display_order),
    INDEX idx_is_active (is_active),
    INDEX idx_calculation_method (calculation_method)
);

-- Create monthly_dashboard_submissions table (team dashboard submissions)
CREATE TABLE IF NOT EXISTS monthly_dashboard_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    organizational_unit_id INT NOT NULL,
    submission_year INT NOT NULL,
    submission_month INT NOT NULL,
    status ENUM('draft', 'submitted', 'approved', 'rejected', 'revision_requested') NOT NULL DEFAULT 'draft',
    submitted_by_user_id INT,
    submitted_at TIMESTAMP NULL,
    approved_by_user_id INT NULL,
    approved_at TIMESTAMP NULL,
    rejection_reason TEXT,
    notes TEXT,
    completion_date DATE, -- When the dashboard was completed
    due_date DATE, -- When it was supposed to be completed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    FOREIGN KEY (submitted_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (approved_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_unit_month (organizational_unit_id, submission_year, submission_month),
    INDEX idx_organizational_unit (organizational_unit_id),
    INDEX idx_submission_period (submission_year, submission_month),
    INDEX idx_status (status),
    INDEX idx_submitted_by (submitted_by_user_id),
    INDEX idx_approved_by (approved_by_user_id),
    INDEX idx_completion_date (completion_date)
);

-- Create monthly_dashboard_kpi_values table (actual KPI values and traffic light status)
CREATE TABLE IF NOT EXISTS monthly_dashboard_kpi_values (
    id INT AUTO_INCREMENT PRIMARY KEY,
    submission_id INT NOT NULL,
    kpi_id INT NOT NULL,
    actual_value DECIMAL(15,4),
    target_value DECIMAL(15,4),
    traffic_light_status ENUM('green', 'yellow', 'red') NOT NULL DEFAULT 'green',
    manual_override_status ENUM('green', 'yellow', 'red') NULL, -- Manual override of traffic light
    override_reason TEXT,
    comments TEXT,
    data_source VARCHAR(255), -- Where the data came from
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (submission_id) REFERENCES monthly_dashboard_submissions(id) ON DELETE CASCADE,
    FOREIGN KEY (kpi_id) REFERENCES monthly_dashboard_kpis(id) ON DELETE CASCADE,
    UNIQUE KEY unique_submission_kpi (submission_id, kpi_id),
    INDEX idx_submission_id (submission_id),
    INDEX idx_kpi_id (kpi_id),
    INDEX idx_traffic_light_status (traffic_light_status),
    INDEX idx_manual_override_status (manual_override_status)
);

-- Create monthly_dashboard_team_targets table (team-specific KPI targets)
CREATE TABLE IF NOT EXISTS monthly_dashboard_team_targets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    organizational_unit_id INT NOT NULL,
    kpi_id INT NOT NULL,
    target_value DECIMAL(15,4) NOT NULL,
    effective_from DATE NOT NULL,
    effective_until DATE NULL,
    notes TEXT,
    created_by_user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
    FOREIGN KEY (kpi_id) REFERENCES monthly_dashboard_kpis(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by_user_id) REFERENCES users(id) ON DELETE RESTRICT,
    INDEX idx_organizational_unit (organizational_unit_id),
    INDEX idx_kpi_id (kpi_id),
    INDEX idx_effective_dates (effective_from, effective_until),
    INDEX idx_created_by (created_by_user_id)
);
