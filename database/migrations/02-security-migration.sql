-- Security Enhancement Migration for EHRX Database
-- This script adds enhanced security fields to the users table for NIS2 compliance

USE ehrx;

-- Add new security columns to users table (MySQL compatible)
-- Check if columns exist before adding them
SET @sql = '';

-- Add account_status column
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'account_status';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN account_status ENUM(''active'', ''inactive'', ''locked'', ''pending_activation'', ''suspended'') NOT NULL DEFAULT ''active'' AFTER is_active;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Add failed_login_attempts column
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'failed_login_attempts';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN failed_login_attempts INT NOT NULL DEFAULT 0 AFTER account_status;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Add other security columns
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'last_login_at';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP NULL AFTER failed_login_attempts;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'last_login_ip';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN last_login_ip VARCHAR(45) NULL AFTER last_login_at;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'password_changed_at';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN password_changed_at TIMESTAMP NULL AFTER last_login_ip;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'must_change_password';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN must_change_password BOOLEAN NOT NULL DEFAULT TRUE AFTER password_changed_at;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'account_locked_until';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN account_locked_until TIMESTAMP NULL AFTER must_change_password;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'two_factor_enabled';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN two_factor_enabled BOOLEAN NOT NULL DEFAULT FALSE AFTER account_locked_until;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'two_factor_secret';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN two_factor_secret VARCHAR(255) NULL AFTER two_factor_enabled;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'session_token';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN session_token VARCHAR(255) NULL AFTER two_factor_secret;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'session_expires_at';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN session_expires_at TIMESTAMP NULL AFTER session_token;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Add indexes for performance (MySQL compatible)
-- Check if indexes exist before adding them
SELECT COUNT(*) INTO @idx_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_account_status';
SET @sql = IF(@idx_exists = 0, 'ALTER TABLE users ADD INDEX idx_account_status (account_status);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @idx_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_session_token';
SET @sql = IF(@idx_exists = 0, 'ALTER TABLE users ADD INDEX idx_session_token (session_token);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @idx_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_last_login_at';
SET @sql = IF(@idx_exists = 0, 'ALTER TABLE users ADD INDEX idx_last_login_at (last_login_at);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Create audit log table for security events
CREATE TABLE IF NOT EXISTS security_audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    event_type ENUM('login_success', 'login_failure', 'logout', 'password_change', 'account_locked', 'account_unlocked', 'session_expired') NOT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    details JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
);

-- Create session management table
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
);

-- Update existing users to have secure default passwords
-- Generate default passwords in format: lastName + 'X123'
UPDATE users 
SET 
    must_change_password = TRUE,
    account_status = 'active',
    failed_login_attempts = 0
WHERE must_change_password IS NULL OR account_status IS NULL;

-- Create default admin user if it doesn't exist
INSERT IGNORE INTO users (
    email,
    password,
    first_name,
    last_name,
    role,
    is_active,
    account_status,
    must_change_password,
    failed_login_attempts
) VALUES (
    '<EMAIL>',
    '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV', -- Default: AdministratorX123 (should be changed)
    'System',
    'Administrator',
    'hr_admin',
    TRUE,
    'active',
    TRUE,
    0
);

-- Create sample users with default passwords for testing
INSERT IGNORE INTO users (
    email,
    password,
    first_name,
    last_name,
    role,
    is_active,
    account_status,
    must_change_password,
    failed_login_attempts
) VALUES
(
    '<EMAIL>',
    '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV', -- Default: DoeX123
    'John',
    'Doe',
    'manager',
    TRUE,
    'active',
    TRUE,
    0
),
(
    '<EMAIL>',
    '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV', -- Default: SmithX123
    'Jane',
    'Smith',
    'engineer',
    TRUE,
    'active',
    TRUE,
    0
),
(
    '<EMAIL>',
    '$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV', -- Default: JohnsonX123
    'Mike',
    'Johnson',
    'senior_engineer',
    TRUE,
    'active',
    TRUE,
    0
);

-- Create stored procedure to clean up expired sessions
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanupExpiredSessions()
BEGIN
    DELETE FROM user_sessions WHERE expires_at < NOW();
    UPDATE users SET session_token = NULL, session_expires_at = NULL 
    WHERE session_expires_at < NOW();
END //
DELIMITER ;

-- Create event to automatically clean up expired sessions every hour
-- Note: This requires the event scheduler to be enabled
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
ON SCHEDULE EVERY 1 HOUR
DO
  CALL CleanupExpiredSessions();

-- Security compliance notes
INSERT IGNORE INTO security_audit_log (user_id, event_type, details) 
VALUES (NULL, 'login_success', JSON_OBJECT('message', 'Security migration completed', 'timestamp', NOW()));

COMMIT;
