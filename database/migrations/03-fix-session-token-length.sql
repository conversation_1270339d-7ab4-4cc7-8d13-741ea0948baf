-- Fix session_token column length for JWT tokens
-- JWT tokens can be longer than 255 characters, especially with custom payload data

USE ehrx;

-- Increase session_token column size in users table
ALTER TABLE users MODIFY COLUMN session_token TEXT NULL;

-- Increase session_token column size in user_sessions table if it exists
ALTER TABLE user_sessions MODIFY COLUMN session_token TEXT NOT NULL;

-- Update the unique constraint on user_sessions.session_token to handle TEXT
-- First drop the existing unique constraint
ALTER TABLE user_sessions DROP INDEX session_token;

-- Add a new unique index with a prefix length for TEXT columns
ALTER TABLE user_sessions ADD UNIQUE INDEX idx_session_token_unique (session_token(255));

-- Update the regular index on users.session_token to handle TEXT
ALTER TABLE users DROP INDEX idx_session_token;
ALTER TABLE users ADD INDEX idx_session_token (session_token(255));

-- Display confirmation
SELECT 'Session token column length fix applied successfully' AS status;
