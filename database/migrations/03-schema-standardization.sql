-- Schema Standardization Migration for EHRX Database
-- This script standardizes field definitions to match TypeORM entities
-- and adds missing security-related fields and indexes

USE ehrx;

-- =====================================================
-- STANDARDIZE USER TABLE SCHEMA
-- =====================================================

-- Add missing refresh token fields if they don't exist
SET @sql = '';

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'refresh_token';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN refresh_token VARCHAR(1000) NULL AFTER session_expires_at;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'refresh_token_expires_at';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN refresh_token_expires_at TIMESTAMP NULL AFTER refresh_token;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Add missing MFA and security fields if they don't exist
SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'mfa_backup_codes';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN mfa_backup_codes TEXT NULL AFTER refresh_token_expires_at;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM information_schema.columns
WHERE table_schema = 'ehrx' AND table_name = 'users' AND column_name = 'password_history';
SET @sql = IF(@col_exists = 0, 'ALTER TABLE users ADD COLUMN password_history TEXT NULL AFTER mfa_backup_codes;', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- STANDARDIZE FIELD LENGTHS AND TYPES
-- =====================================================

-- Standardize session_token length to match TypeORM entity (VARCHAR(1000))
ALTER TABLE users MODIFY COLUMN session_token VARCHAR(1000) NULL;

-- Ensure refresh_token has correct length
ALTER TABLE users MODIFY COLUMN refresh_token VARCHAR(1000) NULL;

-- =====================================================
-- ADD SECURITY-FOCUSED INDEXES
-- =====================================================

-- Add indexes for security and performance if they don't exist
SET @sql = '';

-- Check and add refresh_token index
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_refresh_token';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_refresh_token (refresh_token(255));', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check and add last_login_at index
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_last_login_at';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_last_login_at (last_login_at);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check and add failed_login_attempts index
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_failed_login_attempts';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_failed_login_attempts (failed_login_attempts);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check and add two_factor_enabled index
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_two_factor_enabled';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_two_factor_enabled (two_factor_enabled);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- REMOVE DUPLICATE SESSION MANAGEMENT TABLE
-- =====================================================

-- Check if user_sessions table exists and is redundant
-- We'll keep the User entity approach and remove the separate table if it exists
-- This consolidates session management to one approach

DROP TABLE IF EXISTS user_sessions;

-- =====================================================
-- VERIFICATION AND CLEANUP
-- =====================================================

-- Log the standardization completion
INSERT INTO ehrx.system_logs (log_level, message, created_at) 
VALUES ('INFO', 'Database schema standardization completed - all fields now match TypeORM entities', NOW())
ON DUPLICATE KEY UPDATE message = VALUES(message), created_at = VALUES(created_at);

-- Display final schema verification
SELECT 
    'Schema Standardization Complete' as status,
    COUNT(*) as total_users,
    COUNT(CASE WHEN refresh_token IS NOT NULL THEN 1 END) as users_with_refresh_tokens,
    COUNT(CASE WHEN two_factor_enabled = 1 THEN 1 END) as users_with_mfa
FROM users;
