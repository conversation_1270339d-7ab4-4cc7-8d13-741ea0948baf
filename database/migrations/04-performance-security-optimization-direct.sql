-- =====================================================
-- PERFORMANCE AND SECURITY OPTIMIZATION MIGRATION (DIRECT)
-- =====================================================
-- This migration adds additional indexes and optimizations
-- for enhanced performance and security in authentication flows

USE ehrx;

-- =====================================================
-- ADDITIONAL SECURITY INDEXES FOR USERS TABLE
-- =====================================================

-- Composite index for login validation (email + account_status + is_active)
ALTER TABLE users ADD INDEX idx_login_validation (email, account_status, is_active);

-- Composite index for session validation (session_token + session_expires_at)  
ALTER TABLE users ADD INDEX idx_session_validation (session_token(255), session_expires_at);

-- Composite index for refresh token validation
ALTER TABLE users ADD INDEX idx_refresh_validation (refresh_token(255), refresh_token_expires_at);

-- Index for security monitoring (failed_login_attempts + account_locked_until)
ALTER TABLE users ADD INDEX idx_security_monitoring (failed_login_attempts, account_locked_until);

-- Index for MFA operations
ALTER TABLE users ADD INDEX idx_mfa_operations (two_factor_enabled, id);

-- =====================================================
-- PERFORMANCE INDEXES FOR ORGANIZATIONAL QUERIES
-- =====================================================

-- Composite index for organizational unit queries
ALTER TABLE users ADD INDEX idx_org_hierarchy (organizational_unit_id, manager_id, is_active);

-- Index for role-based queries
ALTER TABLE users ADD INDEX idx_role_active (role, is_active);

-- =====================================================
-- ASSESSMENT PERFORMANCE INDEXES
-- =====================================================

-- Composite index for assessment instances (employee + status + date)
ALTER TABLE assessment_instances ADD INDEX idx_assessment_performance (employee_id, status, created_at);

-- Index for assessment templates by organizational level
ALTER TABLE assessment_templates ADD INDEX idx_template_level (template_level, is_active);

-- =====================================================
-- TEAM MANAGEMENT INDEXES
-- =====================================================

-- Composite index for team member queries
ALTER TABLE team_members ADD INDEX idx_team_membership (organizational_unit_id, user_id, is_active);

-- =====================================================
-- SECURITY AUDIT TABLE
-- =====================================================

-- Create security audit log table for comprehensive logging
CREATE TABLE IF NOT EXISTS security_audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id INT NULL,
    email VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    session_id VARCHAR(255) NULL,
    event_details JSON NULL,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL DEFAULT 'LOW',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for security audit queries
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_severity (severity),
    INDEX idx_ip_address (ip_address),
    INDEX idx_security_search (event_type, severity, timestamp),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB;
