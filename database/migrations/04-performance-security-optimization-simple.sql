-- =====================================================
-- PERFORMANCE AND SECURITY OPTIMIZATION MIGRATION (SIMPLIFIED)
-- =====================================================
-- This migration adds additional indexes and optimizations
-- for enhanced performance and security in authentication flows

USE ehrx;

-- =====================================================
-- ADDITIONAL SECURITY INDEXES FOR USERS TABLE
-- =====================================================

-- Composite index for login validation (email + account_status + is_active)
CREATE INDEX IF NOT EXISTS idx_login_validation ON users (email, account_status, is_active);

-- Composite index for session validation (session_token + session_expires_at)  
CREATE INDEX IF NOT EXISTS idx_session_validation ON users (session_token(255), session_expires_at);

-- Composite index for refresh token validation
CREATE INDEX IF NOT EXISTS idx_refresh_validation ON users (refresh_token(255), refresh_token_expires_at);

-- Index for security monitoring (failed_login_attempts + account_locked_until)
CREATE INDEX IF NOT EXISTS idx_security_monitoring ON users (failed_login_attempts, account_locked_until);

-- Index for MFA operations
CREATE INDEX IF NOT EXISTS idx_mfa_operations ON users (two_factor_enabled, id);

-- =====================================================
-- PERFORMANCE INDEXES FOR ORGANIZATIONAL QUERIES
-- =====================================================

-- Composite index for organizational unit queries
CREATE INDEX IF NOT EXISTS idx_org_hierarchy ON users (organizational_unit_id, manager_id, is_active);

-- Index for role-based queries
CREATE INDEX IF NOT EXISTS idx_role_active ON users (role, is_active);

-- =====================================================
-- ASSESSMENT PERFORMANCE INDEXES
-- =====================================================

-- Composite index for assessment instances (employee + status + date)
CREATE INDEX IF NOT EXISTS idx_assessment_performance ON assessment_instances (employee_id, status, created_at);

-- Index for assessment templates by organizational level
CREATE INDEX IF NOT EXISTS idx_template_level ON assessment_templates (template_level, is_active);

-- =====================================================
-- TEAM MANAGEMENT INDEXES
-- =====================================================

-- Composite index for team member queries
CREATE INDEX IF NOT EXISTS idx_team_membership ON team_members (organizational_unit_id, user_id, is_active);

-- =====================================================
-- SECURITY AUDIT TABLE
-- =====================================================

-- Create security audit log table for comprehensive logging
CREATE TABLE IF NOT EXISTS security_audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id INT NULL,
    email VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    session_id VARCHAR(255) NULL,
    event_details JSON NULL,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL DEFAULT 'LOW',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for security audit queries
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_severity (severity),
    INDEX idx_ip_address (ip_address),
    INDEX idx_security_search (event_type, severity, timestamp),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Show all indexes on users table
SELECT 'Users table indexes:' as info;
SHOW INDEX FROM users;

-- Show all indexes on assessment tables
SELECT 'Assessment instances indexes:' as info;
SHOW INDEX FROM assessment_instances;

SELECT 'Assessment templates indexes:' as info;
SHOW INDEX FROM assessment_templates;

-- Show all indexes on team tables
SELECT 'Team members indexes:' as info;
SHOW INDEX FROM team_members;

SELECT 'Performance optimization migration completed successfully!' as status;
