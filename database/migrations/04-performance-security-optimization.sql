-- =====================================================
-- PERFORMANCE AND SECURITY OPTIMIZATION MIGRATION
-- =====================================================
-- This migration adds additional indexes and optimizations
-- for enhanced performance and security in authentication flows

USE ehrx;

-- =====================================================
-- ADDITIONAL SECURITY INDEXES
-- =====================================================

-- Add composite indexes for common authentication queries
SET @sql = '';
SET @index_exists = 0;

-- Composite index for login validation (email + account_status + is_active)
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_login_validation';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_login_validation (email, account_status, is_active);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Composite index for session validation (session_token + session_expires_at)
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_session_validation';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_session_validation (session_token(255), session_expires_at);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Composite index for refresh token validation
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_refresh_validation';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_refresh_validation (refresh_token(255), refresh_token_expires_at);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Index for security monitoring (failed_login_attempts + account_locked_until)
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_security_monitoring';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_security_monitoring (failed_login_attempts, account_locked_until);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Index for MFA operations
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_mfa_operations';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_mfa_operations (two_factor_enabled, id);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- PERFORMANCE INDEXES FOR ORGANIZATIONAL QUERIES
-- =====================================================

-- Composite index for organizational unit queries
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_org_hierarchy';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_org_hierarchy (organizational_unit_id, manager_id, is_active);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Index for role-based queries
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'users' AND index_name = 'idx_role_active';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE users ADD INDEX idx_role_active (role, is_active);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- ASSESSMENT PERFORMANCE INDEXES
-- =====================================================

-- Composite index for assessment instances (employee + status + date)
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'assessment_instances' AND index_name = 'idx_assessment_performance';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE assessment_instances ADD INDEX idx_assessment_performance (employee_id, status, created_at);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Index for assessment templates by organizational level
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'assessment_templates' AND index_name = 'idx_template_level';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE assessment_templates ADD INDEX idx_template_level (template_level, is_active);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- TEAM MANAGEMENT INDEXES
-- =====================================================

-- Composite index for team member queries
SELECT COUNT(*) INTO @index_exists FROM information_schema.statistics
WHERE table_schema = 'ehrx' AND table_name = 'team_members' AND index_name = 'idx_team_membership';
SET @sql = IF(@index_exists = 0, 'ALTER TABLE team_members ADD INDEX idx_team_membership (organizational_unit_id, user_id, is_active);', '');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- =====================================================
-- SECURITY AUDIT TABLE (if needed for future)
-- =====================================================

-- Create security audit log table for comprehensive logging
CREATE TABLE IF NOT EXISTS security_audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id INT NULL,
    email VARCHAR(255) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    session_id VARCHAR(255) NULL,
    event_details JSON NULL,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL DEFAULT 'LOW',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for security audit queries
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_severity (severity),
    INDEX idx_ip_address (ip_address),
    INDEX idx_security_search (event_type, severity, timestamp),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB;

-- =====================================================
-- DATABASE OPTIMIZATION SETTINGS
-- =====================================================

-- Optimize table storage and performance
-- Note: These are suggestions for production deployment

-- Set optimal InnoDB settings (to be applied at server level)
-- innodb_buffer_pool_size = 70% of available RAM
-- innodb_log_file_size = 256M
-- innodb_flush_log_at_trx_commit = 1 (for ACID compliance)
-- innodb_file_per_table = ON
-- innodb_buffer_pool_instances = 8 (for systems with > 1GB buffer pool)

-- =====================================================
-- CLEANUP AND MAINTENANCE
-- =====================================================

-- Add event to clean up expired sessions periodically
-- This should be implemented as a scheduled job in the application

-- Add event to archive old audit logs
-- This should be implemented as a scheduled job in the application

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify all critical indexes exist
SELECT 
    table_name,
    index_name,
    column_name,
    seq_in_index
FROM information_schema.statistics 
WHERE table_schema = 'ehrx' 
    AND table_name IN ('users', 'assessment_instances', 'team_members', 'assessment_templates')
    AND index_name LIKE 'idx_%'
ORDER BY table_name, index_name, seq_in_index;

-- Performance analysis query for authentication operations
-- (Run this periodically to monitor performance)
/*
EXPLAIN SELECT id, email, password, account_status, failed_login_attempts, account_locked_until
FROM users 
WHERE email = '<EMAIL>' 
    AND account_status = 'active' 
    AND is_active = 1;
*/

COMMIT;
