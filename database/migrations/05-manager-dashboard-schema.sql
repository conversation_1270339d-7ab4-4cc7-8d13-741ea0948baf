-- Manager Dashboard Schema Migration for EHRX Database
-- This script creates the manager dashboard metrics and reminder settings tables
-- Following PostgreSQL patterns and NIS2 compliance requirements

-- =====================================================
-- MANAGER DASHBOARD METRICS TABLE
-- =====================================================

-- Create manager dashboard metrics table
CREATE TABLE IF NOT EXISTS manager_dashboard_metrics (
  id SERIAL PRIMARY KEY,
  organizational_unit_id INTEGER NOT NULL,
  manager_id INTEGER NOT NULL,
  reporting_period DATE NOT NULL,
  
  -- Core Metrics (following Excel dashboard structure)
  fte_count DECIMAL(4,1) DEFAULT 0,
  attrition_resigned INTEGER DEFAULT 0,
  attrition_involuntary INTEGER DEFAULT 0,
  sla_percentage DECIMAL(5,2),
  utilization_percentage DECIMAL(5,2),
  ax_percentage DECIMAL(5,2),
  compliance_score DECIMAL(5,2),
  ab_variance_percentage DECIMAL(6,2),
  vacation_leave_percentage DECIMAL(5,2),
  in_office_percentage DECIMAL(5,2),
  
  -- Metadata
  last_updated_by INTEGER,
  last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'approved')),
  notes TEXT,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Constraints
  CONSTRAINT fk_manager_dashboard_org_unit 
    FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
  CONSTRAINT fk_manager_dashboard_manager 
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_manager_dashboard_updated_by 
    FOREIGN KEY (last_updated_by) REFERENCES users(id) ON DELETE SET NULL,
  
  -- Unique constraint to prevent duplicate entries for same period/unit
  CONSTRAINT unique_period_unit UNIQUE (organizational_unit_id, reporting_period)
);

-- =====================================================
-- DASHBOARD REMINDER SETTINGS TABLE
-- =====================================================

-- Create dashboard reminder settings table
CREATE TABLE IF NOT EXISTS dashboard_reminder_settings (
  id SERIAL PRIMARY KEY,
  manager_id INTEGER NOT NULL,
  reminder_day_of_month INTEGER DEFAULT 25 CHECK (reminder_day_of_month BETWEEN 1 AND 31),
  reminder_enabled BOOLEAN DEFAULT TRUE,
  email_template_id VARCHAR(50) DEFAULT 'monthly_dashboard_reminder',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Constraints
  CONSTRAINT fk_reminder_settings_manager 
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT unique_manager_settings UNIQUE (manager_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Performance optimization indexes for manager dashboard
CREATE INDEX IF NOT EXISTS idx_manager_dashboard_metrics_composite 
ON manager_dashboard_metrics (manager_id, reporting_period, status);

CREATE INDEX IF NOT EXISTS idx_manager_dashboard_metrics_org_period 
ON manager_dashboard_metrics (organizational_unit_id, reporting_period);

CREATE INDEX IF NOT EXISTS idx_manager_dashboard_metrics_updated 
ON manager_dashboard_metrics (last_updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_manager_dashboard_metrics_status 
ON manager_dashboard_metrics (status);

-- Optimize organizational unit queries for manager dashboard
CREATE INDEX IF NOT EXISTS idx_organizational_units_manager_active 
ON organizational_units (manager_id) WHERE is_active = TRUE;

-- Reminder settings indexes
CREATE INDEX IF NOT EXISTS idx_reminder_settings_enabled 
ON dashboard_reminder_settings (reminder_enabled, reminder_day_of_month) 
WHERE reminder_enabled = TRUE;

-- =====================================================
-- TRIGGERS FOR UPDATED_AT COLUMNS
-- =====================================================

-- Create trigger function for updating updated_at column if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_manager_dashboard_metrics_updated_at 
    BEFORE UPDATE ON manager_dashboard_metrics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_dashboard_reminder_settings_updated_at 
    BEFORE UPDATE ON dashboard_reminder_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SAMPLE DATA FOR TESTING (OPTIONAL)
-- =====================================================

-- Insert default reminder settings for existing managers
INSERT INTO dashboard_reminder_settings (manager_id, reminder_day_of_month, reminder_enabled)
SELECT DISTINCT u.id, 25, TRUE
FROM users u
INNER JOIN organizational_units ou ON u.id = ou.manager_id
WHERE u.role IN ('manager', 'director', 'vp', 'ceo')
  AND NOT EXISTS (
    SELECT 1 FROM dashboard_reminder_settings drs 
    WHERE drs.manager_id = u.id
  );

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify table creation
SELECT 
    'Manager Dashboard Schema Migration Complete' as status,
    COUNT(*) as total_reminder_settings
FROM dashboard_reminder_settings;

-- Display table structure verification
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('manager_dashboard_metrics', 'dashboard_reminder_settings')
ORDER BY table_name, ordinal_position;
