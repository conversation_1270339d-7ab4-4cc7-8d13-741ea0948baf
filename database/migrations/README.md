# Database Migrations - Historical Reference

This directory contains the historical migration files that were used to build the enhanced database schema. These files are kept for reference purposes only.

## ⚠️ Important Notice

**These migration files are NO LONGER USED for fresh installations.**

The main `database/init.sql` file now contains the complete, consolidated schema that includes all enhancements from these migration files.

## File Descriptions

### `init-old-backup.sql`
- **Purpose**: Backup of the original, basic database initialization script
- **Issues**: 
  - Used `password_hash` column instead of `password`
  - Limited role definitions (only 4 roles)
  - No security features
  - Missing organizational structure
- **Status**: ❌ OBSOLETE - Do not use

### `01-schema.sql`
- **Purpose**: Enhanced schema with organizational structure and extended user fields
- **Features**: 
  - Enhanced users table with proper `password` column
  - Extended role definitions (10+ roles)
  - Organizational units and manager relationships
  - Employment details and contact information
- **Status**: ✅ INTEGRATED into main init.sql

### `02-security-migration.sql`
- **Purpose**: NIS2-compliant security enhancements
- **Features**:
  - Account status management
  - Failed login attempt tracking
  - Session management
  - Two-factor authentication support
  - Password change requirements
  - Account locking mechanisms
- **Status**: ✅ INTEGRATED into main init.sql

### `03-fix-session-token-length.sql`
- **Purpose**: Fix session token column length for JWT tokens
- **Features**:
  - Changed session_token from VARCHAR(255) to TEXT
  - Updated user_sessions table constraints
- **Status**: ✅ INTEGRATED into main init.sql

## ✅ CONSOLIDATION COMPLETED - July 31, 2025

All migration conflicts have been successfully resolved:

### Resolved Issues:
1. **Conflicting User Table Definitions**: ✅ RESOLVED
   - Consolidated to use enhanced schema with `password` column and full NIS2 compliance
2. **Recognition Badges Table Conflicts**: ✅ RESOLVED
   - Updated to match existing database structure (`point_value` vs `points_value`)
3. **Schema Inconsistencies**: ✅ RESOLVED
   - Standardized on enhanced schema with comprehensive role system

### Validation Results:
- ✅ All tables create successfully
- ✅ Sample data loads correctly
- ✅ Foreign key constraints work properly
- ✅ Matches existing production database structure

## Usage for New Deployments

Use only the consolidated script:
```bash
mysql -u [username] -p [database_name] < database/init.sql
```

**Do not use any files in this migrations directory for new deployments.**
- **Status**: ✅ INTEGRATED into main init.sql

## Current Database Setup

### For Fresh Installations
Use the main initialization script:
```bash
mysql -u your_username -p ehrx < database/init.sql
```

### For Existing Installations
Your database should already have all the enhancements. No action needed.

## Schema Conflicts Resolved

The following conflicts were identified and resolved:

1. **User Table Definition Conflict**:
   - ❌ Old: `password_hash VARCHAR(255)`
   - ✅ New: `password VARCHAR(255)`

2. **Role Definition Conflict**:
   - ❌ Old: Limited to 4 roles
   - ✅ New: 10+ engineering-focused roles

3. **Missing Security Features**:
   - ❌ Old: Basic authentication only
   - ✅ New: NIS2-compliant security features

4. **Missing Tables**:
   - ❌ Old: No user_sessions table
   - ✅ New: Complete session management

## Database Schema Verification

To verify your database has the correct schema:

```sql
-- Check users table structure
DESCRIBE users;

-- Verify security columns exist
SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ehrx' AND TABLE_NAME = 'users' 
AND COLUMN_NAME IN ('account_status', 'session_token', 'must_change_password');

-- Check if user_sessions table exists
SHOW TABLES LIKE 'user_sessions';

-- Verify role definitions
SHOW COLUMNS FROM users LIKE 'role';
```

## Migration History

1. **Phase 1**: Basic schema (`init-old-backup.sql`)
2. **Phase 2**: Enhanced schema (`01-schema.sql`)
3. **Phase 3**: Security features (`02-security-migration.sql`)
4. **Phase 4**: Session token fixes (`03-fix-session-token-length.sql`)
5. **Phase 5**: Consolidated schema (`init.sql`) ← **CURRENT**

## Support

If you encounter issues with the database schema:
1. Check that you're using the main `database/init.sql` file
2. Verify your database matches the expected schema
3. Contact the development team for assistance

---
**Last Updated**: Database consolidation completed
**Status**: All conflicts resolved, single source of truth established
