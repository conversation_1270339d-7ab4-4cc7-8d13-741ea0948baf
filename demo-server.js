const express = require('express');
const https = require('https');
const fs = require('fs');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
// 🔧 FIXED: Consistent port configuration with fallback to development ports
const HTTP_PORT = process.env.HTTP_PORT || (process.env.NODE_ENV === 'production' ? 80 : 8080);
const HTTPS_PORT = process.env.HTTPS_PORT || (process.env.NODE_ENV === 'production' ? 443 : 8443);
const BACKEND_PORT = process.env.BACKEND_PORT || 4000;
const FRONTEND_PORT = process.env.FRONTEND_PORT || 3080;

// Middleware to log requests
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// 🔧 FIXED: Use configurable backend port with health check
app.use('/api', createProxyMiddleware({
  target: `http://localhost:${BACKEND_PORT}`,
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  logLevel: process.env.NODE_ENV === 'development' ? 'info' : 'warn',
  timeout: 30000, // 30 second timeout
  proxyTimeout: 30000,
  onError: (err, req, res) => {
    console.error(`🚨 Backend proxy error (port ${BACKEND_PORT}):`, err.message);
    if (!res.headersSent) {
      res.status(503).json({
        error: 'Backend service unavailable',
        details: `Backend server on port ${BACKEND_PORT} is not responding`,
        timestamp: new Date().toISOString()
      });
    }
  },
  onProxyReq: (proxyReq, req, res) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔗 API: ${req.method} ${req.url} -> http://localhost:${BACKEND_PORT}${req.url}`);
    }
  }
}));

// 🔧 FIXED: Use configurable frontend port with better error handling
app.use('/', createProxyMiddleware({
  target: `http://localhost:${FRONTEND_PORT}`,
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying for React hot reload
  logLevel: process.env.NODE_ENV === 'development' ? 'info' : 'warn',
  timeout: 30000,
  proxyTimeout: 30000,
  onError: (err, req, res) => {
    console.error(`🚨 Frontend proxy error (port ${FRONTEND_PORT}):`, err.message);
    if (!res.headersSent) {
      res.status(503).send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>eHRx - Service Unavailable</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
              .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
              .error { color: #d32f2f; }
              .info { color: #1976d2; margin-top: 20px; }
              .timestamp { color: #666; font-size: 0.9em; margin-top: 20px; }
            </style>
          </head>
          <body>
            <div class="container">
              <h1>🚨 eHRx Frontend Service Unavailable</h1>
              <p class="error">The React development server is not running on port ${FRONTEND_PORT}.</p>
              <div class="info">
                <h3>Troubleshooting Steps:</h3>
                <ol>
                  <li>Check if the frontend server is running: <code>npm run start</code> in the frontend directory</li>
                  <li>Verify the frontend is accessible at: <code>http://localhost:${FRONTEND_PORT}</code></li>
                  <li>Check for port conflicts or firewall issues</li>
                  <li>Review the startup logs for any errors</li>
                </ol>
              </div>
              <p class="timestamp">Error occurred at: ${new Date().toISOString()}</p>
              <p class="timestamp">Error details: ${err.message}</p>
            </div>
          </body>
        </html>
      `);
    }
  },
  onProxyReq: (proxyReq, req, res) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔗 Frontend: ${req.method} ${req.url} -> http://localhost:${FRONTEND_PORT}${req.url}`);
    }
  }
}));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Function to create self-signed certificate if it doesn't exist
function createSelfSignedCert() {
  const { execSync } = require('child_process');

  try {
    // Check if certificate files exist
    if (fs.existsSync('./server.key') && fs.existsSync('./server.crt')) {
      return true;
    }

    console.log('🔐 Creating self-signed SSL certificate...');

    // Create self-signed certificate
    execSync(`openssl req -x509 -newkey rsa:4096 -keyout server.key -out server.crt -days 365 -nodes -subj "/C=DK/ST=Denmark/L=Copenhagen/O=eHRx/OU=Development/CN=dev.trusthansen.dk"`, { stdio: 'inherit' });

    console.log('✅ Self-signed certificate created successfully');
    return true;
  } catch (error) {
    console.warn('⚠️  Could not create self-signed certificate:', error.message);
    console.warn('   HTTPS server will not be available');
    return false;
  }
}

// 🔧 FIXED: Enhanced server startup with health checks
function startServer() {
  // Health check endpoints
  app.get('/health', (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      ports: {
        http: HTTP_PORT,
        https: HTTPS_PORT,
        backend: BACKEND_PORT,
        frontend: FRONTEND_PORT
      }
    });
  });

  // Start HTTP server with error handling
  const httpServer = app.listen(HTTP_PORT, '0.0.0.0', () => {
    console.log(`🚀 eHRx HTTP Proxy Server running on port ${HTTP_PORT}`);
    console.log(`🔗 HTTP Access: http://dev.trusthansen.dk:${HTTP_PORT}/`);
    console.log(`🔗 Local Access: http://localhost:${HTTP_PORT}/`);
    console.log(`📊 Health Check: http://localhost:${HTTP_PORT}/health`);
    console.log(`🎯 Backend Target: http://localhost:${BACKEND_PORT}`);
    console.log(`🎯 Frontend Target: http://localhost:${FRONTEND_PORT}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  });

  httpServer.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
      console.error(`🚨 Port ${HTTP_PORT} is already in use. Please check for conflicting processes.`);
      process.exit(1);
    } else {
      console.error('🚨 HTTP Server error:', err);
    }
  });

  return httpServer;
}

// 🔧 FIXED: Start servers with proper error handling and graceful shutdown
const httpServer = startServer();

// Try to start HTTPS server with better error handling
const hasCert = createSelfSignedCert();
if (hasCert) {
  try {
    const httpsOptions = {
      key: fs.readFileSync('./server.key'),
      cert: fs.readFileSync('./server.crt')
    };

    const httpsServer = https.createServer(httpsOptions, app).listen(HTTPS_PORT, '0.0.0.0', () => {
      console.log(`🔒 eHRx HTTPS Proxy Server running on port ${HTTPS_PORT}`);
      console.log(`🔗 HTTPS Access: https://dev.trusthansen.dk:${HTTPS_PORT}/`);
      console.log('');
      console.log(`📡 Proxying API requests to: http://localhost:${BACKEND_PORT}`);
      console.log(`🌐 Proxying frontend requests to: http://localhost:${FRONTEND_PORT}`);
      console.log('');
      console.log('⚠️  Note: Self-signed certificate will show security warning in browser');
      console.log('   Click "Advanced" → "Proceed to dev.trusthansen.dk" to continue');
      console.log('');
      console.log('Make sure both services are running:');
      console.log(`  - Backend (NestJS): http://localhost:${BACKEND_PORT}`);
      console.log(`  - Frontend (React): http://localhost:${FRONTEND_PORT}`);
    });

    httpsServer.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.error(`🚨 HTTPS Port ${HTTPS_PORT} is already in use.`);
      } else {
        console.error('🚨 HTTPS Server error:', err);
      }
    });
  } catch (error) {
    console.error('❌ Failed to start HTTPS server:', error.message);
    console.log('📝 HTTPS server will not be available. HTTP server is running normally.');
  }
} else {
  console.log('📝 No SSL certificates found. Only HTTP server is running.');
}

// 🔧 FIXED: Enhanced graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  httpServer.close(() => {
    console.log('✅ HTTP server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully...');
  httpServer.close(() => {
    console.log('✅ HTTP server closed');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('🚨 Uncaught Exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
