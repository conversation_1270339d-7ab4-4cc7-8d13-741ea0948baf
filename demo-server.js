const express = require('express');
const https = require('https');
const fs = require('fs');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const HTTP_PORT = process.env.HTTP_PORT || 80;
const HTTPS_PORT = process.env.HTTPS_PORT || 443;

// Middleware to log requests
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Proxy API requests to backend (NestJS on port 4000)
app.use('/api', createProxyMiddleware({
  target: 'http://localhost:4000',
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  logLevel: 'info',
  onError: (err, req, res) => {
    console.error('Backend proxy error:', err.message);
    res.status(500).json({ error: 'Backend service unavailable' });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`Proxying API request: ${req.method} ${req.url} -> http://localhost:4000${req.url}`);
  }
}));

// Proxy all other requests to frontend (React on port 3080)
app.use('/', createProxyMiddleware({
  target: 'http://localhost:3080',
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying for React hot reload
  logLevel: 'info',
  onError: (err, req, res) => {
    console.error('Frontend proxy error:', err.message);
    res.status(500).send(`
      <html>
        <body>
          <h1>Frontend Service Unavailable</h1>
          <p>The React development server is not running.</p>
          <p>Please make sure the frontend is started on port 3080.</p>
          <p>Error: ${err.message}</p>
        </body>
      </html>
    `);
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`Proxying frontend request: ${req.method} ${req.url} -> http://localhost:3080${req.url}`);
  }
}));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Function to create self-signed certificate if it doesn't exist
function createSelfSignedCert() {
  const { execSync } = require('child_process');

  try {
    // Check if certificate files exist
    if (fs.existsSync('./server.key') && fs.existsSync('./server.crt')) {
      return true;
    }

    console.log('🔐 Creating self-signed SSL certificate...');

    // Create self-signed certificate
    execSync(`openssl req -x509 -newkey rsa:4096 -keyout server.key -out server.crt -days 365 -nodes -subj "/C=DK/ST=Denmark/L=Copenhagen/O=eHRx/OU=Development/CN=dev.trusthansen.dk"`, { stdio: 'inherit' });

    console.log('✅ Self-signed certificate created successfully');
    return true;
  } catch (error) {
    console.warn('⚠️  Could not create self-signed certificate:', error.message);
    console.warn('   HTTPS server will not be available');
    return false;
  }
}

// Start HTTP server
app.listen(HTTP_PORT, '0.0.0.0', () => {
  console.log(`🚀 eHRx HTTP Proxy Server running on port ${HTTP_PORT}`);
  console.log(`🔗 HTTP Access: http://dev.trusthansen.dk:${HTTP_PORT}/`);
});

// Try to start HTTPS server
const hasCert = createSelfSignedCert();
if (hasCert) {
  try {
    const httpsOptions = {
      key: fs.readFileSync('./server.key'),
      cert: fs.readFileSync('./server.crt')
    };

    https.createServer(httpsOptions, app).listen(HTTPS_PORT, '0.0.0.0', () => {
      console.log(`🔒 eHRx HTTPS Proxy Server running on port ${HTTPS_PORT}`);
      console.log(`🔗 HTTPS Access: https://dev.trusthansen.dk:${HTTPS_PORT}/`);
      console.log('');
      console.log('📡 Proxying API requests to: http://localhost:4000');
      console.log('🌐 Proxying frontend requests to: http://localhost:3080');
      console.log('');
      console.log('⚠️  Note: Self-signed certificate will show security warning in browser');
      console.log('   Click "Advanced" → "Proceed to dev.trusthansen.dk" to continue');
      console.log('');
      console.log('Make sure both services are running:');
      console.log('  - Backend (NestJS): http://localhost:4000');
      console.log('  - Frontend (React): http://localhost:3080');
    });
  } catch (error) {
    console.error('❌ Failed to start HTTPS server:', error.message);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully');
  process.exit(0);
});
