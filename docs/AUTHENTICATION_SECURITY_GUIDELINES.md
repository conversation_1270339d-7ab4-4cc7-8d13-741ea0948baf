# 🔐 Authentication Security Guidelines

## Overview

This document outlines critical security guidelines for maintaining the integrity of the eHRx authentication system. **ALL developers must follow these guidelines** to prevent security vulnerabilities and system breakage.

## 🚨 CRITICAL RULES - NEVER BREAK THESE

### 1. **JWT Secret Management**
- ❌ **NEVER** hardcode JWT secrets in source code
- ❌ **NEVER** log JWT secrets or tokens in production
- ✅ **ALWAYS** use environment variables for secrets
- ✅ **ALWAYS** validate JWT secret length (minimum 32 characters)

```typescript
// ❌ WRONG
const jwtSecret = 'hardcoded-secret-123';

// ✅ CORRECT
const jwtSecret = process.env.JWT_SECRET;
if (!jwtSecret || jwtSecret.length < 32) {
  throw new Error('JWT_SECRET must be at least 32 characters');
}
```

### 2. **Password Security**
- ❌ **NEVER** store plain text passwords
- ❌ **NEVER** log passwords (even hashed ones in production)
- ✅ **ALWAYS** use bcrypt for password hashing
- ✅ **ALWAYS** use salt rounds >= 10

```typescript
// ❌ WRONG
user.password = plainTextPassword;

// ✅ CORRECT
user.password = await bcrypt.hash(plainTextPassword, 12);
```

### 3. **Session Management**
- ❌ **NEVER** use weak session validation (e.g., `.includes()`)
- ❌ **NEVER** store sensitive data in sessions
- ✅ **ALWAYS** use exact session token matching
- ✅ **ALWAYS** invalidate sessions on logout

```typescript
// ❌ WRONG
if (user.sessionToken.includes(payload.sessionId)) {
  return user;
}

// ✅ CORRECT
const sessionTokens = user.sessionToken.split(',');
if (sessionTokens.includes(payload.sessionId)) {
  return user;
}
```

### 4. **Input Validation & Sanitization**
- ❌ **NEVER** trust user input without validation
- ❌ **NEVER** directly use request data in queries
- ✅ **ALWAYS** sanitize input before processing
- ✅ **ALWAYS** validate input types and formats

```typescript
// ❌ WRONG
const user = await this.userRepository.findOne({ email: req.body.email });

// ✅ CORRECT
const email = this.sanitizeEmail(req.body.email);
if (!this.isValidEmail(email)) {
  throw new BadRequestException('Invalid email format');
}
const user = await this.userRepository.findOne({ email });
```

## 🛡️ SECURITY BEST PRACTICES

### Database Security
- Use TypeORM with parameterized queries
- Disable `synchronize` in production
- Enable audit logging for all auth operations
- Implement proper error handling

### API Security
- Always use HTTPS in production
- Implement rate limiting on auth endpoints
- Use proper CORS configuration
- Validate all request headers

### Error Handling
- Never expose internal errors to users
- Log security events for monitoring
- Use generic error messages for auth failures
- Implement proper exception handling

## 🔍 REQUIRED CHECKS BEFORE COMMITTING

### 1. **Run Security Tests**
```bash
npm test -- --testPathPattern="auth.*spec"
```

### 2. **Run Security Linting**
```bash
npx eslint --config .eslintrc-security.js src/auth/ src/users/ src/security/
```

### 3. **Run Pre-commit Security Check**
```bash
./scripts/pre-commit-auth-check.sh
```

### 4. **Validate TypeScript Compilation**
```bash
npm run build
```

## 🚫 FORBIDDEN PATTERNS

### Never Use These Patterns:
```typescript
// ❌ Hardcoded secrets
const secret = 'my-secret-key';

// ❌ Plain text passwords
user.password = password;

// ❌ Weak session validation
if (sessionToken.includes(userSession)) { }

// ❌ SQL injection vulnerable code
const query = `SELECT * FROM users WHERE email = '${email}'`;

// ❌ Logging sensitive data
console.log('User password:', user.password);

// ❌ Synchronous operations in auth
const hash = bcrypt.hashSync(password, 10);

// ❌ Missing error handling
const user = await this.userService.findByEmail(email); // No try/catch
```

## ✅ REQUIRED PATTERNS

### Always Use These Patterns:
```typescript
// ✅ Environment-based configuration
const secret = process.env.JWT_SECRET;

// ✅ Proper password hashing
const hashedPassword = await bcrypt.hash(password, 12);

// ✅ Secure session validation
const sessionTokens = user.sessionToken.split(',');
if (sessionTokens.includes(payload.sessionId)) { }

// ✅ Parameterized queries
const user = await this.userRepository.findOne({ where: { email } });

// ✅ Production-safe logging
if (process.env.NODE_ENV !== 'production') {
  console.log('Debug info:', debugData);
}

// ✅ Async operations
const hash = await bcrypt.hash(password, 12);

// ✅ Proper error handling
try {
  const user = await this.userService.findByEmail(email);
} catch (error) {
  this.logger.error('Auth error:', error);
  throw new UnauthorizedException('Authentication failed');
}
```

## 🔧 TOOLS & AUTOMATION

### Required Tools:
1. **ESLint Security Rules** - Catches security issues
2. **Pre-commit Hooks** - Validates changes before commit
3. **Authentication Tests** - Comprehensive test coverage
4. **Health Monitoring** - Real-time system monitoring
5. **Security Validation** - Automated security checks

### CI/CD Integration:
- All security tests must pass before deployment
- Security linting must pass with zero errors
- Authentication health checks must be green
- No hardcoded secrets allowed in production builds

## 📊 MONITORING & ALERTING

### Key Metrics to Monitor:
- Failed login attempts
- Account lockouts
- Session anomalies
- Security incidents
- Authentication performance
- System health status

### Alert Triggers:
- Failed login rate > 50% of successful logins
- Security incidents > 5 per day
- Average login time > 5 seconds
- System health status = CRITICAL

## 🆘 INCIDENT RESPONSE

### If Authentication is Broken:
1. **Immediate**: Revert to last known good commit
2. **Assess**: Run full security validation suite
3. **Fix**: Address root cause with security review
4. **Test**: Run comprehensive auth tests
5. **Deploy**: Only after all validations pass
6. **Monitor**: Watch health metrics closely

### Emergency Contacts:
- Security Team: [<EMAIL>]
- DevOps Team: [<EMAIL>]
- On-call Engineer: [<EMAIL>]

## 📚 ADDITIONAL RESOURCES

- [OWASP Authentication Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html)
- [NestJS Security Best Practices](https://docs.nestjs.com/security/authentication)
- [JWT Security Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)
- [bcrypt Best Practices](https://github.com/kelektiv/node.bcrypt.js#security-issues-and-concerns)

---

**Remember: Security is everyone's responsibility. When in doubt, ask for a security review!**
