# 🚀 Quick Start: SQL API Development

## TL;DR - Essential Commands

```bash
# Before starting any new feature
npm run validate:auth-compliance

# Create new controller (use template)
cp backend/templates/controller.template.ts src/your-module/your.controller.ts

# Before committing
npm run security:full

# If compliance check fails
node backend/scripts/validate-auth-compliance.js
```

## 🔐 The 3 Golden Rules

### 1. **ALWAYS use JwtAuthGuard**
```typescript
@Controller('your-endpoint')
@UseGuards(JwtAuthGuard)  // 🔐 MANDATORY
export class YourController {
```

### 2. **ALWAYS pass user context**
```typescript
@Get()
findAll(@Request() req) {  // 🔐 MANDATORY
  return this.service.findAll(req.user.userId, req.user.role);
}
```

### 3. **ALWAYS validate in service**
```typescript
async findAll(userId: number, userRole: UserRole) {
  // 🔐 MANDATORY: Apply user-based filtering
  if (userRole === UserRole.EMPLOYEE) {
    return this.repository.find({ where: { userId } });
  }
  // ... role-based logic
}
```

## 🎯 VS Code Snippets

Type these shortcuts in VS Code:

- `nest-controller-auth` → Complete controller with auth
- `auth-imports` → Import all auth guards
- `auth-decorators` → Add auth decorators
- `user-context` → Add user context parameters
- `validate-permissions` → Add permission validation
- `user-filter` → Add user-based query filtering

## ⚡ Quick Templates

### New Controller
```typescript
// Use: nest-controller-auth snippet
@Controller('projects')
@UseGuards(JwtAuthGuard)
export class ProjectsController {
  @Get()
  findAll(@Request() req) {
    return this.service.findAll(req.user.userId, req.user.role);
  }
  
  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN)
  create(@Body() dto: CreateDto, @Request() req) {
    return this.service.create(dto, req.user.userId, req.user.role);
  }
}
```

### Service Method
```typescript
// Use: nest-service-method snippet
async findAll(userId: number, userRole: UserRole) {
  const query = this.repository.createQueryBuilder('entity');
  
  if (userRole === UserRole.EMPLOYEE) {
    query.where('entity.userId = :userId', { userId });
  }
  
  return query.getMany();
}
```

## 🚨 Common Mistakes to Avoid

### ❌ DON'T DO THIS:
```typescript
// Missing auth guard
@Controller('data')
export class DataController {

// Missing user context
@Get()
getData() {
  return this.service.findAll(); // No user info!
}

// Direct database access
@Get()
async getData() {
  return this.repository.find(); // Bypass service!
}
```

### ✅ DO THIS:
```typescript
@Controller('data')
@UseGuards(JwtAuthGuard)
export class DataController {

@Get()
getData(@Request() req) {
  return this.service.findAll(req.user.userId, req.user.role);
}
```

## 🔍 Validation Commands

```bash
# Quick compliance check
npm run validate:auth-compliance

# Full security validation
npm run security:full

# Security linting only
npm run security:lint

# Auth tests only
npm run security:test
```

## 🆘 Troubleshooting

### "Missing JWT Guard" Error
```bash
# Add to your controller:
@UseGuards(JwtAuthGuard)
```

### "Missing Request Parameter" Warning
```bash
# Add to your methods:
methodName(@Request() req) {
```

### "Missing User Context" Error
```bash
# Pass user info to service:
this.service.method(req.user.userId, req.user.role)
```

## 📚 Need More Help?

- **Full Guidelines**: `docs/SQL_API_DEVELOPMENT_GUIDELINES.md`
- **Security Rules**: `docs/AUTHENTICATION_SECURITY_GUIDELINES.md`
- **Templates**: `backend/templates/controller.template.ts`
- **Snippets**: `.vscode/snippets/nestjs-auth.code-snippets`

## 🎯 Workflow

1. **Start**: `npm run validate:auth-compliance`
2. **Code**: Use templates and snippets
3. **Test**: `npm run security:test`
4. **Validate**: `npm run security:full`
5. **Commit**: Pre-commit hooks run automatically

---

**Remember: Following these patterns is MANDATORY for security and compliance!**
