# 🔐 SQL API Development Guidelines

## Overview

This document establishes **mandatory guidelines** for all future development to ensure consistent use of the SQL API authentication system. These patterns must be followed to maintain security, compliance, and system integrity.

## 🚨 MANDATORY PATTERNS

### 1. **Controller Authentication Pattern**

**ALL controllers MUST follow this exact pattern:**

```typescript
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('your-endpoint')
@UseGuards(JwtAuthGuard)  // 🔐 MANDATORY: All controllers must have this
export class YourController {
  
  @Get()
  findAll(@Request() req) {  // 🔐 MANDATORY: Always include @Request() req
    // Pass user context to service
    return this.service.findAll(req.user.userId, req.user.role);
  }

  @Post()
  @UseGuards(RolesGuard)  // 🔐 MANDATORY: For role-protected endpoints
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  create(@Body() dto: CreateDto, @Request() req) {
    return this.service.create(dto, req.user.userId, req.user.role);
  }
}
```

### 2. **Service User Context Pattern**

**ALL service methods MUST accept user context:**

```typescript
@Injectable()
export class YourService {
  
  async findAll(userId: number, userRole: UserRole, filters?: any) {
    // 🔐 MANDATORY: Apply user-based filtering
    const baseQuery = this.repository.createQueryBuilder('entity');
    
    // Apply role-based access control
    if (userRole === UserRole.EMPLOYEE) {
      baseQuery.where('entity.userId = :userId', { userId });
    }
    
    return baseQuery.getMany();
  }

  async create(dto: CreateDto, userId: number, userRole: UserRole) {
    // 🔐 MANDATORY: Validate user permissions
    if (!this.canUserCreate(userRole)) {
      throw new ForbiddenException('Insufficient permissions');
    }
    
    // 🔐 MANDATORY: Associate with user
    const entity = this.repository.create({
      ...dto,
      createdBy: userId,
    });
    
    return this.repository.save(entity);
  }
}
```

### 3. **Database Query Security Pattern**

**NEVER perform database queries without user context:**

```typescript
// ❌ FORBIDDEN: Direct queries without user context
const users = await this.userRepository.find();
const assessment = await this.assessmentRepository.findOne({ where: { id } });

// ✅ REQUIRED: User-context aware queries
const users = await this.userRepository.find({
  where: this.buildUserAccessFilter(userId, userRole)
});

const assessment = await this.assessmentRepository.findOne({
  where: { 
    id,
    ...this.buildUserAccessFilter(userId, userRole)
  }
});
```

## 🛡️ SECURITY REQUIREMENTS

### Authentication Guards

1. **JwtAuthGuard**: MANDATORY on ALL controllers (except public endpoints)
2. **RolesGuard**: MANDATORY for role-protected endpoints
3. **@Roles()**: MANDATORY to specify required roles
4. **@Request() req**: MANDATORY to access user context

### User Context Validation

```typescript
// 🔐 MANDATORY: Always validate user permissions
private validateUserAccess(userId: number, userRole: UserRole, resourceOwnerId: number): boolean {
  // Users can only access their own data (unless admin/manager)
  if (userRole === UserRole.EMPLOYEE && userId !== resourceOwnerId) {
    return false;
  }
  
  // Managers can access their team's data
  if (userRole === UserRole.MANAGER) {
    return this.isUserInManagerTeam(userId, resourceOwnerId);
  }
  
  // HR Admins can access all data
  return userRole === UserRole.HR_ADMIN;
}
```

## 🔧 DEVELOPMENT TOOLS

### 1. **Use Controller Template**

```bash
# Generate new controller with proper auth patterns
cp backend/templates/controller.template.ts src/your-module/your.controller.ts
# Replace placeholders with your values
```

### 2. **Run Compliance Validation**

```bash
# Before committing any changes
node backend/scripts/validate-auth-compliance.js
```

### 3. **Pre-commit Hook**

```bash
# Automatically runs on every commit
./backend/scripts/pre-commit-auth-check.sh
```

## 📋 COMPLIANCE CHECKLIST

Before creating any new endpoint, verify:

- [ ] Controller has `@UseGuards(JwtAuthGuard)`
- [ ] Role-protected endpoints have `@UseGuards(RolesGuard)` and `@Roles()`
- [ ] All methods have `@Request() req` parameter
- [ ] Service methods accept `userId` and `userRole` parameters
- [ ] Database queries include user-based filtering
- [ ] User permissions are validated before operations
- [ ] Security audit logging is implemented where appropriate
- [ ] No direct database access bypasses authentication

## 🚫 FORBIDDEN PATTERNS

### Never Do These:

```typescript
// ❌ Controller without authentication
@Controller('data')
export class DataController {
  @Get()
  getData() { } // Missing @UseGuards(JwtAuthGuard)
}

// ❌ Service without user context
async findAll() {
  return this.repository.find(); // No user filtering
}

// ❌ Direct database access in controller
@Get()
async getData() {
  return this.repository.find(); // Should go through service
}

// ❌ Hardcoded role checks
if (user.role === 'admin') { } // Use UserRole enum
```

## 🔍 VALIDATION COMMANDS

```bash
# Run before every commit
npm run validate:auth-compliance

# Run security linting
npm run lint:security

# Run authentication tests
npm test -- --testPathPattern="auth.*spec"

# Full security validation
npm run security:validate
```

## 📚 EXAMPLES

### Complete Controller Example

```typescript
@Controller('projects')
@UseGuards(JwtAuthGuard)
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Get()
  findAll(@Request() req, @Query() filters: ProjectFiltersDto) {
    return this.projectsService.findAll(req.user.userId, req.user.role, filters);
  }

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.HR_ADMIN, UserRole.MANAGER)
  create(@Body() createProjectDto: CreateProjectDto, @Request() req) {
    return this.projectsService.create(createProjectDto, req.user.userId, req.user.role);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.projectsService.findOne(+id, req.user.userId, req.user.role);
  }
}
```

## 🎯 ENFORCEMENT

These guidelines are **MANDATORY** and enforced through:

1. **Automated validation scripts**
2. **Pre-commit hooks**
3. **Security-focused ESLint rules**
4. **Code review requirements**
5. **CI/CD pipeline checks**

**Failure to follow these patterns will result in:**
- Build failures
- Security vulnerabilities
- System instability
- Compliance violations

---

**Remember: The SQL API authentication system is the foundation of our security. Never compromise it for convenience.**
