# 🔐 SQL Security Guidelines - <PERSON><PERSON><PERSON><PERSON><PERSON>

## 🚨 CRITICAL: Preventing Rogue SQL Code

This document establishes **MAND<PERSON>ORY** rules to prevent rogue SQL code that bypasses the API authentication layer. **Violation of these rules is a critical security breach.**

## ❌ **ABSOLUTELY FORBIDDEN**

### 1. **Raw SQL Queries**
```typescript
// ❌ FORBIDDEN: Raw SQL queries
await connection.query('SELECT * FROM users');
await connection.execute('DELETE FROM users WHERE id = ?', [userId]);
await repository.query('UPDATE users SET role = "admin"');

// ❌ FORBIDDEN: Template literal SQL
await connection.query(`SELECT * FROM ${tableName}`);
await repository.query(`DELETE FROM users WHERE email = '${email}'`);
```

### 2. **Unfiltered Repository Access**
```typescript
// ❌ FORBIDDEN: Repository access without user context
await this.userRepository.find(); // No user filtering
await this.userRepository.findOne({ where: { id } }); // No permission check
await this.userRepository.delete(id); // No authorization
await this.userRepository.update(id, data); // No user validation
```

### 3. **Generic Database Controllers**
```typescript
// ❌ FORBIDDEN: Generic database access endpoints
@Get('/database/tables/:table')
async getTableData(@Param('table') table: string) {
  return this.databaseService.getTableData(table); // Bypasses all security
}

@Post('/database/query')
async executeQuery(@Body() query: string) {
  return this.connection.query(query); // Direct SQL execution
}
```

### 4. **Query Builders Without User Context**
```typescript
// ❌ FORBIDDEN: Query builder without user filtering
const users = await this.userRepository
  .createQueryBuilder('user')
  .getMany(); // No user-based filtering

// ❌ FORBIDDEN: Joins without permission validation
const data = await this.repository
  .createQueryBuilder('entity')
  .leftJoin('users', 'u', 'entity.userId = u.id')
  .getMany(); // No user context validation
```

## ✅ **REQUIRED PATTERNS**

### 1. **TypeORM with User Context**
```typescript
// ✅ REQUIRED: User-context aware queries
async findUserData(userId: number, userRole: UserRole) {
  const queryBuilder = this.repository.createQueryBuilder('entity');
  
  // 🔐 MANDATORY: Apply user-based filtering
  if (userRole === UserRole.EMPLOYEE) {
    queryBuilder.where('entity.userId = :userId', { userId });
  } else if (userRole === UserRole.MANAGER) {
    queryBuilder.where('entity.managerId = :userId OR entity.userId = :userId', { userId });
  }
  // HR_ADMIN can access all data
  
  return queryBuilder.getMany();
}
```

### 2. **Parameterized Queries Only**
```typescript
// ✅ REQUIRED: Parameterized queries with user context
async findByEmail(email: string, requestingUserId: number, requestingUserRole: UserRole) {
  // 🔐 Validate requesting user can access this data
  if (requestingUserRole === UserRole.EMPLOYEE) {
    // Employees can only look up their own email
    const requestingUser = await this.userRepository.findOne({ 
      where: { id: requestingUserId } 
    });
    if (requestingUser.email !== email) {
      throw new ForbiddenException('Cannot access other users data');
    }
  }
  
  return this.userRepository.findOne({ 
    where: { email },
    select: this.getAllowedFields(requestingUserRole)
  });
}
```

### 3. **Service Layer Validation**
```typescript
// ✅ REQUIRED: All database operations through services with validation
@Injectable()
export class SecureDataService {
  
  async getData(filters: any, userId: number, userRole: UserRole) {
    // 🔐 MANDATORY: Validate user permissions
    this.validateUserAccess(userId, userRole, filters);
    
    // 🔐 MANDATORY: Apply user-based data filtering
    const secureFilters = this.applyUserFilters(filters, userId, userRole);
    
    // 🔐 MANDATORY: Use parameterized queries
    return this.repository.find({
      where: secureFilters,
      select: this.getAllowedFields(userRole)
    });
  }
  
  private validateUserAccess(userId: number, userRole: UserRole, filters: any): void {
    // Implement role-based access validation
    if (userRole === UserRole.EMPLOYEE && filters.userId !== userId) {
      throw new ForbiddenException('Cannot access other users data');
    }
  }
  
  private applyUserFilters(filters: any, userId: number, userRole: UserRole): any {
    const secureFilters = { ...filters };
    
    // 🔐 Force user-based filtering for employees
    if (userRole === UserRole.EMPLOYEE) {
      secureFilters.userId = userId;
    }
    
    return secureFilters;
  }
}
```

## 🛡️ **SECURITY ENFORCEMENT**

### 1. **Automated Detection**
```bash
# Run SQL security validation
npm run validate:auth-compliance

# Check for dangerous SQL patterns
npm run security:lint

# Full security validation
npm run security:full
```

### 2. **Database Access Interceptor**
```typescript
// All database operations are monitored by DatabaseAccessInterceptor
// - Validates user context exists
// - Logs all database access
// - Detects suspicious patterns
// - Prevents unauthorized data access
```

### 3. **ESLint Security Rules**
```javascript
// Automatic detection of:
// - Raw SQL queries
// - Unparameterized queries  
// - Missing user context
// - Dangerous repository patterns
```

## 🔍 **MONITORING & DETECTION**

### Suspicious Patterns Detected:
1. **Raw SQL usage** - `.query()`, `.execute()` with string literals
2. **Unfiltered repository access** - `.find()`, `.findOne()` without user context
3. **Generic database endpoints** - Controllers that accept table names as parameters
4. **Missing user context** - Service methods without `userId` and `userRole` parameters
5. **Large data dumps** - Responses with >1000 records (potential data exfiltration)
6. **Cross-user data access** - Employees accessing other users' data

### Automatic Alerts:
- **Build failures** for dangerous SQL patterns
- **Security audit logs** for all database access
- **Runtime monitoring** via DatabaseAccessInterceptor
- **Pre-commit hooks** prevent dangerous code from entering repository

## 📋 **COMPLIANCE CHECKLIST**

Before any database operation:

- [ ] **User context validated** - `userId` and `userRole` parameters present
- [ ] **Permissions checked** - User authorized for this operation
- [ ] **Data filtered** - Results filtered based on user role and permissions
- [ ] **Parameterized queries** - No string concatenation in queries
- [ ] **Service layer used** - No direct repository access from controllers
- [ ] **Audit logging** - Security-sensitive operations logged
- [ ] **Error handling** - Proper error handling without data leakage

## 🚨 **INCIDENT RESPONSE**

If rogue SQL is detected:

1. **Immediate action**: Block the operation
2. **Security audit**: Log the attempt with full context
3. **Investigation**: Review code and access patterns
4. **Remediation**: Fix the security vulnerability
5. **Prevention**: Update validation rules to prevent recurrence

## 🎯 **EXAMPLES OF SECURE PATTERNS**

### Secure User Data Access:
```typescript
async getUserAssessments(userId: number, requestingUserId: number, requestingUserRole: UserRole) {
  // 🔐 Validate access permissions
  if (requestingUserRole === UserRole.EMPLOYEE && userId !== requestingUserId) {
    throw new ForbiddenException('Cannot access other users assessments');
  }
  
  // 🔐 Apply role-based filtering
  const queryBuilder = this.assessmentRepository
    .createQueryBuilder('assessment')
    .where('assessment.employeeId = :userId', { userId });
    
  // 🔐 Additional filtering for managers
  if (requestingUserRole === UserRole.MANAGER) {
    queryBuilder.andWhere('assessment.managerId = :managerId', { managerId: requestingUserId });
  }
  
  return queryBuilder.getMany();
}
```

---

**Remember: These guidelines are MANDATORY. Any violation compromises the entire security architecture.**
