# EHRX Setup and Deployment Guide

This guide provides step-by-step instructions for setting up, configuring, and deploying the EHRX Employee Performance Management Dashboard application in various environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Development Environment](#local-development-environment)
3. [Database Setup](#database-setup)
4. [Backend Setup](#backend-setup)
5. [Frontend Setup](#frontend-setup)
6. [Containerization with Dock<PERSON>](#containerization-with-docker)
7. [Production Deployment](#production-deployment)
8. [Environment Variables](#environment-variables)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

Before setting up the EHRX application, make sure you have the following tools and services installed:

- Node.js (v16+ recommended)
- npm (v8+ recommended)
- MariaDB (v13+ recommended)
- Git
- Docker and Docker Compose (for containerized deployment)

## Local Development Environment

### Cloning the Repository

```bash
# Clone the repository
git clone https://github.com/your-organization/ehrx.git
cd ehrx
```

### Project Structure

The project is organized with the following structure:

- `/backend`: NestJS backend API server
- `/frontend`: React TypeScript frontend application
- `/database`: Database schema and initialization scripts
- `/docs`: Project documentation and guides

## Database Setup

### Using Local MariaDB

1. Create a MariaDB database for the application:

```bash
psql -U mysql
CREATE DATABASE ehrx;
CREATE USER ehrx_user WITH ENCRYPTED PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE ehrx TO ehrx_user;
\q
```

2. Initialize the database schema:

```bash
cd database
mysql -u ehrx_user -p ehrx < init.sql
```

**Note**: The `init.sql` file now contains the complete, enhanced schema with all security features. No additional migration scripts are needed for fresh installations.

### Using Docker for Database

Alternatively, you can run MariaDB in Docker:

```bash
docker run --name ehrx-mysql \
  -e POSTGRES_DB=ehrx \
  -e POSTGRES_USER=ehrx_user \
  -e POSTGRES_PASSWORD=your_password \
  -p 5432:5432 \
  -d mysql:13
```

## Backend Setup

1. Navigate to the backend directory:

```bash
cd backend
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file with your configuration:

```bash
# Application
PORT=3000
NODE_ENV=development

# Database
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=ehrx_user
DB_PASSWORD=your_password
DB_NAME=ehrx

# JWT Authentication
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=24h
```

4. Run database migrations:

```bash
npm run migration:run
```

5. Start the development server:

```bash
# Development mode with hot-reload
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

### Backend API Documentation

The API documentation is available at `/api-docs` when the server is running. You can also refer to the comprehensive API documentation in `/docs/api/README.md`.

## Frontend Setup

1. Navigate to the frontend directory:

```bash
cd frontend
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file with your configuration:

```bash
REACT_APP_API_URL=http://localhost:3000/api
REACT_APP_ENV=development
```

4. Start the development server:

```bash
npm start
```

The application will be available at `http://localhost:3000`.

5. Build for production:

```bash
npm run build
```

The build artifacts will be stored in the `build/` directory.

## Containerization with Docker

The application can be containerized using Docker for consistent deployment across environments.

### Docker Compose Setup

Create a `docker-compose.yml` file in the project root:

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:13
    environment:
      POSTGRES_DB: ehrx
      POSTGRES_USER: ehrx_user
      POSTGRES_PASSWORD: your_password
    volumes:
      - mysql_data:/var/lib/mysqlql/data
    ports:
      - "5432:5432"
    networks:
      - ehrx_network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      NODE_ENV: production
      PORT: 3000
      DB_HOST: mysql
      DB_PORT: 5432
      DB_USERNAME: ehrx_user
      DB_PASSWORD: your_password
      DB_DATABASE: ehrx
      JWT_SECRET: your_jwt_secret_key
      JWT_EXPIRATION: 24h
    ports:
      - "3000:3000"
    depends_on:
      - mysql
    networks:
      - ehrx_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    environment:
      REACT_APP_API_URL: /api
      REACT_APP_ENV: production
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - ehrx_network

networks:
  ehrx_network:

volumes:
  mysql_data:
```

### Backend Dockerfile

Create a `Dockerfile` in the backend directory:

```dockerfile
FROM node:16-alpine as builder

WORKDIR /usr/src/app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM node:16-alpine

WORKDIR /usr/src/app

COPY package*.json ./
RUN npm ci --production

COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules

EXPOSE 3000

CMD ["node", "dist/main"]
```

### Frontend Dockerfile

Create a `Dockerfile` in the frontend directory:

```dockerfile
FROM node:16-alpine as builder

WORKDIR /usr/src/app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine

COPY --from=builder /usr/src/app/build /usr/share/nginx/html
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

Create an nginx configuration file at `frontend/nginx/default.conf`:

```nginx
server {
  listen 80;
  server_name _;

  root /usr/share/nginx/html;
  index index.html;

  # Frontend routes
  location / {
    try_files $uri $uri/ /index.html =404;
  }

  # API proxy
  location /api {
    proxy_pass http://backend:3000/api;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_cache_bypass $http_upgrade;
  }
}
```

### Building and Running with Docker Compose

```bash
# Build and start all containers
docker-compose up -d

# View logs
docker-compose logs -f

# Stop containers
docker-compose down
```

## Production Deployment

For production deployment, consider the following options:

### Option 1: Kubernetes Deployment

For larger-scale deployments, you can use Kubernetes with Helm charts. Sample manifests are available in the `/deployment/kubernetes` directory.

### Option 2: AWS Deployment

1. Database: Use Amazon RDS for MariaDB
2. Backend: Deploy to Elastic Beanstalk or ECS
3. Frontend: Deploy to S3 + CloudFront or Amplify

### Option 3: Traditional VPS Deployment

1. Set up a virtual server with Nginx
2. Configure SSL/TLS using Let's Encrypt
3. Set up a reverse proxy to the backend service
4. Deploy the frontend as static files

## Environment Variables

### Backend Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| NODE_ENV | Environment (development/production) | development | Yes |
| PORT | Port for the backend server | 3000 | Yes |
| DB_HOST | Database hostname | localhost | Yes |
| DB_PORT | Database port | 5432 | Yes |
| DB_USERNAME | Database username | - | Yes |
| DB_PASSWORD | Database password | - | Yes |
| DB_DATABASE | Database name | ehrx | Yes |
| JWT_SECRET | Secret for JWT token signing | - | Yes |
| JWT_EXPIRATION | JWT token expiration time | 24h | Yes |

### Frontend Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| REACT_APP_API_URL | Backend API URL | http://localhost:3000/api | Yes |
| REACT_APP_ENV | Environment (development/production) | development | Yes |

## Troubleshooting

### Common Issues

#### Database Connection Errors

- Check that MariaDB is running
- Verify database credentials
- Ensure the database has been created and initialized

#### Backend Startup Issues

- Check all environment variables are properly set
- Ensure port 3000 is not in use by another application
- Verify database connection settings

#### Frontend API Connection Errors

- Check that the backend API is running
- Verify the REACT_APP_API_URL is set correctly
- Check for CORS issues if the frontend and backend are on different domains

### Logs and Debugging

- Backend logs: Check the console output or `/logs` directory
- Frontend logs: Check the browser console
- Database logs: Check MariaDB logs

### Getting Support

If you encounter issues not covered in this guide, please:

1. Check the GitHub repository issues
2. Contact the development <NAME_EMAIL>
3. Consult the additional documentation in the `/docs` directory
