#!/bin/bash

# ehrx Server Diagnostics Script
# Run this script to gather comprehensive system information

echo "=== ehrx Server Diagnostics Report ===" 
echo "Generated: $(date)"
echo "Hostname: $(hostname)"
echo "Uptime: $(uptime)"
echo ""

# 1. System Resources
echo "=== SYSTEM RESOURCES ==="
echo "--- Memory Usage ---"
free -h
echo ""
echo "--- CPU Info ---"
lscpu | grep -E "CPU\(s\)|Model name|Thread|Core"
echo ""
echo "--- Load Average ---"
cat /proc/loadavg
echo ""

# 2. Disk Usage
echo "=== DISK USAGE ==="
echo "--- Disk Space ---"
df -h
echo ""
echo "--- Inode Usage ---"
df -i
echo ""
echo "--- /var/log size ---"
du -sh /var/log/*
echo ""

# 3. Process Information
echo "=== PROCESS INFORMATION ==="
echo "--- Top Memory Consumers ---"
ps aux --sort=-%mem | head -10
echo ""
echo "--- Top CPU Consumers ---"
ps aux --sort=-%cpu | head -10
echo ""
echo "--- ehrx Processes ---"
ps aux | grep -i ehrx
echo ""
echo "--- Node.js Processes ---"
ps aux | grep node
echo ""

# 4. SSH Configuration
echo "=== SSH CONFIGURATION ==="
echo "--- SSH Daemon Config ---"
grep -E "ClientAliveInterval|ClientAliveCountMax|MaxSessions|MaxStartups" /etc/ssh/sshd_config
echo ""
echo "--- Active SSH Sessions ---"
who
echo ""

# 5. System Logs Analysis
echo "=== SYSTEM LOGS ANALYSIS ==="
echo "--- Recent OOM Events ---"
dmesg | grep -i "killed process\|out of memory\|oom" | tail -10
echo ""
echo "--- Recent SSH Errors ---"
grep -i "ssh\|sshd" /var/log/auth.log | tail -10
echo ""
echo "--- Kernel Messages ---"
dmesg | tail -20
echo ""
echo "--- System Journal Errors ---"
journalctl --since "1 hour ago" --priority=err --no-pager | tail -10
echo ""

# 6. Network and Limits
echo "=== NETWORK AND LIMITS ==="
echo "--- Network Connections ---"
ss -tuln | grep :22
echo ""
echo "--- System Limits ---"
ulimit -a
echo ""
echo "--- Systemd Limits ---"
systemctl show --property=DefaultLimitNOFILE --property=DefaultLimitNPROC
echo ""

# 7. Swap Information
echo "=== SWAP INFORMATION ==="
swapon --show
echo ""
cat /proc/sys/vm/swappiness
echo ""

# 8. ehrx Application Status
echo "=== EHRX APPLICATION STATUS ==="
if [ -d "/var/www/ehrx" ]; then
    echo "--- ehrx Directory ---"
    ls -la /var/www/ehrx/
    echo ""
    echo "--- Package.json (if exists) ---"
    if [ -f "/var/www/ehrx/package.json" ]; then
        head -20 /var/www/ehrx/package.json
    fi
    echo ""
fi

# 9. Recent System Events
echo "=== RECENT SYSTEM EVENTS ==="
echo "--- Last 10 System Reboots ---"
last reboot | head -10
echo ""
echo "--- Recent Cron Jobs ---"
grep CRON /var/log/syslog | tail -5
echo ""

echo "=== END OF DIAGNOSTICS ==="
echo "Save this output and analyze for:"
echo "1. High memory/CPU usage patterns"
echo "2. OOM killer events"
echo "3. SSH timeout configurations"
echo "4. Disk space issues"
echo "5. Process limits being exceeded"
