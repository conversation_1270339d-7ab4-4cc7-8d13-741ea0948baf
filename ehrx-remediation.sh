#!/bin/bash

# ehrx Server Remediation Script
# Run this AFTER analyzing diagnostics output

set -e  # Exit on any error

echo "=== ehrx Server Remediation Script ==="
echo "Starting remediation at: $(date)"

# Backup original configurations
BACKUP_DIR="/root/ehrx-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
echo "Creating backup directory: $BACKUP_DIR"

# 1. SSH Hardening and Keep-Alive Configuration
echo ""
echo "=== 1. CONFIGURING SSH KEEP-ALIVES ==="
cp /etc/ssh/sshd_config "$BACKUP_DIR/sshd_config.backup"

# Check if settings already exist, if not add them
if ! grep -q "ClientAliveInterval" /etc/ssh/sshd_config; then
    echo "ClientAliveInterval 60" >> /etc/ssh/sshd_config
    echo "Added ClientAliveInterval 60"
fi

if ! grep -q "ClientAliveCountMax" /etc/ssh/sshd_config; then
    echo "ClientAliveCountMax 3" >> /etc/ssh/sshd_config
    echo "Added ClientAliveCountMax 3"
fi

# Increase max sessions if needed
if ! grep -q "MaxSessions" /etc/ssh/sshd_config; then
    echo "MaxSessions 10" >> /etc/ssh/sshd_config
    echo "Added MaxSessions 10"
fi

echo "Testing SSH configuration..."
sshd -t && echo "SSH config is valid" || echo "SSH config has errors!"

# 2. Memory Management and Swap
echo ""
echo "=== 2. CONFIGURING MEMORY MANAGEMENT ==="

# Check if swap exists, if not create it
if [ $(swapon --show | wc -l) -eq 0 ]; then
    echo "No swap detected. Creating 2GB swap file..."
    fallocate -l 2G /swapfile
    chmod 600 /swapfile
    mkswap /swapfile
    swapon /swapfile
    echo "/swapfile none swap sw 0 0" >> /etc/fstab
    echo "Swap file created and enabled"
fi

# Tune swappiness for better performance
echo "vm.swappiness=10" > /etc/sysctl.d/99-ehrx-memory.conf
echo "vm.vfs_cache_pressure=50" >> /etc/sysctl.d/99-ehrx-memory.conf
sysctl -p /etc/sysctl.d/99-ehrx-memory.conf
echo "Memory tuning applied"

# 3. Process Limits and OOM Protection
echo ""
echo "=== 3. CONFIGURING PROCESS LIMITS ==="

# Create systemd service for ehrx if it doesn't exist
if [ ! -f "/etc/systemd/system/ehrx.service" ]; then
    cat > /etc/systemd/system/ehrx.service << 'EOF'
[Unit]
Description=eHRx Application
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/ehrx
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=ehrx

# Resource Limits
MemoryMax=1G
CPUQuota=80%
TasksMax=100

# OOM Protection
OOMScoreAdjust=-500

# Environment
Environment=NODE_ENV=production
Environment=PORT=3000

[Install]
WantedBy=multi-user.target
EOF
    echo "Created ehrx systemd service with resource limits"
fi

# 4. Log Rotation
echo ""
echo "=== 4. CONFIGURING LOG ROTATION ==="
cat > /etc/logrotate.d/ehrx << 'EOF'
/var/log/ehrx/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF

# Ensure log directory exists
mkdir -p /var/log/ehrx
chown www-data:www-data /var/log/ehrx

# 5. System Monitoring Setup
echo ""
echo "=== 5. SETTING UP MONITORING ==="

# Create monitoring script
cat > /usr/local/bin/ehrx-monitor.sh << 'EOF'
#!/bin/bash
# ehrx Health Monitor

LOG_FILE="/var/log/ehrx/monitor.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Check memory usage
MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
if (( $(echo "$MEM_USAGE > 90" | bc -l) )); then
    echo "$TIMESTAMP - WARNING: High memory usage: ${MEM_USAGE}%" >> $LOG_FILE
fi

# Check ehrx process
if ! pgrep -f "ehrx\|node.*server" > /dev/null; then
    echo "$TIMESTAMP - ERROR: ehrx process not running" >> $LOG_FILE
    systemctl restart ehrx
fi

# Check disk space
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 85 ]; then
    echo "$TIMESTAMP - WARNING: High disk usage: ${DISK_USAGE}%" >> $LOG_FILE
fi
EOF

chmod +x /usr/local/bin/ehrx-monitor.sh

# Add to crontab
(crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/ehrx-monitor.sh") | crontab -

echo "Monitoring script installed and scheduled"

# 6. Apply all changes
echo ""
echo "=== 6. APPLYING CHANGES ==="

# Reload systemd
systemctl daemon-reload

# Restart SSH (carefully)
echo "Restarting SSH daemon..."
systemctl restart ssh

# Enable and start ehrx service
systemctl enable ehrx
systemctl restart ehrx

echo ""
echo "=== REMEDIATION COMPLETE ==="
echo "Backup created in: $BACKUP_DIR"
echo ""
echo "Applied changes:"
echo "- SSH keep-alive configuration"
echo "- Swap file creation/tuning"
echo "- Memory management tuning"
echo "- ehrx systemd service with resource limits"
echo "- Log rotation setup"
echo "- Health monitoring script"
echo ""
echo "Next steps:"
echo "1. Monitor /var/log/ehrx/monitor.log for issues"
echo "2. Test SSH connection stability"
echo "3. Monitor system resources during ehrx operation"
echo "4. Check 'systemctl status ehrx' for service health"
