#!/bin/bash

# ehrx Stress Test and Validation Script
# Run this to test system stability after remediation

echo "=== ehrx Stress Test and Validation ==="
echo "Starting test at: $(date)"

# Test duration in seconds (5 minutes)
TEST_DURATION=300
LOG_FILE="/tmp/ehrx-stress-test-$(date +%Y%m%d-%H%M%S).log"

echo "Test duration: ${TEST_DURATION} seconds"
echo "Log file: $LOG_FILE"
echo ""

# Function to log with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to check ehrx health
check_ehrx_health() {
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health 2>/dev/null | grep -q "200"; then
        return 0
    else
        return 1
    fi
}

# Function to monitor resources
monitor_resources() {
    local duration=$1
    local interval=10
    local count=$((duration / interval))
    
    log_message "Starting resource monitoring for ${duration} seconds"
    
    for i in $(seq 1 $count); do
        # Memory usage
        MEM_TOTAL=$(free -m | awk 'NR==2{printf "%.1f", $2}')
        MEM_USED=$(free -m | awk 'NR==2{printf "%.1f", $3}')
        MEM_PERCENT=$(echo "scale=1; $MEM_USED * 100 / $MEM_TOTAL" | bc)
        
        # CPU load
        LOAD_1MIN=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        
        # Disk usage
        DISK_PERCENT=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
        
        # ehrx process check
        EHRX_PROCS=$(pgrep -f "ehrx\|node.*server" | wc -l)
        
        log_message "Resources - Memory: ${MEM_PERCENT}% (${MEM_USED}M/${MEM_TOTAL}M), Load: ${LOAD_1MIN}, Disk: ${DISK_PERCENT}%, ehrx Processes: ${EHRX_PROCS}"
        
        # Check for OOM events
        OOM_COUNT=$(dmesg | grep -c "killed process" || echo "0")
        if [ "$OOM_COUNT" -gt 0 ]; then
            log_message "WARNING: OOM events detected: $OOM_COUNT"
        fi
        
        sleep $interval
    done
}

# Function to stress test ehrx
stress_test_ehrx() {
    log_message "Starting ehrx stress test"
    
    # Check if ehrx is running
    if ! check_ehrx_health; then
        log_message "ERROR: ehrx health check failed before stress test"
        return 1
    fi
    
    # Create multiple concurrent requests
    for i in {1..10}; do
        (
            for j in {1..30}; do
                curl -s http://localhost:3000/health > /dev/null 2>&1
                sleep 1
            done
        ) &
    done
    
    log_message "Stress test requests initiated"
    
    # Monitor during stress test
    monitor_resources $TEST_DURATION
    
    # Wait for background jobs to complete
    wait
    
    log_message "Stress test completed"
}

# Function to test SSH stability
test_ssh_stability() {
    log_message "Testing SSH stability"
    
    # Check SSH configuration
    if sshd -t; then
        log_message "SSH configuration is valid"
    else
        log_message "ERROR: SSH configuration has issues"
        return 1
    fi
    
    # Check SSH service status
    if systemctl is-active --quiet ssh; then
        log_message "SSH service is active"
    else
        log_message "ERROR: SSH service is not active"
        return 1
    fi
    
    # Test keep-alive settings
    ALIVE_INTERVAL=$(grep "ClientAliveInterval" /etc/ssh/sshd_config | awk '{print $2}')
    ALIVE_COUNT=$(grep "ClientAliveCountMax" /etc/ssh/sshd_config | awk '{print $2}')
    
    log_message "SSH Keep-alive settings - Interval: ${ALIVE_INTERVAL:-'not set'}, Count: ${ALIVE_COUNT:-'not set'}"
}

# Function to validate systemd service
validate_systemd_service() {
    log_message "Validating ehrx systemd service"
    
    if systemctl is-active --quiet ehrx; then
        log_message "ehrx systemd service is active"
    else
        log_message "WARNING: ehrx systemd service is not active"
    fi
    
    if systemctl is-enabled --quiet ehrx; then
        log_message "ehrx systemd service is enabled"
    else
        log_message "WARNING: ehrx systemd service is not enabled"
    fi
    
    # Show service status
    systemctl status ehrx --no-pager >> "$LOG_FILE"
}

# Main test execution
main() {
    log_message "=== STARTING COMPREHENSIVE VALIDATION ==="
    
    # 1. Pre-test system check
    log_message "=== PRE-TEST SYSTEM CHECK ==="
    log_message "System uptime: $(uptime)"
    log_message "Available memory: $(free -h | grep Mem | awk '{print $7}')"
    log_message "Disk space: $(df -h / | tail -1 | awk '{print $4}') available"
    
    # 2. Test SSH stability
    test_ssh_stability
    
    # 3. Validate systemd service
    validate_systemd_service
    
    # 4. Run stress test
    log_message "=== STARTING STRESS TEST ==="
    stress_test_ehrx
    
    # 5. Post-test validation
    log_message "=== POST-TEST VALIDATION ==="
    
    if check_ehrx_health; then
        log_message "SUCCESS: ehrx health check passed after stress test"
    else
        log_message "ERROR: ehrx health check failed after stress test"
    fi
    
    # Check for any new errors
    ERROR_COUNT=$(journalctl --since "5 minutes ago" --priority=err --no-pager | wc -l)
    log_message "System errors in last 5 minutes: $ERROR_COUNT"
    
    # Final resource check
    FINAL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $3/$2 * 100.0}')
    FINAL_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    log_message "Final system state - Memory usage: ${FINAL_MEM}%, Load: ${FINAL_LOAD}"
    
    log_message "=== TEST COMPLETE ==="
    log_message "Full log available at: $LOG_FILE"
    
    echo ""
    echo "Test Summary:"
    echo "- Duration: ${TEST_DURATION} seconds"
    echo "- Log file: $LOG_FILE"
    echo "- Check the log for detailed results"
    echo ""
    echo "Key metrics to review:"
    echo "1. Memory usage peaks"
    echo "2. Any OOM events"
    echo "3. SSH service stability"
    echo "4. ehrx service health throughout test"
}

# Run the main test
main
