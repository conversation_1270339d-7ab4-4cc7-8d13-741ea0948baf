#!/usr/bin/env python3
"""
Final cleanup for PostgreSQL conversion
"""

import re

def final_cleanup(input_file, output_file):
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix UNIQUE KEY syntax
    content = re.sub(r'UNIQUE KEY\s+"[^"]+"\s*\("([^"]+)"\)', r'UNIQUE ("\1")', content)
    content = re.sub(r'UNIQUE KEY\s+"[^"]+"\s*\("([^"]+)","([^"]+)"\)', r'UNIQUE ("\1", "\2")', content)
    
    # Fix malformed syntax with extra parentheses and commas
    content = re.sub(r'PRIMARY KEY \("id"\)\),', 'PRIMARY KEY ("id"),', content)
    content = re.sub(r'UNIQUE KEY "[^"]+"\s*\("([^"]+)"\),"([^"]+)"\),"([^"]+)"\),', r'UNIQUE ("\1"),', content)
    
    # Fix INSERT statements with boolean conversion issues
    content = re.sub(r'INSERT INTO "users" VALUES \(TRUE,', 'INSERT INTO "users" VALUES (1,', content)
    content = re.sub(r'INSERT INTO "users" VALUES \(FALSE,', 'INSERT INTO "users" VALUES (2,', content)
    
    # Remove any remaining MySQL-specific syntax
    content = re.sub(r'UN\s*$', '', content, flags=re.MULTILINE)
    
    # Fix any remaining ENGINE specifications
    content = re.sub(r'\s*ENGINE=\w+[^;]*;', ';', content)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Final cleanup completed: {input_file} -> {output_file}")

if __name__ == "__main__":
    final_cleanup('fixed_postgresql_dump.sql', 'final_postgresql_dump.sql')
