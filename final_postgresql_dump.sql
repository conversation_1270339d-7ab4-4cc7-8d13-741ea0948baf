-- Converted from MySQL to PostgreSQL
-- Note: Manual review and testing recommended


-- ENUM Type Definitions
CREATE TYPE status_type AS ENUM ('planning','active','on_hold','completed','cancelled');
CREATE TYPE priority_type AS ENUM ('low','medium','high','critical');
CREATE TYPE insight_type_type AS ENUM ('attrition_risk','engagement_trend','skill_gap','performance_anomaly');
CREATE TYPE target_type_type AS ENUM ('individual','team','department','organization');
CREATE TYPE scoring_method_type AS ENUM ('numeric','percentage','scale_1_5','scale_1_10','boolean');
CREATE TYPE inheritance_type_type AS ENUM ('hr_mandated','organizational_inherited','team_specific');
CREATE TYPE level_type_type AS ENUM ('hr_level','organizational_level','team_level');
CREATE TYPE template_level_type AS ENUM ('hr_level','organizational_level','team_level');
CREATE TYPE risk_level_type AS ENUM ('low','medium','high','critical');
CREATE TYPE survey_type_type AS ENUM ('pulse','annual','onboarding','exit','custom');
CREATE TYPE feedback_type_type AS ENUM ('emoji','quick_poll','check_in','thumbs_up');
CREATE TYPE context_type_type AS ENUM ('task','meeting','project','general');
CREATE TYPE traffic_light_status_type AS ENUM ('green','yellow','red','na');
CREATE TYPE calculation_method_type AS ENUM ('manual','auto_count','auto_percentage','auto_fte');
CREATE TYPE type_type AS ENUM ('organization','division','department','team','squad','unit');
CREATE TYPE metric_type_type AS ENUM ('individual','team','department','organization');
CREATE TYPE badge_type_type AS ENUM ('achievement','appreciation','milestone','skill');
CREATE TYPE rule_type_type AS ENUM ('addition','subtraction','multiplication','division','conditional');
CREATE TYPE condition_operator_type AS ENUM ('equals','not_equals','greater_than','less_than','greater_or_equal','less_or_equal');
CREATE TYPE severity_type AS ENUM ('LOW','MEDIUM','HIGH','CRITICAL');
CREATE TYPE category_type AS ENUM ('programming','infrastructure','database','cloud','security','devops','networking','frontend','backend','mobile','data','ai_ml','project_management','soft_skills');
CREATE TYPE level_required_type AS ENUM ('beginner','intermediate','advanced','expert');
CREATE TYPE role_type AS ENUM ('ceo','vp','director','manager','senior_engineer','engineer','junior_engineer','intern','hr_admin','guest','employee');
CREATE TYPE proficiency_level_type AS ENUM ('beginner','intermediate','advanced','expert');
CREATE TYPE employment_type_type AS ENUM ('full_time','part_time','contract','intern');
CREATE TYPE account_status_type AS ENUM ('active','inactive','locked','pending_activation','suspended');

-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: ehrx
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.1












--
-- Table structure for table "action_items"
--

DROP TABLE IF EXISTS "action_items";


CREATE TABLE "action_items" (
  "id" SERIAL,
  "title" varchar(255) NOT NULL,
  "description" TEXT,
  "status" status_type NOT NULL DEFAULT 'not_started',
  "priority" priority_type NOT NULL DEFAULT 'medium',
  "due_date" TIMESTAMP DEFAULT NULL,
  "assigned_to" INTEGER DEFAULT NULL,
  "created_by" INTEGER NOT NULL,
  "assessment_id" INTEGER DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "completed_at" TIMESTAMP DEFAULT NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_0ad4b6947ef1f33995c3927ff20" FOREIGN KEY ("assigned_to") REFERENCES "users" ("id"),
  CONSTRAINT "FK_e6e5ff1019c2316cef30812d1b3" FOREIGN KEY ("created_by") REFERENCES "users" ("id")
);


--
-- Dumping data for table "action_items"
--





--
-- Table structure for table "ai_insights"
--

DROP TABLE IF EXISTS "ai_insights";


CREATE TABLE "ai_insights" (
  "id" SERIAL,
  "insight_type" insight_type_type NOT NULL,
  "target_type" target_type_type NOT NULL,
  "target_id" INTEGER DEFAULT NULL,
  "insight_data" json NOT NULL,
  "confidence_score" decimal(5,4) DEFAULT NULL,
  "priority" priority_type NOT NULL,
  "status" status_type NOT NULL DEFAULT 'new',
  "expires_at" timestamp NULL DEFAULT NULL,
  "generated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id")
);


--
-- Dumping data for table "ai_insights"
--



INSERT INTO "ai_insights" VALUES (TRUE,'engagement_trend','organization',TRUE,'{\"details\": {\"trend\": \"positive\", \"key_drivers\": [\"recognition_program\", \"flexible_work\"]}, \"message\": \"Organization engagement trending upward (+12% vs last quarter)\"}',0.9200,'low','acknowledged','2024-03-31 23:59:59','2025-07-17 06:25:41.369354'),(2,'skill_gap','organization',TRUE,'{\"details\": {\"skill\": \"cloud_security\", \"gap_percentage\": 65}, \"message\": \"Critical skill gap identified in Cloud Security\"}',0.7800,'high','new','2024-06-30 23:59:59','2025-07-17 06:25:41.369354'),(3,'performance_anomaly','individual',2,'{\"details\": {\"trend\": \"declining\", \"confidence\": 0.75, \"suggested_action\": \"manager_check_in\"}, \"message\": \"Performance decline detected over last 2 months\"}',0.7500,'medium','new','2024-04-01 23:59:59','2025-07-17 06:25:41.369354');


--
-- Table structure for table "analytics_dashboards"
--

DROP TABLE IF EXISTS "analytics_dashboards";


CREATE TABLE "analytics_dashboards" (
  "id" SERIAL,
  "user_id" INTEGER NOT NULL,
  "name" varchar(255) NOT NULL,
  "description" TEXT,
  "layout_config" json DEFAULT NULL,
  "widget_config" json DEFAULT NULL,
  "is_default" SMALLINT NOT NULL DEFAULT '0',
  "is_shared" SMALLINT NOT NULL DEFAULT '0',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_336ebcca9f034ac4dfd3cbb43bd" FOREIGN KEY ("user_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "analytics_dashboards"
--



INSERT INTO "analytics_dashboards" VALUES (16,TRUE,'HR Executive Dashboard','Comprehensive HR analytics for executive leadership','{\"rows\": 4, \"layout\": \"grid\", \"columns\": 3}','{\"widgets\": [{\"type\": \"performance_overview\", \"position\": {\"h\": TRUE, \"w\": 2, \"x\": FALSE, \"y\": 0}}, {\"type\": \"attrition_risk\", \"position\": {\"h\": TRUE, \"w\": TRUE, \"x\": 2, \"y\": 0}}]}',TRUE,TRUE,'2025-07-17 06:25:40.847761','2025-07-17 06:25:40.938979'),(17,2,'Manager Dashboard','Team performance and engagement tracking','{\"rows\": 3, \"layout\": \"grid\", \"columns\": 2}','{\"widgets\": [{\"type\": \"team_performance\", \"position\": {\"h\": TRUE, \"w\": TRUE, \"x\": FALSE, \"y\": 0}}, {\"type\": \"recognition_feed\", \"position\": {\"h\": 2, \"w\": TRUE, \"x\": TRUE, \"y\": 0}}]}',TRUE,FALSE,'2025-07-17 06:25:40.847761','2025-07-17 06:25:40.938979');


--
-- Table structure for table "assessment_areas"
--

DROP TABLE IF EXISTS "assessment_areas";


CREATE TABLE "assessment_areas" (
  "id" SERIAL,
  "template_id" INTEGER NOT NULL,
  "name" varchar(255) NOT NULL,
  "weight" decimal(5,2) NOT NULL DEFAULT '1.00',
  "max_score" INTEGER NOT NULL,
  "order_index" INTEGER NOT NULL,
  "description" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_ad6c8f4a9447dc8a1d6b3f839f8" FOREIGN KEY ("template_id") REFERENCES "assessment_templates" ("id")
);


--
-- Dumping data for table "assessment_areas"
--





--
-- Table structure for table "assessment_criteria"
--

DROP TABLE IF EXISTS "assessment_criteria";


CREATE TABLE "assessment_criteria" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "criteria_level_id" INTEGER NOT NULL,
  "organizational_unit_id" INTEGER DEFAULT NULL,
  "created_by_id" INTEGER NOT NULL,
  "weight" decimal(5,2) NOT NULL DEFAULT '1.00',
  "max_score" INTEGER NOT NULL DEFAULT '100',
  "scoring_method" scoring_method_type NOT NULL DEFAULT 'percentage',
  "is_mandatory" SMALLINT NOT NULL DEFAULT '1',
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "effective_from" date NOT NULL,
  "effective_until" date DEFAULT NULL,
  "order_index" INTEGER NOT NULL DEFAULT '1',
  "description" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_3af9a9515d2c9e6a8d5b14bdc48" FOREIGN KEY ("criteria_level_id") REFERENCES "assessment_criteria_levels" ("id"),
  CONSTRAINT "FK_b4d81ab08e22ee35f2b86c63353" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id"),
  CONSTRAINT "FK_f30a16f490735b9f94bceac28cf" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "assessment_criteria"
--





--
-- Table structure for table "assessment_criteria_inheritance"
--

DROP TABLE IF EXISTS "assessment_criteria_inheritance";


CREATE TABLE "assessment_criteria_inheritance" (
  "id" SERIAL,
  "criteria_id" INTEGER NOT NULL,
  "target_organizational_unit_id" INTEGER NOT NULL,
  "inherited_from_unit_id" INTEGER DEFAULT NULL,
  "inheritance_type" inheritance_type_type NOT NULL,
  "is_overridable" SMALLINT NOT NULL DEFAULT '0',
  "local_weight_override" decimal(5,2) DEFAULT NULL,
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_1708e882d7c2e60431c0b41f1bd" FOREIGN KEY ("criteria_id") REFERENCES "assessment_criteria" ("id"),
  CONSTRAINT "FK_6a2be2e2b8e5ab74b4d69670971" FOREIGN KEY ("target_organizational_unit_id") REFERENCES "organizational_units" ("id"),
  CONSTRAINT "FK_80090e5253edc46ddfabd90660f" FOREIGN KEY ("inherited_from_unit_id") REFERENCES "organizational_units" ("id")
);


--
-- Dumping data for table "assessment_criteria_inheritance"
--





--
-- Table structure for table "assessment_criteria_levels"
--

DROP TABLE IF EXISTS "assessment_criteria_levels";


CREATE TABLE "assessment_criteria_levels" (
  "id" SERIAL,
  "level_type" level_type_type NOT NULL,
  "name" varchar(255) NOT NULL,
  "priority_order" INTEGER NOT NULL DEFAULT '1',
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "description" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id")
);


--
-- Dumping data for table "assessment_criteria_levels"
--





--
-- Table structure for table "assessment_criteria_responses"
--

DROP TABLE IF EXISTS "assessment_criteria_responses";


CREATE TABLE "assessment_criteria_responses" (
  "id" SERIAL,
  "assessment_id" INTEGER NOT NULL,
  "criteria_id" INTEGER NOT NULL,
  "base_performance_score" decimal(5,2) NOT NULL DEFAULT '0.00',
  "scoring_scale_level_id" INTEGER NOT NULL,
  "base_points" INTEGER NOT NULL,
  "manager_adjustment_points" INTEGER NOT NULL DEFAULT '0',
  "final_score" decimal(5,2) NOT NULL DEFAULT '0.00',
  "max_possible_score" decimal(5,2) NOT NULL,
  "weight_applied" decimal(5,2) NOT NULL,
  "weighted_score" decimal(5,2) NOT NULL DEFAULT '0.00',
  "evaluator_comments" varchar(255) DEFAULT NULL,
  "manager_adjustment_reason" varchar(255) DEFAULT NULL,
  "employee_comments" varchar(255) DEFAULT NULL,
  "evidence_links" varchar(255) DEFAULT NULL,
  "improvement_suggestions" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_0221a61f6fff6affc223ec73398" FOREIGN KEY ("assessment_id") REFERENCES "assessment_instances" ("id"),
  CONSTRAINT "FK_0c50d69618f88a7de1ae82a40c5" FOREIGN KEY ("criteria_id") REFERENCES "assessment_criteria" ("id"),
  CONSTRAINT "FK_1fa29bb76cd09896048ec557e3c" FOREIGN KEY ("scoring_scale_level_id") REFERENCES "assessment_scoring_scale_levels" ("id")
);


--
-- Dumping data for table "assessment_criteria_responses"
--





--
-- Table structure for table "assessment_criteria_scoring"
--

DROP TABLE IF EXISTS "assessment_criteria_scoring";


CREATE TABLE "assessment_criteria_scoring" (
  "id" SERIAL,
  "criteria_id" INTEGER NOT NULL,
  "scoring_scale_id" INTEGER NOT NULL,
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_77f601fe95c2f4a5ec5224d6626" FOREIGN KEY ("scoring_scale_id") REFERENCES "assessment_scoring_scales" ("id"),
  CONSTRAINT "FK_ef2cffb47c6e121e45b79b7508d" FOREIGN KEY ("criteria_id") REFERENCES "assessment_criteria" ("id")
);


--
-- Dumping data for table "assessment_criteria_scoring"
--





--
-- Table structure for table "assessment_instances"
--

DROP TABLE IF EXISTS "assessment_instances";


CREATE TABLE "assessment_instances" (
  "id" SERIAL,
  "template_id" INTEGER NOT NULL,
  "employee_id" INTEGER NOT NULL,
  "evaluator_id" INTEGER NOT NULL,
  "status" status_type NOT NULL DEFAULT 'draft',
  "assessment_date" date NOT NULL,
  "total_score" decimal(10,2) NOT NULL DEFAULT '0.00',
  "template_snapshot" json DEFAULT NULL,
  "score_percentage" decimal(5,2) DEFAULT NULL,
  "approved_at" timestamp NULL DEFAULT NULL,
  "approved_by_id" INTEGER DEFAULT NULL,
  "rejected_at" timestamp NULL DEFAULT NULL,
  "rejected_by_id" INTEGER DEFAULT NULL,
  "rejection_reason" TEXT,
  "workflow_history" json DEFAULT NULL,
  "notes" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_0f836879b4044c5245f0d848b34" FOREIGN KEY ("template_id") REFERENCES "assessment_templates" ("id"),
  CONSTRAINT "FK_5c772c3fefe9e961db5a910f684" FOREIGN KEY ("employee_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_a06ccb18b739065608cda4718c2" FOREIGN KEY ("rejected_by_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_d3960bdb9e65e3ca6cf48d27437" FOREIGN KEY ("approved_by_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_eae9959db9211a447afe80fd867" FOREIGN KEY ("evaluator_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "assessment_instances"
--





--
-- Table structure for table "assessment_responses"
--

DROP TABLE IF EXISTS "assessment_responses";


CREATE TABLE "assessment_responses" (
  "id" SERIAL,
  "assessment_id" INTEGER NOT NULL,
  "area_id" INTEGER NOT NULL,
  "score" decimal(10,2) NOT NULL,
  "area_weight" decimal(5,2) NOT NULL DEFAULT '1.00',
  "weighted_score" decimal(10,2) NOT NULL,
  "area_snapshot" json DEFAULT NULL,
  "base_score" decimal(10,2) DEFAULT NULL,
  "score_adjustments" json DEFAULT NULL,
  "additional_data" json DEFAULT NULL,
  "evaluator_comments" varchar(255) DEFAULT NULL,
  "employee_comments" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_ba3085accdc1854db0a3ccfaaff" FOREIGN KEY ("area_id") REFERENCES "assessment_areas" ("id"),
  CONSTRAINT "FK_d0e41ce11aca4480e7c7e3c685f" FOREIGN KEY ("assessment_id") REFERENCES "assessment_instances" ("id")
);


--
-- Dumping data for table "assessment_responses"
--





--
-- Table structure for table "assessment_scoring_scale_levels"
--

DROP TABLE IF EXISTS "assessment_scoring_scale_levels";


CREATE TABLE "assessment_scoring_scale_levels" (
  "id" SERIAL,
  "scoring_scale_id" INTEGER NOT NULL,
  "min_performance_score" decimal(5,2) NOT NULL,
  "max_performance_score" decimal(5,2) NOT NULL,
  "points_awarded" INTEGER NOT NULL,
  "order_index" INTEGER NOT NULL DEFAULT '1',
  "level_name" varchar(255) NOT NULL,
  "level_code" varchar(255) NOT NULL,
  "description" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_8ef6222e37d4c60e4f15d7c0984" FOREIGN KEY ("scoring_scale_id") REFERENCES "assessment_scoring_scales" ("id")
);


--
-- Dumping data for table "assessment_scoring_scale_levels"
--





--
-- Table structure for table "assessment_scoring_scales"
--

DROP TABLE IF EXISTS "assessment_scoring_scales";


CREATE TABLE "assessment_scoring_scales" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "organizational_unit_id" INTEGER DEFAULT NULL,
  "created_by_id" INTEGER NOT NULL,
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "description" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_6b4a1f9b8b4d53875d87719f149" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_dd562fb00e87f8150d8918ddc69" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id")
);


--
-- Dumping data for table "assessment_scoring_scales"
--





--
-- Table structure for table "assessment_template_criteria"
--

DROP TABLE IF EXISTS "assessment_template_criteria";


CREATE TABLE "assessment_template_criteria" (
  "id" SERIAL,
  "template_id" INTEGER NOT NULL,
  "criteria_id" INTEGER NOT NULL,
  "is_required" SMALLINT NOT NULL DEFAULT '1',
  "order_index" INTEGER NOT NULL DEFAULT '1',
  "weight_override" decimal(5,2) DEFAULT NULL,
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_ad4c10420dab90d60ff6f14deff" FOREIGN KEY ("criteria_id") REFERENCES "assessment_criteria" ("id"),
  CONSTRAINT "FK_b7b6e04739d7b26fb58e53a11f6" FOREIGN KEY ("template_id") REFERENCES "assessment_templates" ("id")
);


--
-- Dumping data for table "assessment_template_criteria"
--





--
-- Table structure for table "assessment_templates"
--

DROP TABLE IF EXISTS "assessment_templates";


CREATE TABLE "assessment_templates" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "created_by_id" INTEGER NOT NULL,
  "organizational_unit_id" INTEGER DEFAULT NULL,
  "template_level" template_level_type NOT NULL DEFAULT 'team_level',
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "parent_template_id" INTEGER DEFAULT NULL,
  "is_global" SMALLINT NOT NULL DEFAULT '0',
  "description" varchar(255) DEFAULT NULL,
  "version" varchar(255) NOT NULL DEFAULT '1.0',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_32ae516a7a79e33590bf72b851d" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id"),
  CONSTRAINT "FK_cd5644690bf9f69f76de0b499f7" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_fd7c52a47e343db344f5e274b0b" FOREIGN KEY ("parent_template_id") REFERENCES "assessment_templates" ("id")
);


--
-- Dumping data for table "assessment_templates"
--





--
-- Table structure for table "attrition_predictions"
--

DROP TABLE IF EXISTS "attrition_predictions";


CREATE TABLE "attrition_predictions" (
  "id" SERIAL,
  "user_id" INTEGER NOT NULL,
  "risk_score" decimal(5,4) NOT NULL,
  "risk_level" risk_level_type NOT NULL,
  "contributing_factors" json DEFAULT NULL,
  "prediction_date" date NOT NULL,
  "model_version" varchar(50) DEFAULT NULL,
  "confidence_score" decimal(5,4) DEFAULT NULL,
  "recommended_actions" json DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_68429e6e7b542c4db1b391f6fdb" FOREIGN KEY ("user_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "attrition_predictions"
--



INSERT INTO "attrition_predictions" VALUES (TRUE,TRUE,0.2500,'low','{\"factors\": [\"high_performance\", \"recent_recognition\", \"good_engagement\"], \"weights\": [0.4, 0.3, 0.3]}','2024-03-01','v1.2',0.8500,'{\"actions\": [\"continue_current_engagement\", \"consider_stretch_projects\"]}','2025-07-17 06:25:40.331530'),(2,2,0.6500,'medium','{\"factors\": [\"average_performance\", \"limited_growth_opportunities\", \"workload_concerns\"], \"weights\": [0.3, 0.4, 0.3]}','2024-03-01','v1.2',0.7800,'{\"actions\": [\"career_development_discussion\", \"workload_review\", \"skill_development_opportunities\"]}','2025-07-17 06:25:40.331530');


--
-- Table structure for table "career_paths"
--

DROP TABLE IF EXISTS "career_paths";


CREATE TABLE "career_paths" (
  "id" SERIAL,
  "from_role" varchar(255) NOT NULL,
  "to_role" varchar(255) NOT NULL,
  "organizational_unit_id" INTEGER DEFAULT NULL,
  "required_skills" json DEFAULT NULL,
  "recommended_experience_years" INTEGER DEFAULT NULL,
  "typical_timeline_months" INTEGER DEFAULT NULL,
  "success_rate" decimal(5,2) DEFAULT NULL,
  "created_by_id" INTEGER NOT NULL,
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_1385834c4d94cc02ae8528541fa" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id"),
  CONSTRAINT "FK_ac2b5cc3a097a845d7bb4e7d3c0" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "career_paths"
--



INSERT INTO "career_paths" VALUES (TRUE,'Junior Developer','Senior Developer',NULL,'{\"technical\": [\"advanced_programming\", \"system_design\"], \"soft_skills\": [\"mentoring\", \"leadership\"]}',3,24,85.50,TRUE,TRUE,'2025-07-17 06:25:41.142745'),(2,'Senior Developer','Tech Lead',NULL,'{\"technical\": [\"architecture\", \"team_leadership\"], \"soft_skills\": [\"project_management\", \"communication\"]}',5,18,75.20,TRUE,TRUE,'2025-07-17 06:25:41.142745');


--
-- Table structure for table "competency_frameworks"
--

DROP TABLE IF EXISTS "competency_frameworks";


CREATE TABLE "competency_frameworks" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "description" TEXT,
  "organizational_unit_id" INTEGER DEFAULT NULL,
  "competencies" json NOT NULL,
  "version" varchar(50) NOT NULL DEFAULT '1.0',
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "created_by_id" INTEGER NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_448010ea429c7d07109a44d1a3e" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_ca7d920bc099b7835c831388dae" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id")
);


--
-- Dumping data for table "competency_frameworks"
--



INSERT INTO "competency_frameworks" VALUES (TRUE,'Software Engineering Competencies','Core competencies for software engineering roles',NULL,'{\"technical\": [\"programming\", \"system_design\", \"testing\"], \"soft_skills\": [\"communication\", \"teamwork\", \"problem_solving\"]}','1.0',TRUE,TRUE,'2025-07-17 06:25:40.682979','2025-07-17 06:25:40.799957'),(2,'Leadership Competencies','Leadership and management competencies',NULL,'{\"leadership\": [\"team_management\", \"strategic_thinking\", \"decision_making\"], \"communication\": [\"presentation\", \"negotiation\", \"coaching\"]}','1.0',TRUE,TRUE,'2025-07-17 06:25:40.682979','2025-07-17 06:25:40.799957');


--
-- Table structure for table "engagement_surveys"
--

DROP TABLE IF EXISTS "engagement_surveys";


CREATE TABLE "engagement_surveys" (
  "id" SERIAL,
  "title" varchar(255) NOT NULL,
  "description" TEXT,
  "survey_type" survey_type_type NOT NULL,
  "questions" json NOT NULL,
  "target_audience" json DEFAULT NULL,
  "start_date" date DEFAULT NULL,
  "end_date" date DEFAULT NULL,
  "is_anonymous" SMALLINT NOT NULL DEFAULT '1',
  "created_by_id" INTEGER NOT NULL,
  "status" status_type NOT NULL DEFAULT 'draft',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_b07dab99280aacc4f0950a8882f" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "engagement_surveys"
--



INSERT INTO "engagement_surveys" VALUES (TRUE,'Q1 2024 Pulse Survey','Quarterly engagement and satisfaction survey','pulse','{\"questions\": [{\"id\": TRUE, \"type\": \"rating\", \"scale\": 10, \"question\": \"How satisfied are you with your current role?\"}, {\"id\": 2, \"type\": \"rating\", \"scale\": 10, \"question\": \"How likely are you to recommend this company as a great place to work?\"}]}','{\"roles\": [\"all\"], \"departments\": [\"all\"]}','2024-01-15','2024-01-29',TRUE,TRUE,'completed','2025-07-17 06:25:41.483382','2025-07-17 06:25:41.596542'),(2,'Annual Culture Survey 2024','Comprehensive annual culture and engagement assessment','annual','{\"questions\": [{\"id\": TRUE, \"type\": \"rating\", \"scale\": 5, \"question\": \"I feel valued for my contributions\"}, {\"id\": 2, \"type\": \"rating\", \"scale\": 5, \"question\": \"My manager provides clear direction\"}]}','{\"roles\": [\"all\"], \"departments\": [\"all\"]}','2024-02-01','2024-02-15',TRUE,TRUE,'completed','2025-07-17 06:25:41.483382','2025-07-17 06:25:41.596542');


--
-- Table structure for table "micro_feedback"
--

DROP TABLE IF EXISTS "micro_feedback";


CREATE TABLE "micro_feedback" (
  "id" SERIAL,
  "giver_id" INTEGER NOT NULL,
  "receiver_id" INTEGER DEFAULT NULL,
  "feedback_type" feedback_type_type NOT NULL,
  "feedback_value" varchar(255) DEFAULT NULL,
  "context_type" context_type_type DEFAULT NULL,
  "context_id" INTEGER DEFAULT NULL,
  "given_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_b23fc9ef7f3cb4af8ffae58cb88" FOREIGN KEY ("giver_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_ea17e4e2c43008d60f856fa090e" FOREIGN KEY ("receiver_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "micro_feedback"
--



INSERT INTO "micro_feedback" VALUES (TRUE,2,TRUE,'thumbs_up','great_job','task',1001,'2025-07-17 06:25:40.251569'),(2,TRUE,2,'emoji','🚀','project',2001,'2025-07-17 06:25:40.251569'),(3,TRUE,2,'quick_poll','helpful','meeting',3001,'2025-07-17 06:25:40.251569'),(4,2,TRUE,'check_in','positive','general',NULL,'2025-07-17 06:25:40.251569');


--
-- Table structure for table "monthly_dashboard_kpi_values"
--

DROP TABLE IF EXISTS "monthly_dashboard_kpi_values";


CREATE TABLE "monthly_dashboard_kpi_values" (
  "id" SERIAL,
  "submission_id" INTEGER NOT NULL,
  "kpi_id" INTEGER NOT NULL,
  "value" decimal(12,4) DEFAULT NULL,
  "target_value" decimal(12,4) DEFAULT NULL,
  "notes" TEXT,
  "traffic_light_status" traffic_light_status_type NOT NULL DEFAULT 'na',
  "traffic_light_override" SMALLINT NOT NULL DEFAULT '0',
  "additional_data" json DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_1e3e57c7a69d02e26b6863acfb7" FOREIGN KEY ("submission_id") REFERENCES "monthly_dashboard_submissions" ("id"),
  CONSTRAINT "FK_7bf6bd8c6990e749aed8b3f1a67" FOREIGN KEY ("kpi_id") REFERENCES "monthly_dashboard_kpis" ("id")
);


--
-- Dumping data for table "monthly_dashboard_kpi_values"
--



INSERT INTO "monthly_dashboard_kpi_values" VALUES (TRUE,TRUE,3,1.0000,0.0000,'1 resignation this month - ATTRITION','yellow',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(2,TRUE,4,98.5000,100.0000,'Good SLA performance','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(3,TRUE,5,87.0000,85.0000,'Above target utilization','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(4,TRUE,6,95.0000,100.0000,'Good time registration compliance','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(5,TRUE,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(6,TRUE,8,-2500.0000,0.0000,'Under budget by 2.5K','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(7,TRUE,9,8.0000,25.0000,'8 PTO days taken this month','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(8,TRUE,10,82.0000,80.0000,'Good office attendance','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(9,2,3,0.0000,0.0000,'No resignations','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(10,2,4,92.0000,100.0000,'SLA slightly below target','yellow',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(11,2,5,89.0000,87.0000,'Good utilization','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(12,2,6,88.0000,100.0000,'Time registration needs improvement','red',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(13,2,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(14,2,8,1200.0000,0.0000,'Over budget by 1.2K','yellow',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(15,2,9,12.0000,25.0000,'12 PTO days taken this month','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(16,2,10,78.0000,80.0000,'Slightly below office attendance target','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(17,3,3,0.0000,0.0000,'No resignations','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(18,3,4,94.0000,100.0000,'Good SLA performance','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(19,3,5,77.0000,75.0000,'Above target utilization','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(20,3,6,92.0000,100.0000,'Time registration could be better','yellow',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(21,3,7,1.0000,0.0000,'1 minor compliance issue identified','red',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(22,3,8,-800.0000,0.0000,'Under budget by 800','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(23,3,9,15.0000,25.0000,'15 PTO days taken this month','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(24,3,10,92.0000,90.0000,'Excellent office attendance','green',FALSE,NULL,'2025-07-17 06:25:43.135855','2025-07-17 06:25:43.204074'),(25,4,TRUE,17.0000,17.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:05.922805','2025-07-19 05:19:05.922805'),(26,4,2,8.5076,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:05.945837','2025-07-19 05:19:05.945837'),(27,4,3,7.1103,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 7.110293343612502, \"resignations_this_month\": 2}','2025-07-19 05:19:05.960444','2025-07-19 05:19:05.960444'),(28,4,4,88.4748,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:05.975491','2025-07-19 05:19:05.975491'),(29,4,5,76.5764,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:06.017350','2025-07-19 05:19:06.017350'),(30,4,6,99.8392,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.030117','2025-07-19 05:19:06.030117'),(31,4,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.043324','2025-07-19 05:19:06.043324'),(32,4,8,-47003.2729,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.053945','2025-07-19 05:19:06.053945'),(33,4,9,77.2409,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 77.24088805207462, \"days_taken_this_month\": 1}','2025-07-19 05:19:06.069181','2025-07-19 05:19:06.069181'),(34,4,10,87.7844,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 87.78439084493307, \"leave_days_adjustment\": 2, \"raw_attendance_percentage\": 82.78439084493307}','2025-07-19 05:19:06.084303','2025-07-19 05:19:06.084303'),(35,5,TRUE,9.0000,9.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.116392','2025-07-19 05:19:06.116392'),(36,5,2,71.3937,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.129037','2025-07-19 05:19:06.129037'),(37,5,3,9.9859,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 9.985884421041462, \"resignations_this_month\": 1}','2025-07-19 05:19:06.140722','2025-07-19 05:19:06.140722'),(38,5,4,95.5501,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.153654','2025-07-19 05:19:06.153654'),(39,5,5,94.0489,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.163772','2025-07-19 05:19:06.163772'),(40,5,6,91.7670,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:06.198023','2025-07-19 05:19:06.198023'),(41,5,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.211435','2025-07-19 05:19:06.211435'),(42,5,8,-98253.3164,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.221558','2025-07-19 05:19:06.221558'),(43,5,9,73.8540,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 73.85399523416116, \"days_taken_this_month\": 3}','2025-07-19 05:19:06.238067','2025-07-19 05:19:06.238067'),(44,5,10,72.2834,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 72.28342239384055, \"leave_days_adjustment\": 4, \"raw_attendance_percentage\": 67.28342239384055}','2025-07-19 05:19:06.247125','2025-07-19 05:19:06.247125'),(45,6,TRUE,19.0000,19.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.279921','2025-07-19 05:19:06.279921'),(46,6,2,16.9106,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.288196','2025-07-19 05:19:06.288196'),(47,6,3,6.4570,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 6.457018809106453, \"resignations_this_month\": 2}','2025-07-19 05:19:06.297796','2025-07-19 05:19:06.297796'),(48,6,4,99.7858,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.306102','2025-07-19 05:19:06.306102'),(49,6,5,91.1631,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:06.313922','2025-07-19 05:19:06.313922'),(50,6,6,93.3984,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:06.321940','2025-07-19 05:19:06.321940'),(51,6,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.338604','2025-07-19 05:19:06.338604'),(52,6,8,-38588.3054,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.348797','2025-07-19 05:19:06.348797'),(53,6,9,80.7126,75.0000,NULL,'yellow',FALSE,'{\"ytd_percentage\": 80.71264098889645, \"days_taken_this_month\": 9}','2025-07-19 05:19:06.365502','2025-07-19 05:19:06.365502'),(54,6,10,89.7143,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 89.71431767381654, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 84.71431767381654}','2025-07-19 05:19:06.375033','2025-07-19 05:19:06.375033'),(55,7,TRUE,16.0000,16.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.414007','2025-07-19 05:19:06.414007'),(56,7,2,87.0935,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.431016','2025-07-19 05:19:06.431016'),(57,7,3,1.3501,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 1.3501260192685942, \"resignations_this_month\": 1}','2025-07-19 05:19:06.441625','2025-07-19 05:19:06.441625'),(58,7,4,87.6563,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.451313','2025-07-19 05:19:06.451313'),(59,7,5,84.7529,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.460541','2025-07-19 05:19:06.460541'),(60,7,6,90.6355,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:06.469302','2025-07-19 05:19:06.469302'),(61,7,7,2.0000,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.477900','2025-07-19 05:19:06.477900'),(62,7,8,56583.3418,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.485584','2025-07-19 05:19:06.485584'),(63,7,9,65.6510,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 65.65100342725486, \"days_taken_this_month\": 7}','2025-07-19 05:19:06.497820','2025-07-19 05:19:06.497820'),(64,7,10,97.0532,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 97.05321237939044, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 92.05321237939044}','2025-07-19 05:19:06.508269','2025-07-19 05:19:06.508269'),(65,8,TRUE,7.0000,7.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.533725','2025-07-19 05:19:06.533725'),(66,8,2,70.1762,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.543508','2025-07-19 05:19:06.543508'),(67,8,3,3.3551,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 3.355097029154031, \"resignations_this_month\": 1}','2025-07-19 05:19:06.551061','2025-07-19 05:19:06.551061'),(68,8,4,99.8488,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.558322','2025-07-19 05:19:06.558322'),(69,8,5,88.6303,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.566283','2025-07-19 05:19:06.566283'),(70,8,6,97.7955,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.574350','2025-07-19 05:19:06.574350'),(71,8,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.587373','2025-07-19 05:19:06.587373'),(72,8,8,31878.3308,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.599456','2025-07-19 05:19:06.599456'),(73,8,9,68.2133,75.0000,NULL,'yellow',FALSE,'{\"ytd_percentage\": 68.21332722562678, \"days_taken_this_month\": 4}','2025-07-19 05:19:06.609508','2025-07-19 05:19:06.609508'),(74,8,10,92.0443,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 92.0443450424841, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 87.0443450424841}','2025-07-19 05:19:06.619730','2025-07-19 05:19:06.619730'),(75,9,TRUE,8.0000,8.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.663295','2025-07-19 05:19:06.663295'),(76,9,2,21.0455,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.675356','2025-07-19 05:19:06.675356'),(77,9,3,7.9604,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 7.960386782458608, \"resignations_this_month\": 1}','2025-07-19 05:19:06.693203','2025-07-19 05:19:06.693203'),(78,9,4,88.2006,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.701208','2025-07-19 05:19:06.701208'),(79,9,5,71.7148,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.708650','2025-07-19 05:19:06.708650'),(80,9,6,96.6432,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.715712','2025-07-19 05:19:06.715712'),(81,9,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.721860','2025-07-19 05:19:06.721860'),(82,9,8,43521.0983,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.728748','2025-07-19 05:19:06.728748'),(83,9,9,76.5517,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 76.55174176895574, \"days_taken_this_month\": 4}','2025-07-19 05:19:06.737378','2025-07-19 05:19:06.737378'),(84,9,10,92.9417,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 92.94169934518877, \"leave_days_adjustment\": 4, \"raw_attendance_percentage\": 87.94169934518877}','2025-07-19 05:19:06.753710','2025-07-19 05:19:06.753710'),(85,10,TRUE,16.0000,16.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.786143','2025-07-19 05:19:06.786143'),(86,10,2,73.5043,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.793471','2025-07-19 05:19:06.793471'),(87,10,3,10.5720,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 10.57204431062951, \"resignations_this_month\": 2}','2025-07-19 05:19:06.802771','2025-07-19 05:19:06.802771'),(88,10,4,94.3097,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:06.812941','2025-07-19 05:19:06.812941'),(89,10,5,77.5486,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:06.820987','2025-07-19 05:19:06.820987'),(90,10,6,97.1861,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.831644','2025-07-19 05:19:06.831644'),(91,10,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.839233','2025-07-19 05:19:06.839233'),(92,10,8,31436.7033,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.847305','2025-07-19 05:19:06.847305'),(93,10,9,74.0837,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 74.08365822085541, \"days_taken_this_month\": 3}','2025-07-19 05:19:06.859955','2025-07-19 05:19:06.859955'),(94,10,10,94.8326,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 94.83256298932534, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 89.83256298932534}','2025-07-19 05:19:06.866541','2025-07-19 05:19:06.866541'),(95,11,TRUE,6.0000,6.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.887994','2025-07-19 05:19:06.887994'),(96,11,2,67.7713,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.895837','2025-07-19 05:19:06.895837'),(97,11,3,11.7841,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 11.784076099382716, \"resignations_this_month\": 2}','2025-07-19 05:19:06.904212','2025-07-19 05:19:06.904212'),(98,11,4,88.8910,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.912129','2025-07-19 05:19:06.912129'),(99,11,5,84.5868,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.920414','2025-07-19 05:19:06.920414'),(100,11,6,80.9625,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.930788','2025-07-19 05:19:06.930788'),(101,11,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.939269','2025-07-19 05:19:06.939269'),(102,11,8,-5388.9638,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:06.946605','2025-07-19 05:19:06.946605'),(103,11,9,67.0035,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 67.00350631449608, \"days_taken_this_month\": 7}','2025-07-19 05:19:06.959942','2025-07-19 05:19:06.959942'),(104,11,10,70.9999,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 70.99987989690179, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 65.99987989690179}','2025-07-19 05:19:06.971827','2025-07-19 05:19:06.971827'),(105,12,TRUE,13.0000,13.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:06.996851','2025-07-19 05:19:06.996851'),(106,12,2,80.3253,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.020606','2025-07-19 05:19:07.020606'),(107,12,3,0.7122,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 0.7121720028629168, \"resignations_this_month\": 1}','2025-07-19 05:19:07.027867','2025-07-19 05:19:07.027867'),(108,12,4,86.7773,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.035939','2025-07-19 05:19:07.035939'),(109,12,5,84.5842,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.048199','2025-07-19 05:19:07.048199'),(110,12,6,83.8489,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.061130','2025-07-19 05:19:07.061130'),(111,12,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.069152','2025-07-19 05:19:07.069152'),(112,12,8,-42597.6722,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.077866','2025-07-19 05:19:07.077866'),(113,12,9,80.6861,75.0000,NULL,'yellow',FALSE,'{\"ytd_percentage\": 80.68614587731302, \"days_taken_this_month\": 1}','2025-07-19 05:19:07.093099','2025-07-19 05:19:07.093099'),(114,12,10,76.8994,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 76.89938739474427, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 71.89938739474427}','2025-07-19 05:19:07.103470','2025-07-19 05:19:07.103470'),(115,13,TRUE,10.0000,10.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.138297','2025-07-19 05:19:07.138297'),(116,13,2,58.4197,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.150566','2025-07-19 05:19:07.150566'),(117,13,3,1.9550,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 1.9549711934433895, \"resignations_this_month\": 2}','2025-07-19 05:19:07.159237','2025-07-19 05:19:07.159237'),(118,13,4,86.7847,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.167534','2025-07-19 05:19:07.167534'),(119,13,5,71.2464,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.177065','2025-07-19 05:19:07.177065'),(120,13,6,88.2172,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.185876','2025-07-19 05:19:07.185876'),(121,13,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.197645','2025-07-19 05:19:07.197645'),(122,13,8,-6900.4496,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.204596','2025-07-19 05:19:07.204596'),(123,13,9,66.5794,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 66.57943896100187, \"days_taken_this_month\": 2}','2025-07-19 05:19:07.214110','2025-07-19 05:19:07.214110'),(124,13,10,96.4305,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 96.43053044594024, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 91.43053044594024}','2025-07-19 05:19:07.230654','2025-07-19 05:19:07.230654'),(135,15,TRUE,10.0000,10.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.352441','2025-07-19 05:19:07.352441'),(136,15,2,24.1729,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.361066','2025-07-19 05:19:07.361066'),(137,15,3,2.1821,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 2.1821455483533185, \"resignations_this_month\": 2}','2025-07-19 05:19:07.371253','2025-07-19 05:19:07.371253'),(138,15,4,96.0030,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.378440','2025-07-19 05:19:07.378440'),(139,15,5,81.7426,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.386250','2025-07-19 05:19:07.386250'),(140,15,6,95.7114,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.397018','2025-07-19 05:19:07.397018'),(141,15,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.404981','2025-07-19 05:19:07.404981'),(142,15,8,14578.0817,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.413085','2025-07-19 05:19:07.413085'),(143,15,9,85.9603,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 85.96025385836501, \"days_taken_this_month\": 1}','2025-07-19 05:19:07.421051','2025-07-19 05:19:07.421051'),(144,15,10,85.0003,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 85.00029795594564, \"leave_days_adjustment\": 2, \"raw_attendance_percentage\": 80.00029795594564}','2025-07-19 05:19:07.432296','2025-07-19 05:19:07.432296'),(145,16,TRUE,10.0000,10.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.457894','2025-07-19 05:19:07.457894'),(146,16,2,2.5127,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.468690','2025-07-19 05:19:07.468690'),(147,16,3,7.7799,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 7.77988382948797, \"resignations_this_month\": 1}','2025-07-19 05:19:07.484104','2025-07-19 05:19:07.484104'),(148,16,4,97.7676,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.496739','2025-07-19 05:19:07.496739'),(149,16,5,78.7118,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:07.509661','2025-07-19 05:19:07.509661'),(150,16,6,96.4114,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.518145','2025-07-19 05:19:07.518145'),(151,16,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.527657','2025-07-19 05:19:07.527657'),(152,16,8,76540.5737,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.537140','2025-07-19 05:19:07.537140'),(153,16,9,89.5746,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 89.57455685624495, \"days_taken_this_month\": 3}','2025-07-19 05:19:07.559735','2025-07-19 05:19:07.559735'),(154,16,10,97.8310,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 97.83097333077274, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 92.83097333077274}','2025-07-19 05:19:07.588672','2025-07-19 05:19:07.588672'),(155,17,TRUE,19.0000,19.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.619632','2025-07-19 05:19:07.619632'),(156,17,2,31.9070,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.630454','2025-07-19 05:19:07.630454'),(157,17,3,10.9463,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 10.94627847876316, \"resignations_this_month\": 0}','2025-07-19 05:19:07.639441','2025-07-19 05:19:07.639441'),(158,17,4,92.3917,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:07.651856','2025-07-19 05:19:07.651856'),(159,17,5,75.0345,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.659518','2025-07-19 05:19:07.659518'),(160,17,6,81.8790,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.666222','2025-07-19 05:19:07.666222'),(161,17,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.674163','2025-07-19 05:19:07.674163'),(162,17,8,-99418.0815,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.680947','2025-07-19 05:19:07.680947'),(163,17,9,60.4605,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 60.4605297770332, \"days_taken_this_month\": 6}','2025-07-19 05:19:07.695062','2025-07-19 05:19:07.695062'),(164,17,10,85.4388,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 85.43881316538763, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 80.43881316538763}','2025-07-19 05:19:07.701830','2025-07-19 05:19:07.701830'),(165,18,TRUE,10.0000,10.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.720634','2025-07-19 05:19:07.720634'),(166,18,2,40.0451,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.727680','2025-07-19 05:19:07.727680'),(167,18,3,8.0632,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 8.063184621427496, \"resignations_this_month\": 1}','2025-07-19 05:19:07.733901','2025-07-19 05:19:07.733901'),(168,18,4,94.2716,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:07.740803','2025-07-19 05:19:07.740803'),(169,18,5,79.7322,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:07.747693','2025-07-19 05:19:07.747693'),(170,18,6,94.5120,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:07.755089','2025-07-19 05:19:07.755089'),(171,18,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.762450','2025-07-19 05:19:07.762450'),(172,18,8,69243.1640,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.770311','2025-07-19 05:19:07.770311'),(173,18,9,67.2510,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 67.25102048442277, \"days_taken_this_month\": 2}','2025-07-19 05:19:07.779154','2025-07-19 05:19:07.779154'),(174,18,10,72.0735,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 72.07346627459106, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 67.07346627459106}','2025-07-19 05:19:07.786929','2025-07-19 05:19:07.786929'),(175,19,TRUE,10.0000,10.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.807845','2025-07-19 05:19:07.807845'),(176,19,2,77.9586,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.816669','2025-07-19 05:19:07.816669'),(177,19,3,0.4849,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 0.48488843784807134, \"resignations_this_month\": 1}','2025-07-19 05:19:07.824365','2025-07-19 05:19:07.824365'),(178,19,4,89.8949,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.833049','2025-07-19 05:19:07.833049'),(179,19,5,93.6853,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.840449','2025-07-19 05:19:07.840449'),(180,19,6,81.6008,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.848886','2025-07-19 05:19:07.848886'),(181,19,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.859097','2025-07-19 05:19:07.859097'),(182,19,8,63774.9397,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.866449','2025-07-19 05:19:07.866449'),(183,19,9,64.1134,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 64.11342836657762, \"days_taken_this_month\": 7}','2025-07-19 05:19:07.873565','2025-07-19 05:19:07.873565'),(184,19,10,86.5264,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 86.5264201831777, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 81.5264201831777}','2025-07-19 05:19:07.883710','2025-07-19 05:19:07.883710'),(185,20,TRUE,14.0000,14.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.917466','2025-07-19 05:19:07.917466'),(186,20,2,86.9384,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.929483','2025-07-19 05:19:07.929483'),(187,20,3,8.9931,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 8.99310489499425, \"resignations_this_month\": 2}','2025-07-19 05:19:07.936047','2025-07-19 05:19:07.936047'),(188,20,4,97.3875,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.943897','2025-07-19 05:19:07.943897'),(189,20,5,88.2624,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.951639','2025-07-19 05:19:07.951639'),(190,20,6,98.0676,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.960460','2025-07-19 05:19:07.960460'),(191,20,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:07.969969','2025-07-19 05:19:07.969969'),(192,20,8,78304.8641,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:07.979814','2025-07-19 05:19:07.979814'),(193,20,9,73.6333,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 73.63334520440546, \"days_taken_this_month\": 7}','2025-07-19 05:19:07.989422','2025-07-19 05:19:07.989422'),(194,20,10,90.6799,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 90.67987567048944, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 85.67987567048944}','2025-07-19 05:19:07.998536','2025-07-19 05:19:07.998536'),(195,21,TRUE,10.0000,10.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.019315','2025-07-19 05:19:08.019315'),(196,21,2,2.9909,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.030466','2025-07-19 05:19:08.030466'),(197,21,3,3.6950,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 3.6950198429206367, \"resignations_this_month\": 1}','2025-07-19 05:19:08.038348','2025-07-19 05:19:08.038348'),(198,21,4,91.7182,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.045442','2025-07-19 05:19:08.045442'),(199,21,5,71.7972,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.052440','2025-07-19 05:19:08.052440'),(200,21,6,93.8769,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.058240','2025-07-19 05:19:08.058240'),(201,21,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.064431','2025-07-19 05:19:08.064431'),(202,21,8,-92540.5099,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.071473','2025-07-19 05:19:08.071473'),(203,21,9,64.0157,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 64.01574302993114, \"days_taken_this_month\": 8}','2025-07-19 05:19:08.081822','2025-07-19 05:19:08.081822'),(204,21,10,89.9126,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 89.91263836330934, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 84.91263836330934}','2025-07-19 05:19:08.090424','2025-07-19 05:19:08.090424'),(205,22,TRUE,19.0000,19.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.120508','2025-07-19 05:19:08.120508'),(206,22,2,25.4481,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.133195','2025-07-19 05:19:08.133195'),(207,22,3,3.4321,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 3.432091668725368, \"resignations_this_month\": 2}','2025-07-19 05:19:08.145142','2025-07-19 05:19:08.145142'),(208,22,4,89.8434,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.153711','2025-07-19 05:19:08.153711'),(209,22,5,93.3500,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.164278','2025-07-19 05:19:08.164278'),(210,22,6,82.9647,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.173712','2025-07-19 05:19:08.173712'),(211,22,7,1.0000,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.185932','2025-07-19 05:19:08.185932'),(212,22,8,29195.5460,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.199247','2025-07-19 05:19:08.199247'),(213,22,9,61.7657,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 61.76573767281161, \"days_taken_this_month\": 9}','2025-07-19 05:19:08.212736','2025-07-19 05:19:08.212736'),(214,22,10,77.5984,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 77.5983643991444, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 72.5983643991444}','2025-07-19 05:19:08.221742','2025-07-19 05:19:08.221742'),(215,23,TRUE,19.0000,19.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.248489','2025-07-19 05:19:08.248489'),(216,23,2,28.1486,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.256378','2025-07-19 05:19:08.256378'),(217,23,3,7.8195,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 7.819516272930413, \"resignations_this_month\": 1}','2025-07-19 05:19:08.265808','2025-07-19 05:19:08.265808'),(218,23,4,93.4275,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.278898','2025-07-19 05:19:08.278898'),(219,23,5,84.2311,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.286948','2025-07-19 05:19:08.286948'),(220,23,6,86.9900,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.294630','2025-07-19 05:19:08.294630'),(221,23,7,1.0000,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.302568','2025-07-19 05:19:08.302568'),(222,23,8,75698.0276,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.311037','2025-07-19 05:19:08.311037'),(223,23,9,64.5147,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 64.51474294674486, \"days_taken_this_month\": 3}','2025-07-19 05:19:08.317558','2025-07-19 05:19:08.317558'),(224,23,10,87.1362,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 87.13615256747576, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 82.13615256747576}','2025-07-19 05:19:08.323915','2025-07-19 05:19:08.323915'),(225,24,TRUE,14.0000,14.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.347156','2025-07-19 05:19:08.347156'),(226,24,2,58.6653,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.356142','2025-07-19 05:19:08.356142'),(227,24,3,9.1332,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 9.133180306624851, \"resignations_this_month\": 2}','2025-07-19 05:19:08.363390','2025-07-19 05:19:08.363390'),(228,24,4,86.2737,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.373849','2025-07-19 05:19:08.373849'),(229,24,5,86.2590,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.385432','2025-07-19 05:19:08.385432'),(230,24,6,93.9883,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.395527','2025-07-19 05:19:08.395527'),(231,24,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.403653','2025-07-19 05:19:08.403653'),(232,24,8,74687.7035,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.412027','2025-07-19 05:19:08.412027'),(233,24,9,78.1888,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 78.18880495104383, \"days_taken_this_month\": 10}','2025-07-19 05:19:08.419253','2025-07-19 05:19:08.419253'),(234,24,10,99.9147,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 99.9146899457977, \"leave_days_adjustment\": 2, \"raw_attendance_percentage\": 94.9146899457977}','2025-07-19 05:19:08.427027','2025-07-19 05:19:08.427027'),(245,26,TRUE,15.0000,15.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.545207','2025-07-19 05:19:08.545207'),(246,26,2,73.9249,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.555752','2025-07-19 05:19:08.555752'),(247,26,3,5.0189,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 5.018918730296236, \"resignations_this_month\": 2}','2025-07-19 05:19:08.564534','2025-07-19 05:19:08.564534'),(248,26,4,91.6567,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.574748','2025-07-19 05:19:08.574748'),(249,26,5,77.0793,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.582881','2025-07-19 05:19:08.582881'),(250,26,6,80.9772,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.589108','2025-07-19 05:19:08.589108'),(251,26,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.594662','2025-07-19 05:19:08.594662'),(252,26,8,-44837.0491,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.601950','2025-07-19 05:19:08.601950'),(253,26,9,76.5713,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 76.57129652944293, \"days_taken_this_month\": 8}','2025-07-19 05:19:08.608245','2025-07-19 05:19:08.608245'),(254,26,10,77.3569,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 77.35692045748182, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 72.35692045748182}','2025-07-19 05:19:08.614182','2025-07-19 05:19:08.614182'),(255,27,TRUE,8.0000,8.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.630827','2025-07-19 05:19:08.630827'),(256,27,2,50.6370,50.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.637201','2025-07-19 05:19:08.637201'),(257,27,3,1.9062,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 1.9062360465219823, \"resignations_this_month\": 1}','2025-07-19 05:19:08.650520','2025-07-19 05:19:08.650520'),(258,27,4,85.8195,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.657087','2025-07-19 05:19:08.657087'),(259,27,5,85.4837,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.663696','2025-07-19 05:19:08.663696'),(260,27,6,85.3469,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.670871','2025-07-19 05:19:08.670871'),(261,27,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.680100','2025-07-19 05:19:08.680100'),(262,27,8,50800.1077,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.688653','2025-07-19 05:19:08.688653'),(263,27,9,74.2995,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 74.29952073033613, \"days_taken_this_month\": 1}','2025-07-19 05:19:08.695848','2025-07-19 05:19:08.695848'),(264,27,10,96.3212,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 96.32121286497753, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 91.32121286497753}','2025-07-19 05:19:08.704039','2025-07-19 05:19:08.704039'),(265,28,TRUE,17.0000,17.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.724868','2025-07-19 05:19:08.724868'),(266,28,2,63.2894,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.735227','2025-07-19 05:19:08.735227'),(267,28,3,5.6719,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 5.671875061914298, \"resignations_this_month\": 2}','2025-07-19 05:19:08.742633','2025-07-19 05:19:08.742633'),(268,28,4,93.4417,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.749207','2025-07-19 05:19:08.749207'),(269,28,5,73.3337,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.756014','2025-07-19 05:19:08.756014'),(270,28,6,80.1190,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.765961','2025-07-19 05:19:08.765961'),(271,28,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.772680','2025-07-19 05:19:08.772680'),(272,28,8,49023.4158,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.778414','2025-07-19 05:19:08.778414'),(273,28,9,89.9537,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 89.95372977957201, \"days_taken_this_month\": 10}','2025-07-19 05:19:08.784957','2025-07-19 05:19:08.784957'),(274,28,10,83.4126,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 83.41258788170728, \"leave_days_adjustment\": 4, \"raw_attendance_percentage\": 78.41258788170728}','2025-07-19 05:19:08.793467','2025-07-19 05:19:08.793467'),(275,29,TRUE,6.0000,6.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.813659','2025-07-19 05:19:08.813659'),(276,29,2,59.2172,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.821210','2025-07-19 05:19:08.821210'),(277,29,3,7.0623,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 7.062346548719748, \"resignations_this_month\": 0}','2025-07-19 05:19:08.830464','2025-07-19 05:19:08.830464'),(278,29,4,88.8334,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.842644','2025-07-19 05:19:08.842644'),(279,29,5,89.0089,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.853753','2025-07-19 05:19:08.853753'),(280,29,6,85.9027,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.865181','2025-07-19 05:19:08.865181'),(281,29,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.873302','2025-07-19 05:19:08.873302'),(282,29,8,19355.7722,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.884528','2025-07-19 05:19:08.884528'),(283,29,9,84.7711,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 84.77113172068682, \"days_taken_this_month\": 3}','2025-07-19 05:19:08.891799','2025-07-19 05:19:08.891799'),(284,29,10,71.1083,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 71.10826595746946, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 66.10826595746946}','2025-07-19 05:19:08.900042','2025-07-19 05:19:08.900042'),(285,30,TRUE,15.0000,15.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.930692','2025-07-19 05:19:08.930692'),(286,30,2,26.0765,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.941824','2025-07-19 05:19:08.941824'),(287,30,3,6.5874,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 6.587428751430245, \"resignations_this_month\": 1}','2025-07-19 05:19:08.949987','2025-07-19 05:19:08.949987'),(288,30,4,99.2523,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.956277','2025-07-19 05:19:08.956277'),(289,30,5,93.6833,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.962091','2025-07-19 05:19:08.962091'),(290,30,6,93.2132,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:08.968866','2025-07-19 05:19:08.968866'),(291,30,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:08.975610','2025-07-19 05:19:08.975610'),(292,30,8,7311.1792,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:08.982004','2025-07-19 05:19:08.982004'),(293,30,9,61.0354,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 61.03544965535393, \"days_taken_this_month\": 2}','2025-07-19 05:19:08.988068','2025-07-19 05:19:08.988068'),(294,30,10,98.4459,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 98.44590621286736, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 93.44590621286736}','2025-07-19 05:19:08.993766','2025-07-19 05:19:08.993766'),(295,31,TRUE,16.0000,16.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.014231','2025-07-19 05:19:09.014231'),(296,31,2,16.0262,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.020963','2025-07-19 05:19:09.020963'),(297,31,3,9.0967,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 9.096737153441197, \"resignations_this_month\": 1}','2025-07-19 05:19:09.026921','2025-07-19 05:19:09.026921'),(298,31,4,88.2493,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.034038','2025-07-19 05:19:09.034038'),(299,31,5,79.8689,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.043188','2025-07-19 05:19:09.043188'),(300,31,6,82.0453,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.049926','2025-07-19 05:19:09.049926'),(301,31,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.058186','2025-07-19 05:19:09.058186'),(302,31,8,-43585.9469,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.064370','2025-07-19 05:19:09.064370'),(303,31,9,79.9394,75.0000,NULL,'yellow',FALSE,'{\"ytd_percentage\": 79.93944040133546, \"days_taken_this_month\": 2}','2025-07-19 05:19:09.070567','2025-07-19 05:19:09.070567'),(304,31,10,71.1703,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 71.170325570079, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 66.170325570079}','2025-07-19 05:19:09.076751','2025-07-19 05:19:09.076751'),(305,32,TRUE,5.0000,5.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.098881','2025-07-19 05:19:09.098881'),(306,32,2,54.7208,50.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.107718','2025-07-19 05:19:09.107718'),(307,32,3,4.7410,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 4.740983813464298, \"resignations_this_month\": 2}','2025-07-19 05:19:09.116812','2025-07-19 05:19:09.116812'),(308,32,4,91.1495,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.123626','2025-07-19 05:19:09.123626'),(309,32,5,85.6361,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.130635','2025-07-19 05:19:09.130635'),(310,32,6,92.7667,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.137192','2025-07-19 05:19:09.137192'),(311,32,7,1.0000,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.143986','2025-07-19 05:19:09.143986'),(312,32,8,9025.7440,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.151239','2025-07-19 05:19:09.151239'),(313,32,9,60.3888,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 60.38884500470095, \"days_taken_this_month\": 7}','2025-07-19 05:19:09.163887','2025-07-19 05:19:09.163887'),(314,32,10,97.0686,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 97.06859697333836, \"leave_days_adjustment\": 2, \"raw_attendance_percentage\": 92.06859697333836}','2025-07-19 05:19:09.172411','2025-07-19 05:19:09.172411'),(315,33,TRUE,17.0000,17.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.193393','2025-07-19 05:19:09.193393'),(316,33,2,39.6934,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.200734','2025-07-19 05:19:09.200734'),(317,33,3,11.0600,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 11.060019988199285, \"resignations_this_month\": 2}','2025-07-19 05:19:09.209538','2025-07-19 05:19:09.209538'),(318,33,4,90.5732,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.216692','2025-07-19 05:19:09.216692'),(319,33,5,83.2108,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.223754','2025-07-19 05:19:09.223754'),(320,33,6,86.4409,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.229900','2025-07-19 05:19:09.229900'),(321,33,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.236601','2025-07-19 05:19:09.236601'),(322,33,8,76247.8609,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.244202','2025-07-19 05:19:09.244202'),(323,33,9,72.3803,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 72.38032087744702, \"days_taken_this_month\": 6}','2025-07-19 05:19:09.250976','2025-07-19 05:19:09.250976'),(324,33,10,93.9494,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 93.94938402547524, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 88.94938402547524}','2025-07-19 05:19:09.257364','2025-07-19 05:19:09.257364'),(325,34,TRUE,17.0000,17.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.277428','2025-07-19 05:19:09.277428'),(326,34,2,87.7231,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.283846','2025-07-19 05:19:09.283846'),(327,34,3,7.6992,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 7.699165164832816, \"resignations_this_month\": 0}','2025-07-19 05:19:09.289564','2025-07-19 05:19:09.289564'),(328,34,4,94.3523,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.295781','2025-07-19 05:19:09.295781'),(329,34,5,74.6820,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.301138','2025-07-19 05:19:09.301138'),(330,34,6,93.8590,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.306017','2025-07-19 05:19:09.306017'),(331,34,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.310613','2025-07-19 05:19:09.310613'),(332,34,8,-57210.1370,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.315057','2025-07-19 05:19:09.315057'),(333,34,9,71.1070,75.0000,NULL,'yellow',FALSE,'{\"ytd_percentage\": 71.10700250253943, \"days_taken_this_month\": 8}','2025-07-19 05:19:09.318827','2025-07-19 05:19:09.318827'),(334,34,10,93.5836,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 93.58361305269324, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 88.58361305269324}','2025-07-19 05:19:09.322581','2025-07-19 05:19:09.322581'),(335,35,TRUE,7.0000,7.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.334560','2025-07-19 05:19:09.334560'),(336,35,2,83.6690,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.339986','2025-07-19 05:19:09.339986'),(337,35,3,1.8316,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 1.8315966482708452, \"resignations_this_month\": 0}','2025-07-19 05:19:09.344705','2025-07-19 05:19:09.344705'),(338,35,4,99.3695,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.349751','2025-07-19 05:19:09.349751'),(339,35,5,90.7992,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.355304','2025-07-19 05:19:09.355304'),(340,35,6,92.8356,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.361942','2025-07-19 05:19:09.361942'),(341,35,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.373089','2025-07-19 05:19:09.373089'),(342,35,8,-57791.4755,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.383034','2025-07-19 05:19:09.383034'),(343,35,9,81.3825,75.0000,NULL,'yellow',FALSE,'{\"ytd_percentage\": 81.3825372734923, \"days_taken_this_month\": 7}','2025-07-19 05:19:09.389851','2025-07-19 05:19:09.389851'),(344,35,10,98.8400,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 98.83999787735009, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 93.83999787735009}','2025-07-19 05:19:09.395541','2025-07-19 05:19:09.395541'),(345,36,TRUE,12.0000,12.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.411356','2025-07-19 05:19:09.411356'),(346,36,2,56.8040,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.417665','2025-07-19 05:19:09.417665'),(347,36,3,8.4942,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 8.494150647884648, \"resignations_this_month\": 0}','2025-07-19 05:19:09.423017','2025-07-19 05:19:09.423017'),(348,36,4,88.8632,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.428766','2025-07-19 05:19:09.428766'),(349,36,5,77.2702,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.435448','2025-07-19 05:19:09.435448'),(350,36,6,93.2970,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.441898','2025-07-19 05:19:09.441898'),(351,36,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.447237','2025-07-19 05:19:09.447237'),(352,36,8,-94121.0385,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.453053','2025-07-19 05:19:09.453053'),(353,36,9,63.2257,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 63.225650347042794, \"days_taken_this_month\": 3}','2025-07-19 05:19:09.461149','2025-07-19 05:19:09.461149'),(354,36,10,75.4744,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 75.47440466791068, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 70.47440466791068}','2025-07-19 05:19:09.467514','2025-07-19 05:19:09.467514'),(365,38,TRUE,11.0000,11.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.562224','2025-07-19 05:19:09.562224'),(366,38,2,19.3561,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.567791','2025-07-19 05:19:09.567791'),(367,38,3,4.0055,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 4.005543839623838, \"resignations_this_month\": 1}','2025-07-19 05:19:09.573934','2025-07-19 05:19:09.573934'),(368,38,4,85.2753,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.581808','2025-07-19 05:19:09.581808'),(369,38,5,84.1454,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.589625','2025-07-19 05:19:09.589625'),(370,38,6,97.0947,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.596441','2025-07-19 05:19:09.596441'),(371,38,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.604042','2025-07-19 05:19:09.604042'),(372,38,8,-75389.9481,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.613679','2025-07-19 05:19:09.613679'),(373,38,9,84.0372,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 84.03720779606658, \"days_taken_this_month\": 3}','2025-07-19 05:19:09.621457','2025-07-19 05:19:09.621457'),(374,38,10,91.7445,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 91.74451928541528, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 86.74451928541527}','2025-07-19 05:19:09.628437','2025-07-19 05:19:09.628437'),(375,39,TRUE,9.0000,9.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.654402','2025-07-19 05:19:09.654402'),(376,39,2,38.9902,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.661134','2025-07-19 05:19:09.661134'),(377,39,3,2.6858,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 2.685793934449761, \"resignations_this_month\": 2}','2025-07-19 05:19:09.672945','2025-07-19 05:19:09.672945'),(378,39,4,91.4861,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.679810','2025-07-19 05:19:09.679810'),(379,39,5,82.7037,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.688127','2025-07-19 05:19:09.688127'),(380,39,6,90.7437,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.697767','2025-07-19 05:19:09.697767'),(381,39,7,1.0000,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.707388','2025-07-19 05:19:09.707388'),(382,39,8,70152.0506,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.716485','2025-07-19 05:19:09.716485'),(383,39,9,85.1718,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 85.17175361377627, \"days_taken_this_month\": 10}','2025-07-19 05:19:09.729991','2025-07-19 05:19:09.729991'),(384,39,10,75.3471,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 75.34706978577171, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 70.34706978577171}','2025-07-19 05:19:09.743772','2025-07-19 05:19:09.743772'),(385,40,TRUE,14.0000,14.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.761915','2025-07-19 05:19:09.761915'),(386,40,2,46.4734,50.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:09.768372','2025-07-19 05:19:09.768372'),(387,40,3,0.7334,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 0.7333628941751318, \"resignations_this_month\": 2}','2025-07-19 05:19:09.775807','2025-07-19 05:19:09.775807'),(388,40,4,97.5404,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.784111','2025-07-19 05:19:09.784111'),(389,40,5,94.1983,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.790776','2025-07-19 05:19:09.790776'),(390,40,6,97.4700,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.796968','2025-07-19 05:19:09.796968'),(391,40,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.803444','2025-07-19 05:19:09.803444'),(392,40,8,48036.1717,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.808784','2025-07-19 05:19:09.808784'),(393,40,9,73.4464,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 73.44643145003668, \"days_taken_this_month\": 4}','2025-07-19 05:19:09.813298','2025-07-19 05:19:09.813298'),(394,40,10,85.1815,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 85.18154138194666, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 80.18154138194666}','2025-07-19 05:19:09.819556','2025-07-19 05:19:09.819556'),(395,41,TRUE,15.0000,15.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.841977','2025-07-19 05:19:09.841977'),(396,41,2,63.8968,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.850099','2025-07-19 05:19:09.850099'),(397,41,3,5.5538,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 5.553781180884283, \"resignations_this_month\": 2}','2025-07-19 05:19:09.857110','2025-07-19 05:19:09.857110'),(398,41,4,86.0158,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.863086','2025-07-19 05:19:09.863086'),(399,41,5,87.3959,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.869297','2025-07-19 05:19:09.869297'),(400,41,6,95.2363,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.875494','2025-07-19 05:19:09.875494'),(401,41,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.881021','2025-07-19 05:19:09.881021'),(402,41,8,-37377.6244,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.891470','2025-07-19 05:19:09.891470'),(403,41,9,60.1569,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 60.15686290443499, \"days_taken_this_month\": 9}','2025-07-19 05:19:09.899613','2025-07-19 05:19:09.899613'),(404,41,10,82.5616,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 82.56160275452022, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 77.56160275452022}','2025-07-19 05:19:09.905101','2025-07-19 05:19:09.905101'),(405,42,TRUE,7.0000,7.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.923029','2025-07-19 05:19:09.923029'),(406,42,2,89.6711,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:09.933602','2025-07-19 05:19:09.933602'),(407,42,3,6.6387,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 6.638673679035678, \"resignations_this_month\": 2}','2025-07-19 05:19:09.945827','2025-07-19 05:19:09.945827'),(408,42,4,97.5940,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.958025','2025-07-19 05:19:09.958025'),(409,42,5,84.2064,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.970361','2025-07-19 05:19:09.970361'),(410,42,6,95.8696,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.982435','2025-07-19 05:19:09.982435'),(411,42,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:09.997620','2025-07-19 05:19:09.997620'),(412,42,8,-57035.3277,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.017810','2025-07-19 05:19:10.017810'),(413,42,9,61.3691,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 61.36911471782666, \"days_taken_this_month\": 1}','2025-07-19 05:19:10.030436','2025-07-19 05:19:10.030436'),(414,42,10,80.1600,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 80.15999424194695, \"leave_days_adjustment\": 2, \"raw_attendance_percentage\": 75.15999424194695}','2025-07-19 05:19:10.047838','2025-07-19 05:19:10.047838'),(415,43,TRUE,9.0000,9.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.072978','2025-07-19 05:19:10.072978'),(416,43,2,97.9099,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.083725','2025-07-19 05:19:10.083725'),(417,43,3,0.5270,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 0.5270272486016863, \"resignations_this_month\": 1}','2025-07-19 05:19:10.092842','2025-07-19 05:19:10.092842'),(418,43,4,92.2288,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:10.105891','2025-07-19 05:19:10.105891'),(419,43,5,72.8233,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.114065','2025-07-19 05:19:10.114065'),(420,43,6,94.7993,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:10.121413','2025-07-19 05:19:10.121413'),(421,43,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.129827','2025-07-19 05:19:10.129827'),(422,43,8,-1681.1689,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.137104','2025-07-19 05:19:10.137104'),(423,43,9,88.8336,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 88.83355336033287, \"days_taken_this_month\": 8}','2025-07-19 05:19:10.146239','2025-07-19 05:19:10.146239'),(424,43,10,92.0800,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 92.07996083480488, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 87.07996083480487}','2025-07-19 05:19:10.154174','2025-07-19 05:19:10.154174'),(425,44,TRUE,6.0000,6.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.172299','2025-07-19 05:19:10.172299'),(426,44,2,42.5619,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.178156','2025-07-19 05:19:10.178156'),(427,44,3,10.3619,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 10.361936564495457, \"resignations_this_month\": 2}','2025-07-19 05:19:10.185059','2025-07-19 05:19:10.185059'),(428,44,4,87.9202,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.192854','2025-07-19 05:19:10.192854'),(429,44,5,89.7435,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:10.198170','2025-07-19 05:19:10.198170'),(430,44,6,89.3281,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.205733','2025-07-19 05:19:10.205733'),(431,44,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.213564','2025-07-19 05:19:10.213564'),(432,44,8,60575.5615,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.221416','2025-07-19 05:19:10.221416'),(433,44,9,67.6760,75.0000,NULL,'yellow',FALSE,'{\"ytd_percentage\": 67.67601734679434, \"days_taken_this_month\": 9}','2025-07-19 05:19:10.228689','2025-07-19 05:19:10.228689'),(434,44,10,87.8203,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 87.82026138771948, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 82.82026138771948}','2025-07-19 05:19:10.236045','2025-07-19 05:19:10.236045'),(435,45,TRUE,19.0000,19.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.252567','2025-07-19 05:19:10.252567'),(436,45,2,57.4450,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.258852','2025-07-19 05:19:10.258852'),(437,45,3,9.5377,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 9.537725865968756, \"resignations_this_month\": 0}','2025-07-19 05:19:10.264939','2025-07-19 05:19:10.264939'),(438,45,4,85.2928,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.270800','2025-07-19 05:19:10.270800'),(439,45,5,82.2712,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.278166','2025-07-19 05:19:10.278166'),(440,45,6,86.8179,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.285136','2025-07-19 05:19:10.285136'),(441,45,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.290522','2025-07-19 05:19:10.290522'),(442,45,8,-89666.6818,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.298354','2025-07-19 05:19:10.298354'),(443,45,9,81.7693,75.0000,NULL,'yellow',FALSE,'{\"ytd_percentage\": 81.76932391198471, \"days_taken_this_month\": 1}','2025-07-19 05:19:10.304643','2025-07-19 05:19:10.304643'),(444,45,10,72.7327,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 72.73265829677355, \"leave_days_adjustment\": 2, \"raw_attendance_percentage\": 67.73265829677355}','2025-07-19 05:19:10.310477','2025-07-19 05:19:10.310477'),(445,46,TRUE,13.0000,13.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.339559','2025-07-19 05:19:10.339559'),(446,46,2,97.8971,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.346177','2025-07-19 05:19:10.346177'),(447,46,3,1.6436,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 1.6435894709289691, \"resignations_this_month\": 0}','2025-07-19 05:19:10.354916','2025-07-19 05:19:10.354916'),(448,46,4,95.9337,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.362215','2025-07-19 05:19:10.362215'),(449,46,5,75.2901,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.373032','2025-07-19 05:19:10.373032'),(450,46,6,99.0692,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.383792','2025-07-19 05:19:10.383792'),(451,46,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.390843','2025-07-19 05:19:10.390843'),(452,46,8,-93374.3666,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.396369','2025-07-19 05:19:10.396369'),(453,46,9,82.9017,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 82.90166621617716, \"days_taken_this_month\": 8}','2025-07-19 05:19:10.401871','2025-07-19 05:19:10.401871'),(454,46,10,72.8422,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 72.84222747236629, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 67.84222747236629}','2025-07-19 05:19:10.407620','2025-07-19 05:19:10.407620'),(455,47,TRUE,7.0000,7.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.426032','2025-07-19 05:19:10.426032'),(456,47,2,4.2530,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.432569','2025-07-19 05:19:10.432569'),(457,47,3,10.0782,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 10.07820222516952, \"resignations_this_month\": 2}','2025-07-19 05:19:10.440987','2025-07-19 05:19:10.440987'),(458,47,4,96.4101,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.449625','2025-07-19 05:19:10.449625'),(459,47,5,83.9924,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.456175','2025-07-19 05:19:10.456175'),(460,47,6,89.4331,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.463061','2025-07-19 05:19:10.463061'),(461,47,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.471316','2025-07-19 05:19:10.471316'),(462,47,8,59353.6434,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.478616','2025-07-19 05:19:10.478616'),(463,47,9,73.5754,75.0000,NULL,'green',FALSE,'{\"ytd_percentage\": 73.57541162069913, \"days_taken_this_month\": 8}','2025-07-19 05:19:10.488319','2025-07-19 05:19:10.488319'),(464,47,10,93.7443,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 93.74432499329076, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 88.74432499329075}','2025-07-19 05:19:10.495556','2025-07-19 05:19:10.495556'),(465,48,TRUE,8.0000,8.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.513938','2025-07-19 05:19:10.513938'),(466,48,2,61.0483,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.521788','2025-07-19 05:19:10.521788'),(467,48,3,3.3142,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 3.314241306116089, \"resignations_this_month\": 2}','2025-07-19 05:19:10.532556','2025-07-19 05:19:10.532556'),(468,48,4,90.3525,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:10.540985','2025-07-19 05:19:10.540985'),(469,48,5,73.5725,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.548959','2025-07-19 05:19:10.548959'),(470,48,6,80.1293,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.558622','2025-07-19 05:19:10.558622'),(471,48,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.568245','2025-07-19 05:19:10.568245'),(472,48,8,-82578.1626,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.577975','2025-07-19 05:19:10.577975'),(473,48,9,88.7213,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 88.72132713373486, \"days_taken_this_month\": 5}','2025-07-19 05:19:10.588633','2025-07-19 05:19:10.588633'),(474,48,10,71.9482,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 71.94819037271829, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 66.94819037271829}','2025-07-19 05:19:10.595176','2025-07-19 05:19:10.595176'),(485,50,TRUE,11.0000,11.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.713994','2025-07-19 05:19:10.713994'),(486,50,2,10.7155,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.721361','2025-07-19 05:19:10.721361'),(487,50,3,7.9584,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 7.958417233529766, \"resignations_this_month\": 1}','2025-07-19 05:19:10.730008','2025-07-19 05:19:10.730008'),(488,50,4,92.0891,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:10.741912','2025-07-19 05:19:10.741912'),(489,50,5,70.4332,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.749992','2025-07-19 05:19:10.749992'),(490,50,6,95.3365,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.759492','2025-07-19 05:19:10.759492'),(491,50,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.766596','2025-07-19 05:19:10.766596'),(492,50,8,-53066.2206,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.776154','2025-07-19 05:19:10.776154'),(493,50,9,64.5494,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 64.54937517971501, \"days_taken_this_month\": 8}','2025-07-19 05:19:10.783419','2025-07-19 05:19:10.783419'),(494,50,10,98.4173,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 98.41732169913116, \"leave_days_adjustment\": 4, \"raw_attendance_percentage\": 93.41732169913116}','2025-07-19 05:19:10.791390','2025-07-19 05:19:10.791390'),(495,51,TRUE,19.0000,19.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.812464','2025-07-19 05:19:10.812464'),(496,51,2,15.7367,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.819965','2025-07-19 05:19:10.819965'),(497,51,3,6.1147,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 6.1146731652192345, \"resignations_this_month\": 1}','2025-07-19 05:19:10.825955','2025-07-19 05:19:10.825955'),(498,51,4,91.0293,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:10.833211','2025-07-19 05:19:10.833211'),(499,51,5,73.7362,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.841650','2025-07-19 05:19:10.841650'),(500,51,6,93.9266,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:10.850208','2025-07-19 05:19:10.850208'),(501,51,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.858455','2025-07-19 05:19:10.858455'),(502,51,8,-81117.2264,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.865469','2025-07-19 05:19:10.865469'),(503,51,9,89.8823,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 89.88227474375122, \"days_taken_this_month\": 5}','2025-07-19 05:19:10.872905','2025-07-19 05:19:10.872905'),(504,51,10,90.3136,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 90.31361049814215, \"leave_days_adjustment\": 2, \"raw_attendance_percentage\": 85.31361049814215}','2025-07-19 05:19:10.879321','2025-07-19 05:19:10.879321'),(505,52,TRUE,7.0000,7.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.904281','2025-07-19 05:19:10.904281'),(506,52,2,85.9824,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.913022','2025-07-19 05:19:10.913022'),(507,52,3,11.9297,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 11.929690468126292, \"resignations_this_month\": 0}','2025-07-19 05:19:10.920089','2025-07-19 05:19:10.920089'),(508,52,4,86.5587,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.928057','2025-07-19 05:19:10.928057'),(509,52,5,76.3951,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.934409','2025-07-19 05:19:10.934409'),(510,52,6,82.6651,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.941495','2025-07-19 05:19:10.941495'),(511,52,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.947785','2025-07-19 05:19:10.947785'),(512,52,8,18593.3552,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.953826','2025-07-19 05:19:10.953826'),(513,52,9,87.4567,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 87.45670482398097, \"days_taken_this_month\": 10}','2025-07-19 05:19:10.959298','2025-07-19 05:19:10.959298'),(514,52,10,76.6279,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 76.6279073899453, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 71.6279073899453}','2025-07-19 05:19:10.966060','2025-07-19 05:19:10.966060'),(515,53,TRUE,12.0000,12.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:10.983122','2025-07-19 05:19:10.983122'),(516,53,2,99.0214,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:10.993685','2025-07-19 05:19:10.993685'),(517,53,3,2.6437,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 2.6437314811792527, \"resignations_this_month\": 2}','2025-07-19 05:19:11.001721','2025-07-19 05:19:11.001721'),(518,53,4,92.5991,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:11.012704','2025-07-19 05:19:11.012704'),(519,53,5,81.9496,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.020800','2025-07-19 05:19:11.020800'),(520,53,6,86.1824,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.027300','2025-07-19 05:19:11.027300'),(521,53,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.033417','2025-07-19 05:19:11.033417'),(522,53,8,-43526.4349,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.040218','2025-07-19 05:19:11.040218'),(523,53,9,82.9085,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 82.90850238981581, \"days_taken_this_month\": 4}','2025-07-19 05:19:11.046112','2025-07-19 05:19:11.046112'),(524,53,10,77.9941,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 77.99410164266241, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 72.99410164266241}','2025-07-19 05:19:11.052268','2025-07-19 05:19:11.052268'),(525,54,TRUE,18.0000,18.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.070755','2025-07-19 05:19:11.070755'),(526,54,2,59.8754,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.076830','2025-07-19 05:19:11.076830'),(527,54,3,1.4923,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 1.492312926328685, \"resignations_this_month\": 0}','2025-07-19 05:19:11.084636','2025-07-19 05:19:11.084636'),(528,54,4,94.1849,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:11.091780','2025-07-19 05:19:11.091780'),(529,54,5,83.5603,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.098462','2025-07-19 05:19:11.098462'),(530,54,6,88.7345,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.105528','2025-07-19 05:19:11.105528'),(531,54,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.111785','2025-07-19 05:19:11.111785'),(532,54,8,-41035.3886,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.118137','2025-07-19 05:19:11.118137'),(533,54,9,88.2961,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 88.29608766029918, \"days_taken_this_month\": 4}','2025-07-19 05:19:11.125239','2025-07-19 05:19:11.125239'),(534,54,10,85.6043,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 85.60427340252168, \"leave_days_adjustment\": 4, \"raw_attendance_percentage\": 80.60427340252168}','2025-07-19 05:19:11.135968','2025-07-19 05:19:11.135968'),(535,55,TRUE,18.0000,18.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.158360','2025-07-19 05:19:11.158360'),(536,55,2,26.2925,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.170027','2025-07-19 05:19:11.170027'),(537,55,3,8.7822,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 8.78219483180575, \"resignations_this_month\": 2}','2025-07-19 05:19:11.177598','2025-07-19 05:19:11.177598'),(538,55,4,87.9327,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.185204','2025-07-19 05:19:11.185204'),(539,55,5,87.8054,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.193749','2025-07-19 05:19:11.193749'),(540,55,6,83.9988,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.202171','2025-07-19 05:19:11.202171'),(541,55,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.211363','2025-07-19 05:19:11.211363'),(542,55,8,-76667.5885,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.223088','2025-07-19 05:19:11.223088'),(543,55,9,65.8706,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 65.87055899841691, \"days_taken_this_month\": 10}','2025-07-19 05:19:11.230170','2025-07-19 05:19:11.230170'),(544,55,10,93.7235,100.0000,NULL,'yellow',FALSE,'{\"adjusted_percentage\": 93.7234522375544, \"leave_days_adjustment\": FALSE, \"raw_attendance_percentage\": 88.7234522375544}','2025-07-19 05:19:11.236602','2025-07-19 05:19:11.236602'),(545,56,TRUE,7.0000,7.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.257203','2025-07-19 05:19:11.257203'),(546,56,2,2.9320,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.264051','2025-07-19 05:19:11.264051'),(547,56,3,10.9963,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 10.996256991105936, \"resignations_this_month\": 0}','2025-07-19 05:19:11.270955','2025-07-19 05:19:11.270955'),(548,56,4,90.6159,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:11.276891','2025-07-19 05:19:11.276891'),(549,56,5,71.3966,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.283521','2025-07-19 05:19:11.283521'),(550,56,6,98.9099,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.288539','2025-07-19 05:19:11.288539'),(551,56,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.292944','2025-07-19 05:19:11.292944'),(552,56,8,62813.3273,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.300817','2025-07-19 05:19:11.300817'),(553,56,9,66.9334,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 66.93337145625112, \"days_taken_this_month\": 4}','2025-07-19 05:19:11.308467','2025-07-19 05:19:11.308467'),(554,56,10,80.8123,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 80.81227148262074, \"leave_days_adjustment\": 4, \"raw_attendance_percentage\": 75.81227148262074}','2025-07-19 05:19:11.315990','2025-07-19 05:19:11.315990'),(555,57,TRUE,8.0000,8.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.333625','2025-07-19 05:19:11.333625'),(556,57,2,15.9366,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.342505','2025-07-19 05:19:11.342505'),(557,57,3,9.7609,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 9.760874399509936, \"resignations_this_month\": 2}','2025-07-19 05:19:11.350853','2025-07-19 05:19:11.350853'),(558,57,4,94.3873,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:11.357881','2025-07-19 05:19:11.357881'),(559,57,5,93.4563,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:11.369922','2025-07-19 05:19:11.369922'),(560,57,6,89.7869,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.388109','2025-07-19 05:19:11.388109'),(561,57,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.396155','2025-07-19 05:19:11.396155'),(562,57,8,94352.7227,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.405216','2025-07-19 05:19:11.405216'),(563,57,9,67.0328,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 67.03276372303553, \"days_taken_this_month\": 6}','2025-07-19 05:19:11.417392','2025-07-19 05:19:11.417392'),(564,57,10,76.5959,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 76.59592215115191, \"leave_days_adjustment\": TRUE, \"raw_attendance_percentage\": 71.59592215115191}','2025-07-19 05:19:11.425085','2025-07-19 05:19:11.425085'),(565,58,TRUE,6.0000,6.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.451341','2025-07-19 05:19:11.451341'),(566,58,2,95.5369,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.459026','2025-07-19 05:19:11.459026'),(567,58,3,5.0704,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 5.070398638870584, \"resignations_this_month\": 0}','2025-07-19 05:19:11.470154','2025-07-19 05:19:11.470154'),(568,58,4,88.5313,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.478900','2025-07-19 05:19:11.478900'),(569,58,5,79.2500,85.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:11.485540','2025-07-19 05:19:11.485540'),(570,58,6,89.7545,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.505457','2025-07-19 05:19:11.505457'),(571,58,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.512623','2025-07-19 05:19:11.512623'),(572,58,8,79388.9277,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.523298','2025-07-19 05:19:11.523298'),(573,58,9,65.0075,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 65.0075257296968, \"days_taken_this_month\": 6}','2025-07-19 05:19:11.529929','2025-07-19 05:19:11.529929'),(574,58,10,95.0712,100.0000,NULL,'green',FALSE,'{\"adjusted_percentage\": 95.0712423219946, \"leave_days_adjustment\": 4, \"raw_attendance_percentage\": 90.0712423219946}','2025-07-19 05:19:11.536955','2025-07-19 05:19:11.536955'),(575,59,TRUE,16.0000,16.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.559862','2025-07-19 05:19:11.559862'),(576,59,2,88.1282,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.570558','2025-07-19 05:19:11.570558'),(577,59,3,7.8818,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 7.88184148602016, \"resignations_this_month\": 2}','2025-07-19 05:19:11.579269','2025-07-19 05:19:11.579269'),(578,59,4,87.1704,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.586594','2025-07-19 05:19:11.586594'),(579,59,5,83.3880,85.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.600249','2025-07-19 05:19:11.600249'),(580,59,6,91.3353,100.0000,NULL,'yellow',FALSE,NULL,'2025-07-19 05:19:11.608082','2025-07-19 05:19:11.608082'),(581,59,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.619433','2025-07-19 05:19:11.619433'),(582,59,8,37749.1740,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.626587','2025-07-19 05:19:11.626587'),(583,59,9,60.1049,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 60.10485845076397, \"days_taken_this_month\": 9}','2025-07-19 05:19:11.633922','2025-07-19 05:19:11.633922'),(584,59,10,89.0043,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 89.00426082847864, \"leave_days_adjustment\": 2, \"raw_attendance_percentage\": 84.00426082847864}','2025-07-19 05:19:11.644617','2025-07-19 05:19:11.644617'),(585,60,TRUE,10.0000,10.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.675497','2025-07-19 05:19:11.675497'),(586,60,2,61.3522,50.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.682615','2025-07-19 05:19:11.682615'),(587,60,3,6.2658,0.0000,NULL,'red',FALSE,'{\"annual_rate\": 6.265779384541299, \"resignations_this_month\": 0}','2025-07-19 05:19:11.689958','2025-07-19 05:19:11.689958'),(588,60,4,95.4348,100.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.699525','2025-07-19 05:19:11.699525'),(589,60,5,73.1493,85.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.705844','2025-07-19 05:19:11.705844'),(590,60,6,88.6709,100.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.715947','2025-07-19 05:19:11.715947'),(591,60,7,0.0000,0.0000,NULL,'green',FALSE,NULL,'2025-07-19 05:19:11.722376','2025-07-19 05:19:11.722376'),(592,60,8,-61210.5345,0.0000,NULL,'red',FALSE,NULL,'2025-07-19 05:19:11.728832','2025-07-19 05:19:11.728832'),(593,60,9,86.1570,75.0000,NULL,'red',FALSE,'{\"ytd_percentage\": 86.15704418753587, \"days_taken_this_month\": 2}','2025-07-19 05:19:11.734457','2025-07-19 05:19:11.734457'),(594,60,10,85.4464,100.0000,NULL,'red',FALSE,'{\"adjusted_percentage\": 85.44636042863186, \"leave_days_adjustment\": 3, \"raw_attendance_percentage\": 80.44636042863186}','2025-07-19 05:19:11.742950','2025-07-19 05:19:11.742950'),(650,128,TRUE,12.0000,12.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(651,128,3,0.0000,0.0000,'No attrition','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(652,128,4,97.5000,95.0000,'Exceeded SLA target','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(653,128,5,85.0000,80.0000,'Good utilization','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(654,128,6,96.0000,95.0000,'Good time registration','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(655,128,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(656,128,8,-1200.0000,0.0000,'Under budget','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(657,128,9,15.0000,25.0000,'15 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(658,128,10,88.0000,80.0000,'Good office attendance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(659,129,TRUE,15.0000,15.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(660,129,3,0.0000,0.0000,'No attrition','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(661,129,4,99.1000,95.0000,'Excellent SLA','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(662,129,5,92.0000,80.0000,'High utilization','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(663,129,6,98.0000,95.0000,'Excellent time registration','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(664,129,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(665,129,8,-2500.0000,0.0000,'Well under budget','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(666,129,9,8.0000,25.0000,'8 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(667,129,10,95.0000,80.0000,'Excellent attendance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(668,130,TRUE,8.0000,8.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(669,130,3,1.0000,0.0000,'1 resignation this month','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(670,130,4,94.2000,95.0000,'Slightly below SLA','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(671,130,5,78.0000,80.0000,'Below utilization target','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(672,130,6,93.0000,95.0000,'Below time reg target','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(673,130,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(674,130,8,800.0000,0.0000,'Slightly over budget','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(675,130,9,20.0000,25.0000,'20 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(676,130,10,75.0000,80.0000,'Below attendance target','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(677,131,TRUE,10.0000,10.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(678,131,3,2.0000,0.0000,'2 resignations this month','red',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(679,131,4,91.5000,95.0000,'Below SLA target','red',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(680,131,5,72.0000,80.0000,'Low utilization','red',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(681,131,6,88.0000,95.0000,'Poor time registration','red',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(682,131,7,1.0000,0.0000,'1 compliance issue','red',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(683,131,8,1500.0000,0.0000,'Over budget','red',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(684,131,9,25.0000,25.0000,'25 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(685,131,10,65.0000,80.0000,'Poor attendance','red',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(686,132,TRUE,14.0000,14.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(687,132,3,0.0000,0.0000,'No attrition','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(688,132,4,96.8000,95.0000,'Good SLA performance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(689,132,5,83.0000,80.0000,'Good utilization','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(690,132,6,95.0000,95.0000,'Target time registration','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(691,132,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(692,132,8,-900.0000,0.0000,'Under budget','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(693,132,9,12.0000,25.0000,'12 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(694,132,10,82.0000,80.0000,'Good attendance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(695,133,TRUE,6.0000,6.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(696,133,3,0.0000,0.0000,'No attrition','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(697,133,4,93.5000,95.0000,'Below SLA target','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(698,133,5,79.0000,80.0000,'Slightly below utilization','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(699,133,6,92.0000,95.0000,'Below time registration','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(700,133,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(701,133,8,200.0000,0.0000,'Slightly over budget','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(702,133,9,18.0000,25.0000,'18 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(703,133,10,77.0000,80.0000,'Below attendance target','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(704,134,TRUE,9.0000,9.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(705,134,3,0.0000,0.0000,'No attrition','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(706,134,4,98.7000,95.0000,'Excellent SLA','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(707,134,5,89.0000,80.0000,'High utilization','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(708,134,6,97.0000,95.0000,'Excellent time registration','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(709,134,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(710,134,8,-1800.0000,0.0000,'Well under budget','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(711,134,9,10.0000,25.0000,'10 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(712,134,10,91.0000,80.0000,'Excellent attendance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(713,135,TRUE,11.0000,11.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(714,135,3,1.0000,0.0000,'1 resignation this month','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(715,135,4,95.2000,95.0000,'Good SLA performance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(716,135,5,81.0000,80.0000,'Good utilization','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(717,135,6,94.0000,95.0000,'Slightly below time reg','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(718,135,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(719,135,8,500.0000,0.0000,'Slightly over budget','yellow',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(720,135,9,16.0000,25.0000,'16 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(721,135,10,84.0000,80.0000,'Good attendance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(722,136,TRUE,7.0000,7.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(723,136,3,0.0000,0.0000,'No attrition','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(724,136,4,97.1000,95.0000,'Good SLA performance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(725,136,5,86.0000,80.0000,'High utilization','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(726,136,6,96.0000,95.0000,'Good time registration','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(727,136,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(728,136,8,-600.0000,0.0000,'Under budget','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(729,136,9,14.0000,25.0000,'14 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(730,136,10,87.0000,80.0000,'Good attendance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(731,137,TRUE,13.0000,13.0000,'Team at target size','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(732,137,3,0.0000,0.0000,'No attrition','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(733,137,4,99.3000,95.0000,'Excellent SLA','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(734,137,5,90.0000,80.0000,'High utilization','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(735,137,6,98.0000,95.0000,'Excellent time registration','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(736,137,7,0.0000,0.0000,'No compliance issues','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(737,137,8,-2200.0000,0.0000,'Well under budget','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(738,137,9,9.0000,25.0000,'9 PTO days taken','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601'),(739,137,10,93.0000,80.0000,'Excellent attendance','green',FALSE,NULL,'2025-07-20 05:01:55.953601','2025-07-20 05:01:55.953601');


--
-- Table structure for table "monthly_dashboard_kpis"
--

DROP TABLE IF EXISTS "monthly_dashboard_kpis";


CREATE TABLE "monthly_dashboard_kpis" (
  "id" SERIAL,
  "name" varchar(100) NOT NULL,
  "display_name" varchar(150) NOT NULL,
  "description" TEXT,
  "target_value" decimal(10,4) DEFAULT NULL,
  "unit" varchar(50) DEFAULT NULL,
  "calculation_method" calculation_method_type NOT NULL DEFAULT 'manual',
  "help_text" TEXT NOT NULL,
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "sort_order" INTEGER NOT NULL DEFAULT '0',
  "traffic_light_green_min" decimal(5,2) NOT NULL DEFAULT '-5.00',
  "traffic_light_green_max" decimal(5,2) NOT NULL DEFAULT '5.00',
  "traffic_light_yellow_min" decimal(5,2) NOT NULL DEFAULT '-10.00',
  "traffic_light_yellow_max" decimal(5,2) NOT NULL DEFAULT '10.00',
  "special_rules" json DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id")
);


--
-- Dumping data for table "monthly_dashboard_kpis"
--



INSERT INTO "monthly_dashboard_kpis" VALUES (TRUE,'FTE','Full-Time Equivalents','Number of full-time equivalent employees in the team',NULL,'count','auto_fte','This field is automatically calculated based on the number of active team members. No manual input required.',TRUE,TRUE,-5.00,5.00,-10.00,10.00,NULL,'2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000'),(2,'COMPLETION_DATE','Dashboard Completion Date','Date when the team manager completed the monthly dashboard',NULL,'date','manual','Enter the date when you completed filling out this monthly dashboard. This should be around the 14th of the month.',TRUE,2,0.00,0.00,0.00,0.00,'{\"required\": true, \"validation\": \"date\", \"suggested_day\": 14}','2025-07-17 06:25:42.598218','2025-07-17 06:25:42.683310'),(3,'ATTRITION','Attrition Rate','Monthly resignations and annual attrition rate',0.0000,'count','manual','Enter the number of resignations this month. The system will calculate the annual attrition rate. Mark employees as \"resigned\" in the team management system.',TRUE,3,-5.00,5.00,-10.00,10.00,NULL,'2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000'),(4,'SLA','Service Level Agreement','Team SLA performance percentage',100.0000,'%','manual','Enter the SLA percentage for your team. Target is 100%. If no value is available, leave blank to display \"N/A\". Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Csla%20evidence%20-%20aeven%20-%20newco.qvw',TRUE,4,-5.00,5.00,-10.00,10.00,NULL,'2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000'),(5,'UTILIZATION','Billable Utilization','Team billable utilization percentage',NULL,'%','manual','Enter the team\'s billable utilization percentage. Target varies by team but typically 85%. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cbillable%20utilization%20-%20newco.qvw',TRUE,5,-5.00,5.00,-10.00,10.00,NULL,'2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000'),(6,'TIME_REGISTRATION','Time Registration','Timely time registration compliance percentage',100.0000,'%','manual','Enter the percentage of timely time registrations for your team. Target is 100%. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Cax%20time%20registration%20overview.qvw',TRUE,6,-5.00,5.00,-10.00,10.00,NULL,'2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000'),(7,'COMPLIANCE','Compliance','Number of non-compliance incidents',0.0000,'count','manual','Enter the number of non-compliance incidents for your team. Target is 0. Even 1 incident results in red status. Data received via email from compliance team.',TRUE,7,0.00,0.00,0.00,0.00,'{\"red_threshold\": 1}','2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000'),(8,'AB','Annual Budget vs Actual','Financial performance: Full Year 2025 vs Annual Budget',0.0000,'currency','manual','Enter the financial variance (Full Year 2025 vs AB) for your team. Find data at: http://nnitqlikdk01p.nnitcorp.com/QvAJAXZfc/opendoc.htm?document=userdocs%5Ccost%20-%20newco.qvw',TRUE,8,-5.00,5.00,-10.00,10.00,NULL,'2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000'),(9,'PTO','Paid Time Off','Vacation and sick leave utilization vs expected',NULL,'days','manual','Enter the number of PTO days taken this month. System calculates progress toward 75% of expected annual PTO by this point in the year. Data received via email from Finance.',TRUE,9,-5.00,5.00,-10.00,10.00,NULL,'2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000'),(10,'RTO','Return to Office','Office attendance compliance percentage',NULL,'%','manual','Enter raw office attendance percentage from Facility email. Also enter number of leave days to adjust for legitimate absences. Expected: employees 1x/week, managers 2x/week.',TRUE,10,-5.00,5.00,-10.00,10.00,NULL,'2025-07-17 06:25:42.598218','2025-07-19 05:29:38.000000');


--
-- Table structure for table "monthly_dashboard_submissions"
--

DROP TABLE IF EXISTS "monthly_dashboard_submissions";


CREATE TABLE "monthly_dashboard_submissions" (
  "id" SERIAL,
  "organizational_unit_id" INTEGER NOT NULL,
  "submitted_by_user_id" INTEGER NOT NULL,
  "submission_month" INTEGER NOT NULL,
  "submission_year" INTEGER NOT NULL,
  "submission_date" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "completion_date" date DEFAULT NULL,
  "status" status_type NOT NULL DEFAULT 'draft',
  "notes" TEXT,
  "approved_by_user_id" INTEGER DEFAULT NULL,
  "approved_at" timestamp NULL DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_009520ee8a8b8a54f0993aefdd8" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id"),
  CONSTRAINT "FK_6869e8e1fbefe120c4675f827bc" FOREIGN KEY ("submitted_by_user_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_ed0c8b11a5f4b791b8129e1a283" FOREIGN KEY ("approved_by_user_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "monthly_dashboard_submissions"
--



INSERT INTO "monthly_dashboard_submissions" VALUES (TRUE,15,5,TRUE,2025,'2025-07-17 03:10:55','2025-01-14','submitted','January 2025 dashboard - Frontend Development Team',NULL,NULL,'2025-07-17 06:25:43.625909','2025-07-17 06:25:43.748582'),(2,16,6,TRUE,2025,'2025-07-17 03:10:55','2025-01-15','submitted','January 2025 dashboard - Backend Development Team',NULL,NULL,'2025-07-17 06:25:43.625909','2025-07-17 06:25:43.748582'),(3,35,TRUE,TRUE,2025,'2025-07-17 03:10:55','2025-01-13','approved','January 2025 dashboard - Level 1 Support Team',NULL,NULL,'2025-07-17 06:25:43.625909','2025-07-17 06:25:43.748582'),(4,15,7,8,2024,'2024-08-15 00:00:00','2024-08-15','submitted','Sample submission for Frontend Development Team - 8/2024',NULL,NULL,'2025-07-19 05:19:05.897056','2025-07-19 05:19:05.897056'),(5,15,6,9,2024,'2024-09-12 00:00:00','2024-09-16','submitted','Sample submission for Frontend Development Team - 9/2024',NULL,NULL,'2025-07-19 05:19:06.104127','2025-07-19 05:19:06.104127'),(6,15,7,10,2024,'2024-10-16 00:00:00','2024-10-15','submitted','Sample submission for Frontend Development Team - 10/2024',NULL,NULL,'2025-07-19 05:19:06.261184','2025-07-19 05:19:06.261184'),(7,15,5,11,2024,'2024-11-13 00:00:00','2024-11-14','submitted','Sample submission for Frontend Development Team - 11/2024',NULL,NULL,'2025-07-19 05:19:06.404194','2025-07-19 05:19:06.404194'),(8,15,6,12,2024,'2024-12-16 00:00:00','2024-12-16','submitted','Sample submission for Frontend Development Team - 12/2024',NULL,NULL,'2025-07-19 05:19:06.525080','2025-07-19 05:19:06.525080'),(9,15,7,2,2025,'2025-02-16 00:00:00','2025-02-15','submitted','Sample submission for Frontend Development Team - 2/2025',NULL,NULL,'2025-07-19 05:19:06.652460','2025-07-19 05:19:06.652460'),(10,15,5,3,2025,'2025-03-16 00:00:00','2025-03-16','submitted','Sample submission for Frontend Development Team - 3/2025',NULL,NULL,'2025-07-19 05:19:06.779199','2025-07-19 05:19:06.779199'),(11,15,7,4,2025,'2025-04-14 00:00:00','2025-04-14','submitted','Sample submission for Frontend Development Team - 4/2025',NULL,NULL,'2025-07-19 05:19:06.880006','2025-07-19 05:19:06.880006'),(12,15,5,5,2025,'2025-05-12 00:00:00','2025-05-14','submitted','Sample submission for Frontend Development Team - 5/2025',NULL,NULL,'2025-07-19 05:19:06.988304','2025-07-19 05:19:06.988304'),(13,15,5,6,2025,'2025-06-15 00:00:00','2025-06-15','submitted','Sample submission for Frontend Development Team - 6/2025',NULL,NULL,'2025-07-19 05:19:07.124078','2025-07-19 05:19:07.124078'),(15,16,6,8,2024,'2024-08-15 00:00:00','2024-08-15','submitted','Sample submission for Backend Development Team - 8/2024',NULL,NULL,'2025-07-19 05:19:07.342163','2025-07-19 05:19:07.342163'),(16,16,5,9,2024,'2024-09-15 00:00:00','2024-09-16','submitted','Sample submission for Backend Development Team - 9/2024',NULL,NULL,'2025-07-19 05:19:07.448850','2025-07-19 05:19:07.448850'),(17,16,7,10,2024,'2024-10-14 00:00:00','2024-10-16','submitted','Sample submission for Backend Development Team - 10/2024',NULL,NULL,'2025-07-19 05:19:07.608062','2025-07-19 05:19:07.608062'),(18,16,5,11,2024,'2024-11-12 00:00:00','2024-11-15','submitted','Sample submission for Backend Development Team - 11/2024',NULL,NULL,'2025-07-19 05:19:07.712847','2025-07-19 05:19:07.712847'),(19,16,6,12,2024,'2024-12-14 00:00:00','2024-12-16','submitted','Sample submission for Backend Development Team - 12/2024',NULL,NULL,'2025-07-19 05:19:07.798253','2025-07-19 05:19:07.798253'),(20,16,6,2,2025,'2025-02-13 00:00:00','2025-02-15','submitted','Sample submission for Backend Development Team - 2/2025',NULL,NULL,'2025-07-19 05:19:07.907161','2025-07-19 05:19:07.907161'),(21,16,5,3,2025,'2025-03-12 00:00:00','2025-03-15','submitted','Sample submission for Backend Development Team - 3/2025',NULL,NULL,'2025-07-19 05:19:08.011522','2025-07-19 05:19:08.011522'),(22,16,5,4,2025,'2025-04-15 00:00:00','2025-04-15','submitted','Sample submission for Backend Development Team - 4/2025',NULL,NULL,'2025-07-19 05:19:08.106487','2025-07-19 05:19:08.106487'),(23,16,6,5,2025,'2025-05-15 00:00:00','2025-05-15','submitted','Sample submission for Backend Development Team - 5/2025',NULL,NULL,'2025-07-19 05:19:08.240556','2025-07-19 05:19:08.240556'),(24,16,5,6,2025,'2025-06-12 00:00:00','2025-06-14','submitted','Sample submission for Backend Development Team - 6/2025',NULL,NULL,'2025-07-19 05:19:08.336677','2025-07-19 05:19:08.336677'),(26,17,6,8,2024,'2024-08-15 00:00:00','2024-08-16','submitted','Sample submission for Mobile Development Team - 8/2024',NULL,NULL,'2025-07-19 05:19:08.535261','2025-07-19 05:19:08.535261'),(27,17,7,9,2024,'2024-09-14 00:00:00','2024-09-15','submitted','Sample submission for Mobile Development Team - 9/2024',NULL,NULL,'2025-07-19 05:19:08.624010','2025-07-19 05:19:08.624010'),(28,17,6,10,2024,'2024-10-14 00:00:00','2024-10-14','submitted','Sample submission for Mobile Development Team - 10/2024',NULL,NULL,'2025-07-19 05:19:08.715608','2025-07-19 05:19:08.715608'),(29,17,5,11,2024,'2024-11-12 00:00:00','2024-11-14','submitted','Sample submission for Mobile Development Team - 11/2024',NULL,NULL,'2025-07-19 05:19:08.805871','2025-07-19 05:19:08.805871'),(30,17,5,12,2024,'2024-12-15 00:00:00','2024-12-14','submitted','Sample submission for Mobile Development Team - 12/2024',NULL,NULL,'2025-07-19 05:19:08.921278','2025-07-19 05:19:08.921278'),(31,17,6,TRUE,2025,'2025-01-14 00:00:00','2025-01-14','submitted','Sample submission for Mobile Development Team - 1/2025',NULL,NULL,'2025-07-19 05:19:09.004194','2025-07-19 05:19:09.004194'),(32,17,6,2,2025,'2025-02-14 00:00:00','2025-02-14','submitted','Sample submission for Mobile Development Team - 2/2025',NULL,NULL,'2025-07-19 05:19:09.088364','2025-07-19 05:19:09.088364'),(33,17,7,3,2025,'2025-03-16 00:00:00','2025-03-14','submitted','Sample submission for Mobile Development Team - 3/2025',NULL,NULL,'2025-07-19 05:19:09.186163','2025-07-19 05:19:09.186163'),(34,17,7,4,2025,'2025-04-13 00:00:00','2025-04-15','submitted','Sample submission for Mobile Development Team - 4/2025',NULL,NULL,'2025-07-19 05:19:09.269430','2025-07-19 05:19:09.269430'),(35,17,5,5,2025,'2025-05-14 00:00:00','2025-05-15','submitted','Sample submission for Mobile Development Team - 5/2025',NULL,NULL,'2025-07-19 05:19:09.329825','2025-07-19 05:19:09.329825'),(36,17,5,6,2025,'2025-06-12 00:00:00','2025-06-16','submitted','Sample submission for Mobile Development Team - 6/2025',NULL,NULL,'2025-07-19 05:19:09.405145','2025-07-19 05:19:09.405145'),(38,18,5,8,2024,'2024-08-12 00:00:00','2024-08-16','submitted','Sample submission for Full-Stack Development Team - 8/2024',NULL,NULL,'2025-07-19 05:19:09.556734','2025-07-19 05:19:09.556734'),(39,18,6,9,2024,'2024-09-15 00:00:00','2024-09-16','submitted','Sample submission for Full-Stack Development Team - 9/2024',NULL,NULL,'2025-07-19 05:19:09.644597','2025-07-19 05:19:09.644597'),(40,18,7,10,2024,'2024-10-12 00:00:00','2024-10-15','submitted','Sample submission for Full-Stack Development Team - 10/2024',NULL,NULL,'2025-07-19 05:19:09.754517','2025-07-19 05:19:09.754517'),(41,18,6,11,2024,'2024-11-14 00:00:00','2024-11-14','submitted','Sample submission for Full-Stack Development Team - 11/2024',NULL,NULL,'2025-07-19 05:19:09.832541','2025-07-19 05:19:09.832541'),(42,18,7,12,2024,'2024-12-14 00:00:00','2024-12-16','submitted','Sample submission for Full-Stack Development Team - 12/2024',NULL,NULL,'2025-07-19 05:19:09.917053','2025-07-19 05:19:09.917053'),(43,18,7,TRUE,2025,'2025-01-14 00:00:00','2025-01-14','submitted','Sample submission for Full-Stack Development Team - 1/2025',NULL,NULL,'2025-07-19 05:19:10.059193','2025-07-19 05:19:10.059193'),(44,18,6,2,2025,'2025-02-15 00:00:00','2025-02-15','submitted','Sample submission for Full-Stack Development Team - 2/2025',NULL,NULL,'2025-07-19 05:19:10.163927','2025-07-19 05:19:10.163927'),(45,18,5,3,2025,'2025-03-13 00:00:00','2025-03-16','submitted','Sample submission for Full-Stack Development Team - 3/2025',NULL,NULL,'2025-07-19 05:19:10.246270','2025-07-19 05:19:10.246270'),(46,18,6,4,2025,'2025-04-16 00:00:00','2025-04-16','submitted','Sample submission for Full-Stack Development Team - 4/2025',NULL,NULL,'2025-07-19 05:19:10.331595','2025-07-19 05:19:10.331595'),(47,18,7,5,2025,'2025-05-16 00:00:00','2025-05-16','submitted','Sample submission for Full-Stack Development Team - 5/2025',NULL,NULL,'2025-07-19 05:19:10.416676','2025-07-19 05:19:10.416676'),(48,18,6,6,2025,'2025-06-14 00:00:00','2025-06-15','submitted','Sample submission for Full-Stack Development Team - 6/2025',NULL,NULL,'2025-07-19 05:19:10.504964','2025-07-19 05:19:10.504964'),(50,19,7,8,2024,'2024-08-13 00:00:00','2024-08-16','submitted','Sample submission for Data Engineering Team - 8/2024',NULL,NULL,'2025-07-19 05:19:10.707188','2025-07-19 05:19:10.707188'),(51,19,5,9,2024,'2024-09-16 00:00:00','2024-09-14','submitted','Sample submission for Data Engineering Team - 9/2024',NULL,NULL,'2025-07-19 05:19:10.804062','2025-07-19 05:19:10.804062'),(52,19,6,10,2024,'2024-10-12 00:00:00','2024-10-14','submitted','Sample submission for Data Engineering Team - 10/2024',NULL,NULL,'2025-07-19 05:19:10.895060','2025-07-19 05:19:10.895060'),(53,19,5,11,2024,'2024-11-15 00:00:00','2024-11-14','submitted','Sample submission for Data Engineering Team - 11/2024',NULL,NULL,'2025-07-19 05:19:10.976874','2025-07-19 05:19:10.976874'),(54,19,6,12,2024,'2024-12-12 00:00:00','2024-12-15','submitted','Sample submission for Data Engineering Team - 12/2024',NULL,NULL,'2025-07-19 05:19:11.063323','2025-07-19 05:19:11.063323'),(55,19,7,TRUE,2025,'2025-01-16 00:00:00','2025-01-16','submitted','Sample submission for Data Engineering Team - 1/2025',NULL,NULL,'2025-07-19 05:19:11.148192','2025-07-19 05:19:11.148192'),(56,19,5,2,2025,'2025-02-14 00:00:00','2025-02-16','submitted','Sample submission for Data Engineering Team - 2/2025',NULL,NULL,'2025-07-19 05:19:11.248091','2025-07-19 05:19:11.248091'),(57,19,6,3,2025,'2025-03-14 00:00:00','2025-03-15','submitted','Sample submission for Data Engineering Team - 3/2025',NULL,NULL,'2025-07-19 05:19:11.326085','2025-07-19 05:19:11.326085'),(58,19,5,4,2025,'2025-04-16 00:00:00','2025-04-15','submitted','Sample submission for Data Engineering Team - 4/2025',NULL,NULL,'2025-07-19 05:19:11.440661','2025-07-19 05:19:11.440661'),(59,19,7,5,2025,'2025-05-16 00:00:00','2025-05-16','submitted','Sample submission for Data Engineering Team - 5/2025',NULL,NULL,'2025-07-19 05:19:11.550520','2025-07-19 05:19:11.550520'),(60,19,7,6,2025,'2025-06-13 00:00:00','2025-06-14','submitted','Sample submission for Data Engineering Team - 6/2025',NULL,NULL,'2025-07-19 05:19:11.657513','2025-07-19 05:19:11.657513'),(62,53,5,12,2024,'2024-12-15 10:00:00','2024-12-14','submitted','December 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(63,53,5,11,2024,'2024-11-15 10:00:00','2024-11-14','submitted','November 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(64,53,5,10,2024,'2024-10-15 10:00:00','2024-10-14','submitted','October 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(65,53,5,9,2024,'2024-09-15 10:00:00','2024-09-14','submitted','September 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(66,53,5,8,2024,'2024-08-15 10:00:00','2024-08-14','submitted','August 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(67,53,5,7,2024,'2024-07-15 10:00:00','2024-07-14','submitted','July 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(68,53,5,6,2025,'2025-06-15 10:00:00','2025-06-14','submitted','June 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(69,53,5,5,2025,'2025-05-15 10:00:00','2025-05-14','submitted','May 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(70,53,5,4,2025,'2025-04-15 10:00:00','2025-04-14','submitted','April 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(71,53,5,3,2025,'2025-03-15 10:00:00','2025-03-14','submitted','March 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(72,53,5,2,2025,'2025-02-15 10:00:00','2025-02-14','submitted','February 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(73,53,5,TRUE,2025,'2025-01-15 10:00:00','2025-01-14','submitted','January 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(74,54,6,12,2024,'2024-12-16 14:00:00','2024-12-16','submitted','December 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(75,54,6,11,2024,'2024-11-16 14:00:00','2024-11-16','submitted','November 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(76,54,6,10,2024,'2024-10-16 14:00:00','2024-10-16','submitted','October 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(77,54,6,9,2024,'2024-09-16 14:00:00','2024-09-16','submitted','September 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(78,54,6,8,2024,'2024-08-16 14:00:00','2024-08-16','submitted','August 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(79,54,6,7,2024,'2024-07-16 14:00:00','2024-07-16','submitted','July 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(80,54,6,6,2025,'2025-06-16 14:00:00','2025-06-16','submitted','June 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(81,54,6,5,2025,'2025-05-16 14:00:00','2025-05-16','submitted','May 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(82,54,6,4,2025,'2025-04-16 14:00:00','2025-04-16','submitted','April 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(83,54,6,3,2025,'2025-03-16 14:00:00','2025-03-16','submitted','March 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(84,54,6,2,2025,'2025-02-16 14:00:00','2025-02-16','submitted','February 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(85,54,6,TRUE,2025,'2025-01-16 14:00:00','2025-01-16','submitted','January 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(86,55,9,12,2024,'2024-12-14 09:00:00','2024-12-13','submitted','December 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(87,55,9,11,2024,'2024-11-14 09:00:00','2024-11-13','submitted','November 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(88,55,9,10,2024,'2024-10-14 09:00:00','2024-10-13','submitted','October 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(89,55,9,9,2024,'2024-09-14 09:00:00','2024-09-13','submitted','September 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(90,55,9,8,2024,'2024-08-14 09:00:00','2024-08-13','submitted','August 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(91,55,9,7,2024,'2024-07-14 09:00:00','2024-07-13','submitted','July 2024 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(92,55,9,6,2025,'2025-06-14 09:00:00','2025-06-13','submitted','June 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(93,55,9,5,2025,'2025-05-14 09:00:00','2025-05-13','submitted','May 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(94,55,9,4,2025,'2025-04-14 09:00:00','2025-04-13','submitted','April 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(95,55,9,3,2025,'2025-03-14 09:00:00','2025-03-13','submitted','March 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(96,55,9,2,2025,'2025-02-14 09:00:00','2025-02-13','submitted','February 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(97,55,9,TRUE,2025,'2025-01-14 09:00:00','2025-01-13','submitted','January 2025 submission',NULL,NULL,'2025-07-19 05:43:46.000000','2025-07-19 05:43:46.000000'),(128,15,5,7,2025,'2025-07-20 05:01:55','2025-07-14','submitted','July 2025 dashboard - Frontend Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(129,16,6,7,2025,'2025-07-20 05:01:55','2025-07-13','approved','July 2025 dashboard - Backend Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(130,17,7,7,2025,'2025-07-20 05:01:55','2025-07-15','submitted','July 2025 dashboard - Mobile Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(131,18,8,7,2025,'2025-07-20 05:01:55','2025-07-12','approved','July 2025 dashboard - Full-Stack Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(132,19,9,7,2025,'2025-07-20 05:01:55','2025-07-16','submitted','July 2025 dashboard - Data Engineering Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(133,20,10,7,2025,'2025-07-20 05:01:55','2025-07-14','submitted','July 2025 dashboard - BI Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(134,21,5,7,2025,'2025-07-20 05:01:55','2025-07-11','approved','July 2025 dashboard - ML Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(135,22,6,7,2025,'2025-07-20 05:01:55','2025-07-17','submitted','July 2025 dashboard - Cloud Infrastructure Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(136,23,7,7,2025,'2025-07-20 05:01:55','2025-07-13','submitted','July 2025 dashboard - DevOps Automation Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558'),(137,24,8,7,2025,'2025-07-20 05:01:55','2025-07-10','approved','July 2025 dashboard - Platform Engineering Team',NULL,NULL,'2025-07-20 05:01:55.947558','2025-07-20 05:01:55.947558');


--
-- Table structure for table "monthly_dashboard_team_targets"
--

DROP TABLE IF EXISTS "monthly_dashboard_team_targets";


CREATE TABLE "monthly_dashboard_team_targets" (
  "id" SERIAL,
  "organizational_unit_id" INTEGER NOT NULL,
  "kpi_id" INTEGER NOT NULL,
  "target_value" decimal(12,4) NOT NULL,
  "effective_from" date NOT NULL,
  "effective_to" date DEFAULT NULL,
  "notes" TEXT,
  "created_by_user_id" INTEGER NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_0f358b50d069de847e1904670df" FOREIGN KEY ("created_by_user_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_43f30f3b953f185972b09c43f4e" FOREIGN KEY ("kpi_id") REFERENCES "monthly_dashboard_kpis" ("id"),
  CONSTRAINT "FK_83795420061155c338eed27cc4d" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id")
);


--
-- Dumping data for table "monthly_dashboard_team_targets"
--



INSERT INTO "monthly_dashboard_team_targets" VALUES (4,15,5,85.0000,'2025-01-01',NULL,'Frontend Development team target utilization',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(5,16,5,87.0000,'2025-01-01',NULL,'Backend Development team target utilization',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(6,17,5,80.0000,'2025-01-01',NULL,'Mobile Development team target utilization',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(7,19,5,82.0000,'2025-01-01',NULL,'Data Engineering team target utilization',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(8,35,5,75.0000,'2025-01-01',NULL,'Level 1 Support team target utilization',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(9,27,5,90.0000,'2025-01-01',NULL,'Process Optimization team target utilization',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(10,15,9,25.0000,'2025-01-01',NULL,'Annual PTO days per employee - Frontend Development',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(11,16,9,25.0000,'2025-01-01',NULL,'Annual PTO days per employee - Backend Development',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(12,17,9,25.0000,'2025-01-01',NULL,'Annual PTO days per employee - Mobile Development',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(13,19,9,25.0000,'2025-01-01',NULL,'Annual PTO days per employee - Data Engineering',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(14,35,9,25.0000,'2025-01-01',NULL,'Annual PTO days per employee - Level 1 Support',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(15,27,9,25.0000,'2025-01-01',NULL,'Annual PTO days per employee - Process Optimization',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(16,15,10,80.0000,'2025-01-01',NULL,'Expected office attendance percentage - Frontend Development',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(17,16,10,80.0000,'2025-01-01',NULL,'Expected office attendance percentage - Backend Development',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(18,17,10,85.0000,'2025-01-01',NULL,'Expected office attendance percentage - Mobile Development',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(19,19,10,75.0000,'2025-01-01',NULL,'Expected office attendance percentage - Data Engineering',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(20,35,10,90.0000,'2025-01-01',NULL,'Expected office attendance percentage - Level 1 Support',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267'),(21,27,10,85.0000,'2025-01-01',NULL,'Expected office attendance percentage - Process Optimization',TRUE,'2025-07-17 06:25:41.669905','2025-07-17 06:25:41.750267');


--
-- Table structure for table "organizational_units"
--

DROP TABLE IF EXISTS "organizational_units";


CREATE TABLE "organizational_units" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "type" type_type NOT NULL DEFAULT 'team',
  "description" TEXT,
  "parent_id" INTEGER DEFAULT NULL,
  "level" INTEGER NOT NULL DEFAULT '0',
  "manager_id" INTEGER DEFAULT NULL,
  "budget" decimal(15,2) NOT NULL DEFAULT '0.00',
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_10fdfd80d39690289d9b9b9d08f" FOREIGN KEY ("parent_id") REFERENCES "organizational_units" ("id"),
  CONSTRAINT "FK_e2f5cbe464f58a8b506d5cdd7cd" FOREIGN KEY ("manager_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "organizational_units"
--



INSERT INTO "organizational_units" VALUES (TRUE,'TrustHansen Technologies','organization','Main organization - IT consulting and development company',NULL,FALSE,TRUE,10000000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(2,'Technology Division','division','Core technology development and engineering',TRUE,TRUE,2,5000000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(3,'Consulting Division','division','Client consulting and professional services',TRUE,TRUE,3,3000000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(4,'Operations Division','division','IT operations, support, and infrastructure',TRUE,TRUE,4,2000000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(5,'Software Development','department','Custom software development and applications',2,2,NULL,2000000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(6,'Data & Analytics','department','Data science, analytics, and BI solutions',2,2,NULL,1500000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(7,'DevOps & Infrastructure','department','DevOps, cloud infrastructure, and automation',2,2,NULL,1000000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(8,'Quality Assurance','department','Software testing and quality assurance',2,2,NULL,500000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(9,'Business Consulting','department','Business process consulting and optimization',3,2,NULL,1500000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(10,'Technical Consulting','department','Technical architecture and implementation consulting',3,2,NULL,1000000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(11,'Project Management','department','Project management and delivery services',3,2,NULL,500000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(12,'IT Support','department','Technical support and helpdesk services',4,2,NULL,800000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(13,'System Administration','department','System administration and maintenance',4,2,NULL,600000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(14,'Security & Compliance','department','Information security and compliance management',4,2,NULL,600000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(15,'Frontend Development Team','team','React, Angular, and frontend technologies',5,3,5,600000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(16,'Backend Development Team','team','Node.js, .NET, and backend services',5,3,6,700000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(17,'Mobile Development Team','team','iOS and Android mobile applications',5,3,7,400000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(18,'Full-Stack Development Team','team','End-to-end application development',5,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(19,'Data Engineering Team','team','Data pipelines and ETL processes',6,3,8,500000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(20,'Business Intelligence Team','team','BI dashboards and reporting solutions',6,3,NULL,400000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(21,'Machine Learning Team','team','AI/ML models and data science',6,3,NULL,600000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(22,'Cloud Infrastructure Team','team','AWS, Azure, and cloud services',7,3,9,400000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(23,'DevOps Automation Team','team','CI/CD pipelines and automation',7,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(24,'Platform Engineering Team','team','Internal platforms and tooling',7,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(25,'Manual Testing Team','team','Manual testing and user acceptance testing',8,3,10,200000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(26,'Automation Testing Team','team','Test automation and performance testing',8,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(27,'Process Optimization Team','team','Business process analysis and optimization',9,3,NULL,500000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(28,'Digital Transformation Team','team','Digital transformation consulting',9,3,NULL,600000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(29,'Change Management Team','team','Organizational change management',9,3,NULL,400000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(30,'Solution Architecture Team','team','Technical solution design and architecture',10,3,NULL,500000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(31,'Integration Services Team','team','System integration and API development',10,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(32,'Performance Optimization Team','team','System performance and optimization',10,3,NULL,200000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(33,'Agile Delivery Team','team','Agile project management and Scrum',11,3,NULL,250000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(34,'Enterprise PMO Team','team','Enterprise project management office',11,3,NULL,250000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(35,'Level 1 Support Team','team','First-line technical support',12,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(36,'Level 2 Support Team','team','Advanced technical support',12,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(37,'Customer Success Team','team','Customer success and account management',12,3,NULL,200000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(38,'Server Administration Team','team','Server management and maintenance',13,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(39,'Network Administration Team','team','Network infrastructure and management',13,3,NULL,300000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(40,'Information Security Team','team','Cybersecurity and threat management',14,3,NULL,400000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(41,'Compliance & Audit Team','team','Regulatory compliance and internal audits',14,3,NULL,200000.00,TRUE,'2025-07-17 06:25:20.892391','2025-07-17 06:25:21.003322'),(42,'Frontend Development','team','Frontend Development under Technology Division',2,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.794367','2025-07-19 04:54:46.794367'),(43,'Backend Development','team','Backend Development under Technology Division',2,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.815893','2025-07-19 04:54:46.815893'),(44,'DevOps','team','DevOps under Technology Division',2,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.829853','2025-07-19 04:54:46.829853'),(45,'Customer Support','team','Customer Support under Operations Division',4,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.848478','2025-07-19 04:54:46.848478'),(46,'Quality Assurance','team','Quality Assurance under Operations Division',4,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.862534','2025-07-19 04:54:46.862534'),(47,'Infrastructure','team','Infrastructure under Operations Division',4,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.880667','2025-07-19 04:54:46.880667'),(48,'Business Division','division','Sales and Marketing teams',NULL,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.898128','2025-07-19 04:54:46.898128'),(49,'Sales Team','team','Sales Team under Business Division',48,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.914100','2025-07-19 04:54:46.914100'),(50,'Marketing Team','team','Marketing Team under Business Division',48,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.930882','2025-07-19 04:54:46.930882'),(51,'Business Development','team','Business Development under Business Division',48,FALSE,NULL,0.00,TRUE,'2025-07-19 04:54:46.943820','2025-07-19 04:54:46.943820'),(52,'Technology Division','division','IT and Development teams',NULL,FALSE,NULL,0.00,TRUE,'2025-07-19 05:26:49.901394','2025-07-19 05:26:49.901394'),(53,'Frontend Development','team','Frontend development team',52,TRUE,NULL,0.00,TRUE,'2025-07-19 05:27:06.363091','2025-07-19 05:27:06.363091'),(54,'Backend Development','team','Backend development team',52,TRUE,NULL,0.00,TRUE,'2025-07-19 05:27:23.224578','2025-07-19 05:27:23.224578'),(55,'DevOps Team','team','DevOps and infrastructure team',52,TRUE,NULL,0.00,TRUE,'2025-07-19 05:27:23.356296','2025-07-19 05:27:23.356296'),(56,'CHOI','division','CHOI Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(57,'2nd Line Global','team','2nd Line Global Support Team',56,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(58,'Patch Planning with Server Order','team','Patch Planning and Server Order Team',56,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(59,'Change Management - SLM','team','Change Management and SLM Team',56,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(60,'Automation & AI','team','Automation and AI Team',56,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(61,'THNQ','division','THNQ Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(62,'IAM Operations','team','Identity and Access Management Operations',61,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(63,'Security Operations','team','Security Operations Team',61,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(64,'Network Operations & NetScaler','team','Network Operations and NetScaler Team',61,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(65,'ALDW','division','ALDW Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(66,'AIS Operations','team','AIS Operations Team',65,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(67,'Windows Services','team','Windows Services Team',65,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(68,'Unix/Linux Services','team','Unix/Linux Services Team',65,FALSE,NULL,0.00,TRUE,'2025-07-20 04:55:17.008223','2025-07-20 04:55:17.008223'),(69,'CHOI','division','CHOI Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(70,'2nd Line Global','team','2nd Line Global Support Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(71,'Patch Planning with Server Order','team','Patch Planning and Server Order Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(72,'Change Management - SLM','team','Change Management and SLM Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(73,'Automation & AI','team','Automation and AI Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(74,'THNQ','division','THNQ Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(75,'IAM Operations','team','Identity and Access Management Operations',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(76,'Security Operations','team','Security Operations Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(77,'Network Operations & NetScaler','team','Network Operations and NetScaler Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(78,'ALDW','division','ALDW Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(79,'AIS Operations','team','AIS Operations Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(80,'Windows Services','team','Windows Services Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(81,'Unix/Linux Services','team','Unix/Linux Services Team',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:56:17.383569','2025-07-20 04:56:17.383569'),(82,'CHOI','division','CHOI Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.280355','2025-07-20 04:57:12.280355'),(83,'THNQ','division','THNQ Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.280355','2025-07-20 04:57:12.280355'),(84,'ALDW','division','ALDW Division',NULL,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.280355','2025-07-20 04:57:12.280355'),(85,'2nd Line Global','team','2nd Line Global Support Team',56,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(86,'Patch Planning with Server Order','team','Patch Planning and Server Order Team',56,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(87,'Change Management - SLM','team','Change Management and SLM Team',56,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(88,'Automation & AI','team','Automation and AI Team',56,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(89,'IAM Operations','team','Identity and Access Management Operations',61,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(90,'Security Operations','team','Security Operations Team',61,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(91,'Network Operations & NetScaler','team','Network Operations and NetScaler Team',61,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(92,'AIS Operations','team','AIS Operations Team',65,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(93,'Windows Services','team','Windows Services Team',65,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942'),(94,'Unix/Linux Services','team','Unix/Linux Services Team',65,FALSE,NULL,0.00,TRUE,'2025-07-20 04:57:12.287942','2025-07-20 04:57:12.287942');


--
-- Table structure for table "performance_metrics"
--

DROP TABLE IF EXISTS "performance_metrics";


CREATE TABLE "performance_metrics" (
  "id" SERIAL,
  "user_id" INTEGER DEFAULT NULL,
  "organizational_unit_id" INTEGER DEFAULT NULL,
  "metric_type" metric_type_type NOT NULL,
  "metric_name" varchar(255) NOT NULL,
  "metric_value" decimal(10,4) DEFAULT NULL,
  "period_start" date DEFAULT NULL,
  "period_end" date DEFAULT NULL,
  "metadata" json DEFAULT NULL,
  "calculation_date" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_7b02ef6e66cfdf97afc51cb5d93" FOREIGN KEY ("user_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_afa373e69298874223b101e5b83" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id")
);


--
-- Dumping data for table "performance_metrics"
--



INSERT INTO "performance_metrics" VALUES (TRUE,TRUE,NULL,'individual','overall_performance_score',8.5000,'2024-01-01','2024-03-31','{\"areas\": [\"technical\", \"communication\", \"leadership\"], \"assessment_id\": 1}','2025-07-17 06:25:41.065519'),(2,2,NULL,'individual','overall_performance_score',7.8000,'2024-01-01','2024-03-31','{\"areas\": [\"technical\", \"communication\", \"teamwork\"], \"assessment_id\": 2}','2025-07-17 06:25:41.065519'),(3,TRUE,NULL,'individual','engagement_score',8.2000,'2024-01-01','2024-03-31','{\"survey_id\": TRUE, \"response_rate\": 100}','2025-07-17 06:25:41.065519'),(4,2,NULL,'individual','engagement_score',7.5000,'2024-01-01','2024-03-31','{\"survey_id\": TRUE, \"response_rate\": 100}','2025-07-17 06:25:41.065519'),(5,NULL,NULL,'organization','average_engagement_score',8.3000,'2024-01-01','2024-03-31','{\"response_rate\": 95}','2025-07-17 06:25:41.065519'),(6,NULL,NULL,'organization','average_performance_score',8.1500,'2024-01-01','2024-03-31','{\"completion_rate\": 100}','2025-07-17 06:25:41.065519');


--
-- Table structure for table "project_assignments"
--

DROP TABLE IF EXISTS "project_assignments";


CREATE TABLE "project_assignments" (
  "id" SERIAL,
  "project_id" INTEGER NOT NULL,
  "user_id" INTEGER NOT NULL,
  "role" varchar(255) NOT NULL,
  "allocation_percentage" decimal(5,2) DEFAULT '100.00',
  "start_date" date DEFAULT NULL,
  "end_date" date DEFAULT NULL,
  "hourly_rate" decimal(8,2) DEFAULT NULL,
  "is_active" BOOLEAN NOT NULL DEFAULT '1',
  "created_at" timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  UNIQUE ("project_id", "user_id"),
  CONSTRAINT "project_assignments_ibfk_1" FOREIGN KEY ("project_id") REFERENCES "projects" ("id") ON DELETE CASCADE,
  CONSTRAINT "project_assignments_ibfk_2" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE
);


--
-- Dumping data for table "project_assignments"
--





--
-- Table structure for table "projects"
--

DROP TABLE IF EXISTS "projects";


CREATE TABLE "projects" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "description" TEXT,
  "client_name" varchar(255) DEFAULT NULL,
  "organizational_unit_id" INTEGER DEFAULT NULL,
  "project_manager_id" INTEGER DEFAULT NULL,
  "status" status_type NOT NULL DEFAULT 'planning',
  "start_date" date DEFAULT NULL,
  "end_date" date DEFAULT NULL,
  "budget" decimal(15,2) DEFAULT NULL,
  "priority" priority_type NOT NULL DEFAULT 'medium',
  "created_at" timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "projects_ibfk_1" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id") ON DELETE SET NULL,
  CONSTRAINT "projects_ibfk_2" FOREIGN KEY ("project_manager_id") REFERENCES "users" ("id") ON DELETE SET NULL
);


--
-- Dumping data for table "projects"
--





--
-- Table structure for table "recognition_badges"
--

DROP TABLE IF EXISTS "recognition_badges";


CREATE TABLE "recognition_badges" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "description" TEXT,
  "icon_url" varchar(500) DEFAULT NULL,
  "badge_type" badge_type_type NOT NULL,
  "point_value" INTEGER NOT NULL DEFAULT '0',
  "criteria" json DEFAULT NULL,
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "created_by_id" INTEGER NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_463967502d05380822e779de971" FOREIGN KEY ("created_by_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "recognition_badges"
--



INSERT INTO "recognition_badges" VALUES (TRUE,'Team Player','Awarded for exceptional collaboration and teamwork','/icons/team-player.svg','appreciation',50,'{\"criteria\": \"Demonstrates outstanding collaboration skills\"}',TRUE,TRUE,'2025-07-17 06:25:41.945637'),(2,'Innovation Champion','Recognizes creative problem-solving and innovative thinking','/icons/innovation.svg','achievement',100,'{\"criteria\": \"Introduces innovative solutions or processes\"}',TRUE,TRUE,'2025-07-17 06:25:41.945637'),(3,'Mentor','Acknowledges dedication to helping others grow and learn','/icons/mentor.svg','skill',75,'{\"criteria\": \"Actively mentors colleagues and shares knowledge\"}',TRUE,TRUE,'2025-07-17 06:25:41.945637'),(4,'Problem Solver','Awarded for tackling complex challenges with creative solutions','/icons/problem-solver.svg','achievement',70,'{\"criteria\": \"Solves complex technical or business problems\"}',TRUE,TRUE,'2025-07-17 06:25:41.945637'),(5,'Code Quality Champion','Awarded for exceptional code quality',NULL,'achievement',100,NULL,TRUE,TRUE,'2025-07-31 10:32:40.697975'),(6,'Team Player','Awarded for outstanding collaboration',NULL,'appreciation',75,NULL,TRUE,TRUE,'2025-07-31 10:32:40.697975'),(7,'Innovation Leader','Awarded for innovative solutions',NULL,'skill',150,NULL,TRUE,TRUE,'2025-07-31 10:32:40.697975'),(8,'Milestone Master','Awarded for meeting project milestones',NULL,'milestone',50,NULL,TRUE,TRUE,'2025-07-31 10:32:40.697975'),(9,'Code Quality Champion','Awarded for exceptional code quality',NULL,'achievement',100,NULL,TRUE,TRUE,'2025-07-31 10:33:09.833623'),(10,'Team Player','Awarded for outstanding collaboration',NULL,'appreciation',75,NULL,TRUE,TRUE,'2025-07-31 10:33:09.833623'),(11,'Innovation Leader','Awarded for innovative solutions',NULL,'skill',150,NULL,TRUE,TRUE,'2025-07-31 10:33:09.833623'),(12,'Milestone Master','Awarded for meeting project milestones',NULL,'milestone',50,NULL,TRUE,TRUE,'2025-07-31 10:33:09.833623');


--
-- Table structure for table "recognition_instances"
--

DROP TABLE IF EXISTS "recognition_instances";


CREATE TABLE "recognition_instances" (
  "id" SERIAL,
  "badge_id" INTEGER DEFAULT NULL,
  "giver_id" INTEGER NOT NULL,
  "receiver_id" INTEGER NOT NULL,
  "message" TEXT,
  "points_awarded" INTEGER NOT NULL DEFAULT '0',
  "is_public" SMALLINT NOT NULL DEFAULT '1',
  "given_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_0993234f752c40abec8ad0540a9" FOREIGN KEY ("giver_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_5463413d7c865d00d7eb9485cd4" FOREIGN KEY ("receiver_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_a120632d525463eafac25b2a003" FOREIGN KEY ("badge_id") REFERENCES "recognition_badges" ("id")
);


--
-- Dumping data for table "recognition_instances"
--



INSERT INTO "recognition_instances" VALUES (TRUE,TRUE,2,TRUE,'Outstanding collaboration on the Q1 project delivery. Your teamwork made all the difference!',50,TRUE,'2025-07-17 06:25:41.782584'),(2,2,TRUE,2,'Your innovative approach to the API optimization saved us weeks of development time. Brilliant work!',100,TRUE,'2025-07-17 06:25:41.782584'),(3,3,TRUE,2,'Thank you for taking the time to mentor our new team members. Your guidance is invaluable.',75,TRUE,'2025-07-17 06:25:41.782584'),(4,4,2,TRUE,'Incredible problem-solving on the database performance issue. You saved the day!',70,TRUE,'2025-07-17 06:25:41.782584');


--
-- Table structure for table "scoring_rules"
--

DROP TABLE IF EXISTS "scoring_rules";


CREATE TABLE "scoring_rules" (
  "id" SERIAL,
  "area_id" INTEGER NOT NULL,
  "rule_type" rule_type_type NOT NULL,
  "condition_value" varchar(255) DEFAULT NULL,
  "condition_field" varchar(255) DEFAULT NULL,
  "condition_operator" condition_operator_type DEFAULT NULL,
  "score_adjustment" INTEGER NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_1b35264441cefdf7133a1c17bb2" FOREIGN KEY ("area_id") REFERENCES "assessment_areas" ("id")
);


--
-- Dumping data for table "scoring_rules"
--





--
-- Table structure for table "security_audit_log"
--

DROP TABLE IF EXISTS "security_audit_log";


CREATE TABLE "security_audit_log" (
  "id" BIGSERIAL,
  "event_type" varchar(50) NOT NULL,
  "user_id" INTEGER DEFAULT NULL,
  "email" varchar(255) DEFAULT NULL,
  "ip_address" varchar(45) DEFAULT NULL,
  "user_agent" TEXT,
  "session_id" varchar(255) DEFAULT NULL,
  "event_details" json DEFAULT NULL,
  "severity" severity_type NOT NULL DEFAULT 'LOW',
  "timestamp" timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "security_audit_log_ibfk_1" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE SET NULL
);


--
-- Dumping data for table "security_audit_log"
--





--
-- Table structure for table "skillsets"
--

DROP TABLE IF EXISTS "skillsets";


CREATE TABLE "skillsets" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "category" category_type NOT NULL,
  "description" TEXT,
  "level_required" level_required_type NOT NULL DEFAULT 'intermediate',
  "is_core_skill" SMALLINT NOT NULL DEFAULT '0',
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  UNIQUE ("name")
);


--
-- Dumping data for table "skillsets"
--



INSERT INTO "skillsets" VALUES (TRUE,'JavaScript','programming','JavaScript programming language','intermediate',FALSE,TRUE,'2025-07-31 10:27:23.624715','2025-07-31 10:27:23.624715'),(2,'TypeScript','programming','TypeScript programming language','intermediate',FALSE,TRUE,'2025-07-31 10:27:23.624715','2025-07-31 10:27:23.624715'),(3,'React','frontend','React frontend framework','intermediate',FALSE,TRUE,'2025-07-31 10:27:23.624715','2025-07-31 10:27:23.624715'),(4,'Node.js','backend','Node.js backend runtime','intermediate',FALSE,TRUE,'2025-07-31 10:27:23.624715','2025-07-31 10:27:23.624715'),(5,'MySQL','database','MySQL database management','intermediate',FALSE,TRUE,'2025-07-31 10:27:23.624715','2025-07-31 10:27:23.624715'),(6,'Project Management','','Project planning and execution','intermediate',FALSE,TRUE,'2025-07-31 10:27:23.624715','2025-07-31 10:27:23.624715'),(7,'Team Leadership','','Leading and managing teams','intermediate',FALSE,TRUE,'2025-07-31 10:27:23.624715','2025-07-31 10:27:23.624715');


--
-- Table structure for table "survey_responses"
--

DROP TABLE IF EXISTS "survey_responses";


CREATE TABLE "survey_responses" (
  "id" SERIAL,
  "survey_id" INTEGER NOT NULL,
  "respondent_id" INTEGER DEFAULT NULL,
  "responses" json NOT NULL,
  "completion_time" INTEGER DEFAULT NULL,
  "ip_address" varchar(45) DEFAULT NULL,
  "user_agent" TEXT,
  "submitted_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_2b4e3f83ce0b4a0d7617ac0cd44" FOREIGN KEY ("survey_id") REFERENCES "engagement_surveys" ("id"),
  CONSTRAINT "FK_38d2d02c8ec65ccd8ab29131d13" FOREIGN KEY ("respondent_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "survey_responses"
--



INSERT INTO "survey_responses" VALUES (TRUE,TRUE,TRUE,'{\"1\": 8, \"2\": 9}',180,NULL,NULL,'2025-07-17 06:25:41.145843'),(2,TRUE,2,'{\"1\": 7, \"2\": 8}',165,NULL,NULL,'2025-07-17 06:25:41.145843'),(3,2,TRUE,'{\"1\": 4, \"2\": 4}',320,NULL,NULL,'2025-07-17 06:25:41.145843'),(4,2,2,'{\"1\": 3, \"2\": 4}',285,NULL,NULL,'2025-07-17 06:25:41.145843');


--
-- Table structure for table "team_members"
--

DROP TABLE IF EXISTS "team_members";


CREATE TABLE "team_members" (
  "id" SERIAL,
  "team_id" INTEGER NOT NULL,
  "user_id" INTEGER NOT NULL,
  "role" role_type NOT NULL DEFAULT 'member',
  "added_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_c2bf4967c8c2a6b845dadfbf3d4" FOREIGN KEY ("user_id") REFERENCES "users" ("id"),
  CONSTRAINT "FK_fdad7d5768277e60c40e01cdcea" FOREIGN KEY ("team_id") REFERENCES "teams" ("id")
);


--
-- Dumping data for table "team_members"
--





--
-- Table structure for table "teams"
--

DROP TABLE IF EXISTS "teams";


CREATE TABLE "teams" (
  "id" SERIAL,
  "name" varchar(255) NOT NULL,
  "description" TEXT,
  "created_by" INTEGER NOT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "FK_e9998d2ac53a30bf287cb328b26" FOREIGN KEY ("created_by") REFERENCES "users" ("id")
);


--
-- Dumping data for table "teams"
--



INSERT INTO "teams" VALUES (TRUE,'Frontend Team','Responsible for user interface development',2,'2025-07-31 10:27:23.631569','2025-07-31 10:27:23.631569'),(2,'Backend Team','Responsible for server-side development',2,'2025-07-31 10:27:23.631569','2025-07-31 10:27:23.631569'),(3,'DevOps Team','Responsible for deployment and infrastructure',2,'2025-07-31 10:27:23.631569','2025-07-31 10:27:23.631569'),(4,'Frontend Team','Responsible for user interface development',2,'2025-07-31 10:32:40.691863','2025-07-31 10:32:40.691863'),(5,'Backend Team','Responsible for server-side development',2,'2025-07-31 10:32:40.691863','2025-07-31 10:32:40.691863'),(6,'DevOps Team','Responsible for deployment and infrastructure',2,'2025-07-31 10:32:40.691863','2025-07-31 10:32:40.691863'),(7,'Frontend Team','Responsible for user interface development',2,'2025-07-31 10:33:09.830629','2025-07-31 10:33:09.830629'),(8,'Backend Team','Responsible for server-side development',2,'2025-07-31 10:33:09.830629','2025-07-31 10:33:09.830629'),(9,'DevOps Team','Responsible for deployment and infrastructure',2,'2025-07-31 10:33:09.830629','2025-07-31 10:33:09.830629');


--
-- Table structure for table "user_gamification"
--

DROP TABLE IF EXISTS "user_gamification";


CREATE TABLE "user_gamification" (
  "id" SERIAL,
  "user_id" INTEGER NOT NULL,
  "total_points" INTEGER NOT NULL DEFAULT '0',
  "current_level" INTEGER NOT NULL DEFAULT '1',
  "badges_earned" INTEGER NOT NULL DEFAULT '0',
  "recognitions_given" INTEGER NOT NULL DEFAULT '0',
  "recognitions_received" INTEGER NOT NULL DEFAULT '0',
  "last_activity_date" date DEFAULT NULL,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  UNIQUE ("user_id"),
  UNIQUE ("user_id"),
  CONSTRAINT "FK_dbbacb32749a0f1b1639e1f6c1d" FOREIGN KEY ("user_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "user_gamification"
--



INSERT INTO "user_gamification" VALUES (TRUE,TRUE,195,3,2,2,2,'2024-03-01','2025-07-17 06:25:40.153242'),(2,2,175,3,2,2,2,'2024-03-01','2025-07-17 06:25:40.153242');


--
-- Table structure for table "user_sessions"
--

DROP TABLE IF EXISTS "user_sessions";


CREATE TABLE "user_sessions" (
  "id" SERIAL,
  "user_id" INTEGER NOT NULL,
  "session_token" TEXT NOT NULL,
  "ip_address" varchar(45) DEFAULT NULL,
  "user_agent" TEXT,
  "expires_at" timestamp NOT NULL,
  "is_active" BOOLEAN NOT NULL DEFAULT '1',
  "created_at" timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamp NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  CONSTRAINT "user_sessions_ibfk_1" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE
);


--
-- Dumping data for table "user_sessions"
--





--
-- Table structure for table "user_skillsets"
--

DROP TABLE IF EXISTS "user_skillsets";


CREATE TABLE "user_skillsets" (
  "id" SERIAL,
  "user_id" INTEGER NOT NULL,
  "skillset_id" INTEGER NOT NULL,
  "proficiency_level" proficiency_level_type NOT NULL DEFAULT 'intermediate',
  "years_experience" decimal(3,TRUE) NOT NULL DEFAULT '0.0',
  "is_certified" SMALLINT NOT NULL DEFAULT '0',
  "certification_name" varchar(255) DEFAULT NULL,
  "certification_date" date DEFAULT NULL,
  "last_used_date" date DEFAULT NULL,
  "notes" TEXT,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  PRIMARY KEY ("id"),
  UNIQUE ("user_id", "skillset_id"),
  CONSTRAINT "FK_8738c27d0a3283636cd473378cd" FOREIGN KEY ("skillset_id") REFERENCES "skillsets" ("id") ON DELETE CASCADE,
  CONSTRAINT "FK_8ba48aaea48ded469c1e676dc84" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE
);


--
-- Dumping data for table "user_skillsets"
--





--
-- Table structure for table "users"
--

DROP TABLE IF EXISTS "users";


CREATE TABLE "users" (
  "id" SERIAL,
  "email" varchar(255) NOT NULL,
  "password" varchar(255) NOT NULL,
  "title" varchar(255) DEFAULT NULL,
  "role" role_type NOT NULL DEFAULT 'engineer',
  "organizational_unit_id" INTEGER DEFAULT NULL,
  "manager_id" INTEGER DEFAULT NULL,
  "hire_date" date DEFAULT NULL,
  "salary" decimal(10,2) DEFAULT NULL,
  "employment_type" employment_type_type NOT NULL DEFAULT 'full_time',
  "location" varchar(255) DEFAULT NULL,
  "emergency_contact_name" varchar(255) DEFAULT NULL,
  "is_active" SMALLINT NOT NULL DEFAULT '1',
  "account_status" account_status_type NOT NULL DEFAULT 'active',
  "failed_login_attempts" INTEGER NOT NULL DEFAULT '0',
  "last_login_at" timestamp NULL DEFAULT NULL,
  "password_changed_at" timestamp NULL DEFAULT NULL,
  "must_change_password" SMALLINT NOT NULL DEFAULT '1',
  "account_locked_until" timestamp NULL DEFAULT NULL,
  "two_factor_enabled" SMALLINT NOT NULL DEFAULT '0',
  "two_factor_secret" varchar(255) DEFAULT NULL,
  "session_expires_at" timestamp NULL DEFAULT NULL,
  "first_name" varchar(255) NOT NULL,
  "last_name" varchar(255) NOT NULL,
  "phone" varchar(255) DEFAULT NULL,
  "emergency_contact_phone" varchar(255) DEFAULT NULL,
  "last_login_ip" varchar(255) DEFAULT NULL,
  "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  "session_token" varchar(1000) DEFAULT NULL,
  "refresh_token" varchar(1000) DEFAULT NULL,
  "refresh_token_expires_at" timestamp NULL DEFAULT NULL,
  "mfa_backup_codes" TEXT,
  "password_history" TEXT,
  PRIMARY KEY ("id"),
  UNIQUE ("email"),"session_expires_at"),"refresh_token_expires_at"),
  CONSTRAINT "FK_3ccd41aa5096a12523add756d72" FOREIGN KEY ("organizational_unit_id") REFERENCES "organizational_units" ("id"),
  CONSTRAINT "FK_fba2d8e029689aa8fea98e53c91" FOREIGN KEY ("manager_id") REFERENCES "users" ("id")
);


--
-- Dumping data for table "users"
--



INSERT INTO "users" VALUES (1,'<EMAIL>','$2b$10$/chBhfcvJJfeFEWMHYnUJuzRh3o4WTH/ZjXldujNMhjqgVZgKiPNm','Software Engineer','engineer',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',TRUE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-31 13:18:43.000000',NULL,NULL,NULL,NULL,NULL),(2,'<EMAIL>','$2b$10$v/6Yvo26KgyXaO49FIs5oeg7h7j6T98cEmpggbT4zhWh3h51KnUVq','System Administrator','hr_admin',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'locked',FALSE,'2025-07-30 13:47:24',NULL,FALSE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,'127.0.0.1','2025-07-17 06:25:25.502904','2025-07-31 10:27:23.644408',NULL,NULL,NULL,NULL,NULL),(3,'<EMAIL>','$2b$10$example',NULL,'director',3,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-17 06:25:25.803880',NULL,NULL,NULL,NULL,NULL),(4,'<EMAIL>','$2b$10$example',NULL,'director',4,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-17 06:25:25.803880',NULL,NULL,NULL,NULL,NULL),(5,'<EMAIL>','$2b$10$example',NULL,'manager',15,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-17 06:25:25.803880',NULL,NULL,NULL,NULL,NULL),(6,'<EMAIL>','$2b$10$example',NULL,'manager',16,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-17 06:25:25.803880',NULL,NULL,NULL,NULL,NULL),(7,'<EMAIL>','$2b$10$example',NULL,'manager',17,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-17 06:25:25.803880',NULL,NULL,NULL,NULL,NULL),(8,'<EMAIL>','$2b$10$example',NULL,'manager',19,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-17 06:25:25.803880',NULL,NULL,NULL,NULL,NULL),(9,'<EMAIL>','$2b$10$example',NULL,'manager',22,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-17 06:25:25.803880',NULL,NULL,NULL,NULL,NULL),(10,'<EMAIL>','$2b$10$example',NULL,'manager',25,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'','',NULL,NULL,NULL,'2025-07-17 06:25:25.502904','2025-07-17 06:25:25.803880',NULL,NULL,NULL,NULL,NULL),(11,'<EMAIL>','$2b$10$rPS.JOic1t2lMUGQRmDaXuCUmA7SFgg2Ml0unL9r88Lv4/0eN/9TO',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,'2025-07-18 04:59:41',NULL,FALSE,NULL,FALSE,NULL,NULL,'Test','User',NULL,NULL,'127.0.0.1','2025-07-18 04:37:07.868680','2025-07-31 10:27:23.644408',NULL,NULL,NULL,NULL,NULL),(12,'<EMAIL>','$2b$10$dummy.hash.for.john.smith',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'John','Smith',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(13,'<EMAIL>','$2b$10$dummy.hash.for.sarah.johnson',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Sarah','Johnson',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(14,'<EMAIL>','$2b$10$dummy.hash.for.mike.wilson',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Mike','Wilson',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(15,'<EMAIL>','$2b$10$dummy.hash.for.lisa.chen',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Lisa','Chen',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(16,'<EMAIL>','$2b$10$dummy.hash.for.david.brown',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'David','Brown',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(17,'<EMAIL>','$2b$10$dummy.hash.for.emma.davis',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Emma','Davis',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(18,'<EMAIL>','$2b$10$dummy.hash.for.tom.anderson',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Tom','Anderson',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(19,'<EMAIL>','$2b$10$dummy.hash.for.alex.martinez',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Alex','Martinez',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(20,'<EMAIL>','$2b$10$dummy.hash.for.rachel.green',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Rachel','Green',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(21,'<EMAIL>','$2b$10$dummy.hash.for.chris.taylor',NULL,'manager',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Chris','Taylor',NULL,NULL,NULL,'2025-07-20 04:56:17.406840','2025-07-20 04:56:17.406840',NULL,NULL,NULL,NULL,NULL),(23,'<EMAIL>','$2b$10$hVQ30xwQ5Jr9NFRr4kcjGuDWmG4lUd8IKX2I5nE.HQFXKa4uJhIdO',NULL,'engineer',NULL,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',TRUE,'2025-07-31 10:13:14',NULL,FALSE,NULL,FALSE,NULL,NULL,'Test','User',NULL,NULL,'127.0.0.1','2025-07-31 10:07:14.100200','2025-07-31 10:14:52.000000',NULL,NULL,NULL,NULL,NULL),(24,'<EMAIL>','$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV','Engineering Manager','manager',TRUE,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'John','Doe',NULL,NULL,NULL,'2025-07-31 10:27:23.601299','2025-07-31 10:27:23.601299',NULL,NULL,NULL,NULL,NULL),(25,'<EMAIL>','$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV','Senior Software Engineer','senior_engineer',TRUE,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Jane','Smith',NULL,NULL,NULL,'2025-07-31 10:27:23.601299','2025-07-31 10:27:23.601299',NULL,NULL,NULL,NULL,NULL),(26,'<EMAIL>','$2b$12$K8gDKVkzjhGQqXRVQqXRVOeKQqXRVQqXRVQqXRVQqXRVQqXRVQqXRV','Software Engineer','engineer',TRUE,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,TRUE,NULL,FALSE,NULL,NULL,'Mike','Johnson',NULL,NULL,NULL,'2025-07-31 10:27:23.601299','2025-07-31 10:27:23.601299',NULL,NULL,NULL,NULL,NULL),(27,'<EMAIL>','$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlG.','Test Engineer','engineer',TRUE,NULL,NULL,NULL,'full_time',NULL,NULL,TRUE,'active',FALSE,NULL,NULL,FALSE,NULL,FALSE,NULL,NULL,'Test','User',NULL,NULL,NULL,'2025-07-31 10:27:23.601299','2025-07-31 10:27:23.601299',NULL,NULL,NULL,NULL,NULL);


--
-- Dumping routines for database 'ehrx'
--











-- Dump completed on 2025-08-04  6:45:26
