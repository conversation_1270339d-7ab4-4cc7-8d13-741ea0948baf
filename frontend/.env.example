# 🔐 eHRx Frontend Environment Configuration Template
# Copy this file to .env and update with your actual values

# ==================== SERVER CONFIGURATION ====================
PORT=3080
HOST=0.0.0.0

# ==================== API CONFIGURATION ====================
# Backend API URL
REACT_APP_API_URL=http://localhost:4000/api

# Environment (development, staging, production)
REACT_APP_ENV=development

# ==================== SECURITY CONFIGURATION ====================
# Content Security Policy settings
REACT_APP_CSP_ENABLED=true

# Session timeout in milliseconds (should match backend)
REACT_APP_SESSION_TIMEOUT=1800000

# Rate limiting for client-side operations
REACT_APP_RATE_LIMIT_WINDOW=60000
REACT_APP_RATE_LIMIT_MAX_REQUESTS=100

# ==================== COMPLIANCE CONFIGURATION ====================
# GDPR compliance features
REACT_APP_GDPR_ENABLED=true
REACT_APP_PRIVACY_MODE_AVAILABLE=true

# Audit logging on client-side
REACT_APP_AUDIT_LOGGING=true

# ==================== DEVELOPMENT SETTINGS ====================
# WebSocket configuration for development server
WDS_SOCKET_HOST=localhost
WDS_SOCKET_PORT=3080
WDS_SOCKET_PATH=/ws

# Disable HTTPS for WebSocket in development
HTTPS=false

# Fast refresh configuration
FAST_REFRESH=true

# Development debugging
REACT_APP_DEBUG_MODE=false
REACT_APP_VERBOSE_LOGGING=false

# ==================== PRODUCTION SETTINGS ====================
# Only set these in production
# HTTPS=true
# SSL_CRT_FILE=/path/to/ssl/cert.pem
# SSL_KEY_FILE=/path/to/ssl/private.key

# ==================== MONITORING & ANALYTICS ====================
# External monitoring services (if needed)
# REACT_APP_MONITORING_URL=https://your-monitoring-service.com
# REACT_APP_ANALYTICS_ID=your_analytics_id

# ==================== FEATURE FLAGS ====================
# Enable/disable features
REACT_APP_FEATURE_COMPLIANCE_DASHBOARD=true
REACT_APP_FEATURE_SECURITY_MONITORING=true
REACT_APP_FEATURE_AUDIT_TRAIL=true

# ==================== NOTES ====================
# 1. Only REACT_APP_ prefixed variables are available in the browser
# 2. Never include sensitive data in frontend environment variables
# 3. All frontend environment variables are public
# 4. Use backend API for sensitive operations
# 5. Keep production URLs secure and use HTTPS
