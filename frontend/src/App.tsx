import React, { useState } from 'react';
// import { <PERSON><PERSON><PERSON><PERSON>outer as Router } from 'react-router-dom';
import {
  Box,
  CssBaseline,
  ThemeProvider,
  createTheme,
  useMediaQuery,
} from '@mui/material';

// Import Authentication
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SecurityProvider } from './contexts/SecurityContext';
import LoginPage from './components/auth/LoginPage';
import {
  Dashboard as DashboardIcon,
  Assessment as AssessmentIcon,
  People as PeopleIcon,
  BarChart as ReportsIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  BugReport as TestIcon,
  CalendarToday as CalendarIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';

// Import Modular Components
import MainDashboard from './components/dashboard/MainDashboard';
import MonthlyAnalyticsDashboard from './components/analytics/MonthlyAnalyticsDashboard';
import { ManagerDashboard } from './components/analytics/ManagerDashboard';
import MonthlyDashboardsPage from './components/monthly-dashboards/MonthlyDashboardsPage';
import AssessmentsPage from './components/assessments/AssessmentsPage';
import OrganizationalManagement from './components/teams/OrganizationalManagement';
import ReportsPage from './pages/ReportsPage';
import SettingsPage from './components/settings/SettingsPage';

// Import Layout Components
import AppHeader from './components/layout/AppHeader';
import AppSidebar from './components/layout/AppSidebar';
import MainContent from './components/layout/MainContent';

// Import Context Providers
import { OrganizationProvider } from './contexts/OrganizationContext';

// Import Error Handling
import ErrorBoundary from './components/common/ErrorBoundary';
import ErrorTestComponent from './components/test/ErrorTestComponent';

// Import Toast Provider
import { ToastProvider } from './components/ui/toast';

// Theme Configuration
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
  },
});

const AuthenticatedApp: React.FC = () => {
  const [selectedPage, setSelectedPage] = useState('dashboard');
  const [mobileOpen, setMobileOpen] = useState(false);
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  console.log('🔍 App component rendering, selectedPage:', selectedPage);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // App initialization
  React.useEffect(() => {
    console.log('🔍 App component rendering, selectedPage:', selectedPage);
  }, [selectedPage]);

  const menuItems = [
    { id: 'dashboard', text: 'Dashboard', icon: <DashboardIcon /> },
    {
      id: 'analytics',
      text: 'Analytics',
      icon: <AnalyticsIcon />,
      submenu: [
        { id: 'analytics-overview', text: 'Overview', icon: <TrendingUpIcon /> },
        { id: 'manager-dashboard', text: 'Manager Dashboard', icon: <DashboardIcon /> },
        { id: 'monthly-dashboards', text: 'Monthly Dashboards', icon: <CalendarIcon /> }
      ]
    },
    { id: 'assessments', text: 'Assessments', icon: <AssessmentIcon /> },
    { id: 'organization', text: 'Organization', icon: <PeopleIcon /> },
    { id: 'reports', text: 'Reports', icon: <ReportsIcon /> },
    { id: 'settings', text: 'Settings', icon: <SettingsIcon /> },
    { id: 'test-dashboard', text: 'Test Dashboard', icon: <TestIcon /> },
    { id: 'test-logging', text: 'Test Logging', icon: <TestIcon /> },
  ];

  const renderContent = () => {
    switch (selectedPage) {
      case 'dashboard':
        return <MainDashboard onNavigate={setSelectedPage} />;
      case 'analytics':
      case 'analytics-overview':
        return <MonthlyAnalyticsDashboard onNavigate={setSelectedPage} />;
      case 'manager-dashboard':
        return <ManagerDashboard />;
      case 'monthly-dashboards':
        return <MonthlyDashboardsPage />;
      case 'assessments':
        return <AssessmentsPage />;
      case 'organization':
        return <OrganizationalManagement />;
      case 'reports':
        return <ReportsPage />;
      case 'settings':
        return <SettingsPage />;
      case 'test-logging':
        return <ErrorTestComponent />;
      default:
        return <MainDashboard onNavigate={setSelectedPage} />;
    }
  };

  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <ToastProvider>
          <OrganizationProvider>
            <CssBaseline />
            <Box sx={{ display: 'flex', minHeight: '100vh' }}>
              <AppHeader />
              <AppSidebar
                menuItems={menuItems}
                selectedPage={selectedPage}
                onPageSelect={setSelectedPage}
                mobileOpen={mobileOpen}
                onMobileToggle={handleDrawerToggle}
                isMobile={isMobile}
              />
              <MainContent renderContent={renderContent}>
                {/* Additional content can be added here if needed */}
              </MainContent>
            </Box>
          </OrganizationProvider>
        </ToastProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

// Main App Component with Authentication and Security
const App: React.FC = () => {
  return (
    <SecurityProvider>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </SecurityProvider>
  );
};

// App Content Component that checks authentication
const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading, user } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        sx={{ backgroundColor: '#f5f5f5' }}
      >
        <div>Loading...</div>
      </Box>
    );
  }

  // Show login page if not authenticated
  if (!isAuthenticated || !user) {
    return <LoginPage onLoginSuccess={() => {}} />;
  }

  // Show authenticated app
  return <AuthenticatedApp />;
};

export default App;
