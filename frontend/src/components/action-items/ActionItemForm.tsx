import React, { useState, useEffect } from 'react';
import { ActionItemStatus, ActionItemPriority, ActionItemCategory } from './ActionItemsDashboard';
import { UserRole } from '../../types';
import { ApiService } from '../../services/api';

interface ActionItem {
  id?: number;
  title: string;
  description: string;
  status: ActionItemStatus;
  priority: ActionItemPriority;
  category: ActionItemCategory;
  dueDate: string;
  assignedToId: number;
  assessmentId?: number;
  progressPercentage: number;
  notes?: string;
  estimatedHours?: number;
  tags?: string[];
  isRecurring?: boolean;
  recurrencePattern?: string;
  parentActionItemId?: number;
}

interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
}

interface ActionItemFormProps {
  actionItem?: ActionItem;
  onSave: (actionItem: ActionItem) => void;
  onCancel: () => void;
  currentUserRole: UserRole;
  currentUserId: number;
}

const ActionItemForm: React.FC<ActionItemFormProps> = ({
  actionItem,
  onSave,
  onCancel,
  currentUserRole: _currentUserRole,
  currentUserId
}) => {
  const [formData, setFormData] = useState<ActionItem>({
    title: '',
    description: '',
    status: ActionItemStatus.OPEN,
    priority: ActionItemPriority.MEDIUM,
    category: ActionItemCategory.OTHER,
    dueDate: '',
    assignedToId: currentUserId,
    progressPercentage: 0,
    notes: '',
    estimatedHours: 0,
    tags: [],
    isRecurring: false,
    recurrencePattern: '',
    ...actionItem
  });

  const [users, setUsers] = useState<User[]>([]);
  const [assessments, setAssessments] = useState<any[]>([]);
  const [parentActionItems, setParentActionItems] = useState<ActionItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    // Use database API for data fetching
    fetchUsers();
    fetchAssessments();
    fetchParentActionItems();
  }, []);

  const fetchUsers = async () => {
    try {
      // Use centralized database API for users
      const response = await ApiService.getTableData('users', 1, 100);

      if (response && response.records) {
        const transformedUsers = response.records.map((user: any) => ({
          id: user.id,
          firstName: user.firstName || user.first_name,
          lastName: user.lastName || user.last_name,
          name: user.name || `${user.firstName || user.first_name} ${user.lastName || user.last_name}`,
          email: user.email,
          role: user.role,
          isActive: user.isActive !== false
        }));
        setUsers(transformedUsers);
      }
    } catch (err) {
      console.error('Error fetching users from database API:', err);
    }
  };

  const fetchAssessments = async () => {
    try {
      // Use centralized database API for assessment instances
      const response = await ApiService.getTableData('assessment_instances', 1, 50);

      if (response && response.records) {
        const transformedAssessments = response.records.map((assessment: any) => ({
          id: assessment.id,
          title: assessment.title || `Assessment ${assessment.id}`,
          employeeId: assessment.employeeId || assessment.employee_id,
          evaluatorId: assessment.evaluatorId || assessment.evaluator_id,
          templateId: assessment.templateId || assessment.template_id,
          status: assessment.status,
          createdAt: assessment.createdAt || assessment.created_at,
          dueDate: assessment.dueDate || assessment.due_date
        }));
        setAssessments(transformedAssessments);
      } else {
        setAssessments([]);
      }
    } catch (err) {
      console.error('Error fetching assessments from database API:', err);
      setAssessments([]);
    }
  };

  const fetchParentActionItems = async () => {
    try {
      // Use centralized database API for action items
      const response = await ApiService.getTableData('action_items', 1, 100);

      if (response && response.records) {
        const transformedActionItems = response.records.map((item: any) => ({
          id: item.id,
          title: item.title,
          description: item.description,
          status: item.status,
          priority: item.priority,
          category: item.category,
          assigneeId: item.assigneeId || item.assignee_id,
          createdById: item.createdById || item.created_by_id,
          dueDate: item.dueDate || item.due_date,
          createdAt: item.createdAt || item.created_at
        }));
        setParentActionItems(transformedActionItems);
      } else {
        setParentActionItems([]);
      }
    } catch (err) {
      console.error('Error fetching parent action items from database API:', err);
      setParentActionItems([]);
    }
  };

  const handleInputChange = (field: keyof ActionItem, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleTagsChange = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    handleInputChange('tags', tags);
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) {
      errors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }

    if (!formData.assignedToId) {
      errors.assignedToId = 'Assigned user is required';
    }

    if (formData.progressPercentage < 0 || formData.progressPercentage > 100) {
      errors.progressPercentage = 'Progress must be between 0 and 100';
    }

    if (formData.estimatedHours && formData.estimatedHours < 0) {
      errors.estimatedHours = 'Estimated hours cannot be negative';
    }

    if (formData.dueDate && new Date(formData.dueDate) < new Date()) {
      errors.dueDate = 'Due date cannot be in the past';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const submitData = {
        ...formData,
        dueDate: formData.dueDate || undefined,
        assessmentId: formData.assessmentId || undefined,
        parentActionItemId: formData.parentActionItemId || undefined,
        estimatedHours: formData.estimatedHours || undefined,
        notes: formData.notes || undefined,
        recurrencePattern: formData.isRecurring ? formData.recurrencePattern : undefined
      };

      let response;
      if (actionItem?.id) {
        response = await ApiService.patch(`/action-items/${actionItem.id}`, submitData);
      } else {
        response = await ApiService.post('/action-items', submitData);
      }

      onSave(response.data);
    } catch (err: any) {
      console.error('Error saving action item:', err);
      setError(err.response?.data?.message || 'Failed to save action item');
    } finally {
      setIsLoading(false);
    }
  };

  const formatCategory = (category: ActionItemCategory) => {
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const isEditing = !!actionItem?.id;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            {isEditing ? 'Edit Action Item' : 'Create Action Item'}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className={`block w-full rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${validationErrors.title ? 'border-red-300' : 'border-gray-300'
                  }`}
                placeholder="Enter action item title"
              />
              {validationErrors.title && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.title}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className={`block w-full rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${validationErrors.description ? 'border-red-300' : 'border-gray-300'
                  }`}
                placeholder="Describe the action item in detail"
              />
              {validationErrors.description && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.description}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority
              </label>
              <select
                value={formData.priority}
                onChange={(e) => handleInputChange('priority', e.target.value as ActionItemPriority)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={ActionItemPriority.LOW}>Low</option>
                <option value={ActionItemPriority.MEDIUM}>Medium</option>
                <option value={ActionItemPriority.HIGH}>High</option>
                <option value={ActionItemPriority.CRITICAL}>Critical</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value as ActionItemCategory)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                {Object.values(ActionItemCategory).map(category => (
                  <option key={category} value={category}>
                    {formatCategory(category)}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Assigned To *
              </label>
              <select
                value={formData.assignedToId}
                onChange={(e) => handleInputChange('assignedToId', parseInt(e.target.value))}
                className={`block w-full rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${validationErrors.assignedToId ? 'border-red-300' : 'border-gray-300'
                  }`}
              >
                <option value="">Select user...</option>
                {users.map(user => (
                  <option key={user.id} value={user.id}>
                    {user.firstName} {user.lastName} ({user.email})
                  </option>
                ))}
              </select>
              {validationErrors.assignedToId && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.assignedToId}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Due Date
              </label>
              <input
                type="date"
                value={formData.dueDate}
                onChange={(e) => handleInputChange('dueDate', e.target.value)}
                className={`block w-full rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${validationErrors.dueDate ? 'border-red-300' : 'border-gray-300'
                  }`}
              />
              {validationErrors.dueDate && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.dueDate}</p>
              )}
            </div>

            {isEditing && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value as ActionItemStatus)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={ActionItemStatus.OPEN}>Open</option>
                  <option value={ActionItemStatus.IN_PROGRESS}>In Progress</option>
                  <option value={ActionItemStatus.COMPLETED}>Completed</option>
                  <option value={ActionItemStatus.CANCELLED}>Cancelled</option>
                </select>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Progress (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={formData.progressPercentage}
                onChange={(e) => handleInputChange('progressPercentage', parseInt(e.target.value) || 0)}
                className={`block w-full rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${validationErrors.progressPercentage ? 'border-red-300' : 'border-gray-300'
                  }`}
              />
              {validationErrors.progressPercentage && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.progressPercentage}</p>
              )}
            </div>
          </div>

          {/* Additional Information */}
          <div className="border-t pt-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">Additional Information</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Estimated Hours
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.5"
                  value={formData.estimatedHours || ''}
                  onChange={(e) => handleInputChange('estimatedHours', parseFloat(e.target.value) || undefined)}
                  className={`block w-full rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 ${validationErrors.estimatedHours ? 'border-red-300' : 'border-gray-300'
                    }`}
                  placeholder="0.0"
                />
                {validationErrors.estimatedHours && (
                  <p className="mt-1 text-sm text-red-600">{validationErrors.estimatedHours}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Related Assessment
                </label>
                <select
                  value={formData.assessmentId || ''}
                  onChange={(e) => handleInputChange('assessmentId', parseInt(e.target.value) || undefined)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">No related assessment</option>
                  {assessments.map(assessment => (
                    <option key={assessment.id} value={assessment.id}>
                      {assessment.template?.name} - {assessment.employee?.firstName} {assessment.employee?.lastName}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Parent Action Item
                </label>
                <select
                  value={formData.parentActionItemId || ''}
                  onChange={(e) => handleInputChange('parentActionItemId', parseInt(e.target.value) || undefined)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">No parent item</option>
                  {parentActionItems.filter(item => item.id !== actionItem?.id).map(item => (
                    <option key={item.id} value={item.id}>
                      {item.title}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tags (comma-separated)
                </label>
                <input
                  type="text"
                  value={formData.tags?.join(', ') || ''}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="tag1, tag2, tag3"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Notes
                </label>
                <textarea
                  value={formData.notes || ''}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Additional notes or comments"
                />
              </div>

              <div className="md:col-span-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isRecurring || false}
                    onChange={(e) => handleInputChange('isRecurring', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    This is a recurring action item
                  </span>
                </label>
              </div>

              {formData.isRecurring && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Recurrence Pattern
                  </label>
                  <select
                    value={formData.recurrencePattern || ''}
                    onChange={(e) => handleInputChange('recurrencePattern', e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select pattern...</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="yearly">Yearly</option>
                  </select>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${isLoading
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
                }`}
            >
              {isLoading ? 'Saving...' : (isEditing ? 'Update Action Item' : 'Create Action Item')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ActionItemForm;
