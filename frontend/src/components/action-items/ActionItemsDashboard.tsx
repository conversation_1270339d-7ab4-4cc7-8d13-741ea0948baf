import React, { useState, useEffect } from 'react';
import { UserRole } from '../../types';
import { ApiService } from '../../services/api';

export enum ActionItemStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue'
}

export enum ActionItemPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ActionItemCategory {
  PERFORMANCE_IMPROVEMENT = 'performance_improvement',
  SKILL_DEVELOPMENT = 'skill_development',
  PROCESS_IMPROVEMENT = 'process_improvement',
  COMPLIANCE = 'compliance',
  SAFETY = 'safety',
  QUALITY = 'quality',
  TRAINING = 'training',
  GOAL_SETTING = 'goal_setting',
  OTHER = 'other'
}

interface ActionItem {
  id: number;
  title: string;
  description: string;
  status: ActionItemStatus;
  priority: ActionItemPriority;
  category: ActionItemCategory;
  dueDate: string;
  progressPercentage: number;
  assignedTo: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdBy: {
    id: number;
    firstName: string;
    lastName: string;
  };
  assessment?: {
    id: number;
    template: { name: string };
  };
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

interface ActionItemStatistics {
  totalItems: number;
  byStatus: Record<ActionItemStatus, number>;
  byPriority: Record<ActionItemPriority, number>;
  byCategory: Record<ActionItemCategory, number>;
  overdueItems: number;
  completedThisMonth: number;
  upcomingDueDates: number;
}

interface ActionItemsDashboardProps {
  currentUserRole: UserRole;
  currentUserId: number;
}

const ActionItemsDashboard: React.FC<ActionItemsDashboardProps> = ({
  currentUserRole,
  currentUserId
}) => {
  const [actionItems, setActionItems] = useState<ActionItem[]>([]);
  const [statistics, setStatistics] = useState<ActionItemStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter state
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    category: '',
    search: '',
    page: 1,
    limit: 20
  });
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Selection state
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // Modal state
  const [success] = useState<string | null>(null);
  const [, setShowCreateModal] = useState(false);
  const [, setShowBulkActions] = useState(false);
  const [, setEditingItem] = useState<ActionItem | null>(null);

  useEffect(() => {
    fetchActionItems();
    fetchStatistics();
  }, [filters]);

  const fetchActionItems = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use centralized database API for action items
      const response = await ApiService.getTableData('action_items', filters.page, filters.limit);

      if (response && response.records) {
        // Transform database records to match expected format
        const transformedActionItems = response.records
          .filter((item: any) => {
            // Apply filters
            if (filters.status && item.status !== filters.status) return false;
            if (filters.priority && item.priority !== filters.priority) return false;
            if (filters.category && item.category !== filters.category) return false;
            if (filters.search && !item.title?.toLowerCase().includes(filters.search.toLowerCase()) &&
              !item.description?.toLowerCase().includes(filters.search.toLowerCase())) return false;
            return true;
          })
          .map((item: any) => ({
            id: item.id,
            title: item.title,
            description: item.description,
            status: item.status || 'pending',
            priority: item.priority || 'medium',
            category: item.category || 'general',
            assigneeId: item.assigneeId || item.assignee_id,
            createdById: item.createdById || item.created_by_id,
            dueDate: item.dueDate || item.due_date,
            createdAt: item.createdAt || item.created_at,
            updatedAt: item.updatedAt || item.updated_at
          }));

        setActionItems(transformedActionItems);
        setTotalPages(response.pagination?.totalPages || 1);
        setTotalItems(response.pagination?.total || transformedActionItems.length);
      } else {
        setActionItems([]);
        setTotalPages(1);
        setTotalItems(0);
      }
    } catch (err: any) {
      console.error('Error fetching action items from database API:', err);
      setError(err.response?.data?.message || 'Failed to fetch action items from database');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      // Use centralized database API to calculate statistics
      const response = await ApiService.getTableData('action_items', 1, 1000); // Get all action items for stats

      if (response && response.records) {
        const items = response.records;
        const stats: ActionItemStatistics = {
          totalItems: items.length,
          byStatus: {
            [ActionItemStatus.OPEN]: items.filter((item: any) => item.status === 'open' || item.status === 'pending').length,
            [ActionItemStatus.IN_PROGRESS]: items.filter((item: any) => item.status === 'in_progress').length,
            [ActionItemStatus.COMPLETED]: items.filter((item: any) => item.status === 'completed').length,
            [ActionItemStatus.CANCELLED]: items.filter((item: any) => item.status === 'cancelled').length,
            [ActionItemStatus.OVERDUE]: items.filter((item: any) => {
              const dueDate = new Date(item.dueDate || item.due_date);
              return dueDate < new Date() && item.status !== 'completed';
            }).length,
          },
          byPriority: {
            [ActionItemPriority.LOW]: items.filter((item: any) => item.priority === 'low').length,
            [ActionItemPriority.MEDIUM]: items.filter((item: any) => item.priority === 'medium').length,
            [ActionItemPriority.HIGH]: items.filter((item: any) => item.priority === 'high').length,
            [ActionItemPriority.CRITICAL]: items.filter((item: any) => item.priority === 'critical').length,
          },
          byCategory: {
            [ActionItemCategory.PERFORMANCE_IMPROVEMENT]: items.filter((item: any) => item.category === 'performance_improvement').length,
            [ActionItemCategory.SKILL_DEVELOPMENT]: items.filter((item: any) => item.category === 'skill_development').length,
            [ActionItemCategory.PROCESS_IMPROVEMENT]: items.filter((item: any) => item.category === 'process_improvement').length,
            [ActionItemCategory.COMPLIANCE]: items.filter((item: any) => item.category === 'compliance').length,
            [ActionItemCategory.SAFETY]: items.filter((item: any) => item.category === 'safety').length,
            [ActionItemCategory.QUALITY]: items.filter((item: any) => item.category === 'quality').length,
            [ActionItemCategory.TRAINING]: items.filter((item: any) => item.category === 'training').length,
            [ActionItemCategory.GOAL_SETTING]: items.filter((item: any) => item.category === 'goal_setting').length,
            [ActionItemCategory.OTHER]: items.filter((item: any) => !Object.values(ActionItemCategory).includes(item.category)).length,
          },
          overdueItems: items.filter((item: any) => {
            const dueDate = new Date(item.dueDate || item.due_date);
            return dueDate < new Date() && item.status !== 'completed';
          }).length,
          completedThisMonth: items.filter((item: any) => {
            const completedDate = new Date(item.updatedAt);
            const now = new Date();
            return item.status === 'completed' &&
              completedDate.getMonth() === now.getMonth() &&
              completedDate.getFullYear() === now.getFullYear();
          }).length,
          upcomingDueDates: items.filter((item: any) => {
            const dueDate = new Date(item.dueDate || item.due_date);
            const nextWeek = new Date();
            nextWeek.setDate(nextWeek.getDate() + 7);
            return dueDate <= nextWeek && dueDate >= new Date() && item.status !== 'completed';
          }).length,
        };
        setStatistics(stats);
      }
    } catch (err) {
      console.error('Error fetching action items statistics from database API:', err);
      // Fallback to empty statistics
      setStatistics({
        totalItems: 0,
        byStatus: {
          [ActionItemStatus.OPEN]: 0,
          [ActionItemStatus.IN_PROGRESS]: 0,
          [ActionItemStatus.COMPLETED]: 0,
          [ActionItemStatus.CANCELLED]: 0,
          [ActionItemStatus.OVERDUE]: 0,
        },
        byPriority: {
          [ActionItemPriority.LOW]: 0,
          [ActionItemPriority.MEDIUM]: 0,
          [ActionItemPriority.HIGH]: 0,
          [ActionItemPriority.CRITICAL]: 0,
        },
        byCategory: {
          [ActionItemCategory.PERFORMANCE_IMPROVEMENT]: 0,
          [ActionItemCategory.SKILL_DEVELOPMENT]: 0,
          [ActionItemCategory.PROCESS_IMPROVEMENT]: 0,
          [ActionItemCategory.COMPLIANCE]: 0,
          [ActionItemCategory.SAFETY]: 0,
          [ActionItemCategory.QUALITY]: 0,
          [ActionItemCategory.TRAINING]: 0,
          [ActionItemCategory.GOAL_SETTING]: 0,
          [ActionItemCategory.OTHER]: 0,
        },
        overdueItems: 0,
        completedThisMonth: 0,
        upcomingDueDates: 0,
      });
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value
    }));
  };

  const handleItemSelection = (itemId: number, selected: boolean) => {
    const newSelection = new Set(selectedItems);
    if (selected) {
      newSelection.add(itemId);
    } else {
      newSelection.delete(itemId);
    }
    setSelectedItems(newSelection);
    setSelectAll(newSelection.size === actionItems.length);
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedItems(new Set(actionItems.map(item => item.id)));
    } else {
      setSelectedItems(new Set());
    }
    setSelectAll(selected);
  };



  const getStatusColor = (status: ActionItemStatus) => {
    switch (status) {
      case ActionItemStatus.OPEN:
        return 'bg-gray-100 text-gray-800';
      case ActionItemStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case ActionItemStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case ActionItemStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      case ActionItemStatus.OVERDUE:
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: ActionItemPriority) => {
    switch (priority) {
      case ActionItemPriority.LOW:
        return 'bg-green-100 text-green-800';
      case ActionItemPriority.MEDIUM:
        return 'bg-yellow-100 text-yellow-800';
      case ActionItemPriority.HIGH:
        return 'bg-orange-100 text-orange-800';
      case ActionItemPriority.CRITICAL:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCategory = (category: ActionItemCategory) => {
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date() && dueDate;
  };

  const canCreateItems = [UserRole.HR_ADMIN, UserRole.MANAGER].includes(currentUserRole);
  const canBulkUpdate = [UserRole.HR_ADMIN, UserRole.MANAGER].includes(currentUserRole);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Action Items</h1>
          <p className="text-gray-600 mt-1">
            Track and manage action items, improvements, and initiatives
          </p>
        </div>

        {canCreateItems && (
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700"
          >
            Create Action Item
          </button>
        )}
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Items</h3>
            <p className="text-2xl font-bold text-gray-900">{statistics.totalItems}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Completed This Month</h3>
            <p className="text-2xl font-bold text-green-600">{statistics.completedThisMonth}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Overdue Items</h3>
            <p className="text-2xl font-bold text-red-600">{statistics.overdueItems}</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Due This Week</h3>
            <p className="text-2xl font-bold text-orange-600">{statistics.upcomingDueDates}</p>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Search action items..."
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value={ActionItemStatus.OPEN}>Open</option>
              <option value={ActionItemStatus.IN_PROGRESS}>In Progress</option>
              <option value={ActionItemStatus.COMPLETED}>Completed</option>
              <option value={ActionItemStatus.OVERDUE}>Overdue</option>
              <option value={ActionItemStatus.CANCELLED}>Cancelled</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Priority
            </label>
            <select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Priorities</option>
              <option value={ActionItemPriority.CRITICAL}>Critical</option>
              <option value={ActionItemPriority.HIGH}>High</option>
              <option value={ActionItemPriority.MEDIUM}>Medium</option>
              <option value={ActionItemPriority.LOW}>Low</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">All Categories</option>
              <option value={ActionItemCategory.PERFORMANCE_IMPROVEMENT}>Performance Improvement</option>
              <option value={ActionItemCategory.SKILL_DEVELOPMENT}>Skill Development</option>
              <option value={ActionItemCategory.TRAINING}>Training</option>
              <option value={ActionItemCategory.QUALITY}>Quality</option>
              <option value={ActionItemCategory.SAFETY}>Safety</option>
              <option value={ActionItemCategory.COMPLIANCE}>Compliance</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => setFilters({ status: '', priority: '', category: '', search: '', page: 1, limit: 20 })}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {success}
        </div>
      )}

      {/* Bulk Actions */}
      {canBulkUpdate && selectedItems.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-blue-800">
              {selectedItems.size} item(s) selected
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowBulkActions(true)}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Bulk Actions
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Action Items Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">
            Action Items ({totalItems})
          </h3>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : actionItems.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">No action items found matching your criteria.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {canBulkUpdate && (
                    <th className="px-6 py-3 text-left">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                  )}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Assigned To
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {actionItems.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    {canBulkUpdate && (
                      <td className="px-6 py-4">
                        <input
                          type="checkbox"
                          checked={selectedItems.has(item.id)}
                          onChange={(e) => handleItemSelection(item.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                    )}
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{item.title}</div>
                        <div className="text-sm text-gray-500">{formatCategory(item.category)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                        {item.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>
                        {item.priority.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${item.progressPercentage}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-600">{item.progressPercentage}%</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {item.assignedTo.firstName} {item.assignedTo.lastName}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {item.dueDate ? (
                        <span className={isOverdue(item.dueDate) ? 'text-red-600 font-medium' : ''}>
                          {new Date(item.dueDate).toLocaleDateString()}
                        </span>
                      ) : (
                        'No due date'
                      )}
                    </td>
                    <td className="px-6 py-4 text-right text-sm font-medium">
                      <button
                        onClick={() => setEditingItem(item)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        View
                      </button>
                      {(item.assignedTo.id === currentUserId || canCreateItems) && (
                        <button
                          onClick={() => setEditingItem(item)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Edit
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handleFilterChange('page', Math.max(1, filters.page - 1))}
                disabled={filters.page <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => handleFilterChange('page', Math.min(totalPages, filters.page + 1))}
                disabled={filters.page >= totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{filters.page}</span> of{' '}
                  <span className="font-medium">{totalPages}</span>
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handleFilterChange('page', Math.max(1, filters.page - 1))}
                    disabled={filters.page <= 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handleFilterChange('page', Math.min(totalPages, filters.page + 1))}
                    disabled={filters.page >= totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ActionItemsDashboard;
