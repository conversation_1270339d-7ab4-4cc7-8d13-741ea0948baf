import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
} from '@mui/icons-material';
import { ApiService } from '../../services/api';

interface LogEntry {
  id: string;
  timestamp: Date;
  level: string;
  source: string;
  message: string;
  details?: any;
  userId?: string;
  component?: string;
  action?: string;
}

interface LogStats {
  totalLogs: number;
  errorCount: number;
  warnCount: number;
  infoCount: number;
  debugCount: number;
  traceCount: number;
  sourceBreakdown: Record<string, number>;
  recentErrors: LogEntry[];
  topErrors: Array<{ message: string; count: number; lastOccurrence: Date }>;
}

const LogManagementDashboard: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [stats, setStats] = useState<LogStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState('');
  const [sourceFilter, setSourceFilter] = useState('');
  const [selectedLog, setSelectedLog] = useState<LogEntry | null>(null);
  const [cleanupDialogOpen, setCleanupDialogOpen] = useState(false);

  useEffect(() => {
    loadLogs();
    loadStats();
  }, []);

  const loadLogs = async () => {
    setLoading(true);
    try {
      // Use centralized API service for log queries
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (levelFilter) params.append('level', levelFilter);
      if (sourceFilter) params.append('source', sourceFilter);
      params.append('limit', '100');

      const response = await ApiService.get(`/logs/query?${params}`);
      const data = response.data || response;

      setLogs(Array.isArray(data) ? data.map((log: any) => ({
        ...log,
        timestamp: new Date(log.timestamp),
      })) : []);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load logs');
      console.error('Error loading logs:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      // Use centralized API service for log statistics
      const response = await ApiService.get('/logs/stats');
      const data = response.data || response;

      setStats({
        ...data,
        recentErrors: (data.recentErrors || []).map((log: any) => ({
          ...log,
          timestamp: new Date(log.timestamp),
        })),
        topErrors: (data.topErrors || []).map((error: any) => ({
          ...error,
          lastOccurrence: new Date(error.lastOccurrence),
        })),
      });
    } catch (err) {
      console.error('Failed to load log stats:', err);
    }
  };

  const handleCleanup = async () => {
    try {
      // Use centralized API service for log cleanup
      const response = await ApiService.post('/logs/cleanup');
      const result = response.data || response;

      alert(`Cleanup completed: ${result.deletedFiles?.length || 0} files deleted, ${result.compressedFiles?.length || 0} files compressed`);

      setCleanupDialogOpen(false);
      loadStats();
    } catch (err) {
      alert('Cleanup failed: ' + (err instanceof Error ? err.message : 'Unknown error'));
      console.error('Log cleanup error:', err);
    }
  };

  const getLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'error': return 'error';
      case 'warn': return 'warning';
      case 'info': return 'info';
      case 'debug': return 'default';
      case 'trace': return 'default';
      default: return 'default';
    }
  };

  const formatTimestamp = (date: Date) => {
    return date.toLocaleString();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Log Management Dashboard
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6">Total Logs</Typography>
                <Typography variant="h4" color="primary">
                  {stats.totalLogs.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6">Errors</Typography>
                <Typography variant="h4" color="error">
                  {stats.errorCount.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6">Warnings</Typography>
                <Typography variant="h4" color="warning.main">
                  {stats.warnCount.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6">Info</Typography>
                <Typography variant="h4" color="info.main">
                  {stats.infoCount.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Controls */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <TextField
          label="Search"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          size="small"
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1, color: 'action.active' }} />,
          }}
        />

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Level</InputLabel>
          <Select
            value={levelFilter}
            label="Level"
            onChange={(e) => setLevelFilter(e.target.value)}
          >
            <MenuItem value="">All</MenuItem>
            <MenuItem value="error">Error</MenuItem>
            <MenuItem value="warn">Warning</MenuItem>
            <MenuItem value="info">Info</MenuItem>
            <MenuItem value="debug">Debug</MenuItem>
            <MenuItem value="trace">Trace</MenuItem>
          </Select>
        </FormControl>

        <FormControl size="small" sx={{ minWidth: 120 }}>
          <InputLabel>Source</InputLabel>
          <Select
            value={sourceFilter}
            label="Source"
            onChange={(e) => setSourceFilter(e.target.value)}
          >
            <MenuItem value="">All</MenuItem>
            <MenuItem value="frontend">Frontend</MenuItem>
            <MenuItem value="backend">Backend</MenuItem>
            <MenuItem value="database">Database</MenuItem>
            <MenuItem value="external_api">External API</MenuItem>
            <MenuItem value="system">System</MenuItem>
          </Select>
        </FormControl>

        <Button
          variant="contained"
          startIcon={<SearchIcon />}
          onClick={loadLogs}
          disabled={loading}
        >
          Search
        </Button>

        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={() => { loadLogs(); loadStats(); }}
          disabled={loading}
        >
          Refresh
        </Button>

        <Button
          variant="outlined"
          color="warning"
          startIcon={<DeleteIcon />}
          onClick={() => setCleanupDialogOpen(true)}
        >
          Cleanup
        </Button>
      </Box>

      {/* Logs Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Timestamp</TableCell>
              <TableCell>Level</TableCell>
              <TableCell>Source</TableCell>
              <TableCell>Message</TableCell>
              <TableCell>Component</TableCell>
              <TableCell>User</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : logs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No logs found
                </TableCell>
              </TableRow>
            ) : (
              logs.map((log) => (
                <TableRow
                  key={log.id}
                  hover
                  onClick={() => setSelectedLog(log)}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell>{formatTimestamp(log.timestamp)}</TableCell>
                  <TableCell>
                    <Chip
                      label={log.level.toUpperCase()}
                      color={getLevelColor(log.level) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{log.source}</TableCell>
                  <TableCell sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {log.message}
                  </TableCell>
                  <TableCell>{log.component || '-'}</TableCell>
                  <TableCell>{log.userId || '-'}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Log Detail Dialog */}
      <Dialog
        open={!!selectedLog}
        onClose={() => setSelectedLog(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Log Details</DialogTitle>
        <DialogContent>
          {selectedLog && (
            <Box>
              <Typography variant="body2" component="pre" sx={{
                bgcolor: 'grey.100',
                p: 2,
                borderRadius: 1,
                overflow: 'auto',
                maxHeight: '400px',
              }}>
                {JSON.stringify(selectedLog, null, 2)}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSelectedLog(null)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Cleanup Confirmation Dialog */}
      <Dialog
        open={cleanupDialogOpen}
        onClose={() => setCleanupDialogOpen(false)}
      >
        <DialogTitle>Confirm Log Cleanup</DialogTitle>
        <DialogContent>
          <Typography>
            This will delete old log files and compress recent ones. This action cannot be undone.
            Are you sure you want to proceed?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCleanupDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCleanup} color="warning" variant="contained">
            Cleanup
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default LogManagementDashboard;
