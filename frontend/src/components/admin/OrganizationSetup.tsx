import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  TextField,
  Alert,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { configManager, ORGANIZATION_TEMPLATES, OrganizationConfig } from '../../utils/config';

interface OrganizationSetupProps {
  onConfigurationSaved?: (config: OrganizationConfig) => void;
}

export const OrganizationSetup: React.FC<OrganizationSetupProps> = ({
  onConfigurationSaved
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [currentConfig, setCurrentConfig] = useState<OrganizationConfig>(configManager.getConfig());
  const [validationResult, setValidationResult] = useState<{ isValid: boolean; errors: string[] }>({ isValid: true, errors: [] });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Validate configuration whenever it changes
    const result = configManager.validateConfiguration();
    setValidationResult(result);
  }, [currentConfig]);

  const handleTemplateSelect = (templateName: string) => {
    setSelectedTemplate(templateName);
    if (templateName && templateName !== 'custom') {
      configManager.applyOrganizationTemplate(templateName as keyof typeof ORGANIZATION_TEMPLATES);
      setCurrentConfig(configManager.getConfig());
    }
  };

  const handleFeatureToggle = (feature: keyof OrganizationConfig['features']) => {
    const updatedConfig = {
      ...currentConfig,
      features: {
        ...currentConfig.features,
        [feature]: !currentConfig.features[feature]
      }
    };
    setCurrentConfig(updatedConfig);
    configManager.updateConfig(updatedConfig);
  };

  const handleSecuritySettingChange = (setting: string, value: any) => {
    const updatedConfig = {
      ...currentConfig,
      security: {
        ...currentConfig.security,
        [setting]: value
      }
    };
    setCurrentConfig(updatedConfig);
    configManager.updateConfig(updatedConfig);
  };

  const handleSaveConfiguration = async () => {
    setIsLoading(true);
    try {
      // Validate before saving
      const validation = configManager.validateConfiguration();
      if (!validation.isValid) {
        setValidationResult(validation);
        setIsLoading(false);
        return;
      }

      // Save configuration (in a real app, this would call an API)
      configManager.updateConfig(currentConfig);

      if (onConfigurationSaved) {
        onConfigurationSaved(currentConfig);
      }

      console.log('🔧 [SETUP] Organization configuration saved successfully');
    } catch (error) {
      console.error('🔧 [SETUP] Failed to save configuration:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTemplateDescription = (templateKey: string) => {
    const template = ORGANIZATION_TEMPLATES[templateKey as keyof typeof ORGANIZATION_TEMPLATES];
    return template ? template.description : '';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Organization Setup
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Configure your organization's settings, features, and security policies.
      </Typography>

      {/* Validation Alerts */}
      {!validationResult.isValid && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="subtitle2">Configuration Issues:</Typography>
          <List dense>
            {validationResult.errors.map((error, index) => (
              <ListItem key={index} sx={{ py: 0 }}>
                <ListItemIcon sx={{ minWidth: 20 }}>
                  <WarningIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary={error} />
              </ListItem>
            ))}
          </List>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Template Selection */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <BusinessIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Organization Template
              </Typography>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Select Template</InputLabel>
                <Select
                  value={selectedTemplate}
                  onChange={(e) => handleTemplateSelect(e.target.value)}
                  label="Select Template"
                >
                  <MenuItem value="">
                    <em>Custom Configuration</em>
                  </MenuItem>
                  {Object.entries(ORGANIZATION_TEMPLATES).map(([key, template]) => (
                    <MenuItem key={key} value={key}>
                      {template.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              {selectedTemplate && (
                <Alert severity="info">
                  {getTemplateDescription(selectedTemplate)}
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Features Configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <AnalyticsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Features
              </Typography>
              <Grid container spacing={1}>
                {Object.entries(currentConfig.features).map(([feature, enabled]) => {
                  if (typeof enabled === 'boolean') {
                    return (
                      <Grid item xs={12} key={feature}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={enabled}
                              onChange={() => handleFeatureToggle(feature as keyof OrganizationConfig['features'])}
                            />
                          }
                          label={feature.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                        />
                      </Grid>
                    );
                  }
                  return null;
                })}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Configuration */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Security Settings
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Session Timeout (minutes)"
                    type="number"
                    value={Math.floor(currentConfig.security.sessionTimeout / 60000)}
                    onChange={(e) => handleSecuritySettingChange('sessionTimeout', parseInt(e.target.value) * 60000)}
                    inputProps={{ min: 5, max: 1440 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Max Login Attempts"
                    type="number"
                    value={currentConfig.security.maxLoginAttempts}
                    onChange={(e) => handleSecuritySettingChange('maxLoginAttempts', parseInt(e.target.value))}
                    inputProps={{ min: 1, max: 10 }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={currentConfig.security.requireMFA}
                        onChange={(e) => handleSecuritySettingChange('requireMFA', e.target.checked)}
                      />
                    }
                    label="Require Multi-Factor Authentication"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Configuration Summary */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Configuration Summary
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {Object.values(currentConfig.features).filter(v => v === true).length}
                    </Typography>
                    <Typography variant="body2">Features Enabled</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {currentConfig.features.maxUsersPerTeam}
                    </Typography>
                    <Typography variant="body2">Max Users/Team</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {Math.floor(currentConfig.security.sessionTimeout / 60000)}m
                    </Typography>
                    <Typography variant="body2">Session Timeout</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Chip
                      icon={currentConfig.security.requireMFA ? <CheckIcon /> : <WarningIcon />}
                      label={currentConfig.security.requireMFA ? 'MFA Enabled' : 'MFA Disabled'}
                      color={currentConfig.security.requireMFA ? 'success' : 'warning'}
                    />
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Save Button */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="contained"
              onClick={handleSaveConfiguration}
              disabled={!validationResult.isValid || isLoading}
              size="large"
            >
              {isLoading ? 'Saving...' : 'Save Configuration'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default OrganizationSetup;
