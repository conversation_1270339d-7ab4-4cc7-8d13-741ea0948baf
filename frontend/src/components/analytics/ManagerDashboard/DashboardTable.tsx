import React, { useState, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Box,
  Tooltip,
  Typography,
  LinearProgress,
} from '@mui/material';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { ManagerDashboardMetrics } from '../../../services/managerDashboardApi';
import { 
  MetricCell, 
  FteMetricCell, 
  PercentageMetricCell, 
  AttritionMetricCell, 
  VarianceMetricCell 
} from './MetricCell';

interface DashboardTableProps {
  metrics: ManagerDashboardMetrics[];
  onUpdateMetrics: (id: number, data: Partial<ManagerDashboardMetrics>) => Promise<void>;
  loading: boolean;
}

export const DashboardTable: React.FC<DashboardTableProps> = ({
  metrics,
  onUpdateMetrics,
  loading,
}) => {
  const [editingRow, setEditingRow] = useState<number | null>(null);
  const [editData, setEditData] = useState<Partial<ManagerDashboardMetrics>>({});
  const [saving, setSaving] = useState(false);

  /**
   * Start editing a row
   */
  const handleStartEdit = useCallback((metric: ManagerDashboardMetrics) => {
    if (metric.status !== 'approved') { // Only allow editing non-approved metrics
      setEditingRow(metric.id);
      setEditData({ ...metric });
    }
  }, []);

  /**
   * Save changes
   */
  const handleSaveEdit = useCallback(async () => {
    if (editingRow && editData) {
      setSaving(true);
      try {
        await onUpdateMetrics(editingRow, editData);
        setEditingRow(null);
        setEditData({});
      } catch (error) {
        console.error('Failed to save changes:', error);
        // Error handling is done in parent component
      } finally {
        setSaving(false);
      }
    }
  }, [editingRow, editData, onUpdateMetrics]);

  /**
   * Cancel editing
   */
  const handleCancelEdit = useCallback(() => {
    setEditingRow(null);
    setEditData({});
  }, []);

  /**
   * Handle field changes
   */
  const handleFieldChange = useCallback(
    (field: keyof ManagerDashboardMetrics, value: any) => {
      setEditData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  /**
   * Get status color for chips
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'submitted':
        return 'warning';
      default:
        return 'default';
    }
  };

  /**
   * Check if row can be edited
   */
  const canEdit = (metric: ManagerDashboardMetrics) => {
    return metric.status !== 'approved';
  };

  /**
   * Format date for display
   */
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    });
  };

  if (loading && metrics.length === 0) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2, textAlign: 'center' }}>
          Loading dashboard data...
        </Typography>
      </Box>
    );
  }

  if (metrics.length === 0) {
    return (
      <Box sx={{ p: 6, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No metrics data available
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Create new metrics or select a different reporting period.
        </Typography>
      </Box>
    );
  }

  return (
    <TableContainer sx={{ maxHeight: 600 }}>
      <Table stickyHeader size="small">
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>Director</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 150 }}>Sub Area</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>Manager</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 100 }}>Period</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 80 }}>FTE</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>Attrition</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 80 }}>SLA %</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 80 }}>Util %</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 80 }}>AX %</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 100 }}>Compliance %</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 100 }}>AB Variance %</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 80 }}>VL %</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 100 }}>In-Office %</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 100 }}>Status</TableCell>
            <TableCell sx={{ fontWeight: 'bold', minWidth: 100 }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {metrics.map((metric) => {
            const isEditing = editingRow === metric.id;
            const currentData = isEditing ? editData : metric;
            const editable = canEdit(metric);

            return (
              <TableRow 
                key={metric.id} 
                hover
                sx={{ 
                  '&:hover': { 
                    backgroundColor: 'action.hover' 
                  },
                  opacity: metric.status === 'approved' ? 0.8 : 1,
                }}
              >
                {/* Director */}
                <TableCell>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {metric.organizationalUnit?.parent?.name || 'N/A'}
                  </Typography>
                </TableCell>

                {/* Sub Area */}
                <TableCell>
                  <Typography variant="body2">
                    {metric.organizationalUnit?.name || 'N/A'}
                  </Typography>
                </TableCell>

                {/* Manager */}
                <TableCell>
                  <Typography variant="body2">
                    {metric.manager?.firstName} {metric.manager?.lastName}
                  </Typography>
                </TableCell>

                {/* Period */}
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(metric.reportingPeriod)}
                  </Typography>
                </TableCell>

                {/* FTE */}
                <FteMetricCell
                  value={currentData.fteCount}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('fteCount', value)}
                  disabled={!editable}
                />

                {/* Attrition */}
                <TableCell>
                  <Box display="flex" gap={1} flexDirection="column">
                    <AttritionMetricCell
                      value={currentData.attritionResigned}
                      isEditing={isEditing}
                      onChange={(value) => handleFieldChange('attritionResigned', value)}
                      label="Resigned"
                      disabled={!editable}
                    />
                    <AttritionMetricCell
                      value={currentData.attritionInvoluntary}
                      isEditing={isEditing}
                      onChange={(value) => handleFieldChange('attritionInvoluntary', value)}
                      label="Involuntary"
                      disabled={!editable}
                    />
                  </Box>
                </TableCell>

                {/* SLA % */}
                <PercentageMetricCell
                  value={currentData.slaPercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('slaPercentage', value)}
                  target={95}
                  showStatus={true}
                  disabled={!editable}
                />

                {/* Utilization % */}
                <PercentageMetricCell
                  value={currentData.utilizationPercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('utilizationPercentage', value)}
                  target={85}
                  showStatus={true}
                  disabled={!editable}
                />

                {/* AX % */}
                <PercentageMetricCell
                  value={currentData.axPercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('axPercentage', value)}
                  disabled={!editable}
                />

                {/* Compliance % */}
                <PercentageMetricCell
                  value={currentData.complianceScore}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('complianceScore', value)}
                  target={95}
                  showStatus={true}
                  disabled={!editable}
                />

                {/* AB Variance % */}
                <VarianceMetricCell
                  value={currentData.abVariancePercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('abVariancePercentage', value)}
                  disabled={!editable}
                />

                {/* VL % */}
                <PercentageMetricCell
                  value={currentData.vacationLeavePercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('vacationLeavePercentage', value)}
                  disabled={!editable}
                />

                {/* In-Office % */}
                <PercentageMetricCell
                  value={currentData.inOfficePercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('inOfficePercentage', value)}
                  disabled={!editable}
                />

                {/* Status */}
                <TableCell>
                  <Chip
                    label={metric.status.charAt(0).toUpperCase() + metric.status.slice(1)}
                    color={getStatusColor(metric.status)}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>

                {/* Actions */}
                <TableCell>
                  {isEditing ? (
                    <Box display="flex" gap={0.5}>
                      <Tooltip title="Save Changes">
                        <IconButton 
                          onClick={handleSaveEdit} 
                          color="primary" 
                          size="small"
                          disabled={saving}
                        >
                          <SaveIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Cancel">
                        <IconButton 
                          onClick={handleCancelEdit} 
                          size="small"
                          disabled={saving}
                        >
                          <CancelIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  ) : (
                    <Box display="flex" gap={0.5}>
                      {editable ? (
                        <Tooltip title="Edit Metrics">
                          <IconButton
                            onClick={() => handleStartEdit(metric)}
                            size="small"
                            disabled={loading || saving}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                      ) : (
                        <Tooltip title="View Only (Approved)">
                          <IconButton size="small" disabled>
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
