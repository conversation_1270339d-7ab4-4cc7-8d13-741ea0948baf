import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { DashboardTable } from './DashboardTable';
import { MetricsCard } from './MetricsCard';
import { useManagerDashboard } from '../../../hooks/useManagerDashboard';
import { ManagerDashboardMetrics } from '../../../services/managerDashboardApi';
import { useAuth } from '../../../contexts/AuthContext';

interface ManagerDashboardProps {
  managerId?: number;
  defaultReportingPeriod?: Date;
}

export const ManagerDashboard: React.FC<ManagerDashboardProps> = ({ 
  managerId: propManagerId,
  defaultReportingPeriod 
}) => {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState<Date>(
    defaultReportingPeriod || new Date()
  );
  const [selectedManagerId, setSelectedManagerId] = useState<number>(
    propManagerId || user?.id || 0
  );

  const {
    dashboardMetrics,
    organizationalUnits,
    managers,
    summary,
    loading,
    error,
    updateMetrics,
    refreshData,
  } = useManagerDashboard(selectedManagerId, selectedPeriod);

  /**
   * Handle metrics update
   */
  const handleMetricsUpdate = useCallback(
    async (metricsId: number, updatedData: Partial<ManagerDashboardMetrics>) => {
      try {
        await updateMetrics(metricsId, updatedData);
        // Data is automatically updated in the hook
      } catch (error) {
        console.error('Failed to update metrics:', error);
        // Error is handled in the hook and displayed via error state
      }
    },
    [updateMetrics]
  );

  /**
   * Calculate summary statistics
   */
  const calculateSummaryStats = useCallback(() => {
    if (!dashboardMetrics?.length) return null;

    const totalFte = dashboardMetrics.reduce((sum, metric) => sum + (metric.fteCount || 0), 0);
    const avgUtilization = dashboardMetrics.reduce(
      (sum, metric) => sum + (metric.utilizationPercentage || 0),
      0,
    ) / dashboardMetrics.length;
    const totalAttrition = dashboardMetrics.reduce(
      (sum, metric) => sum + (metric.attritionResigned || 0) + (metric.attritionInvoluntary || 0),
      0,
    );

    return {
      totalFte,
      avgUtilization: Math.round(avgUtilization * 100) / 100,
      totalUnits: dashboardMetrics.length,
      totalAttrition,
      completedUnits: dashboardMetrics.filter(m => m.status === 'approved').length,
      pendingUnits: dashboardMetrics.filter(m => m.status === 'draft').length,
    };
  }, [dashboardMetrics]);

  /**
   * Handle period change
   */
  const handlePeriodChange = useCallback((date: Date | null) => {
    if (date) {
      // Set to first day of the month
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      setSelectedPeriod(firstDay);
    }
  }, []);

  /**
   * Handle manager change
   */
  const handleManagerChange = useCallback((managerId: number) => {
    setSelectedManagerId(managerId);
  }, []);

  /**
   * Get status color for chips
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'submitted':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Loading state
  if (loading && !dashboardMetrics.length) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading dashboard data...
        </Typography>
      </Box>
    );
  }

  const summaryStats = summary || calculateSummaryStats();

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
          <DashboardIcon sx={{ fontSize: 32, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
            AEVEN Manager Dashboard
          </Typography>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => {}}>
            {error}
          </Alert>
        )}

        {/* Controls */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Manager</InputLabel>
                <Select
                  value={selectedManagerId || ''}
                  onChange={(e) => handleManagerChange(Number(e.target.value))}
                  label="Manager"
                  disabled={loading}
                >
                  {managers?.map((manager) => (
                    <MenuItem key={manager.id} value={manager.id}>
                      {manager.firstName} {manager.lastName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={3}>
              <DatePicker
                label="Reporting Period"
                value={selectedPeriod}
                onChange={handlePeriodChange}
                views={['year', 'month']}
                slotProps={{ 
                  textField: { 
                    fullWidth: true,
                    disabled: loading
                  } 
                }}
              />
            </Grid>
            
            <Grid item xs={12} md={3}>
              <Button
                variant="contained"
                onClick={refreshData}
                disabled={loading}
                fullWidth
                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
              >
                {loading ? 'Refreshing...' : 'Refresh Data'}
              </Button>
            </Grid>

            <Grid item xs={12} md={3}>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2" color="text.secondary">
                  Status:
                </Typography>
                <Chip
                  label={`${summaryStats?.completedUnits || 0} Completed`}
                  color="success"
                  size="small"
                />
                <Chip
                  label={`${summaryStats?.pendingUnits || 0} Pending`}
                  color="warning"
                  size="small"
                />
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Summary Cards */}
        {summaryStats && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <MetricsCard
                title="Total FTE"
                value={summaryStats.totalFte}
                subtitle="Full-Time Equivalents"
                icon={<PeopleIcon />}
                color="primary"
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <MetricsCard
                title="Average Utilization"
                value={`${summaryStats.avgUtilization}%`}
                subtitle="Across all units"
                icon={<TrendingUpIcon />}
                color="success"
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <MetricsCard
                title="Organizational Units"
                value={summaryStats.totalUnits}
                subtitle="Under management"
                icon={<AssessmentIcon />}
                color="info"
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <MetricsCard
                title="Total Attrition"
                value={summaryStats.totalAttrition}
                subtitle="This period"
                icon={<PeopleIcon />}
                color="warning"
              />
            </Grid>
          </Grid>
        )}

        {/* Main Dashboard Table */}
        <Paper sx={{ overflow: 'hidden' }}>
          <DashboardTable
            metrics={dashboardMetrics || []}
            onUpdateMetrics={handleMetricsUpdate}
            loading={loading}
          />
        </Paper>

        {/* Empty State */}
        {!loading && (!dashboardMetrics || dashboardMetrics.length === 0) && (
          <Card sx={{ mt: 3, textAlign: 'center', py: 6 }}>
            <CardContent>
              <DashboardIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No dashboard data found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                {selectedManagerId 
                  ? 'No metrics found for the selected manager and period.'
                  : 'Please select a manager to view dashboard data.'
                }
              </Typography>
              {selectedManagerId && (
                <Button variant="outlined" onClick={refreshData}>
                  Refresh Data
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </Box>
    </LocalizationProvider>
  );
};
