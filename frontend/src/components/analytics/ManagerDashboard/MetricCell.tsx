import React, { useState, useEffect } from 'react';
import {
  Table<PERSON><PERSON>,
  TextField,
  Typography,
  Box,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';

interface MetricCellProps {
  value: number | null | undefined;
  isEditing: boolean;
  onChange: (value: number | null) => void;
  type?: 'number' | 'percentage' | 'currency';
  step?: number;
  min?: number;
  max?: number;
  allowNegative?: boolean;
  label?: string;
  target?: number;
  showStatus?: boolean;
  disabled?: boolean;
}

export const MetricCell: React.FC<MetricCellProps> = ({
  value,
  isEditing,
  onChange,
  type = 'number',
  step = 1,
  min,
  max,
  allowNegative = false,
  label,
  target,
  showStatus = false,
  disabled = false,
}) => {
  const [localValue, setLocalValue] = useState<string>('');
  const [error, setError] = useState<string>('');

  // Update local value when prop value changes
  useEffect(() => {
    if (value !== null && value !== undefined) {
      setLocalValue(value.toString());
    } else {
      setLocalValue('');
    }
  }, [value]);

  /**
   * Handle input change
   */
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    setLocalValue(inputValue);
    setError('');

    // Validate and convert value
    if (inputValue === '') {
      onChange(null);
      return;
    }

    const numericValue = parseFloat(inputValue);

    // Validation
    if (isNaN(numericValue)) {
      setError('Invalid number');
      return;
    }

    if (!allowNegative && numericValue < 0) {
      setError('Negative values not allowed');
      return;
    }

    if (min !== undefined && numericValue < min) {
      setError(`Value must be at least ${min}`);
      return;
    }

    if (max !== undefined && numericValue > max) {
      setError(`Value must be at most ${max}`);
      return;
    }

    onChange(numericValue);
  };

  /**
   * Format display value
   */
  const formatDisplayValue = (val: number | null | undefined): string => {
    if (val === null || val === undefined) {
      return 'N/A';
    }

    switch (type) {
      case 'percentage':
        return `${val.toFixed(1)}%`;
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(val);
      case 'number':
      default:
        return step < 1 ? val.toFixed(1) : val.toString();
    }
  };

  /**
   * Get status color based on target
   */
  const getStatusColor = (): 'success' | 'warning' | 'error' | 'default' => {
    if (!showStatus || !target || value === null || value === undefined) {
      return 'default';
    }

    const percentage = (value / target) * 100;
    
    if (percentage >= 95) return 'success';
    if (percentage >= 80) return 'warning';
    return 'error';
  };

  /**
   * Get status icon
   */
  const getStatusIcon = () => {
    if (!showStatus || !target || value === null || value === undefined) {
      return null;
    }

    const color = getStatusColor();
    const iconProps = { fontSize: 'small' as const };

    switch (color) {
      case 'success':
        return <CheckIcon color="success" {...iconProps} />;
      case 'warning':
        return <WarningIcon color="warning" {...iconProps} />;
      case 'error':
        return <ErrorIcon color="error" {...iconProps} />;
      default:
        return null;
    }
  };

  if (isEditing && !disabled) {
    return (
      <TableCell>
        <Box display="flex" flexDirection="column" gap={0.5}>
          {label && (
            <Typography variant="caption" color="text.secondary">
              {label}
            </Typography>
          )}
          <TextField
            value={localValue}
            onChange={handleChange}
            type="number"
            size="small"
            fullWidth
            error={!!error}
            helperText={error}
            inputProps={{
              step,
              min: allowNegative ? undefined : (min ?? 0),
              max,
            }}
            sx={{
              '& .MuiInputBase-input': {
                fontSize: '0.875rem',
                padding: '8px 12px',
              },
            }}
          />
          {target && (
            <Typography variant="caption" color="text.secondary">
              Target: {formatDisplayValue(target)}
            </Typography>
          )}
        </Box>
      </TableCell>
    );
  }

  return (
    <TableCell>
      <Box display="flex" flexDirection="column" alignItems="flex-start" gap={0.5}>
        {label && (
          <Typography variant="caption" color="text.secondary">
            {label}
          </Typography>
        )}
        
        <Box display="flex" alignItems="center" gap={1}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: value !== null && value !== undefined ? 500 : 400,
              color: value !== null && value !== undefined ? 'text.primary' : 'text.secondary',
            }}
          >
            {formatDisplayValue(value)}
          </Typography>
          
          {getStatusIcon()}
        </Box>

        {target && showStatus && (
          <Tooltip title={`Target: ${formatDisplayValue(target)}`}>
            <Chip
              label={`${((value || 0) / target * 100).toFixed(0)}%`}
              size="small"
              color={getStatusColor()}
              variant="outlined"
            />
          </Tooltip>
        )}
      </Box>
    </TableCell>
  );
};

// Specialized metric cells for common use cases
export const FteMetricCell: React.FC<{
  value: number | null | undefined;
  isEditing: boolean;
  onChange: (value: number | null) => void;
  disabled?: boolean;
}> = ({ value, isEditing, onChange, disabled }) => (
  <MetricCell
    value={value}
    isEditing={isEditing}
    onChange={onChange}
    type="number"
    step={0.1}
    min={0}
    max={999.9}
    disabled={disabled}
  />
);

export const PercentageMetricCell: React.FC<{
  value: number | null | undefined;
  isEditing: boolean;
  onChange: (value: number | null) => void;
  target?: number;
  showStatus?: boolean;
  disabled?: boolean;
}> = ({ value, isEditing, onChange, target, showStatus, disabled }) => (
  <MetricCell
    value={value}
    isEditing={isEditing}
    onChange={onChange}
    type="percentage"
    step={0.1}
    min={0}
    max={100}
    target={target}
    showStatus={showStatus}
    disabled={disabled}
  />
);

export const AttritionMetricCell: React.FC<{
  value: number | null | undefined;
  isEditing: boolean;
  onChange: (value: number | null) => void;
  label: string;
  disabled?: boolean;
}> = ({ value, isEditing, onChange, label, disabled }) => (
  <MetricCell
    value={value}
    isEditing={isEditing}
    onChange={onChange}
    type="number"
    step={1}
    min={0}
    max={999}
    label={label}
    disabled={disabled}
  />
);

export const VarianceMetricCell: React.FC<{
  value: number | null | undefined;
  isEditing: boolean;
  onChange: (value: number | null) => void;
  disabled?: boolean;
}> = ({ value, isEditing, onChange, disabled }) => (
  <MetricCell
    value={value}
    isEditing={isEditing}
    onChange={onChange}
    type="percentage"
    step={0.01}
    min={-999.99}
    max={999.99}
    allowNegative={true}
    disabled={disabled}
  />
);
