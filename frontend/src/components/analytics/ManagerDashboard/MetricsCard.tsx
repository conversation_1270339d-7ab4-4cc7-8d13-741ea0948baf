import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';

interface MetricsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    value: number;
    label: string;
    isPositive: boolean;
  };
  tooltip?: string;
  onClick?: () => void;
}

export const MetricsCard: React.FC<MetricsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  trend,
  tooltip,
  onClick,
}) => {
  const getColorValue = (colorName: string) => {
    const colorMap = {
      primary: '#1976d2',
      secondary: '#dc004e',
      success: '#2e7d32',
      warning: '#ed6c02',
      error: '#d32f2f',
      info: '#0288d1',
    };
    return colorMap[colorName as keyof typeof colorMap] || colorMap.primary;
  };

  const cardContent = (
    <Card
      sx={{
        height: '100%',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.2s ease-in-out',
        '&:hover': onClick ? {
          transform: 'translateY(-2px)',
          boxShadow: 3,
        } : {},
        borderTop: `4px solid ${getColorValue(color)}`,
      }}
      onClick={onClick}
    >
      <CardContent sx={{ p: 3 }}>
        <Box display="flex" alignItems="flex-start" justifyContent="space-between">
          <Box flex={1}>
            <Typography
              variant="h6"
              component="h3"
              color="text.secondary"
              gutterBottom
              sx={{ fontSize: '0.875rem', fontWeight: 500 }}
            >
              {title}
            </Typography>
            
            <Typography
              variant="h4"
              component="div"
              sx={{
                fontWeight: 'bold',
                color: 'text.primary',
                mb: 1,
                fontSize: { xs: '1.5rem', sm: '2rem' },
              }}
            >
              {value}
            </Typography>
            
            {subtitle && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mb: trend ? 1 : 0 }}
              >
                {subtitle}
              </Typography>
            )}
            
            {trend && (
              <Box display="flex" alignItems="center" gap={0.5}>
                {trend.isPositive ? (
                  <TrendingUpIcon 
                    sx={{ 
                      fontSize: 16, 
                      color: trend.isPositive ? 'success.main' : 'error.main' 
                    }} 
                  />
                ) : (
                  <TrendingDownIcon 
                    sx={{ 
                      fontSize: 16, 
                      color: 'error.main' 
                    }} 
                  />
                )}
                <Typography
                  variant="caption"
                  sx={{
                    color: trend.isPositive ? 'success.main' : 'error.main',
                    fontWeight: 500,
                  }}
                >
                  {Math.abs(trend.value)}% {trend.label}
                </Typography>
              </Box>
            )}
          </Box>
          
          {icon && (
            <Avatar
              sx={{
                bgcolor: `${getColorValue(color)}20`,
                color: getColorValue(color),
                width: 56,
                height: 56,
              }}
            >
              {icon}
            </Avatar>
          )}
        </Box>
      </CardContent>
    </Card>
  );

  if (tooltip) {
    return (
      <Tooltip title={tooltip} arrow>
        {cardContent}
      </Tooltip>
    );
  }

  return cardContent;
};

// Specialized metrics cards for common use cases
export const FteMetricsCard: React.FC<{
  value: number;
  target?: number;
  onClick?: () => void;
}> = ({ value, target, onClick }) => {
  const trend = target ? {
    value: Math.abs(((value - target) / target) * 100),
    label: 'vs target',
    isPositive: value >= target,
  } : undefined;

  return (
    <MetricsCard
      title="Full-Time Equivalents"
      value={value}
      subtitle={target ? `Target: ${target}` : undefined}
      color="primary"
      trend={trend}
      onClick={onClick}
    />
  );
};

export const UtilizationMetricsCard: React.FC<{
  value: number;
  target?: number;
  onClick?: () => void;
}> = ({ value, target, onClick }) => {
  const trend = target ? {
    value: Math.abs(value - target),
    label: 'vs target',
    isPositive: value >= target,
  } : undefined;

  return (
    <MetricsCard
      title="Utilization Rate"
      value={`${value.toFixed(1)}%`}
      subtitle={target ? `Target: ${target}%` : undefined}
      color="success"
      trend={trend}
      onClick={onClick}
    />
  );
};

export const AttritionMetricsCard: React.FC<{
  resigned: number;
  involuntary: number;
  onClick?: () => void;
}> = ({ resigned, involuntary, onClick }) => {
  const total = resigned + involuntary;
  
  return (
    <MetricsCard
      title="Attrition"
      value={total}
      subtitle={`${resigned} resigned, ${involuntary} involuntary`}
      color={total > 5 ? 'warning' : total > 10 ? 'error' : 'success'}
      onClick={onClick}
    />
  );
};

export const ComplianceMetricsCard: React.FC<{
  score: number;
  target?: number;
  onClick?: () => void;
}> = ({ score, target = 95, onClick }) => {
  const trend = {
    value: Math.abs(score - target),
    label: 'vs target',
    isPositive: score >= target,
  };

  return (
    <MetricsCard
      title="Compliance Score"
      value={`${score.toFixed(1)}%`}
      subtitle={`Target: ${target}%`}
      color={score >= target ? 'success' : score >= 80 ? 'warning' : 'error'}
      trend={trend}
      onClick={onClick}
    />
  );
};
