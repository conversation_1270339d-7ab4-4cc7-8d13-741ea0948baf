import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ManagerDashboard } from '../ManagerDashboard';
import { AuthContext } from '../../../../contexts/AuthContext';
import * as managerDashboardApi from '../../../../services/managerDashboardApi';

// Mock the API service
jest.mock('../../../../services/managerDashboardApi');
const mockManagerDashboardApi = managerDashboardApi as jest.Mocked<typeof managerDashboardApi>;

// Mock the custom hook
jest.mock('../../../../hooks/useManagerDashboard');

const theme = createTheme();

const mockAuthContext = {
  user: {
    id: 1,
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: 'manager',
  },
  isAuthenticated: true,
  isLoading: false,
  login: jest.fn(),
  logout: jest.fn(),
  refreshToken: jest.fn(),
};

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <AuthContext.Provider value={mockAuthContext}>
        {children}
      </AuthContext.Provider>
    </LocalizationProvider>
  </ThemeProvider>
);

describe('ManagerDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock the useManagerDashboard hook
    const mockUseManagerDashboard = require('../../../../hooks/useManagerDashboard').useManagerDashboard;
    mockUseManagerDashboard.mockReturnValue({
      dashboardMetrics: [
        {
          id: 1,
          organizationalUnitId: 1,
          managerId: 1,
          reportingPeriod: '2024-01-01',
          fteCount: 10,
          attritionResigned: 1,
          attritionInvoluntary: 0,
          slaPercentage: 95.5,
          utilizationPercentage: 87.2,
          status: 'draft',
          organizationalUnit: {
            id: 1,
            name: 'Engineering Team',
            parent: {
              id: 2,
              name: 'Technology Division',
            },
          },
          manager: {
            id: 1,
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
          },
        },
      ],
      organizationalUnits: [
        {
          id: 1,
          name: 'Engineering Team',
          managerId: 1,
          isActive: true,
        },
      ],
      managers: [
        {
          id: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'manager',
        },
      ],
      summary: {
        totalFte: 10,
        avgUtilization: 87.2,
        totalUnits: 1,
        totalAttrition: 1,
        completedUnits: 0,
        pendingUnits: 1,
      },
      loading: false,
      error: null,
      updateMetrics: jest.fn(),
      refreshData: jest.fn(),
    });
  });

  it('renders the dashboard title', () => {
    render(
      <TestWrapper>
        <ManagerDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('AEVEN Manager Dashboard')).toBeInTheDocument();
  });

  it('displays summary cards with correct data', () => {
    render(
      <TestWrapper>
        <ManagerDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Total FTE')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText('Average Utilization')).toBeInTheDocument();
    expect(screen.getByText('87.2%')).toBeInTheDocument();
    expect(screen.getByText('Organizational Units')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('displays the dashboard table with metrics', () => {
    render(
      <TestWrapper>
        <ManagerDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Engineering Team')).toBeInTheDocument();
    expect(screen.getByText('Technology Division')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('shows loading state when data is being fetched', () => {
    const mockUseManagerDashboard = require('../../../../hooks/useManagerDashboard').useManagerDashboard;
    mockUseManagerDashboard.mockReturnValue({
      dashboardMetrics: [],
      organizationalUnits: [],
      managers: [],
      summary: null,
      loading: true,
      error: null,
      updateMetrics: jest.fn(),
      refreshData: jest.fn(),
    });

    render(
      <TestWrapper>
        <ManagerDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Loading dashboard data...')).toBeInTheDocument();
  });

  it('displays error message when there is an error', () => {
    const mockUseManagerDashboard = require('../../../../hooks/useManagerDashboard').useManagerDashboard;
    mockUseManagerDashboard.mockReturnValue({
      dashboardMetrics: [],
      organizationalUnits: [],
      managers: [],
      summary: null,
      loading: false,
      error: 'Failed to load dashboard data',
      updateMetrics: jest.fn(),
      refreshData: jest.fn(),
    });

    render(
      <TestWrapper>
        <ManagerDashboard />
      </TestWrapper>
    );

    expect(screen.getByText('Failed to load dashboard data')).toBeInTheDocument();
  });

  it('calls refreshData when refresh button is clicked', async () => {
    const mockRefreshData = jest.fn();
    const mockUseManagerDashboard = require('../../../../hooks/useManagerDashboard').useManagerDashboard;
    mockUseManagerDashboard.mockReturnValue({
      dashboardMetrics: [],
      organizationalUnits: [],
      managers: [
        {
          id: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'manager',
        },
      ],
      summary: null,
      loading: false,
      error: null,
      updateMetrics: jest.fn(),
      refreshData: mockRefreshData,
    });

    render(
      <TestWrapper>
        <ManagerDashboard />
      </TestWrapper>
    );

    const refreshButton = screen.getByText('Refresh Data');
    fireEvent.click(refreshButton);

    expect(mockRefreshData).toHaveBeenCalled();
  });

  it('shows empty state when no data is available', () => {
    const mockUseManagerDashboard = require('../../../../hooks/useManagerDashboard').useManagerDashboard;
    mockUseManagerDashboard.mockReturnValue({
      dashboardMetrics: [],
      organizationalUnits: [],
      managers: [
        {
          id: 1,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'manager',
        },
      ],
      summary: null,
      loading: false,
      error: null,
      updateMetrics: jest.fn(),
      refreshData: jest.fn(),
    });

    render(
      <TestWrapper>
        <ManagerDashboard managerId={1} />
      </TestWrapper>
    );

    expect(screen.getByText('No dashboard data found')).toBeInTheDocument();
    expect(screen.getByText('No metrics found for the selected manager and period.')).toBeInTheDocument();
  });
});
