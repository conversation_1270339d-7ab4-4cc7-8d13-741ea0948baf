import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  Psychology as PsychologyIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { ApiService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import AttritionHeatmapWidget from './widgets/AttritionHeatmapWidget';
import EngagementTrendsWidget from './widgets/EngagementTrendsWidget';
import SkillGapAnalysisWidget from './widgets/SkillGapAnalysisWidget';
import SentimentAnalysisWidget from './widgets/SentimentAnalysisWidget';
import AiInsightsWidget from './widgets/AiInsightsWidget';
import PredictiveAnalyticsWidget from './widgets/PredictiveAnalyticsWidget';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const PeopleAnalyticsDashboard: React.FC = () => {
  const { user: _user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [filters, setFilters] = useState({
    department: 'all',
    timeRange: '6months',
    team: 'all',
  });

  // Sample departments and teams for filters
  const departments = ['all', 'Engineering', 'Sales', 'Marketing', 'HR', 'Finance'];
  const teams = ['all', 'Frontend', 'Backend', 'DevOps', 'Sales Team A', 'Marketing Team B'];

  useEffect(() => {
    loadDashboardData();
  }, [filters]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load AI dashboard summary
      const summaryResponse = await ApiService.get('/ai/dashboard/summary');
      if (summaryResponse.data) {
        setDashboardData(summaryResponse.data);
      }
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleFilterChange = (filterType: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value,
    }));
  };

  const generateInsights = async () => {
    try {
      setLoading(true);
      const response = await ApiService.post('/ai/insights/generate');
      if (response.data) {
        await loadDashboardData(); // Reload to get new insights
      }
    } catch (err) {
      console.error('Error generating insights:', err);
      setError('Failed to generate insights');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !dashboardData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          People Analytics Dashboard
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          AI-powered insights into your workforce dynamics, engagement, and performance
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" alignItems="center" gap={2} flexWrap="wrap">
          <FilterIcon color="action" />
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Department</InputLabel>
            <Select
              value={filters.department}
              label="Department"
              onChange={(e) => handleFilterChange('department', e.target.value)}
            >
              {departments.map(dept => (
                <MenuItem key={dept} value={dept}>
                  {dept === 'all' ? 'All Departments' : dept}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={filters.timeRange}
              label="Time Range"
              onChange={(e) => handleFilterChange('timeRange', e.target.value)}
            >
              <MenuItem value="1month">Last Month</MenuItem>
              <MenuItem value="3months">Last 3 Months</MenuItem>
              <MenuItem value="6months">Last 6 Months</MenuItem>
              <MenuItem value="1year">Last Year</MenuItem>
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Team</InputLabel>
            <Select
              value={filters.team}
              label="Team"
              onChange={(e) => handleFilterChange('team', e.target.value)}
            >
              {teams.map(team => (
                <MenuItem key={team} value={team}>
                  {team === 'all' ? 'All Teams' : team}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<PsychologyIcon />}
            onClick={generateInsights}
            disabled={loading}
          >
            Generate AI Insights
          </Button>
        </Box>
      </Paper>

      {/* Key Metrics Summary */}
      {dashboardData && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      AI Health Score
                    </Typography>
                    <Typography variant="h4">
                      {dashboardData.healthScore || 75}
                    </Typography>
                  </Box>
                  <PsychologyIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
                <Box mt={1}>
                  <Chip
                    label={dashboardData.healthScore >= 80 ? 'Excellent' : dashboardData.healthScore >= 60 ? 'Good' : 'Needs Attention'}
                    color={dashboardData.healthScore >= 80 ? 'success' : dashboardData.healthScore >= 60 ? 'warning' : 'error'}
                    size="small"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Attrition Risk
                    </Typography>
                    <Typography variant="h4">
                      {dashboardData.attritionRisk?.atRiskCount || 0}
                    </Typography>
                  </Box>
                  <WarningIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  of {dashboardData.attritionRisk?.totalEmployees || 0} employees
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Engagement Score
                    </Typography>
                    <Typography variant="h4">
                      {dashboardData.sentimentTrends?.currentScore || 0}
                    </Typography>
                  </Box>
                  <Box>
                    {dashboardData.sentimentTrends?.trend === 'improving' ? (
                      <TrendingUpIcon color="success" sx={{ fontSize: 40 }} />
                    ) : dashboardData.sentimentTrends?.trend === 'declining' ? (
                      <TrendingDownIcon color="error" sx={{ fontSize: 40 }} />
                    ) : (
                      <TimelineIcon color="info" sx={{ fontSize: 40 }} />
                    )}
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {dashboardData.sentimentTrends?.trend || 'stable'} trend
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography color="text.secondary" gutterBottom>
                      Critical Skill Gaps
                    </Typography>
                    <Typography variant="h4">
                      {dashboardData.skillGaps?.criticalGaps || 0}
                    </Typography>
                  </Box>
                  <AssessmentIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
                <Typography variant="body2" color="text.secondary">
                  of {dashboardData.skillGaps?.totalSkillsAssessed || 0} skills assessed
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Tabs for different analytics views */}
      <Paper sx={{ width: '100%' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="analytics dashboard tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Overview" />
          <Tab label="Attrition Analysis" />
          <Tab label="Engagement Insights" />
          <Tab label="Skill Analytics" />
          <Tab label="Predictive Models" />
          <Tab label="AI Insights" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={6}>
              <EngagementTrendsWidget filters={filters} />
            </Grid>
            <Grid item xs={12} lg={6}>
              <AttritionHeatmapWidget filters={filters} />
            </Grid>
            <Grid item xs={12}>
              <AiInsightsWidget insights={dashboardData?.recentInsights || []} />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <AttritionHeatmapWidget filters={filters} detailed={true} />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} lg={8}>
              <SentimentAnalysisWidget filters={filters} />
            </Grid>
            <Grid item xs={12} lg={4}>
              <EngagementTrendsWidget filters={filters} compact={true} />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <SkillGapAnalysisWidget filters={filters} />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <PredictiveAnalyticsWidget filters={filters} />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={5}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <AiInsightsWidget insights={dashboardData?.recentInsights || []} detailed={true} />
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default PeopleAnalyticsDashboard;
