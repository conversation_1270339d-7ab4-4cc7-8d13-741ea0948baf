import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider,
  LinearProgress,
} from '@mui/material';
import {
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';
import { getRiskColor, handleAnalyticsError } from '../../../utils/analytics';

interface AttritionRiskData {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  riskScore: number;
  totalEmployees: number;
  atRiskCount: number;
  riskDistribution: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  topRiskFactors: string[];
  recommendations: string[];
}

const AttritionRiskWidget: React.FC = () => {
  const [data, setData] = useState<AttritionRiskData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAttritionData();
  }, []);

  const loadAttritionData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 🔐 NIS2-COMPLIANT: Use real data from centralized API
      const response = await ApiService.getAttritionRiskAnalysis();

      if (response && response.success) {
        // Transform the data to match our interface
        const transformedData: AttritionRiskData = {
          overallRisk: response.data.overallRisk,
          riskScore: response.data.riskScore,
          totalEmployees: response.data.totalEmployees,
          atRiskCount: response.data.atRiskCount,
          riskDistribution: response.data.riskDistribution,
          topRiskFactors: response.data.topRiskFactors || [],
          recommendations: response.data.recommendations || []
        };
        setData(transformedData);
      } else {
        throw new Error(response?.error || 'Failed to load attrition risk data');
      }
    } catch (err) {
      setError(handleAnalyticsError(err, 'Attrition Risk Widget'));

      // Fallback to basic data structure if API fails
      setData({
        overallRisk: 'low',
        riskScore: 0,
        totalEmployees: 0,
        atRiskCount: 0,
        riskDistribution: { low: 0, medium: 0, high: 0, critical: 0 },
        topRiskFactors: [],
        recommendations: ['Unable to load risk analysis. Please check system connectivity.']
      });
    } finally {
      setLoading(false);
    }
  };



  const getRiskIcon = (risk: string) => {
    switch (risk) {
      case 'low':
        return <CheckCircleIcon sx={{ color: '#4caf50' }} />;
      case 'medium':
        return <WarningIcon sx={{ color: '#ff9800' }} />;
      case 'high':
      case 'critical':
        return <WarningIcon sx={{ color: '#f44336' }} />;
      default:
        return <WarningIcon />;
    }
  };

  if (loading) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Typography variant="body2" color="textSecondary">
            No attrition risk data available
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: 400 }}>
      <CardContent sx={{ height: '100%', overflow: 'auto' }}>
        {/* Header */}
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar sx={{ bgcolor: getRiskColor(data.overallRisk), mr: 2 }}>
            {getRiskIcon(data.overallRisk)}
          </Avatar>
          <Box>
            <Typography variant="h6">Attrition Risk</Typography>
            <Chip
              label={`${data.overallRisk.toUpperCase()} RISK`}
              size="small"
              sx={{
                bgcolor: getRiskColor(data.overallRisk),
                color: 'white',
                fontWeight: 'bold',
              }}
            />
          </Box>
        </Box>

        {/* Risk Score */}
        <Box mb={3}>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
            <Typography variant="h4" sx={{ color: getRiskColor(data.overallRisk) }}>
              {(data.riskScore * 100).toFixed(0)}%
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {data.atRiskCount} of {data.totalEmployees} at risk
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={data.riskScore * 100}
            sx={{
              height: 8,
              borderRadius: 4,
              bgcolor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                bgcolor: getRiskColor(data.overallRisk),
              },
            }}
          />
        </Box>

        {/* Risk Distribution */}
        <Box mb={3}>
          <Typography variant="subtitle2" color="textSecondary" gutterBottom>
            Risk Distribution
          </Typography>
          <Box display="flex" gap={1} flexWrap="wrap">
            <Chip
              label={`Low: ${data.riskDistribution.low}`}
              size="small"
              sx={{ bgcolor: '#e8f5e8', color: '#2e7d32' }}
            />
            <Chip
              label={`Medium: ${data.riskDistribution.medium}`}
              size="small"
              sx={{ bgcolor: '#fff3e0', color: '#f57c00' }}
            />
            <Chip
              label={`High: ${data.riskDistribution.high}`}
              size="small"
              sx={{ bgcolor: '#ffebee', color: '#d32f2f' }}
            />
            {data.riskDistribution.critical > 0 && (
              <Chip
                label={`Critical: ${data.riskDistribution.critical}`}
                size="small"
                sx={{ bgcolor: '#ffcdd2', color: '#b71c1c' }}
              />
            )}
          </Box>
        </Box>

        {/* Top Risk Factors */}
        <Box mb={2}>
          <Typography variant="subtitle2" color="textSecondary" gutterBottom>
            Top Risk Factors
          </Typography>
          <List dense>
            {data.topRiskFactors.slice(0, 3).map((factor, index) => (
              <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                <ListItemAvatar sx={{ minWidth: 32 }}>
                  <Avatar sx={{ width: 24, height: 24, bgcolor: 'warning.main', fontSize: 12 }}>
                    {index + 1}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={factor}
                  primaryTypographyProps={{ variant: 'body2' }}
                />
              </ListItem>
            ))}
          </List>
        </Box>

        <Divider sx={{ my: 1 }} />

        {/* Quick Recommendations */}
        <Box>
          <Typography variant="subtitle2" color="textSecondary" gutterBottom>
            Recommended Actions
          </Typography>
          <List dense>
            {data.recommendations.slice(0, 2).map((recommendation, index) => (
              <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                <ListItemAvatar sx={{ minWidth: 32 }}>
                  <TrendingUpIcon sx={{ width: 20, height: 20, color: 'success.main' }} />
                </ListItemAvatar>
                <ListItemText
                  primary={recommendation}
                  primaryTypographyProps={{ variant: 'body2' }}
                />
              </ListItem>
            ))}
          </List>
        </Box>
      </CardContent>
    </Card>
  );
};

export default AttritionRiskWidget;
