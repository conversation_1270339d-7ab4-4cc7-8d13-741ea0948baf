import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardH<PERSON>er,
  CardContent,
  Box,
  Typography,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Psychology as AiIcon,
  Insights as InsightsIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';
import { getRiskColor } from '../../../utils/analytics';

interface PredictiveAnalyticsWidgetProps {
  filters: any;
}

interface PredictionModel {
  name: string;
  type: string;
  accuracy: number;
  lastTrained: string;
  predictions: number;
}

interface RiskPrediction {
  employeeId: number;
  employeeName: string;
  department: string;
  riskScore: number;
  riskLevel: string;
  factors: string[];
  confidence: number;
}

const PredictiveAnalyticsWidget: React.FC<PredictiveAnalyticsWidgetProps> = ({ filters }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [predictiveData, setPredictiveData] = useState<any>(null);
  const [selectedModel, setSelectedModel] = useState('attrition');
  const [timeHorizon, setTimeHorizon] = useState('3months');

  useEffect(() => {
    loadPredictiveData();
  }, [filters, selectedModel, timeHorizon]);

  const loadPredictiveData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load model performance
      const modelsResponse = await ApiService.get('/ai/predictive/models');

      // Load predictions based on selected model
      let predictionsResponse;
      if (selectedModel === 'attrition') {
        predictionsResponse = await ApiService.get('/ai/attrition/predictions', {
          params: {
            timeHorizon,
            department: filters.department !== 'all' ? filters.department : undefined,
          },
        });
      }

      if (modelsResponse.data) {
        setPredictiveData({
          models: modelsResponse.data || [],
          predictions: predictionsResponse?.data || {},
        });
      }
    } catch (err) {
      console.error('Error loading predictive analytics data:', err);
      setError('Failed to load predictive analytics');
    } finally {
      setLoading(false);
    }
  };

  const runPrediction = async () => {
    try {
      setLoading(true);
      const response = await ApiService.post('/ai/predictive/run', {
        model: selectedModel,
        timeHorizon,
        filters,
      });

      if (response.data) {
        await loadPredictiveData();
      }
    } catch (err) {
      console.error('Error running prediction:', err);
      setError('Failed to run prediction');
    } finally {
      setLoading(false);
    }
  };

  const runAnomalyDetection = async () => {
    try {
      setLoading(true);
      const response = await ApiService.get('/ai/predictive/anomalies', {
        params: {
          department: filters.department !== 'all' ? filters.department : undefined,
        },
      });

      if (response.data) {
        setPredictiveData((prev: any) => ({
          ...prev,
          anomalies: response.data,
        }));
      }
    } catch (err) {
      console.error('Error detecting anomalies:', err);
      setError('Failed to detect anomalies');
    } finally {
      setLoading(false);
    }
  };



  const getModelAccuracyColor = (accuracy: number) => {
    if (accuracy >= 90) return 'success';
    if (accuracy >= 80) return 'info';
    if (accuracy >= 70) return 'warning';
    return 'error';
  };

  if (loading && !predictiveData) {
    return (
      <Card>
        <CardHeader title="Predictive Analytics" />
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Predictive Analytics"
        subheader="AI-powered predictions and risk modeling for workforce planning"
        action={
          <Box display="flex" gap={1}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Model</InputLabel>
              <Select
                value={selectedModel}
                label="Model"
                onChange={(e) => setSelectedModel(e.target.value)}
              >
                <MenuItem value="attrition">Attrition Risk</MenuItem>
                <MenuItem value="performance">Performance</MenuItem>
                <MenuItem value="anomaly">Anomaly Detection</MenuItem>
                <MenuItem value="engagement">Engagement</MenuItem>
              </Select>
            </FormControl>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Time Horizon</InputLabel>
              <Select
                value={timeHorizon}
                label="Time Horizon"
                onChange={(e) => setTimeHorizon(e.target.value)}
              >
                <MenuItem value="1month">1 Month</MenuItem>
                <MenuItem value="3months">3 Months</MenuItem>
                <MenuItem value="6months">6 Months</MenuItem>
                <MenuItem value="1year">1 Year</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="contained"
              startIcon={<AiIcon />}
              onClick={selectedModel === 'anomaly' ? runAnomalyDetection : runPrediction}
              disabled={loading}
              size="small"
            >
              {selectedModel === 'anomaly' ? 'Detect Anomalies' : 'Run Prediction'}
            </Button>
          </Box>
        }
      />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Model Performance Overview */}
        {predictiveData?.models && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={8}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Model Performance
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Model</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell align="right">Accuracy</TableCell>
                        <TableCell align="right">Predictions</TableCell>
                        <TableCell>Last Trained</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {predictiveData.models.map((model: PredictionModel, index: number) => (
                        <TableRow key={index}>
                          <TableCell component="th" scope="row">
                            {model.name}
                          </TableCell>
                          <TableCell>{model.type}</TableCell>
                          <TableCell align="right">
                            <Box display="flex" alignItems="center" gap={1}>
                              <LinearProgress
                                variant="determinate"
                                value={model.accuracy}
                                sx={{ width: 60, height: 8 }}
                                color={getModelAccuracyColor(model.accuracy) as any}
                              />
                              <Typography variant="body2">
                                {model.accuracy.toFixed(1)}%
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell align="right">{model.predictions}</TableCell>
                          <TableCell>
                            {new Date(model.lastTrained).toLocaleDateString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </Grid>
            <Grid item xs={12} md={4}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Prediction Summary
                </Typography>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Selected Model
                  </Typography>
                  <Typography variant="h6" color="primary">
                    {selectedModel.charAt(0).toUpperCase() + selectedModel.slice(1)} Risk
                  </Typography>
                </Box>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Time Horizon
                  </Typography>
                  <Typography variant="h6">
                    {timeHorizon.replace(/(\d+)/, '$1 ').replace(/([a-z])([A-Z])/, '$1 $2')}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Confidence Level
                  </Typography>
                  <Typography variant="h6" color="success.main">
                    {predictiveData.predictions?.confidence || 85}%
                  </Typography>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        )}

        {/* Prediction Results */}
        {selectedModel === 'attrition' && predictiveData?.predictions && (
          <>
            {/* Risk Distribution */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={4}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="error">
                    {predictiveData.predictions.highRisk || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    High Risk Employees
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="warning.main">
                    {predictiveData.predictions.mediumRisk || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Medium Risk Employees
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="success.main">
                    {predictiveData.predictions.lowRisk || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Low Risk Employees
                  </Typography>
                </Paper>
              </Grid>
            </Grid>

            {/* Top Risk Predictions */}
            {predictiveData.predictions.topRiskEmployees && (
              <Paper sx={{ p: 2, mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Top Risk Predictions
                </Typography>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Employee</TableCell>
                        <TableCell>Department</TableCell>
                        <TableCell align="right">Risk Score</TableCell>
                        <TableCell align="center">Risk Level</TableCell>
                        <TableCell align="right">Confidence</TableCell>
                        <TableCell>Key Factors</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {predictiveData.predictions.topRiskEmployees.slice(0, 10).map((prediction: RiskPrediction, index: number) => (
                        <TableRow key={index}>
                          <TableCell component="th" scope="row">
                            {prediction.employeeName}
                          </TableCell>
                          <TableCell>{prediction.department}</TableCell>
                          <TableCell align="right">
                            {prediction.riskScore.toFixed(1)}
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              label={prediction.riskLevel}
                              sx={{
                                backgroundColor: getRiskColor(prediction.riskLevel),
                                color: 'white',
                              }}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="right">
                            {(prediction.confidence * 100).toFixed(0)}%
                          </TableCell>
                          <TableCell>
                            <Box display="flex" gap={0.5} flexWrap="wrap">
                              {prediction.factors.slice(0, 3).map((factor, idx) => (
                                <Chip
                                  key={idx}
                                  label={factor}
                                  size="small"
                                  variant="outlined"
                                />
                              ))}
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            )}
          </>
        )}

        {/* Anomaly Detection Results */}
        {selectedModel === 'anomaly' && predictiveData?.anomalies && (
          <Paper sx={{ p: 2, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Anomaly Detection Results
            </Typography>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={3}>
                <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#ffebee' }}>
                  <Typography variant="h4" color="error">
                    {predictiveData.anomalies.highSeverity || 0}
                  </Typography>
                  <Typography variant="body2">High Severity</Typography>
                </Paper>
              </Grid>
              <Grid item xs={3}>
                <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#fff3e0' }}>
                  <Typography variant="h4" color="warning.main">
                    {predictiveData.anomalies.mediumSeverity || 0}
                  </Typography>
                  <Typography variant="body2">Medium Severity</Typography>
                </Paper>
              </Grid>
              <Grid item xs={3}>
                <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#e8f5e8' }}>
                  <Typography variant="h4" color="success.main">
                    {predictiveData.anomalies.lowSeverity || 0}
                  </Typography>
                  <Typography variant="body2">Low Severity</Typography>
                </Paper>
              </Grid>
              <Grid item xs={3}>
                <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#f3e5f5' }}>
                  <Typography variant="h4" color="primary">
                    {predictiveData.anomalies.totalAnomalies || 0}
                  </Typography>
                  <Typography variant="body2">Total Anomalies</Typography>
                </Paper>
              </Grid>
            </Grid>

            {/* Anomaly Categories */}
            <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
              Anomalies by Category
            </Typography>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              {predictiveData.anomalies.categories && Object.entries(predictiveData.anomalies.categories).map(([category, count]) => (
                <Grid item xs={6} sm={3} key={category}>
                  <Paper sx={{ p: 1.5, textAlign: 'center' }}>
                    <Typography variant="h6">{String(count)}</Typography>
                    <Typography variant="caption" sx={{ textTransform: 'capitalize' }}>
                      {category}
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>

            {/* Top Anomalies */}
            {predictiveData.anomalies.anomalies && predictiveData.anomalies.anomalies.length > 0 && (
              <>
                <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                  Top Anomalies
                </Typography>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Employee</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Severity</TableCell>
                        <TableCell>Confidence</TableCell>
                        <TableCell>Description</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {predictiveData.anomalies.anomalies.slice(0, 10).map((anomaly: any, index: number) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {anomaly.userName}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {anomaly.department}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={anomaly.type}
                              size="small"
                              variant="outlined"
                              sx={{ textTransform: 'capitalize' }}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={anomaly.severity}
                              size="small"
                              color={
                                anomaly.severity === 'high' ? 'error' :
                                  anomaly.severity === 'medium' ? 'warning' : 'success'
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {(anomaly.confidence * 100).toFixed(0)}%
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ maxWidth: 200 }}>
                              {anomaly.description}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            {/* Anomaly Insights */}
            {predictiveData.anomalies.insights && predictiveData.anomalies.insights.length > 0 && (
              <>
                <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                  Key Insights
                </Typography>
                <List>
                  {predictiveData.anomalies.insights.map((insight: any, index: number) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <InsightsIcon
                          color={insight.priority === 'urgent' ? 'error' :
                            insight.priority === 'high' ? 'warning' : 'primary'}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={insight.message}
                        secondary={insight.recommendation}
                      />
                    </ListItem>
                  ))}
                </List>
              </>
            )}
          </Paper>
        )}

        {/* Prediction Insights */}
        {selectedModel !== 'anomaly' && predictiveData?.predictions?.insights && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Predictive Insights
            </Typography>
            <List>
              {predictiveData.predictions.insights.map((insight: any, index: number) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <InsightsIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={insight.message}
                    secondary={`Confidence: ${(insight.confidence * 100).toFixed(0)}% | Impact: ${insight.impact}`}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        )}

        {/* Model Training Status */}
        <Box sx={{ mt: 3 }}>
          <Alert severity="info">
            <Typography variant="subtitle2">Model Training Status</Typography>
            <Typography variant="body2">
              Models are automatically retrained weekly with the latest data.
              Last training: {new Date().toLocaleDateString()}.
              Next training: {new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}.
            </Typography>
          </Alert>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PredictiveAnalyticsWidget;
