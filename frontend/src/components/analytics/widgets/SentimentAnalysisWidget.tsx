import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardHeader,
  CardContent,
  Box,
  Typography,
  Grid,
  Alert,
  CircularProgress,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Psychology as PsychologyIcon,
  Lightbulb as InsightIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';

interface SentimentAnalysisWidgetProps {
  filters: any;
}

const SentimentAnalysisWidget: React.FC<SentimentAnalysisWidgetProps> = ({ filters }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sentimentTrends, setSentimentTrends] = useState<any>(null);
  const [keyDrivers, setKeyDrivers] = useState<any[]>([]);

  useEffect(() => {
    loadSentimentData();
  }, [filters]);

  const loadSentimentData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load sentiment trends
      const trendsResponse = await ApiService.get('/ai/sentiment/trends', {
        params: {
          period: filters.timeRange,
          teamId: filters.team !== 'all' ? filters.team : undefined,
        },
      });
      if (trendsResponse.data) {
        setSentimentTrends(trendsResponse.data);
      }

      // Load key drivers
      const driversResponse = await ApiService.get('/ai/sentiment/key-drivers', {
        params: {
          period: filters.timeRange,
        },
      });
      if (driversResponse.data) {
        setKeyDrivers(driversResponse.data || []);
      }

    } catch (err) {
      console.error('Error loading sentiment data:', err);
      setError('Failed to load sentiment analysis data');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !sentimentTrends) {
    return (
      <Card>
        <CardHeader title="Sentiment Analysis" />
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Sentiment Analysis"
        subheader="AI-powered analysis of employee sentiment and engagement drivers"
        action={
          <Button
            variant="outlined"
            startIcon={<PsychologyIcon />}
            onClick={() => {/* Trigger new analysis */ }}
            size="small"
          >
            Analyze Latest Survey
          </Button>
        }
      />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Current Sentiment Overview */}
        {sentimentTrends && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Current Sentiment Score
                </Typography>
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography variant="h3" color="primary">
                    {sentimentTrends.currentScore || 0}
                  </Typography>
                  <Box>
                    {sentimentTrends.trend === 'improving' ? (
                      <TrendingUpIcon color="success" />
                    ) : sentimentTrends.trend === 'declining' ? (
                      <TrendingDownIcon color="error" />
                    ) : null}
                    <Typography variant="body2" color="text.secondary">
                      {sentimentTrends.trend || 'stable'} trend
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Based on {sentimentTrends.trendData?.length || 0} recent surveys
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Sentiment Distribution
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  No recent analysis data available
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        )}

        {/* Key Engagement Drivers */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                Key Engagement Drivers
              </Typography>
              <List dense>
                {keyDrivers.slice(0, 5).map((driver, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <InsightIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary={driver.driver}
                      secondary={`Impact: ${driver.averageImpact?.toFixed(2)} | Frequency: ${driver.frequency}`}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                Top Themes
              </Typography>
              <Typography variant="body2" color="text.secondary">
                No theme data available
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* Emotional Drivers */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Emotional Drivers
          </Typography>
          <Typography variant="body2" color="text.secondary">
            No emotional driver data available
          </Typography>
        </Paper>

        {/* Sentiment Trend Chart */}
        {sentimentTrends?.trendData && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Sentiment Trends Over Time
            </Typography>
            <Box sx={{ height: 200, display: 'flex', alignItems: 'end', gap: 1 }}>
              {sentimentTrends.trendData.map((point: any, index: number) => (
                <Box
                  key={index}
                  sx={{
                    flex: 1,
                    height: `${(point.score / 100) * 100}%`,
                    backgroundColor: point.score >= 70 ? '#4caf50' : point.score >= 50 ? '#ff9800' : '#f44336',
                    borderRadius: '4px 4px 0 0',
                    minHeight: '20px',
                    position: 'relative',
                  }}
                  title={`${new Date(point.date).toLocaleDateString()}: ${point.score}`}
                >
                  <Typography
                    variant="caption"
                    sx={{
                      position: 'absolute',
                      bottom: -20,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      fontSize: '10px',
                    }}
                  >
                    {new Date(point.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        )}

        {/* AI Insights */}
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            AI-Generated Insights
          </Typography>
          <Typography variant="body2" color="text.secondary">
            No insights available
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default SentimentAnalysisWidget;
