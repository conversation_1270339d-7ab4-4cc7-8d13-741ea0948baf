import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  LinearProgress,
  Chip,
  IconButton,
} from '@mui/material';
import {
  Group as GroupIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { ApiService } from '../../../services/api';

interface TeamMember {
  id: number;
  name: string;
  role: string;
  performanceScore: number;
  trend: 'up' | 'down' | 'stable';
  lastAssessment: string;
}

interface TeamPerformanceData {
  teamName: string;
  averageScore: number;
  memberCount: number;
  completionRate: number;
  topPerformers: TeamMember[];
  needsAttention: TeamMember[];
}

const TeamPerformanceWidget: React.FC = () => {
  const [data, setData] = useState<TeamPerformanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTeamPerformanceData();
  }, []);

  const loadTeamPerformanceData = async () => {
    try {
      setLoading(true);
      const metricsData = await ApiService.getMetricsSummary();
      
      // Process team metrics data
      const teamMetrics = metricsData.team || [];
      const averageScore = teamMetrics.find((m: any) => m.metricName === 'average_performance_score')?.metricValue || 0;

      // 🔐 NIS2-COMPLIANT: Use real API data from team analytics endpoint
      const teamAnalyticsResponse = await ApiService.get('/analytics/team/performance', {
        params: {
          period: '3months'
        }
      });

      const teamAnalytics = teamAnalyticsResponse.data || {};

      // Process real team data
      const realTeamData: TeamPerformanceData = {
        teamName: teamAnalytics.teamName || 'Team',
        averageScore: teamAnalytics.averageScore || averageScore,
        memberCount: teamAnalytics.memberCount || 0,
        completionRate: teamAnalytics.completionRate || 0,
        topPerformers: (teamAnalytics.topPerformers || []).map((performer: any) => ({
          id: performer.id,
          name: performer.name,
          role: performer.role || 'Team Member',
          performanceScore: performer.performanceScore || 0,
          trend: performer.trend || 'stable',
          lastAssessment: performer.lastAssessment || new Date().toISOString().split('T')[0],
        })),
        needsAttention: (teamAnalytics.needsAttention || []).map((member: any) => ({
          id: member.id,
          name: member.name,
          role: member.role || 'Team Member',
          performanceScore: member.performanceScore || 0,
          trend: member.trend || 'stable',
          lastAssessment: member.lastAssessment || new Date().toISOString().split('T')[0],
        })),
      };

      setData(realTeamData);
    } catch (err) {
      setError('Failed to load team performance data');
      console.error('Error loading team performance data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8.5) return 'success';
    if (score >= 7.0) return 'warning';
    return 'error';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon color="success" fontSize="small" />;
      case 'down':
        return <TrendingDownIcon color="error" fontSize="small" />;
      default:
        return null;
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  if (loading) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" height="100%">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Alert severity="error">{error}</Alert>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent>
          <Typography variant="body2" color="textSecondary">
            No team performance data available
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: 400 }}>
      <CardContent sx={{ height: '100%', overflow: 'auto' }}>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center">
            <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
              <GroupIcon />
            </Avatar>
            <Box>
              <Typography variant="h6">{data.teamName}</Typography>
              <Typography variant="caption" color="textSecondary">
                {data.memberCount} members
              </Typography>
            </Box>
          </Box>
          
          <IconButton size="small" onClick={loadTeamPerformanceData}>
            <RefreshIcon />
          </IconButton>
        </Box>

        {/* Team Overview */}
        <Box mb={3}>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
            <Typography variant="h4" color={`${getScoreColor(data.averageScore)}.main`}>
              {data.averageScore.toFixed(1)}
            </Typography>
            <Chip
              label={`${data.completionRate}% Complete`}
              size="small"
              color="success"
              variant="outlined"
            />
          </Box>
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Team Average Performance
          </Typography>
          <LinearProgress
            variant="determinate"
            value={(data.averageScore / 10) * 100}
            sx={{
              height: 6,
              borderRadius: 3,
              bgcolor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                bgcolor: `${getScoreColor(data.averageScore)}.main`,
              },
            }}
          />
        </Box>

        {/* Top Performers */}
        {data.topPerformers.length > 0 && (
          <Box mb={3}>
            <Typography variant="subtitle2" color="textSecondary" gutterBottom>
              Top Performers
            </Typography>
            <List dense>
              {data.topPerformers.map((member) => (
                <ListItem key={member.id} sx={{ px: 0, py: 0.5 }}>
                  <ListItemAvatar>
                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'success.main' }}>
                      {getInitials(member.name)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {member.name}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {member.role}
                          </Typography>
                        </Box>
                        <Box display="flex" alignItems="center">
                          {getTrendIcon(member.trend)}
                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            color={`${getScoreColor(member.performanceScore)}.main`}
                            sx={{ ml: 0.5 }}
                          >
                            {member.performanceScore.toFixed(1)}
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Needs Attention */}
        {data.needsAttention.length > 0 && (
          <Box>
            <Typography variant="subtitle2" color="textSecondary" gutterBottom>
              Needs Attention
            </Typography>
            <List dense>
              {data.needsAttention.map((member) => (
                <ListItem key={member.id} sx={{ px: 0, py: 0.5 }}>
                  <ListItemAvatar>
                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'warning.main' }}>
                      {getInitials(member.name)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {member.name}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {member.role}
                          </Typography>
                        </Box>
                        <Box display="flex" alignItems="center">
                          {getTrendIcon(member.trend)}
                          <Typography
                            variant="body2"
                            fontWeight="bold"
                            color={`${getScoreColor(member.performanceScore)}.main`}
                            sx={{ ml: 0.5 }}
                          >
                            {member.performanceScore.toFixed(1)}
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default TeamPerformanceWidget;
