import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  PerformanceAssessment,
  AssessmentStatus,
  UserRole
} from '../../types';
import { ApiService } from '../../services/api';
import { ReportingService } from '../../services/reporting';
import { Button, Menu, MenuItem, ListItemIcon, ListItemText } from '@mui/material';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import CodeIcon from '@mui/icons-material/Code';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import {
  getAssessmentStatusColor,
  getScoreColor,
  formatAssessmentStatus,
  canEditAssessment,
  canDeleteAssessment
} from '../../utils/assessment-utils';

interface AssessmentDetailProps {
  assessmentId: number;
  userRole: UserRole;
  userId: number;
  onDelete?: () => void;
}

const AssessmentDetail: React.FC<AssessmentDetailProps> = ({
  assessmentId,
  userRole,
  userId,
  onDelete
}) => {
  const navigate = useNavigate();
  const [assessment, setAssessment] = useState<PerformanceAssessment | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<boolean>(false);
  const [exportMenuAnchorEl, setExportMenuAnchorEl] = useState<null | HTMLElement>(null);
  const exportMenuOpen = Boolean(exportMenuAnchorEl);

  useEffect(() => {
    const fetchAssessment = async () => {
      try {
        setIsLoading(true);

        // In a real implementation, this would be an API call
        // const response = await api.get(`/assessments/${assessmentId}`);
        // const assessment = response.data;

        // 🔐 NIS2-COMPLIANT: Use centralized API service for real data
        const response = await ApiService.getAssessment(assessmentId);
        const assessment = response.data || response;

        if (assessment) {
          setAssessment(assessment);
        } else {
          setError('Assessment not found');
        }
        setError(null);
      } catch (err) {
        console.error('Error fetching assessment:', err);
        setError('Failed to load assessment details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssessment();
  }, [assessmentId]);

  const handleDelete = async () => {
    try {
      setIsLoading(true);

      // In a real implementation, this would be an API call
      // await api.delete(`/assessments/${assessmentId}`);

      // Mock successful delete
      setTimeout(() => {
        if (onDelete) {
          onDelete();
        } else {
          navigate('/assessments');
        }
      }, 500);
    } catch (err) {
      console.error('Error deleting assessment:', err);
      setError('Failed to delete assessment. Please try again.');
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: AssessmentStatus) => {
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAssessmentStatusColor(status)}`}>
        {formatAssessmentStatus(status)}
      </span>
    );
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading assessment details...</p>
      </div>
    </div>;
  }

  if (error) {
    return <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <p>{error}</p>
      <button
        className="mt-2 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
        onClick={() => window.location.reload()}
      >
        Retry
      </button>
    </div>;
  }

  if (!assessment) {
    return <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
      <p>Assessment not found.</p>
      <Link
        to="/assessments"
        className="mt-2 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
      >
        Back to Assessments
      </Link>
    </div>;
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">{assessment.templateName}</h2>
          <div className="flex items-center mt-1">
            <span className="text-sm text-gray-600 mr-2">Status:</span>
            {getStatusBadge(assessment.status)}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {canEditAssessment(assessment, userRole, userId) && (
            <Link
              to={`/assessments/${assessment.id}/edit`}
              className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg flex items-center"
            >
              <span className="material-icons mr-1" style={{ fontSize: '18px' }}>edit</span>
              Edit
            </Link>
          )}

          {canDeleteAssessment(assessment, userRole, userId) && (
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg flex items-center"
            >
              <span className="material-icons mr-1" style={{ fontSize: '18px' }}>delete</span>
              Delete
            </button>
          )}
        </div>
      </div>

      {/* Assessment Information */}
      <div className="mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Employee</h3>
            <p className="mt-1 text-lg text-gray-800">{assessment.employeeName}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Manager</h3>
            <p className="mt-1 text-lg text-gray-800">{assessment.managerName}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Overall Score</h3>
            {assessment.score ? (
              <p className={`mt-1 text-lg font-bold ${getScoreColor(assessment.score)}`}>
                {assessment.score.toFixed(1)}%
              </p>
            ) : (
              <p className="mt-1 text-lg text-gray-400">Not scored</p>
            )}
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Created</h3>
            <p className="mt-1 text-gray-800">
              {assessment.createdAt ? new Date(assessment.createdAt).toLocaleDateString() : 'N/A'}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Due Date</h3>
            <p className="mt-1 text-gray-800">
              {assessment.dueDate ? new Date(assessment.dueDate).toLocaleDateString() : 'N/A'}
            </p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-500">Submitted</h3>
            <p className="mt-1 text-gray-800">
              {assessment.submittedAt
                ? new Date(assessment.submittedAt).toLocaleDateString()
                : 'Not submitted'}
            </p>
          </div>
        </div>
      </div>

      {/* Assessment Areas */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Assessment Areas</h3>

        {assessment.responses && assessment.responses.map((response, index) => (
          <div key={index} className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium text-gray-800">{response.areaName}</h4>
              {response.score !== null && (
                <span className={`text-lg font-bold ${getScoreColor(response.score)}`}>
                  {response.score}%
                </span>
              )}
            </div>

            {response.notes && (
              <div className="mt-2">
                <h5 className="text-sm font-medium text-gray-600 mb-1">Feedback:</h5>
                <p className="text-gray-800">{response.notes}</p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Overall Notes */}
      {assessment.notes && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Overall Notes</h3>
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-gray-800 whitespace-pre-line">{assessment.notes}</p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between items-center mt-8">
        <Link
          to="/assessments"
          className="text-blue-600 hover:text-blue-800 flex items-center"
        >
          <span className="material-icons mr-1" style={{ fontSize: '18px' }}>arrow_back</span>
          Back to Assessments
        </Link>

        {assessment.status === AssessmentStatus.COMPLETED && (
          <div>
            <Button
              variant="contained"
              color="primary"
              endIcon={<ArrowDropDownIcon />}
              onClick={(e) => setExportMenuAnchorEl(e.currentTarget)}
              sx={{
                backgroundColor: '#047857',
                '&:hover': { backgroundColor: '#065f46' }
              }}
            >
              Export Assessment
            </Button>
            <Menu
              anchorEl={exportMenuAnchorEl}
              open={exportMenuOpen}
              onClose={() => setExportMenuAnchorEl(null)}
            >
              <MenuItem
                onClick={() => {
                  setExportMenuAnchorEl(null);
                  ReportingService.exportAssessmentPdf(assessmentId);
                }}
              >
                <ListItemIcon>
                  <PictureAsPdfIcon fontSize="small" color="error" />
                </ListItemIcon>
                <ListItemText>Export as PDF</ListItemText>
              </MenuItem>
              <MenuItem
                onClick={() => {
                  setExportMenuAnchorEl(null);
                  ReportingService.exportAssessmentJson(assessmentId);
                }}
              >
                <ListItemIcon>
                  <CodeIcon fontSize="small" color="primary" />
                </ListItemIcon>
                <ListItemText>Export as JSON</ListItemText>
              </MenuItem>
            </Menu>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Delete Assessment</h3>
            <p className="mb-6">
              Are you sure you want to delete this assessment for {assessment.employeeName}? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssessmentDetail;
