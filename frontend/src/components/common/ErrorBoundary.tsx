import { Component, ErrorInfo, ReactNode } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Content,
  Al<PERSON>,
  Collapse,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  BugReport as BugReportIcon,
} from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log to our logging service
    if ((window as any).__EHRX_LOG_REACT_ERROR__) {
      (window as any).__EHRX_LOG_REACT_ERROR__(error, errorInfo);
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log to console for development
    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  handleRefresh = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
    });
  };

  toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails,
    }));
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '400px',
            p: 3,
          }}
        >
          <Card sx={{ maxWidth: 600, width: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BugReportIcon color="error" sx={{ mr: 1, fontSize: 32 }} />
                <Typography variant="h5" color="error">
                  Something went wrong
                </Typography>
              </Box>

              <Typography variant="body1" sx={{ mb: 3 }}>
                We're sorry, but something unexpected happened. The error has been logged 
                and our team will investigate the issue.
              </Typography>

              <Alert severity="error" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  <strong>Error:</strong> {this.state.error?.message || 'Unknown error'}
                </Typography>
              </Alert>

              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleRefresh}
                >
                  Refresh Page
                </Button>
                <Button
                  variant="outlined"
                  onClick={this.handleReset}
                >
                  Try Again
                </Button>
              </Box>

              {/* Technical Details (collapsible) */}
              <Box>
                <Button
                  variant="text"
                  size="small"
                  onClick={this.toggleDetails}
                  endIcon={this.state.showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                >
                  Technical Details
                </Button>
                
                <Collapse in={this.state.showDetails}>
                  <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                    {this.state.error && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Error Details:
                        </Typography>
                        <Typography variant="body2" component="pre" sx={{ 
                          fontSize: '0.75rem',
                          overflow: 'auto',
                          maxHeight: '200px',
                          bgcolor: 'background.paper',
                          p: 1,
                          borderRadius: 1,
                        }}>
                          {this.state.error.stack || this.state.error.message}
                        </Typography>
                      </Box>
                    )}

                    {this.state.errorInfo && (
                      <Box>
                        <Typography variant="subtitle2" gutterBottom>
                          Component Stack:
                        </Typography>
                        <Typography variant="body2" component="pre" sx={{ 
                          fontSize: '0.75rem',
                          overflow: 'auto',
                          maxHeight: '200px',
                          bgcolor: 'background.paper',
                          p: 1,
                          borderRadius: 1,
                        }}>
                          {this.state.errorInfo.componentStack}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Collapse>
              </Box>
            </CardContent>
          </Card>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
