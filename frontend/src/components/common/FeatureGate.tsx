import React from 'react';
import { Alert, Typography } from '@mui/material';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import { OrganizationConfig } from '../../utils/config';

interface FeatureGateProps {
  /** The feature flag to check */
  feature: keyof OrganizationConfig['features'];
  /** Children to render if feature is enabled */
  children: React.ReactNode;
  /** Optional fallback component to render if feature is disabled */
  fallback?: React.ReactNode;
  /** Whether to show a message when feature is disabled */
  showDisabledMessage?: boolean;
  /** Custom message to show when feature is disabled */
  disabledMessage?: string;
  /** Whether this is a development-only feature */
  developmentOnly?: boolean;
  /** User role for role-based access control */
  userRole?: string;
  /** Current usage for usage-based limits */
  currentUsage?: number;
  /** Whether to render as inline (span) or block (div) */
  inline?: boolean;
}

/**
 * FeatureGate component that conditionally renders children based on feature flags
 * and organizational configuration
 */
export const FeatureGate: React.FC<FeatureGateProps> = ({
  feature,
  children,
  fallback,
  showDisabledMessage = false,
  disabledMessage,
  developmentOnly = false,
  userRole,
  currentUsage,
  inline = false,
}) => {
  const { shouldShowFeature, canAccessFeature } = useFeatureFlags();

  // Check if feature should be shown
  const shouldShow = shouldShowFeature(feature, developmentOnly);

  // Check access permissions if user role or usage is provided
  const accessCheck = canAccessFeature(feature, userRole, currentUsage);

  // Determine if we should render the children
  const shouldRender = shouldShow && accessCheck.canAccess;

  // If feature is enabled and accessible, render children
  if (shouldRender) {
    return <>{children}</>;
  }

  // If fallback is provided, render it
  if (fallback) {
    return <>{fallback}</>;
  }

  // If we should show a disabled message, render it
  if (showDisabledMessage) {
    const message = disabledMessage || 
      accessCheck.reason || 
      `This feature is not available for your organization.`;

    const content = (
      <Alert severity="info" sx={{ my: 1 }}>
        <Typography variant="body2">{message}</Typography>
      </Alert>
    );

    return inline ? <span>{content}</span> : content;
  }

  // Otherwise, render nothing
  return null;
};

/**
 * Higher-order component that wraps a component with feature gating
 */
export const withFeatureGate = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  feature: keyof OrganizationConfig['features'],
  options?: {
    fallback?: React.ReactNode;
    showDisabledMessage?: boolean;
    disabledMessage?: string;
    developmentOnly?: boolean;
  }
) => {
  const FeatureGatedComponent: React.FC<P> = (props) => (
    <FeatureGate
      feature={feature}
      fallback={options?.fallback}
      showDisabledMessage={options?.showDisabledMessage}
      disabledMessage={options?.disabledMessage}
      developmentOnly={options?.developmentOnly}
    >
      <WrappedComponent {...props} />
    </FeatureGate>
  );

  FeatureGatedComponent.displayName = `withFeatureGate(${WrappedComponent.displayName || WrappedComponent.name})`;

  return FeatureGatedComponent;
};

/**
 * Hook for conditional feature rendering in functional components
 */
export const useFeatureGate = (
  feature: keyof OrganizationConfig['features'],
  options?: {
    developmentOnly?: boolean;
    userRole?: string;
    currentUsage?: number;
  }
) => {
  const { shouldShowFeature, canAccessFeature } = useFeatureFlags();

  const shouldShow = shouldShowFeature(feature, options?.developmentOnly);
  const accessCheck = canAccessFeature(feature, options?.userRole, options?.currentUsage);

  return {
    isEnabled: shouldShow && accessCheck.canAccess,
    canAccess: accessCheck.canAccess,
    reason: accessCheck.reason,
    shouldShow,
  };
};

/**
 * Component for rendering multiple features with different access levels
 */
interface MultiFeatureGateProps {
  /** Array of features that must ALL be enabled */
  requiredFeatures?: Array<keyof OrganizationConfig['features']>;
  /** Array of features where ANY can be enabled */
  anyFeatures?: Array<keyof OrganizationConfig['features']>;
  /** Children to render if conditions are met */
  children: React.ReactNode;
  /** Fallback component */
  fallback?: React.ReactNode;
  /** Whether to show disabled message */
  showDisabledMessage?: boolean;
  /** User role for access control */
  userRole?: string;
}

export const MultiFeatureGate: React.FC<MultiFeatureGateProps> = ({
  requiredFeatures = [],
  anyFeatures = [],
  children,
  fallback,
  showDisabledMessage = false,
  userRole,
}) => {
  const { isFeatureEnabled, canAccessFeature } = useFeatureFlags();

  // Check if all required features are enabled and accessible
  const allRequiredEnabled = requiredFeatures.every(feature => {
    const enabled = isFeatureEnabled(feature);
    const access = canAccessFeature(feature, userRole);
    return enabled && access.canAccess;
  });

  // Check if any of the "any" features are enabled and accessible
  const anyFeatureEnabled = anyFeatures.length === 0 || anyFeatures.some(feature => {
    const enabled = isFeatureEnabled(feature);
    const access = canAccessFeature(feature, userRole);
    return enabled && access.canAccess;
  });

  const shouldRender = allRequiredEnabled && anyFeatureEnabled;

  if (shouldRender) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showDisabledMessage) {
    return (
      <Alert severity="info" sx={{ my: 1 }}>
        <Typography variant="body2">
          This feature requires additional permissions or features to be enabled.
        </Typography>
      </Alert>
    );
  }

  return null;
};

/**
 * Component for rendering content based on organization type/template
 */
interface OrganizationTypeGateProps {
  /** Organization types that can access this content */
  allowedTypes: string[];
  /** Children to render if organization type matches */
  children: React.ReactNode;
  /** Fallback component */
  fallback?: React.ReactNode;
}

export const OrganizationTypeGate: React.FC<OrganizationTypeGateProps> = ({
  allowedTypes,
  children,
  fallback,
}) => {
  const { config } = useFeatureFlags();

  // In a real implementation, you would get the organization type from the config
  // For now, we'll use a simple heuristic based on enabled features
  const getOrganizationType = (): string => {
    if (config.security.requireMFA && config.features.enableAuditReports && !config.features.enableCustomBranding) {
      return 'government';
    }
    if (config.features.enableAdvancedAnalytics && config.features.enableAPIAccess && config.features.enableMultiTenant) {
      return 'enterprise';
    }
    if (!config.features.enableMonthlyDashboards && !config.features.enableAdvancedAnalytics) {
      return 'startup';
    }
    return 'custom';
  };

  const organizationType = getOrganizationType();
  const isAllowed = allowedTypes.includes(organizationType);

  if (isAllowed) {
    return <>{children}</>;
  }

  return fallback ? <>{fallback}</> : null;
};

export default FeatureGate;
