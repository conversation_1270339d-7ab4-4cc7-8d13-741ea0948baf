import React, { useState, useEffect } from 'react';
import { ApiService } from '../../services/api';
import { DashboardCard } from './shared';
import { getDashboardCardActions, getDashboardCardTooltip } from '../../utils/dashboardNavigation';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  LinearProgress,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  CircularProgress
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Assessment as AssessmentIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  Notifications as NotificationsIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Star as StarIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';

interface DashboardStats {
  totalEmployees: number;
  activeAssessments: number;
  completedAssessments: number;
  pendingReviews: number;
  averageScore: number;
  improvementTrend: number;
}

interface RecentActivity {
  id: number;
  type: 'assessment' | 'review' | 'goal' | 'feedback';
  title: string;
  description: string;
  timestamp: string;
  status: 'completed' | 'pending' | 'overdue';
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  action: () => void;
}

interface MainDashboardProps {
  onNavigate?: (page: string) => void;
}

const MainDashboard: React.FC<MainDashboardProps> = ({ onNavigate }) => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Helper function to format timestamps
  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffHours / 24);

      if (diffHours < 1) return 'Just now';
      if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
      if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
      return date.toLocaleDateString();
    } catch {
      return 'Recently';
    }
  };
  const [currentUser] = useState({
    name: 'Admin User',
    role: 'HR Administrator',
    avatar: '/api/placeholder/40/40',
    lastLogin: '2025-07-15T08:30:00Z'
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);

      try {
        // 🔐 NIS2-COMPLIANT: Use centralized API service for real data
        const [userCountResponse, assessmentStatsResponse, dashboardStatsResponse, recentActivityResponse] = await Promise.all([
          ApiService.getUserCount(),
          ApiService.getAssessmentStats(),
          ApiService.getDashboardStats(),
          ApiService.getRecentActivity()
        ]);

        const userCount = userCountResponse.data?.count || 0;
        const assessmentStats = assessmentStatsResponse.data || { active: 0, completed: 0, pending: 0 };
        const dashboardStats = dashboardStatsResponse.data || { averageScore: 0, improvementTrend: 0 };
        const recentActivityData = recentActivityResponse.data || [];

        setStats({
          totalEmployees: userCount,
          activeAssessments: assessmentStats.active,
          completedAssessments: assessmentStats.completed,
          pendingReviews: assessmentStats.pending,
          averageScore: dashboardStats.averageScore || 0,
          improvementTrend: dashboardStats.improvementTrend || 0
        });

        // Process recent activity data
        const processedActivity = Array.isArray(recentActivityData)
          ? recentActivityData.slice(0, 4).map((item, index) => ({
            id: item.id || index + 1,
            type: (['assessment', 'review', 'goal', 'feedback'].includes(item.type) ? item.type : 'assessment') as 'assessment' | 'review' | 'goal' | 'feedback',
            title: item.title || `Activity ${index + 1}`,
            description: item.description || 'No description available',
            timestamp: item.timestamp ? formatTimestamp(item.timestamp) : 'Just now',
            status: (['completed', 'pending', 'overdue'].includes(item.status) ? item.status : 'pending') as 'completed' | 'pending' | 'overdue'
          }))
          : [];

        setRecentActivity(processedActivity);

      } catch (error) {
        console.error('Error fetching dashboard data:', error);

        // Fallback to minimal data on error
        setStats({
          totalEmployees: 0,
          activeAssessments: 0,
          completedAssessments: 0,
          pendingReviews: 0,
          averageScore: 0,
          improvementTrend: 0
        });
        setRecentActivity([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const quickActions: QuickAction[] = [
    {
      id: 'new-assessment',
      title: 'Create Assessment',
      description: 'Start a new performance assessment',
      icon: <AssessmentIcon />,
      color: 'primary',
      action: () => onNavigate?.('assessments')
    },
    {
      id: 'view-reports',
      title: 'View Reports',
      description: 'Access performance analytics',
      icon: <TrendingUpIcon />,
      color: 'secondary',
      action: () => onNavigate?.('analytics')
    },
    {
      id: 'manage-teams',
      title: 'Manage Teams',
      description: 'Organize team structure',
      icon: <PeopleIcon />,
      color: 'success',
      action: () => onNavigate?.('organization')
    },
    {
      id: 'review-pending',
      title: 'Pending Reviews',
      description: 'Review awaiting assessments',
      icon: <ScheduleIcon />,
      color: 'warning',
      action: () => onNavigate?.('assessments')
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'assessment': return <AssessmentIcon color="primary" />;
      case 'review': return <CheckCircleIcon color="success" />;
      case 'goal': return <StarIcon color="warning" />;
      case 'feedback': return <NotificationsIcon color="secondary" />;
      default: return <AssignmentIcon />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'overdue': return 'error';
      default: return 'default';
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Welcome Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          Welcome back, {currentUser.name}! 👋
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Here's what's happening with your team today
        </Typography>
      </Box>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title="Total Employees"
            value={stats?.totalEmployees || 0}
            icon={<PeopleIcon />}
            color="primary"
            interactive={true}
            tooltip={getDashboardCardTooltip('employee', 'admin')}
            actions={getDashboardCardActions('employee', 'admin', onNavigate)}
            trend={{
              value: 5.2,
              label: 'vs last month',
              isPositive: true
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title="Active Assessments"
            value={stats?.activeAssessments || 0}
            icon={<AssessmentIcon />}
            color="secondary"
            interactive={true}
            tooltip={getDashboardCardTooltip('assessment', 'admin')}
            actions={getDashboardCardActions('assessment', 'admin', onNavigate)}
            badge={{
              text: 'In Progress',
              color: 'warning'
            }}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title="Average Score"
            value={`${stats?.averageScore || 0}%`}
            icon={<TrendingUpIcon />}
            color="success"
            interactive={true}
            tooltip="System-wide average assessment score"
            trend={{
              value: stats?.improvementTrend || 0,
              label: 'improvement',
              isPositive: (stats?.improvementTrend || 0) >= 0
            }}
            actions={getDashboardCardActions('report', 'admin', onNavigate)}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <DashboardCard
            title="Pending Reviews"
            value={stats?.pendingReviews || 0}
            icon={<ScheduleIcon />}
            color="warning"
            interactive={true}
            tooltip="Assessments awaiting manager review"
            actions={getDashboardCardActions('assessment', 'admin', onNavigate)}
            badge={stats?.pendingReviews && stats.pendingReviews > 0 ? {
              text: 'Action Required',
              color: 'error'
            } : undefined}
          />
        </Grid>
      </Grid>

      {/* Performance Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUpIcon sx={{ mr: 1 }} />
                Performance Trend
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Overall team performance improvement: +{stats?.improvementTrend}% this quarter
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={stats?.averageScore || 0}
                  sx={{ height: 8, borderRadius: 4, mb: 1 }}
                />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="caption">0%</Typography>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                    {stats?.averageScore}%
                  </Typography>
                  <Typography variant="caption">100%</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <DashboardIcon sx={{ mr: 1 }} />
                Quick Actions
              </Typography>
              <Grid container spacing={2}>
                {quickActions.map((action) => (
                  <Grid item xs={12} sm={6} key={action.id}>
                    <Paper
                      sx={{
                        p: 2,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: 3
                        }
                      }}
                      onClick={action.action}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar sx={{ bgcolor: `${action.color}.main`, mr: 2 }}>
                          {action.icon}
                        </Avatar>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                            {action.title}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {action.description}
                          </Typography>
                        </Box>
                        <ArrowForwardIcon color="action" />
                      </Box>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: 'fit-content' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <NotificationsIcon sx={{ mr: 1 }} />
                Recent Activity
              </Typography>
              <List dense>
                {recentActivity.map((activity, index) => (
                  <React.Fragment key={activity.id}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        {getActivityIcon(activity.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <span style={{ fontWeight: 500, fontSize: '0.875rem' }}>
                              {activity.title}
                            </span>
                            <Chip
                              label={activity.status}
                              size="small"
                              color={getStatusColor(activity.status) as any}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <span style={{ color: 'rgba(0, 0, 0, 0.6)', fontSize: '0.875rem', display: 'block' }}>
                              {activity.description}
                            </span>
                            <span style={{ color: 'rgba(0, 0, 0, 0.6)', fontSize: '0.75rem', display: 'block' }}>
                              {activity.timestamp}
                            </span>
                          </Box>
                        }
                        primaryTypographyProps={{ component: 'div' }}
                        secondaryTypographyProps={{ component: 'div' }}
                      />
                    </ListItem>
                    {index < recentActivity.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* System Status Footer */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <strong>System Status:</strong> All services operational. Last updated: {new Date().toLocaleString()}
          </Alert>
        </Grid>
      </Grid>

      {/* Additional Info Cards */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card variant="outlined">
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircleIcon color="success" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                {stats?.completedAssessments}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Assessments Completed This Quarter
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card variant="outlined">
            <CardContent sx={{ textAlign: 'center' }}>
              <StarIcon color="warning" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Top Performer
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {stats?.averageScore ? `${stats.averageScore}% Team Average` : 'Data Loading...'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card variant="outlined">
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUpIcon color="primary" sx={{ fontSize: 40, mb: 1 }} />
              <Typography variant="h6" gutterBottom>
                Growth Rate
              </Typography>
              <Typography variant="body2" color="text.secondary">
                +{stats?.improvementTrend}% Team Improvement
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default MainDashboard;
