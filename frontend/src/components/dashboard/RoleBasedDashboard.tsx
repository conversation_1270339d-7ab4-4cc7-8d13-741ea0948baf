import React, { useState, useEffect } from 'react';
import { Box, Typography, Alert } from '@mui/material';
import { useEnterpriseConfig, useDashboardLayout, useFeatureFlags } from '../../hooks/useEnterpriseConfig';
import { DashboardLoading, DashboardError } from './shared';
import MainDashboard from './MainDashboard';
import EmployeeDashboard from './EmployeeDashboard';
import ManagerDashboard from './ManagerDashboard';

// 🔐 NIS2-COMPLIANT: Role-based dashboard wrapper
// Provides organization-specific dashboard customization and role-based views

export interface RoleBasedDashboardProps {
  userId: number;
  userRole: string;
  teamId?: number;
  onNavigate?: (page: string) => void;
}

interface DashboardSection {
  id: string;
  title: string;
  component: React.ComponentType<any>;
  requiredPermissions?: string[];
  requiredFeatures?: string[];
}

const RoleBasedDashboard: React.FC<RoleBasedDashboardProps> = ({
  userId,
  userRole,
  teamId,
  onNavigate
}) => {
  const [currentView, setCurrentView] = useState<string>('overview');
  const [hasPermissions, setHasPermissions] = useState<Record<string, boolean>>({});

  const {
    organizationConfig,
    roleConfig,
    isLoading: configLoading,
    error: configError,
    hasPermission
  } = useEnterpriseConfig(userId.toString(), userRole);

  const {
    layout,
    isLoading: layoutLoading
  } = useDashboardLayout(userId.toString(), userRole);

  const {
    features,
    isFeatureEnabled,
    isLoading: featuresLoading
  } = useFeatureFlags();

  // Define available dashboard sections
  const dashboardSections: Record<string, DashboardSection> = {
    overview: {
      id: 'overview',
      title: 'Dashboard Overview',
      component: MainDashboard,
      requiredPermissions: ['canViewDetails']
    },
    employee: {
      id: 'employee',
      title: 'My Dashboard',
      component: EmployeeDashboard,
      requiredPermissions: ['canViewDetails']
    },
    manager: {
      id: 'manager',
      title: 'Team Dashboard',
      component: ManagerDashboard,
      requiredPermissions: ['canManageTeam'],
      requiredFeatures: ['advancedAnalytics']
    }
  };

  // Check permissions for all sections
  useEffect(() => {
    const checkPermissions = async () => {
      const permissionChecks: Record<string, boolean> = {};
      
      for (const section of Object.values(dashboardSections)) {
        if (section.requiredPermissions) {
          const checks = await Promise.all(
            section.requiredPermissions.map(permission => hasPermission(permission))
          );
          permissionChecks[section.id] = checks.every(Boolean);
        } else {
          permissionChecks[section.id] = true;
        }

        // Check feature flags
        if (section.requiredFeatures) {
          const featureChecks = section.requiredFeatures.map(feature => isFeatureEnabled(feature));
          permissionChecks[section.id] = permissionChecks[section.id] && featureChecks.every(Boolean);
        }
      }
      
      setHasPermissions(permissionChecks);
    };

    if (!configLoading && !featuresLoading) {
      checkPermissions();
    }
  }, [configLoading, featuresLoading, hasPermission, isFeatureEnabled]);

  // Set default view based on role configuration
  useEffect(() => {
    if (layout.defaultView && currentView === 'overview') {
      setCurrentView(layout.defaultView);
    }
  }, [layout.defaultView, currentView]);

  // Loading state
  if (configLoading || layoutLoading || featuresLoading) {
    return (
      <Box className="p-6">
        <DashboardLoading 
          message="Loading your personalized dashboard..." 
          variant="skeleton" 
          size="large" 
        />
      </Box>
    );
  }

  // Error state
  if (configError) {
    return (
      <Box className="p-6">
        <DashboardError
          title="Configuration Error"
          message={configError}
          variant="card"
          onRetry={() => window.location.reload()}
        />
      </Box>
    );
  }

  // Get available sections based on role and permissions
  const getAvailableSections = (): DashboardSection[] => {
    if (!roleConfig) return [];

    return Object.values(dashboardSections).filter(section => {
      // Check if section is enabled for this role
      const isEnabledForRole = roleConfig.enabledSections.includes(section.id) ||
                              roleConfig.enabledSections.includes('dashboard');
      
      // Check if section is not hidden
      const isNotHidden = !roleConfig.hiddenSections.includes(section.id);
      
      // Check permissions
      const hasRequiredPermissions = hasPermissions[section.id] !== false;
      
      return isEnabledForRole && isNotHidden && hasRequiredPermissions;
    });
  };

  // Render dashboard based on current view and role
  const renderDashboard = () => {
    const availableSections = getAvailableSections();
    
    if (availableSections.length === 0) {
      return (
        <Alert severity="warning" className="m-6">
          <Typography variant="h6">Access Restricted</Typography>
          <Typography variant="body2">
            You don't have permission to view any dashboard sections. 
            Please contact your administrator for access.
          </Typography>
        </Alert>
      );
    }

    // Determine which dashboard to show based on role and current view
    let DashboardComponent: React.ComponentType<any>;
    let dashboardProps: any = { onNavigate };

    switch (userRole) {
      case 'admin':
        DashboardComponent = MainDashboard;
        break;
      case 'manager':
        if (currentView === 'manager' && hasPermissions.manager) {
          DashboardComponent = ManagerDashboard;
          dashboardProps = { ...dashboardProps, teamId };
        } else {
          DashboardComponent = MainDashboard;
        }
        break;
      case 'employee':
      default:
        if (currentView === 'employee' && hasPermissions.employee) {
          DashboardComponent = EmployeeDashboard;
          dashboardProps = { ...dashboardProps, userId };
        } else {
          DashboardComponent = MainDashboard;
        }
        break;
    }

    return <DashboardComponent {...dashboardProps} />;
  };

  // Render organization branding if available
  const renderBranding = () => {
    if (!organizationConfig) return null;

    return (
      <Box className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg">
        <Box className="flex items-center gap-4">
          {organizationConfig.logo && (
            <img 
              src={organizationConfig.logo} 
              alt={organizationConfig.name}
              className="h-12 w-auto"
            />
          )}
          <Box>
            <Typography variant="h5" className="font-bold text-gray-800">
              {organizationConfig.name}
            </Typography>
            <Typography variant="body2" className="text-gray-600">
              Enterprise Dashboard
            </Typography>
          </Box>
        </Box>
      </Box>
    );
  };

  // Render feature availability notice
  const renderFeatureNotice = () => {
    const unavailableFeatures = Object.entries(features)
      .filter(([_, enabled]) => !enabled)
      .map(([feature, _]) => feature);

    if (unavailableFeatures.length === 0) return null;

    return (
      <Alert severity="info" className="mb-4">
        <Typography variant="body2">
          Some advanced features are not available in your current plan: {unavailableFeatures.join(', ')}
        </Typography>
      </Alert>
    );
  };

  return (
    <Box className="min-h-screen bg-gray-50">
      <Box className="p-6">
        {renderBranding()}
        {renderFeatureNotice()}
        
        {/* Role-based dashboard content */}
        <Box 
          className="bg-white rounded-lg shadow-sm"
          style={{
            borderColor: organizationConfig?.primaryColor || '#1976d2',
            borderTopWidth: '4px',
            borderTopStyle: 'solid'
          }}
        >
          {renderDashboard()}
        </Box>

        {/* Compliance footer */}
        {organizationConfig?.complianceSettings.nis2Compliant && (
          <Box className="mt-6 p-3 bg-green-50 border border-green-200 rounded-lg">
            <Typography variant="caption" className="text-green-700">
              🔐 This dashboard is NIS2 compliant and follows enterprise security standards.
              Data retention: {organizationConfig.complianceSettings.dataRetentionPeriod} days
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default RoleBasedDashboard;
