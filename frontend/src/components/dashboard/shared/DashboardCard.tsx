import React from 'react';
import { <PERSON>, CardContent, Typo<PERSON>, Box, IconButton, Tooltip, Button, Chip, CardActions } from '@mui/material';
import { Info as InfoIcon, MoreVert as MoreVertIcon } from '@mui/icons-material';

// 🔐 NIS2-COMPLIANT: Shared dashboard card component
// Provides consistent styling and behavior across dashboard components

export interface DashboardCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    value: number;
    label: string;
    isPositive?: boolean;
  };
  onClick?: () => void;
  loading?: boolean;
  error?: string;
  tooltip?: string;
  className?: string;
  interactive?: boolean;
  actions?: Array<{
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  }>;
  badge?: {
    text: string;
    color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  };
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  trend,
  onClick,
  loading = false,
  error,
  tooltip,
  className = '',
  interactive = false,
  actions = [],
  badge
}) => {
  const getColorClass = (color: string) => {
    const colorMap: Record<string, string> = {
      primary: 'text-blue-600',
      secondary: 'text-gray-600',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      error: 'text-red-600',
      info: 'text-cyan-600'
    };
    return colorMap[color] || colorMap.primary;
  };

  const getTrendColor = (isPositive: boolean) => {
    return isPositive ? 'text-green-600' : 'text-red-600';
  };

  const getBadgeColor = (color: string) => {
    const colorMap: Record<string, string> = {
      primary: 'primary',
      secondary: 'default',
      success: 'success',
      warning: 'warning',
      error: 'error',
      info: 'info'
    };
    return colorMap[color] || 'default';
  };

  const getActionButtonColor = (color: string) => {
    const colorMap: Record<string, string> = {
      primary: 'primary',
      secondary: 'inherit',
      success: 'success',
      warning: 'warning',
      error: 'error'
    };
    return colorMap[color] || 'primary';
  };

  if (loading) {
    return (
      <Card className={`h-full ${onClick ? 'cursor-pointer hover:shadow-lg transition-shadow' : ''} ${className}`}>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              {tooltip && <div className="h-4 w-4 bg-gray-200 rounded"></div>}
            </div>
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`h-full border-red-200 ${className}`}>
        <CardContent className="p-6">
          <Typography variant="h6" className="text-red-600 mb-2">
            {title}
          </Typography>
          <Typography variant="body2" className="text-red-500">
            {error}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={`h-full ${onClick || interactive ? 'cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105' : ''} ${className}`}
      onClick={onClick}
    >
      <CardContent className="p-6">
        <Box className="flex items-center justify-between mb-4">
          <Box className="flex items-center gap-2">
            {icon && (
              <Box className={`p-2 rounded-lg bg-gray-100 ${getColorClass(color)}`}>
                {icon}
              </Box>
            )}
            <Typography variant="h6" className="font-semibold text-gray-700">
              {title}
            </Typography>
            {badge && (
              <Chip
                label={badge.text}
                size="small"
                color={getBadgeColor(badge.color) as any}
                className="ml-2"
              />
            )}
          </Box>
          <Box className="flex items-center gap-1">
            {tooltip && (
              <Tooltip title={tooltip} arrow>
                <IconButton size="small" className="text-gray-400">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            {interactive && actions.length === 0 && (
              <IconButton size="small" className="text-gray-400">
                <MoreVertIcon fontSize="small" />
              </IconButton>
            )}
          </Box>
        </Box>

        <Box className="mb-2">
          <Typography variant="h3" className={`font-bold ${getColorClass(color)}`}>
            {value}
          </Typography>
        </Box>

        {subtitle && (
          <Typography variant="body2" className="text-gray-500 mb-2">
            {subtitle}
          </Typography>
        )}

        {trend && (
          <Box className="flex items-center gap-1 mb-2">
            <Typography
              variant="body2"
              className={`font-medium ${getTrendColor(trend.isPositive ?? true)}`}
            >
              {trend.isPositive !== false ? '+' : ''}{trend.value}%
            </Typography>
            <Typography variant="body2" className="text-gray-500">
              {trend.label}
            </Typography>
          </Box>
        )}
      </CardContent>

      {actions.length > 0 && (
        <CardActions className="px-6 pb-4 pt-0">
          <Box className="flex gap-2 flex-wrap">
            {actions.map((action, index) => (
              <Button
                key={index}
                size="small"
                color={getActionButtonColor(action.color || 'primary') as any}
                startIcon={action.icon}
                onClick={(e) => {
                  e.stopPropagation();
                  action.onClick();
                }}
                className="text-xs"
              >
                {action.label}
              </Button>
            ))}
          </Box>
        </CardActions>
      )}
    </Card>
  );
};

export default DashboardCard;
