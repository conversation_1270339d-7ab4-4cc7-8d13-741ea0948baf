import React from 'react';
import { <PERSON>, Typography, <PERSON><PERSON>, <PERSON><PERSON>, Al<PERSON>Title } from '@mui/material';
import { Refresh as RefreshIcon, Error as ErrorIcon } from '@mui/icons-material';

// 🔐 NIS2-COMPLIANT: Shared dashboard error component
// Provides consistent error handling and display across dashboard components

export interface DashboardErrorProps {
  title?: string;
  message: string;
  code?: string;
  onRetry?: () => void;
  variant?: 'alert' | 'card' | 'minimal';
  severity?: 'error' | 'warning' | 'info';
  showDetails?: boolean;
  timestamp?: Date;
}

const DashboardError: React.FC<DashboardErrorProps> = ({
  title = 'Dashboard Error',
  message,
  code,
  onRetry,
  variant = 'alert',
  severity = 'error',
  showDetails = false,
  timestamp
}) => {
  const formatTimestamp = (date: Date) => {
    return date.toLocaleString();
  };

  if (variant === 'minimal') {
    return (
      <Box className="flex items-center justify-center p-4">
        <Typography variant="body2" className="text-red-600">
          {message}
        </Typography>
        {onRetry && (
          <Button
            size="small"
            onClick={onRetry}
            startIcon={<RefreshIcon />}
            className="ml-2"
          >
            Retry
          </Button>
        )}
      </Box>
    );
  }

  if (variant === 'card') {
    return (
      <Box className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <ErrorIcon className="text-red-500 text-4xl mb-4" />
        <Typography variant="h6" className="text-red-800 mb-2">
          {title}
        </Typography>
        <Typography variant="body1" className="text-red-700 mb-4">
          {message}
        </Typography>
        
        {showDetails && (
          <Box className="bg-red-100 rounded p-3 mb-4 text-left">
            {code && (
              <Typography variant="body2" className="text-red-600 font-mono">
                Error Code: {code}
              </Typography>
            )}
            {timestamp && (
              <Typography variant="body2" className="text-red-600">
                Time: {formatTimestamp(timestamp)}
              </Typography>
            )}
          </Box>
        )}

        {onRetry && (
          <Button
            variant="contained"
            color="error"
            onClick={onRetry}
            startIcon={<RefreshIcon />}
          >
            Try Again
          </Button>
        )}
      </Box>
    );
  }

  return (
    <Alert 
      severity={severity} 
      className="mb-4"
      action={
        onRetry && (
          <Button
            color="inherit"
            size="small"
            onClick={onRetry}
            startIcon={<RefreshIcon />}
          >
            Retry
          </Button>
        )
      }
    >
      <AlertTitle>{title}</AlertTitle>
      {message}
      
      {showDetails && (code || timestamp) && (
        <Box className="mt-2 pt-2 border-t border-gray-300">
          {code && (
            <Typography variant="body2" className="font-mono">
              Error Code: {code}
            </Typography>
          )}
          {timestamp && (
            <Typography variant="body2">
              Time: {formatTimestamp(timestamp)}
            </Typography>
          )}
        </Box>
      )}
    </Alert>
  );
};

export default DashboardError;
