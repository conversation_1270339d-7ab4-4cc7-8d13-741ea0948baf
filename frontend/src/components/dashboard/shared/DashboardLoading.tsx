import React from 'react';
import { Box, CircularProgress, Typography, Skeleton } from '@mui/material';

// 🔐 NIS2-COMPLIANT: Shared dashboard loading components
// Provides consistent loading states across dashboard components

export interface DashboardLoadingProps {
  message?: string;
  variant?: 'spinner' | 'skeleton' | 'minimal';
  size?: 'small' | 'medium' | 'large';
}

const DashboardLoading: React.FC<DashboardLoadingProps> = ({
  message = 'Loading dashboard...',
  variant = 'spinner',
  size = 'medium'
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'h-32';
      case 'large':
        return 'h-96';
      default:
        return 'h-64';
    }
  };

  const getSpinnerSize = () => {
    switch (size) {
      case 'small':
        return 24;
      case 'large':
        return 60;
      default:
        return 40;
    }
  };

  if (variant === 'minimal') {
    return (
      <Box className="flex items-center justify-center p-4">
        <CircularProgress size={getSpinnerSize()} />
      </Box>
    );
  }

  if (variant === 'skeleton') {
    return (
      <Box className="space-y-4 p-4">
        <Skeleton variant="text" width="60%" height={32} />
        <Skeleton variant="rectangular" width="100%" height={120} />
        <Box className="flex gap-4">
          <Skeleton variant="rectangular" width="48%" height={80} />
          <Skeleton variant="rectangular" width="48%" height={80} />
        </Box>
        <Skeleton variant="text" width="40%" height={24} />
      </Box>
    );
  }

  return (
    <Box className={`flex justify-center items-center ${getSizeClasses()}`}>
      <Box className="text-center">
        <CircularProgress size={getSpinnerSize()} className="text-blue-600" />
        <Typography variant="body1" className="mt-4 text-gray-600">
          {message}
        </Typography>
      </Box>
    </Box>
  );
};

export default DashboardLoading;
