// 🔐 NIS2-COMPLIANT: Shared dashboard components and utilities
// Centralized exports for consistent dashboard functionality

export { default as DashboardCard } from './DashboardCard';
export type { DashboardCardProps } from './DashboardCard';

export { default as DashboardLoading } from './DashboardLoading';
export type { DashboardLoadingProps } from './DashboardLoading';

export { default as DashboardError } from './DashboardError';
export type { DashboardErrorProps } from './DashboardError';

// Re-export dashboard hooks
export {
  useUserDashboardData,
  useManagerDashboardData,
  useMainDashboardData
} from '../../../hooks/useDashboardData';

export type {
  DashboardError as DashboardErrorType,
  LoadingState
} from '../../../hooks/useDashboardData';
