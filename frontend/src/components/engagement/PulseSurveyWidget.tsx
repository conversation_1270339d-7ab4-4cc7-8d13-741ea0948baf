import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON><PERSON>er,
  CardContent,
  Box,
  Typography,
  Button,
  TextField,
  Rating,

  Alert,
  CircularProgress,
  Paper,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,

  Select,
  MenuItem,
  Slider,
} from '@mui/material';
import {
  Poll as PollIcon,
  Send as SendIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  SentimentSatisfied as HappyIcon,
  SentimentNeutral as NeutralIcon,
  SentimentDissatisfied as SadIcon,
  Lightbulb as InsightIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { ApiService } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

interface PulseSurveyWidgetProps {
  isManager?: boolean;
  teamId?: number;
}

interface PulseSurveyQuestion {
  id: number;
  question: string;
  type: 'rating' | 'scale' | 'text' | 'choice';
  options?: string[];
  required: boolean;
}

interface PulseSurveyResponse {
  questionId: number;
  value: any;
  comment?: string;
}

const PulseSurveyWidget: React.FC<PulseSurveyWidgetProps> = ({ isManager = false, teamId }) => {
  const { } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [surveyOpen, setSurveyOpen] = useState(false);
  const [currentSurvey, setCurrentSurvey] = useState<any>(null);
  const [responses, setResponses] = useState<PulseSurveyResponse[]>([]);
  const [pulseData, setPulseData] = useState<any>(null);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadPulseData();
    checkForActiveSurvey();
  }, [teamId]);

  const loadPulseData = async () => {
    try {
      setLoading(true);
      const response = await ApiService.get('/analytics/pulse-survey/summary', {
        params: { teamId },
      });

      if (response.data) {
        setPulseData(response.data);
      }
    } catch (err) {
      console.error('Error loading pulse data:', err);
      setError('Failed to load pulse survey data');
    } finally {
      setLoading(false);
    }
  };

  const checkForActiveSurvey = async () => {
    try {
      const response = await ApiService.get('/analytics/pulse-survey/active');
      if (response.data && response.data) {
        setCurrentSurvey(response.data);
        // Initialize responses
        const initialResponses = response.data.questions.map((q: PulseSurveyQuestion) => ({
          questionId: q.id,
          value: q.type === 'rating' ? 3 : q.type === 'scale' ? 5 : '',
        }));
        setResponses(initialResponses);
      }
    } catch (err) {
      console.error('Error checking for active survey:', err);
    }
  };

  const handleResponseChange = (questionId: number, value: any, comment?: string) => {
    setResponses(prev => prev.map(response =>
      response.questionId === questionId
        ? { ...response, value, comment }
        : response
    ));
  };

  const submitSurvey = async () => {
    try {
      setSubmitting(true);
      const response = await ApiService.post('/analytics/pulse-survey/submit', {
        surveyId: currentSurvey.id,
        responses,
      });

      if (response.data) {
        setSurveyOpen(false);
        setCurrentSurvey(null);
        await loadPulseData();
        // Show success message
      }
    } catch (err) {
      console.error('Error submitting survey:', err);
      setError('Failed to submit survey');
    } finally {
      setSubmitting(false);
    }
  };

  const createPulseSurvey = async () => {
    try {
      setLoading(true);
      const response = await ApiService.post('/analytics/pulse-survey/create', {
        teamId,
        questions: [
          {
            question: "How satisfied are you with your current workload?",
            type: "rating",
            required: true,
          },
          {
            question: "How would you rate team collaboration this week?",
            type: "scale",
            required: true,
          },
          {
            question: "Any feedback or concerns you'd like to share?",
            type: "text",
            required: false,
          },
        ],
      });

      if (response.data) {
        await checkForActiveSurvey();
      }
    } catch (err) {
      console.error('Error creating pulse survey:', err);
      setError('Failed to create pulse survey');
    } finally {
      setLoading(false);
    }
  };

  const getSentimentIcon = (score: number) => {
    if (score >= 4) return <HappyIcon sx={{ color: '#4caf50' }} />;
    if (score >= 3) return <NeutralIcon sx={{ color: '#ff9800' }} />;
    return <SadIcon sx={{ color: '#f44336' }} />;
  };

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ?
      <TrendingUpIcon sx={{ color: '#4caf50' }} /> :
      <TrendingDownIcon sx={{ color: '#f44336' }} />;
  };

  const renderQuestion = (question: PulseSurveyQuestion) => {
    const response = responses.find(r => r.questionId === question.id);

    return (
      <Box key={question.id} sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          {question.question}
          {question.required && <span style={{ color: 'red' }}> *</span>}
        </Typography>

        {question.type === 'rating' && (
          <Box>
            <Rating
              value={response?.value || 3}
              onChange={(_, newValue) => handleResponseChange(question.id, newValue)}
              size="large"
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              1 = Very Dissatisfied, 5 = Very Satisfied
            </Typography>
          </Box>
        )}

        {question.type === 'scale' && (
          <Box sx={{ px: 2 }}>
            <Slider
              value={response?.value || 5}
              onChange={(_, newValue) => handleResponseChange(question.id, newValue)}
              min={1}
              max={10}
              marks
              valueLabelDisplay="on"
            />
            <Box display="flex" justifyContent="space-between">
              <Typography variant="caption">Poor</Typography>
              <Typography variant="caption">Excellent</Typography>
            </Box>
          </Box>
        )}

        {question.type === 'text' && (
          <TextField
            fullWidth
            multiline
            rows={3}
            value={response?.value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
            placeholder="Your feedback..."
            variant="outlined"
          />
        )}

        {question.type === 'choice' && question.options && (
          <FormControl fullWidth>
            <Select
              value={response?.value || ''}
              onChange={(e) => handleResponseChange(question.id, e.target.value)}
            >
              {question.options.map((option, index) => (
                <MenuItem key={index} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
      </Box>
    );
  };

  if (loading && !pulseData) {
    return (
      <Card>
        <CardHeader title="Pulse Survey" />
        <CardContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader
          title="Pulse Survey"
          subheader="Quick engagement check-ins and real-time feedback"
          action={
            <Box display="flex" gap={1}>
              {currentSurvey && (
                <Button
                  variant="contained"
                  startIcon={<PollIcon />}
                  onClick={() => setSurveyOpen(true)}
                  color="primary"
                >
                  Take Survey
                </Button>
              )}
              {isManager && (
                <Button
                  variant="outlined"
                  startIcon={<ScheduleIcon />}
                  onClick={createPulseSurvey}
                  disabled={loading || currentSurvey}
                >
                  Create Pulse
                </Button>
              )}
            </Box>
          }
        />
        <CardContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {currentSurvey && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="subtitle2">Active Pulse Survey</Typography>
              <Typography variant="body2">
                "{currentSurvey.title}" - Please take a moment to provide your feedback
              </Typography>
            </Alert>
          )}

          {/* Pulse Summary */}
          {pulseData && (
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Box display="flex" alignItems="center" justifyContent="center" gap={1} mb={1}>
                    {getSentimentIcon(pulseData.averageScore || 3)}
                    <Typography variant="h5">
                      {(pulseData.averageScore || 0).toFixed(1)}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Average Score
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h5" color="primary">
                    {pulseData.responseRate || 0}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Response Rate
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                    {getTrendIcon(pulseData.trend || 'stable')}
                    <Typography variant="h6">
                      {pulseData.trend === 'up' ? 'Improving' : pulseData.trend === 'down' ? 'Declining' : 'Stable'}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    Trend
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h5">
                    {pulseData.totalResponses || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Responses
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          )}

          {/* Recent Insights */}
          {pulseData?.insights && pulseData.insights.length > 0 && (
            <Paper sx={{ p: 2, mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Recent Insights
              </Typography>
              <List>
                {pulseData.insights.slice(0, 3).map((insight: any, index: number) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemIcon>
                        <InsightIcon color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={insight.message}
                        secondary={`Confidence: ${(insight.confidence * 100).toFixed(0)}%`}
                      />
                    </ListItem>
                    {index < pulseData.insights.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          )}
        </CardContent>
      </Card>

      {/* Survey Dialog */}
      <Dialog
        open={surveyOpen}
        onClose={() => setSurveyOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {currentSurvey?.title || 'Pulse Survey'}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            {currentSurvey?.description || 'Please take a moment to share your feedback'}
          </Typography>

          {currentSurvey?.questions?.map((question: PulseSurveyQuestion) =>
            renderQuestion(question)
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSurveyOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            startIcon={<SendIcon />}
            onClick={submitSurvey}
            disabled={submitting}
          >
            {submitting ? 'Submitting...' : 'Submit'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PulseSurveyWidget;
