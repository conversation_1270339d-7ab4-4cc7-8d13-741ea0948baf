import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import { Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import { User } from '../../contexts/OrganizationContext';

interface MemberCardProps {
  member: User;
  onEdit?: (member: User) => void;
  onRemove?: (memberId: number) => void;
  onSelect?: (member: User) => void;
  selectable?: boolean;
  showActions?: boolean;
  compact?: boolean;
}

const MemberCard: React.FC<MemberCardProps> = ({
  member,
  onEdit,
  onRemove,
  onSelect,
  selectable = false,
  showActions = true,
  compact = false
}) => {
  const getRoleColor = (role: string) => {
    const colors: Record<string, 'primary' | 'secondary' | 'success' | 'warning' | 'error'> = {
      ceo: 'error',
      vp: 'warning',
      director: 'primary',
      manager: 'secondary',
      senior_engineer: 'success',
      engineer: 'primary',
      junior_engineer: 'secondary',
      intern: 'secondary',
      hr_admin: 'primary',
      guest: 'secondary'
    };
    return colors[role] || 'secondary';
  };

  const formatRole = (role: string) => {
    return role.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const handleCardClick = () => {
    if (selectable && onSelect) {
      onSelect(member);
    }
  };

  return (
    <Card
      variant="outlined"
      sx={{
        height: '100%',
        cursor: selectable ? 'pointer' : 'default',
        transition: 'all 0.2s ease',
        '&:hover': selectable ? {
          borderColor: 'primary.main',
          boxShadow: 2
        } : {}
      }}
      onClick={handleCardClick}
    >
      <CardContent sx={{ p: compact ? 2 : 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
          <Avatar
            sx={{
              width: compact ? 32 : 40,
              height: compact ? 32 : 40,
              mr: 2,
              bgcolor: 'primary.main'
            }}
          >
            {member.firstName?.[0]}{member.lastName?.[0]}
          </Avatar>

          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography
              variant={compact ? "subtitle2" : "subtitle1"}
              sx={{
                fontWeight: 'bold',
                lineHeight: 1.2,
                wordBreak: 'break-word'
              }}
            >
              {member.firstName} {member.lastName}
            </Typography>

            {member.title && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  lineHeight: 1.3,
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}
              >
                {member.title}
              </Typography>
            )}
          </Box>

          {showActions && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              {onEdit && (
                <Tooltip title="Edit Member">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(member);
                    }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              {onRemove && (
                <Tooltip title="Remove Member">
                  <IconButton
                    size="small"
                    color="error"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemove(member.id);
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          )}
        </Box>

        {/* Role */}
        <Box sx={{ mb: 2 }}>
          <Chip
            label={formatRole(member.role)}
            color={getRoleColor(member.role)}
            size="small"
            sx={{ fontSize: '0.75rem' }}
          />
        </Box>

        {/* Contact Info */}
        <Box sx={{ mb: compact ? 1 : 2 }}>
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              display: 'block',
              wordBreak: 'break-all',
              lineHeight: 1.3
            }}
          >
            📧 {member.email}
          </Typography>

          {member.phone && (
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ display: 'block', mt: 0.5 }}
            >
              📞 {member.phone}
            </Typography>
          )}

          {member.location && (
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ display: 'block', mt: 0.5 }}
            >
              📍 {member.location}
            </Typography>
          )}
        </Box>

        {/* Employment Info */}
        {!compact && (
          <Box>
            {member.employmentType && (
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                💼 {member.employmentType.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
              </Typography>
            )}

            {member.startDate && (
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                📅 Hired: {new Date(member.startDate).toLocaleDateString()}
              </Typography>
            )}
          </Box>
        )}

        {/* Selection Indicator */}
        {selectable && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Typography variant="caption" color="primary" sx={{ fontWeight: 'bold' }}>
              Click to select
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default MemberCard;
