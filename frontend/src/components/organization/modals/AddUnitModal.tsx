import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Autocomplete,
  Typography,
  Box
} from '@mui/material';
import { OrganizationalUnit, useOrganization } from '../../../contexts/OrganizationContext';

interface AddUnitModalProps {
  parentUnit?: OrganizationalUnit;
  open: boolean;
  onClose: () => void;
}

const AddUnitModal: React.FC<AddUnitModalProps> = ({
  parentUnit,
  open,
  onClose
}) => {
  const { state, actions } = useOrganization();
  const [formData, setFormData] = useState({
    name: '',
    type: 'team',
    description: '',
    budget: '',
    managerId: null as number | null
  });

  const unitTypes = ['organization', 'division', 'department', 'team', 'squad', 'unit'];

  // Get available managers
  const availableManagers = state.users.filter(user =>
    user.role === 'manager' || user.role === 'director' || user.role === 'vp' || user.role === 'ceo'
  );

  const handleCreate = async () => {
    try {
      const unitData = {
        name: formData.name,
        type: formData.type as "organization" | "division" | "department" | "team" | "squad" | "unit",
        description: formData.description,
        budget: parseFloat(formData.budget) || 0,
        parentId: parentUnit?.id,
        managerId: formData.managerId || undefined
      };

      await actions.createUnit(unitData);

      // Reset form
      setFormData({
        name: '',
        type: 'team',
        description: '',
        budget: '',
        managerId: null
      });

      onClose();
    } catch (error) {
      console.error('Failed to create unit:', error);
    }
  };

  const isFormValid = formData.name && formData.type;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        ➕ Add New {parentUnit ? 'Sub-unit' : 'Root Unit'}
      </DialogTitle>

      <DialogContent>
        {parentUnit && (
          <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Creating sub-unit under:
            </Typography>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              {parentUnit.name}
            </Typography>
          </Box>
        )}

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Unit Name *"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., Engineering Team, Marketing Department"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Autocomplete
              options={unitTypes}
              value={formData.type}
              onChange={(_, newValue) => {
                setFormData(prev => ({ ...prev, type: newValue || 'team' }));
              }}
              renderInput={(params) => (
                <TextField {...params} label="Unit Type *" />
              )}
              getOptionLabel={(option) => option.charAt(0).toUpperCase() + option.slice(1)}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Budget"
              type="number"
              value={formData.budget}
              onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
              InputProps={{
                startAdornment: '$'
              }}
              placeholder="0"
            />
          </Grid>

          <Grid item xs={12}>
            <Autocomplete
              options={availableManagers}
              value={availableManagers.find(m => m.id === formData.managerId) || null}
              onChange={(_, newValue) => {
                setFormData(prev => ({ ...prev, managerId: newValue?.id || null }));
              }}
              renderInput={(params) => (
                <TextField {...params} label="Manager (Optional)" />
              )}
              getOptionLabel={(option) => `${option.firstName} ${option.lastName} - ${option.title}`}
              isOptionEqualToValue={(option, value) => option.id === value.id}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={3}
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Brief description of this unit's purpose and responsibilities"
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleCreate}
          disabled={!isFormValid}
        >
          Create Unit
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddUnitModal;
