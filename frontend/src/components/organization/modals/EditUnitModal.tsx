import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Tabs,
  Tab,
  Grid,
  Typography,
  Autocomplete
} from '@mui/material';
import { OrganizationalUnit, useOrganization } from '../../../contexts/OrganizationContext';
import MemberCard from '../MemberCard';
import UnitCard from '../UnitCard';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`unit-tabpanel-${index}`}
      aria-labelledby={`unit-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface EditUnitModalProps {
  unit: OrganizationalUnit;
  open: boolean;
  onClose: () => void;
  onAddMember: (unit: OrganizationalUnit) => void;
  onAddSubunit: (parentId: number) => void;
}

const EditUnitModal: React.FC<EditUnitModalProps> = ({
  unit,
  open,
  onClose,
  onAddMember,
  onAddSubunit
}) => {
  const { state, actions } = useOrganization();
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState({
    name: unit.name,
    description: unit.description || '',
    budget: (unit.budget || 0).toString(),
    type: unit.type
  });

  // Reset form when unit changes
  useEffect(() => {
    setFormData({
      name: unit.name,
      description: unit.description || '',
      budget: (unit.budget || 0).toString(),
      type: unit.type
    });
  }, [unit]);

  // Get unit members
  const unitMembers = state.users.filter(user => user.organizationalUnitId === unit.id);

  // Get subunits
  const subunits = state.units.filter(u => u.parentId === unit.id);

  // Get available managers
  const availableManagers = state.users.filter(user =>
    user.role === 'manager' || user.role === 'director' || user.role === 'vp' || user.role === 'ceo'
  );

  // Use availableManagers to avoid unused variable warning
  console.log('Available managers:', availableManagers.length);

  const unitTypes = ['organization', 'division', 'department', 'team', 'squad', 'unit'];

  const handleSave = async () => {
    try {
      await actions.updateUnit(unit.id, {
        name: formData.name,
        description: formData.description,
        budget: parseFloat(formData.budget) || 0,
        type: formData.type
      });
      onClose();
    } catch (error) {
      console.error('Failed to update unit:', error);
    }
  };

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${unit.name}"? This will remove all subunits and reassign members.`)) {
      try {
        await actions.deleteUnit(unit.id);
        onClose();
      } catch (error) {
        console.error('Failed to delete unit:', error);
      }
    }
  };

  const handleRemoveMember = async (memberId: number) => {
    try {
      await actions.updateUser(memberId, { organizationalUnitId: undefined });
    } catch (error) {
      console.error('Failed to remove member:', error);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        ✏️ Edit {unit.name}
      </DialogTitle>

      <DialogContent>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label="📋 Basic Info" />
          <Tab label={`👥 Members (${unitMembers.length})`} />
          <Tab label={`🏢 Subunits (${subunits.length})`} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Unit Name *"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Autocomplete
                options={unitTypes}
                value={formData.type}
                onChange={(_, newValue) => {
                  setFormData(prev => ({ ...prev, type: (newValue || 'unit') as "organization" | "division" | "department" | "team" | "squad" | "unit" }));
                }}
                renderInput={(params) => (
                  <TextField {...params} label="Unit Type *" />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Budget"
                type="number"
                value={formData.budget}
                onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
                InputProps={{
                  startAdornment: '$'
                }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Current Members ({unitMembers.length})
            </Typography>
            <Button
              variant="contained"
              onClick={() => onAddMember(unit)}
            >
              ➕ Add Member
            </Button>
          </Box>

          <Grid container spacing={2}>
            {unitMembers.map((member) => (
              <Grid item xs={12} sm={6} md={4} key={member.id}>
                <MemberCard
                  member={member}
                  onRemove={handleRemoveMember}
                  compact
                />
              </Grid>
            ))}
            {unitMembers.length === 0 && (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
                  No members assigned to this unit yet.
                </Typography>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Subunits ({subunits.length})
            </Typography>
            <Button
              variant="contained"
              onClick={() => onAddSubunit(unit.id)}
            >
              ➕ Add Subunit
            </Button>
          </Box>

          <Grid container spacing={2}>
            {subunits.map((subunit) => (
              <Grid item xs={12} sm={6} key={subunit.id}>
                <UnitCard
                  unit={subunit}
                  members={state.users.filter(user => user.organizationalUnitId === subunit.id)}
                  onEdit={() => { }} // Will be handled by parent
                  onAddSubunit={onAddSubunit}
                />
              </Grid>
            ))}
            {subunits.length === 0 && (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
                  No subunits created yet.
                </Typography>
              </Grid>
            )}
          </Grid>
        </TabPanel>
      </DialogContent>

      <DialogActions>
        {tabValue === 0 && (
          <>
            <Button color="error" onClick={handleDelete}>
              🗑️ Delete Unit
            </Button>
            <Box sx={{ flex: 1 }} />
            <Button onClick={onClose}>
              Cancel
            </Button>
            <Button variant="contained" onClick={handleSave}>
              Save Changes
            </Button>
          </>
        )}
        {tabValue !== 0 && (
          <Button variant="contained" onClick={onClose}>
            Close
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default EditUnitModal;
