import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON>alogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Tabs,
  Tab,
  Grid,
  Typography,
  Autocomplete
} from '@mui/material';
import { OrganizationalUnit, User, useOrganization } from '../../../contexts/OrganizationContext';
import MemberCard from '../MemberCard';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`member-tabpanel-${index}`}
      aria-labelledby={`member-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface MemberSelectorModalProps {
  unit: OrganizationalUnit;
  open: boolean;
  onClose: () => void;
}

const MemberSelectorModal: React.FC<MemberSelectorModalProps> = ({
  unit,
  open,
  onClose
}) => {
  const { state, actions } = useOrganization();
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  // 🔐 Generate secure random password using crypto-secure method
  const generateSecurePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    let password = '';

    // Use crypto.getRandomValues for secure random generation
    const array = new Uint8Array(16); // 16 characters for strong password
    window.crypto.getRandomValues(array);

    for (let i = 0; i < array.length; i++) {
      password += chars.charAt(array[i] % chars.length);
    }

    return password;
  };

  const [newMemberForm, setNewMemberForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    title: '',
    role: 'engineer',
    password: generateSecurePassword(),
    phone: '',
    location: ''
  });

  // Available roles and titles
  const availableRoles = ['ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest'];
  const availableTitles = [
    'Chief Executive Officer', 'Chief Technology Officer', 'VP of Technology', 'VP of Engineering',
    'Director of Engineering', 'Engineering Manager', 'Senior Frontend Engineer', 'Senior Backend Engineer',
    'Frontend Developer', 'Backend Developer', 'Full Stack Developer', 'DevOps Engineer',
    'Data Engineer', 'Security Engineer', 'QA Engineer', 'Product Manager', 'Project Manager',
    'HR Manager', 'Office Manager', 'Secretary', 'Administrative Assistant'
  ];

  // Get available members (not assigned to this unit)
  const availableMembers = state.users.filter(user =>
    user.organizationalUnitId !== unit.id &&
    (user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.title && user.title.toLowerCase().includes(searchTerm.toLowerCase())))
  );

  const handleAssignMember = async (member: User) => {
    try {
      await actions.assignUserToUnit(member.id, unit.id);
      onClose();
    } catch (error) {
      console.error('Failed to assign member:', error);
    }
  };

  const handleCreateMember = async () => {
    try {
      const memberData = {
        ...newMemberForm,
        organizationalUnitId: unit.id,
        role: newMemberForm.role as 'engineer' | 'hr_admin' | 'manager' | 'ceo' | 'director' | 'vp' | 'senior_engineer' | 'junior_engineer' | 'intern' | 'guest'
      };

      await actions.createUser(memberData);

      // Reset form
      setNewMemberForm({
        firstName: '',
        lastName: '',
        email: '',
        title: '',
        role: 'engineer',
        password: 'defaultPassword123',
        phone: '',
        location: ''
      });

      onClose();
    } catch (error) {
      console.error('Failed to create member:', error);
    }
  };

  const isFormValid = newMemberForm.firstName && newMemberForm.lastName && newMemberForm.email && newMemberForm.title;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        👥 Add Member to {unit.name}
      </DialogTitle>

      <DialogContent>
        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab label={`📋 Select Existing (${availableMembers.length})`} />
          <Tab label="➕ Create New Member" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              label="Search members..."
              placeholder="Search by name, email, or title"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ mb: 2 }}
            />
          </Box>

          <Grid container spacing={2} sx={{ maxHeight: '400px', overflow: 'auto' }}>
            {availableMembers.map((member) => (
              <Grid item xs={12} sm={6} key={member.id}>
                <MemberCard
                  member={member}
                  onSelect={handleAssignMember}
                  selectable
                  showActions={false}
                  compact
                />
              </Grid>
            ))}
            {availableMembers.length === 0 && (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
                  {searchTerm ? 'No members found matching your search.' : 'No available members to assign.'}
                </Typography>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name *"
                value={newMemberForm.firstName}
                onChange={(e) => setNewMemberForm(prev => ({ ...prev, firstName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name *"
                value={newMemberForm.lastName}
                onChange={(e) => setNewMemberForm(prev => ({ ...prev, lastName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email *"
                type="email"
                value={newMemberForm.email}
                onChange={(e) => setNewMemberForm(prev => ({ ...prev, email: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={newMemberForm.phone}
                onChange={(e) => setNewMemberForm(prev => ({ ...prev, phone: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Autocomplete
                options={availableTitles}
                value={newMemberForm.title}
                onChange={(_, newValue) => {
                  setNewMemberForm(prev => ({ ...prev, title: newValue || '' }));
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Job Title *"
                    placeholder="Search or enter custom title"
                  />
                )}
                freeSolo
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Autocomplete
                options={availableRoles}
                value={newMemberForm.role}
                onChange={(_, newValue) => {
                  setNewMemberForm(prev => ({ ...prev, role: newValue || 'engineer' }));
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Role"
                    placeholder="Search roles"
                  />
                )}
                getOptionLabel={(option) => option.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Location"
                value={newMemberForm.location}
                onChange={(e) => setNewMemberForm(prev => ({ ...prev, location: e.target.value }))}
                placeholder="e.g., New York, Remote, Building A"
              />
            </Grid>
          </Grid>
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        {tabValue === 1 && (
          <Button
            variant="contained"
            onClick={handleCreateMember}
            disabled={!isFormValid}
          >
            Create & Add Member
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default MemberSelectorModal;
