import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
  Pagination
} from '@mui/material';
import {
  Storage as StorageIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import { ApiService } from '../../services/api';

interface DatabaseTable {
  id: string;
  name: string;
  description: string;
}

interface TableColumn {
  name: string;
  type: string;
  nullable: boolean;
  isPrimary: boolean;
}

interface TableData {
  records: any[];
  columns: TableColumn[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const DatabaseManagement: React.FC = () => {
  const [tables, setTables] = useState<DatabaseTable[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Modal states
  const [editDialog, setEditDialog] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [isNewRecord, setIsNewRecord] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);

  useEffect(() => {
    fetchAvailableTables();
  }, []);

  useEffect(() => {
    if (selectedTable) {
      fetchTableData(selectedTable, currentPage);
    }
  }, [selectedTable, currentPage]);

  const fetchAvailableTables = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch real database tables from API
      const response = await ApiService.getDatabaseTables();
      const tablesData = Array.isArray(response) ? response : [];

      // Transform API response to match DatabaseTable interface
      const transformedTables: DatabaseTable[] = tablesData.map((table: any) => ({
        id: table.id?.toString() || table.table_name || table.name || '',
        name: table.name || table.display_name || table.table_name || '',
        description: table.description || table.table_description || `Database table: ${table.name || table.table_name}`
      }));

      setTables(transformedTables);
    } catch (err) {
      setError('Error fetching database tables from API');
      console.error('Error fetching tables:', err);

      // NIS2-compliant fallback - no system structure exposure
      setTables([
        {
          id: 'database_unavailable',
          name: 'Database Connection Required',
          description: 'Please ensure database connection is established'
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchTableData = async (tableName: string, page: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch real table data from API
      const response = await ApiService.getTableData(tableName, page, pageSize);

      // The API returns { success: true, data: { records, columns, pagination } }
      if (response && response.records && response.columns && response.pagination) {
        setTableData({
          records: response.records,
          columns: response.columns,
          pagination: response.pagination
        });
      } else {
        throw new Error('Invalid response format');
      }

    } catch (err) {
      setError('Error fetching table data from API');
      console.error('Error fetching table data:', err);

      // Fallback to empty data on error
      setTableData({
        records: [],
        columns: [],
        pagination: {
          page: 1,
          limit: pageSize,
          total: 0,
          totalPages: 0
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditRecord = (record: any) => {
    setEditingRecord({ ...record });
    setIsNewRecord(false);
    setEditDialog(true);
  };

  const handleNewRecord = () => {
    if (!tableData?.columns) return;

    const newRecord: any = {};
    tableData.columns.forEach(col => {
      if (!col.isPrimary) {
        newRecord[col.name] = '';
      }
    });

    setEditingRecord(newRecord);
    setIsNewRecord(true);
    setEditDialog(true);
  };

  const handleSaveRecord = async () => {
    if (!selectedTable || !editingRecord) return;

    try {
      setIsLoading(true);

      // 🔐 NIS2-COMPLIANT: Log database operation attempt
      console.log('🔐 [SECURITY-AUDIT] Database operation attempted:', {
        operation: isNewRecord ? 'CREATE' : 'UPDATE',
        table: selectedTable,
        recordId: editingRecord.id || 'new',
        timestamp: new Date().toISOString()
      });

      if (isNewRecord) {
        await ApiService.createRecord(selectedTable, editingRecord);
        setSuccess('Record created successfully');
      } else {
        const id = editingRecord.id;
        await ApiService.updateRecord(selectedTable, id, editingRecord);
        setSuccess('Record updated successfully');
      }

      // 🔐 NIS2-COMPLIANT: Log successful database operation
      console.log('🔐 [SECURITY-AUDIT] Database operation completed successfully:', {
        operation: isNewRecord ? 'CREATE' : 'UPDATE',
        table: selectedTable,
        timestamp: new Date().toISOString()
      });

      setEditDialog(false);
      setEditingRecord(null);
      fetchTableData(selectedTable, currentPage);
    } catch (err) {
      // 🔐 NIS2-COMPLIANT: Log failed database operation
      console.error('🔐 [SECURITY-AUDIT] Database operation failed:', {
        operation: isNewRecord ? 'CREATE' : 'UPDATE',
        table: selectedTable,
        error: err instanceof Error ? err.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });

      setError('Error saving record');
      console.error('Error saving record:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteRecord = async (recordId: number) => {
    if (!selectedTable || !window.confirm('Are you sure you want to delete this record?')) return;

    try {
      setIsLoading(true);

      // 🔐 NIS2-COMPLIANT: Log database deletion attempt
      console.log('🔐 [SECURITY-AUDIT] Database deletion attempted:', {
        operation: 'DELETE',
        table: selectedTable,
        recordId: recordId,
        timestamp: new Date().toISOString()
      });

      await ApiService.deleteRecord(selectedTable, recordId.toString());

      // 🔐 NIS2-COMPLIANT: Log successful deletion
      console.log('🔐 [SECURITY-AUDIT] Database deletion completed successfully:', {
        operation: 'DELETE',
        table: selectedTable,
        recordId: recordId,
        timestamp: new Date().toISOString()
      });

      setSuccess('Record deleted successfully');
      fetchTableData(selectedTable, currentPage);
    } catch (err) {
      // 🔐 NIS2-COMPLIANT: Log failed deletion
      console.error('🔐 [SECURITY-AUDIT] Database deletion failed:', {
        operation: 'DELETE',
        table: selectedTable,
        recordId: recordId,
        error: err instanceof Error ? err.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });

      setError('Error deleting record');
      console.error('Error deleting record:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const renderTableSelector = () => (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          <StorageIcon sx={{ mr: 1 }} />
          Database Tables
        </Typography>

        <Grid container spacing={2}>
          {tables && tables.length > 0 ? tables.map((table) => (
            <Grid item xs={12} sm={6} md={4} key={table.id}>
              <Card
                variant="outlined"
                sx={{
                  cursor: 'pointer',
                  border: selectedTable === table.id ? 2 : 1,
                  borderColor: selectedTable === table.id ? 'primary.main' : 'divider',
                  '&:hover': { borderColor: 'primary.main' }
                }}
                onClick={() => {
                  setSelectedTable(table.id);
                  setCurrentPage(1);
                }}
              >
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                    {table.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {table.description}
                  </Typography>
                  {selectedTable === table.id && (
                    <Chip label="Selected" color="primary" size="small" sx={{ mt: 1 }} />
                  )}
                </CardContent>
              </Card>
            </Grid>
          )) : (
            <Grid item xs={12}>
              <Typography variant="body2" color="text.secondary" align="center">
                {isLoading ? 'Loading tables...' : 'No database tables available'}
              </Typography>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  );

  const renderTableData = () => {
    if (!selectedTable || !tableData) return null;

    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              {tables.find(t => t.id === selectedTable)?.name} Data
            </Typography>
            <Box>
              <Tooltip title="Refresh Data">
                <IconButton onClick={() => fetchTableData(selectedTable, currentPage)}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleNewRecord}
                sx={{ ml: 1 }}
              >
                Add Record
              </Button>
            </Box>
          </Box>

          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {tableData?.columns?.map((column) => (
                        <TableCell key={column.name} sx={{ fontWeight: 'bold' }}>
                          {column.name}
                          {column.isPrimary && <Chip label="PK" size="small" sx={{ ml: 1 }} />}
                        </TableCell>
                      ))}
                      <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {tableData?.records?.map((record, index) => (
                      <TableRow key={record.id || index} hover>
                        {tableData?.columns?.map((column) => (
                          <TableCell key={column.name}>
                            {record[column.name] !== null && record[column.name] !== undefined
                              ? String(record[column.name])
                              : <em style={{ color: '#999' }}>NULL</em>
                            }
                          </TableCell>
                        ))}
                        <TableCell>
                          <Tooltip title="Edit Record">
                            <IconButton size="small" onClick={() => handleEditRecord(record)}>
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Record">
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteRecord(record.id)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    )) || (
                        <TableRow>
                          <TableCell colSpan={100} align="center">
                            <Typography variant="body2" color="text.secondary">
                              {isLoading ? 'Loading data...' : 'No data available'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                  </TableBody>
                </Table>
              </TableContainer>

              {tableData?.pagination?.totalPages && tableData.pagination.totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                  <Pagination
                    count={tableData?.pagination?.totalPages || 1}
                    page={currentPage}
                    onChange={(e, page) => setCurrentPage(page)}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
        Database Management
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        🔐 Secure database monitoring and controlled data access through API endpoints
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      {renderTableSelector()}
      {renderTableData()}

      {/* Edit Record Dialog */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {isNewRecord ? 'Create New Record' : 'Edit Record'}
        </DialogTitle>
        <DialogContent>
          {editingRecord && tableData?.columns && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              {tableData.columns
                .filter(col => !col.isPrimary || !isNewRecord)
                .map((column) => (
                  <Grid item xs={12} sm={6} key={column.name}>
                    <TextField
                      fullWidth
                      label={column.name}
                      value={editingRecord[column.name] || ''}
                      onChange={(e) => setEditingRecord({
                        ...editingRecord,
                        [column.name]: e.target.value
                      })}
                      disabled={column.isPrimary && !isNewRecord}
                      helperText={column.isPrimary ? 'Primary Key' : `Type: ${column.type}`}
                    />
                  </Grid>
                ))}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveRecord} variant="contained">
            {isNewRecord ? 'Create' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DatabaseManagement;
