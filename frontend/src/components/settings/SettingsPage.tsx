import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>pography,
  Ta<PERSON>,
  Tab,
  Card,
  CardContent,
  Grid,
  Button,
  Alert
} from '@mui/material';
import UserManagement from './UserManagement';
import SystemSettings from './SystemSettings';
import DatabaseManagement from './DatabaseManagement';
import ApiConfiguration from './ApiConfiguration';
import { ApiService } from '../../services/api';
import { UserRole } from '../../types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SettingsPage: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const currentUserRole: UserRole = UserRole.HR_ADMIN; // This would come from auth context
  console.log('Current user role:', currentUserRole); // Use variable to avoid warning
  const isAuthenticated = ApiService.isAuthenticated();

  const handleLoginRedirect = () => {
    window.location.href = '/login';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
        Settings & Database Management
      </Typography>
      <Typography variant="h6" gutterBottom color="text.secondary" sx={{ mb: 4 }}>
        System configuration, user management, and database administration
      </Typography>

      {!isAuthenticated && (
        <Alert
          severity="warning"
          sx={{ mb: 3 }}
          action={
            <Button color="inherit" size="small" onClick={handleLoginRedirect}>
              Login
            </Button>
          }
        >
          You need to be logged in to access all Settings features. Some functionality may be limited.
        </Alert>
      )}

      <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="User Management" />
        <Tab label="System Settings" />
        <Tab label="Database Tools" />
        <Tab label="API Configuration" />
      </Tabs>

      <TabPanel value={selectedTab} index={0}>
        <UserManagement />
      </TabPanel>

      <TabPanel value={selectedTab} index={1}>
        <SystemSettings />
      </TabPanel>

      <TabPanel value={selectedTab} index={2}>
        <DatabaseManagement />
      </TabPanel>

      <TabPanel value={selectedTab} index={3}>
        <ApiConfiguration />
      </TabPanel>
    </Box>
  );
};

export default SettingsPage;
