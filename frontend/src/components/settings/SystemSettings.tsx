import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Chip,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Palette as PaletteIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  Email as EmailIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { ApiService } from '../../services/api';

interface SystemConfig {
  id: string;
  category: string;
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  description: string;
  isEditable: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`system-settings-tabpanel-${index}`}
      aria-labelledby={`system-settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SystemSettings: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [configs, setConfigs] = useState<SystemConfig[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [editDialog, setEditDialog] = useState(false);
  const [editingConfig, setEditingConfig] = useState<SystemConfig | null>(null);

  useEffect(() => {
    fetchSystemConfigs();
  }, []);

  const fetchSystemConfigs = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check authentication before making API call
      if (!ApiService.isAuthenticated()) {
        setError('Please log in to access system settings');
        // Use minimal fallback for unauthenticated users (NIS2 compliant - no system details)
        setConfigs([
          {
            id: 'auth_required',
            category: 'security',
            key: 'authentication_required',
            value: 'true',
            type: 'boolean',
            description: 'Authentication required to access system settings',
            isEditable: false
          }
        ]);
        return;
      }

      // Fetch real system configurations from database
      const response = await ApiService.getSystemConfigurations();
      const configsData = Array.isArray(response) ? response : response.data || response.configurations || [];

      // Transform API response to match SystemConfig interface
      const transformedConfigs: SystemConfig[] = configsData.map((config: any) => ({
        id: config.id?.toString() || '',
        category: config.category || 'general',
        key: config.key || config.config_key || '',
        value: config.value || config.config_value || '',
        type: config.type || config.value_type || 'string',
        description: config.description || '',
        isEditable: config.isEditable !== undefined ? config.isEditable : config.is_editable !== undefined ? config.is_editable : true
      }));

      setConfigs(transformedConfigs);
    } catch (err: any) {
      if (err?.response?.status === 401) {
        setError('Authentication expired. Please log in again to access system settings.');
      } else {
        setError(`Error fetching system configurations: ${err?.message || 'Unknown error'}`);
      }
      console.error('Error fetching configs:', err);

      // NIS2-compliant fallback - minimal system exposure
      setConfigs([
        {
          id: 'error_fallback',
          category: 'system',
          key: 'configuration_status',
          value: 'unavailable',
          type: 'string',
          description: 'System configuration temporarily unavailable',
          isEditable: false
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditConfig = (config: SystemConfig) => {
    setEditingConfig({ ...config });
    setEditDialog(true);
  };

  const handleSaveConfig = async () => {
    if (!editingConfig) return;

    try {
      setIsLoading(true);

      // 🔐 NIS2-COMPLIANT: Log configuration change attempt
      console.log('🔐 [SECURITY-AUDIT] System configuration change attempted:', {
        configKey: editingConfig.key,
        category: editingConfig.category,
        timestamp: new Date().toISOString()
      });

      // Update configuration via API
      const configData = {
        category: editingConfig.category,
        key: editingConfig.key,
        value: editingConfig.value,
        type: editingConfig.type,
        description: editingConfig.description,
        isEditable: editingConfig.isEditable
      };

      await ApiService.updateSystemConfiguration(editingConfig.id, configData);

      // 🔐 NIS2-COMPLIANT: Log successful configuration change
      console.log('🔐 [SECURITY-AUDIT] System configuration changed successfully:', {
        configKey: editingConfig.key,
        category: editingConfig.category,
        timestamp: new Date().toISOString()
      });

      setSuccess('Configuration updated successfully');

      // Refresh configurations list
      await fetchSystemConfigs();

      setEditDialog(false);
      setEditingConfig(null);
    } catch (err) {
      // 🔐 NIS2-COMPLIANT: Log failed configuration change
      console.error('🔐 [SECURITY-AUDIT] System configuration change failed:', {
        configKey: editingConfig?.key,
        error: err instanceof Error ? err.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });

      setError(`Error saving configuration: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const getConfigsByCategory = (category: string) => {
    return configs.filter(config => config.category === category);
  };

  const renderConfigValue = (config: SystemConfig) => {
    if (config.type === 'boolean') {
      return (
        <Chip
          label={config.value === 'true' ? 'Enabled' : 'Disabled'}
          color={config.value === 'true' ? 'success' : 'default'}
          size="small"
        />
      );
    }
    return config.value;
  };

  const renderGeneralSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <SettingsIcon sx={{ mr: 1 }} />
              General Configuration
            </Typography>
            <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Setting</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Value</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getConfigsByCategory('general').map((config) => (
                    <TableRow key={config.id} hover>
                      <TableCell sx={{ fontWeight: 'medium' }}>{config.key}</TableCell>
                      <TableCell>{renderConfigValue(config)}</TableCell>
                      <TableCell>{config.description}</TableCell>
                      <TableCell>
                        {config.isEditable && (
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => handleEditConfig(config)}
                          >
                            Edit
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderSecuritySettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <SecurityIcon sx={{ mr: 1 }} />
              Security Configuration
            </Typography>
            <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Setting</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Value</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getConfigsByCategory('security').map((config) => (
                    <TableRow key={config.id} hover>
                      <TableCell sx={{ fontWeight: 'medium' }}>{config.key}</TableCell>
                      <TableCell>{renderConfigValue(config)}</TableCell>
                      <TableCell>{config.description}</TableCell>
                      <TableCell>
                        {config.isEditable && (
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => handleEditConfig(config)}
                          >
                            Edit
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderNotificationSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <NotificationsIcon sx={{ mr: 1 }} />
              Notification Configuration
            </Typography>
            <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Setting</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Value</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getConfigsByCategory('notifications').map((config) => (
                    <TableRow key={config.id} hover>
                      <TableCell sx={{ fontWeight: 'medium' }}>{config.key}</TableCell>
                      <TableCell>{renderConfigValue(config)}</TableCell>
                      <TableCell>{config.description}</TableCell>
                      <TableCell>
                        {config.isEditable && (
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => handleEditConfig(config)}
                          >
                            Edit
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderPerformanceSettings = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <SpeedIcon sx={{ mr: 1 }} />
              Performance Configuration
            </Typography>
            <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Setting</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Value</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getConfigsByCategory('performance').map((config) => (
                    <TableRow key={config.id} hover>
                      <TableCell sx={{ fontWeight: 'medium' }}>{config.key}</TableCell>
                      <TableCell>{renderConfigValue(config)}</TableCell>
                      <TableCell>{config.description}</TableCell>
                      <TableCell>
                        {config.isEditable && (
                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => handleEditConfig(config)}
                          >
                            Edit
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
        System Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Configure system-wide settings, security, and performance options
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
          {success}
        </Alert>
      )}

      <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="General" />
        <Tab label="Security" />
        <Tab label="Notifications" />
        <Tab label="Performance" />
      </Tabs>

      <TabPanel value={selectedTab} index={0}>
        {renderGeneralSettings()}
      </TabPanel>

      <TabPanel value={selectedTab} index={1}>
        {renderSecuritySettings()}
      </TabPanel>

      <TabPanel value={selectedTab} index={2}>
        {renderNotificationSettings()}
      </TabPanel>

      <TabPanel value={selectedTab} index={3}>
        {renderPerformanceSettings()}
      </TabPanel>

      {/* Configuration Edit Dialog */}
      <Dialog open={editDialog} onClose={() => setEditDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Edit Configuration: {editingConfig?.key}
        </DialogTitle>
        <DialogContent>
          {editingConfig && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {editingConfig.description}
              </Typography>

              {editingConfig.type === 'boolean' ? (
                <FormControlLabel
                  control={
                    <Switch
                      checked={editingConfig.value === 'true'}
                      onChange={(e) => setEditingConfig({
                        ...editingConfig,
                        value: e.target.checked ? 'true' : 'false'
                      })}
                    />
                  }
                  label={editingConfig.key}
                />
              ) : editingConfig.key === 'default_theme' ? (
                <FormControl fullWidth>
                  <InputLabel>Theme</InputLabel>
                  <Select
                    value={editingConfig.value}
                    label="Theme"
                    onChange={(e) => setEditingConfig({
                      ...editingConfig,
                      value: e.target.value
                    })}
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                    <MenuItem value="auto">Auto</MenuItem>
                  </Select>
                </FormControl>
              ) : (
                <TextField
                  fullWidth
                  label="Value"
                  type={editingConfig.type === 'number' ? 'number' : 'text'}
                  value={editingConfig.value}
                  onChange={(e) => setEditingConfig({
                    ...editingConfig,
                    value: e.target.value
                  })}
                />
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveConfig} variant="contained">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SystemSettings;
