import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AssessmentTemplate, UserRole } from '../../types';
import { ApiService } from '../../services/api';

interface EnhancedTemplateListProps {
  userRole: UserRole;
}

const EnhancedTemplateList: React.FC<EnhancedTemplateListProps> = ({ userRole }) => {
  const navigate = useNavigate();
  const [templates, setTemplates] = useState<AssessmentTemplate[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'global' | 'personal'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'created' | 'version'>('name');

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use centralized database API for assessment templates
      const response = await ApiService.getTableData('assessment_templates', 1, 100);

      if (response && response.records) {
        const transformedTemplates = response.records.map((template: any) => ({
          id: template.id,
          name: template.name,
          description: template.description,
          isActive: template.isActive !== false,
          isGlobal: template.isGlobal !== false,
          version: template.version || 1,
          createdById: template.createdById || template.created_by_id,
          createdAt: template.createdAt || template.created_at,
          updatedAt: template.updatedAt || template.updated_at,
          areas: template.areas || []
        }));
        setTemplates(transformedTemplates);
      } else {
        setTemplates([]);
      }
    } catch (err: any) {
      console.error('Error fetching templates from database API:', err);
      setError(err.response?.data?.message || 'Failed to load templates from database');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloneTemplate = async (templateId: number, templateName: string) => {
    try {
      const customName = prompt(`Enter name for cloned template:`, `${templateName} (Copy)`);
      if (!customName) return;

      const response = await ApiService.post(`/templates/${templateId}/clone`, { name: customName });
      setTemplates([...templates, response.data]);

      // Navigate to edit the cloned template
      navigate(`/templates/${response.data.id}/edit`);
    } catch (err: any) {
      console.error('Error cloning template:', err);
      alert(err.response?.data?.message || 'Failed to clone template');
    }
  };

  const handleCreateNewVersion = async (templateId: number) => {
    try {
      const response = await ApiService.post(`/templates/${templateId}/new-version`);
      setTemplates([...templates, response.data]);

      // Navigate to edit the new version
      navigate(`/templates/${response.data.id}/edit`);
    } catch (err: any) {
      console.error('Error creating new version:', err);
      alert(err.response?.data?.message || 'Failed to create new version');
    }
  };

  // Filter and sort templates
  const filteredTemplates = templates
    .filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (template.description || '').toLowerCase().includes(searchTerm.toLowerCase());

      const matchesFilter = filterType === 'all' ||
        (filterType === 'global' && template.isGlobal) ||
        (filterType === 'personal' && !template.isGlobal);

      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'created':
          return new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime();
        case 'version':
          return (b.version || 0) - (a.version || 0);
        default:
          return 0;
      }
    });

  const canCreateTemplate = userRole === UserRole.HR_ADMIN || userRole === UserRole.MANAGER;
  const canEditTemplate = (template: AssessmentTemplate) => {
    return userRole === UserRole.HR_ADMIN ||
      (userRole === UserRole.MANAGER && !template.isGlobal);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <p className="font-bold">Error loading templates</p>
        <p>{error}</p>
        <button
          onClick={fetchTemplates}
          className="mt-2 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Assessment Templates</h1>
          <p className="text-gray-600 mt-1">
            Manage assessment templates with advanced scoring rules and configurations
          </p>
        </div>

        {canCreateTemplate && (
          <Link
            to="/templates/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Create Template
          </Link>
        )}
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg shadow border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search Templates
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name or description..."
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Filter by Type
            </label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as 'all' | 'global' | 'personal')}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="all">All Templates</option>
              <option value="global">Global Templates</option>
              <option value="personal">Personal Templates</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort by
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'created' | 'version')}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="name">Name</option>
              <option value="created">Date Created</option>
              <option value="version">Version</option>
            </select>
          </div>
        </div>
      </div>

      {/* Templates Grid */}
      {filteredTemplates.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow border border-gray-200">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No templates found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || filterType !== 'all'
              ? 'Try adjusting your search or filter criteria.'
              : 'Get started by creating your first assessment template.'
            }
          </p>
          {canCreateTemplate && !searchTerm && filterType === 'all' && (
            <div className="mt-6">
              <Link
                to="/templates/new"
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create Template
              </Link>
            </div>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template) => (
            <div key={template.id} className="bg-white rounded-lg shadow border border-gray-200 hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-1">
                      <Link
                        to={`/templates/${template.id}`}
                        className="hover:text-blue-600 transition-colors"
                      >
                        {template.name}
                      </Link>
                    </h3>
                    <div className="flex items-center space-x-2 mb-2">
                      {template.isGlobal && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Global
                        </span>
                      )}
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        v{template.version || 1}
                      </span>
                      {template.isActive === false && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Inactive
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Actions Dropdown */}
                  <div className="relative">
                    <button className="text-gray-400 hover:text-gray-600 p-1">
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                      </svg>
                    </button>
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {template.description || 'No description provided'}
                </p>

                <div className="space-y-2 text-xs text-gray-500">
                  <div className="flex justify-between">
                    <span>Areas:</span>
                    <span className="font-medium">{template.areas?.length || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Scoring Rules:</span>
                    <span className="font-medium">
                      {template.areas?.reduce((total, area) => total + (area.scoringRules?.length || 0), 0) || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">
                      {template.createdAt ? new Date(template.createdAt).toLocaleDateString() : 'Unknown'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-between">
                <div className="flex space-x-2">
                  <Link
                    to={`/templates/${template.id}`}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View
                  </Link>
                  {canEditTemplate(template) && (
                    <Link
                      to={`/templates/${template.id}/edit`}
                      className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                    >
                      Edit
                    </Link>
                  )}
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleCloneTemplate(template.id!, template.name)}
                    className="text-sm text-gray-600 hover:text-gray-800 font-medium"
                    title="Clone template"
                  >
                    Clone
                  </button>
                  {canEditTemplate(template) && (
                    <button
                      onClick={() => handleCreateNewVersion(template.id!)}
                      className="text-sm text-gray-600 hover:text-gray-800 font-medium"
                      title="Create new version"
                    >
                      New Version
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EnhancedTemplateList;
