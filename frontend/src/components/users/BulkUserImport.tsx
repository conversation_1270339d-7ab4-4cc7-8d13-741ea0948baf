import React, { useState } from 'react';
import { UserRole } from '../../types';
import api from '../../services/api';

interface BulkUserImportProps {
  onClose: () => void;
  onSuccess: (result: any) => void;
}

interface UserImportData {
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  password: string;
}

const BulkUserImport: React.FC<BulkUserImportProps> = ({
  onClose,
  onSuccess
}) => {
  const [importMethod, setImportMethod] = useState<'csv' | 'manual'>('manual');
  const [users, setUsers] = useState<UserImportData[]>([
    { email: '', firstName: '', lastName: '', role: UserRole.EMPLOYEE, password: '' }
  ]);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<UserImportData[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<number, Record<string, string>>>({});
  const [sendInvitations, setSendInvitations] = useState(true);

  const addUser = () => {
    setUsers([...users, { email: '', firstName: '', lastName: '', role: UserRole.EMPLOYEE, password: '' }]);
  };

  const removeUser = (index: number) => {
    if (users.length > 1) {
      setUsers(users.filter((_, i) => i !== index));
    }
  };

  const updateUser = (index: number, field: keyof UserImportData, value: string) => {
    const updatedUsers = [...users];
    updatedUsers[index] = { ...updatedUsers[index], [field]: value };
    setUsers(updatedUsers);

    // Clear validation error for this field
    if (validationErrors[index]?.[field]) {
      const newErrors = { ...validationErrors };
      delete newErrors[index][field];
      if (Object.keys(newErrors[index]).length === 0) {
        delete newErrors[index];
      }
      setValidationErrors(newErrors);
    }
  };

  const generatePassword = (index: number) => {
    const password = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
    updateUser(index, 'password', password);
  };

  // 🔐 SECURITY: CSV injection protection
  const sanitizeCsvValue = (value: string): string => {
    if (!value) return '';

    // Remove dangerous characters that could lead to CSV injection
    const dangerous = ['=', '+', '-', '@', '\t', '\r'];
    let sanitized = value.trim();

    // If value starts with dangerous character, prefix with single quote
    if (dangerous.some(char => sanitized.startsWith(char))) {
      sanitized = `'${sanitized}`;
    }

    // Remove any formula-like patterns
    sanitized = sanitized.replace(/^[\=\+\-@]/, '');

    // Limit length to prevent buffer overflow
    sanitized = sanitized.substring(0, 255);

    return sanitized;
  };

  const handleCsvUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 🔐 SECURITY: Validate file type and size
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setError('Only CSV files are allowed');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setError('File size must be less than 5MB');
      return;
    }

    setCsvFile(file);
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const lines = text.split('\n').filter(line => line.trim());

        if (lines.length < 2) {
          setError('CSV file must contain at least a header row and one data row');
          return;
        }

        const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
        const requiredHeaders = ['email', 'firstname', 'lastname'];

        const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
        if (missingHeaders.length > 0) {
          setError(`Missing required headers: ${missingHeaders.join(', ')}`);
          return;
        }

        const parsedUsers: UserImportData[] = [];

        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(',').map(v => sanitizeCsvValue(v));

          if (values.length < headers.length) continue;

          const user: UserImportData = {
            email: sanitizeCsvValue(values[headers.indexOf('email')] || ''),
            firstName: sanitizeCsvValue(values[headers.indexOf('firstname')] || ''),
            lastName: sanitizeCsvValue(values[headers.indexOf('lastname')] || ''),
            role: (values[headers.indexOf('role')] as UserRole) || UserRole.EMPLOYEE,
            password: values[headers.indexOf('password')] || Math.random().toString(36).slice(-12)
          };

          parsedUsers.push(user);
        }

        setCsvData(parsedUsers);
        setError(null);
      } catch (err) {
        setError('Failed to parse CSV file. Please check the format.');
      }
    };

    reader.readAsText(file);
  };

  const validateUsers = (usersToValidate: UserImportData[]): boolean => {
    const errors: Record<number, Record<string, string>> = {};
    let hasErrors = false;

    usersToValidate.forEach((user, index) => {
      const userErrors: Record<string, string> = {};

      if (!user.email.trim()) {
        userErrors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(user.email)) {
        userErrors.email = 'Invalid email format';
      }

      if (!user.firstName.trim()) {
        userErrors.firstName = 'First name is required';
      }

      if (!user.lastName.trim()) {
        userErrors.lastName = 'Last name is required';
      }

      if (!user.password.trim()) {
        userErrors.password = 'Password is required';
      } else if (user.password.length < 8) {
        userErrors.password = 'Password must be at least 8 characters';
      }

      if (!Object.values(UserRole).includes(user.role)) {
        userErrors.role = 'Invalid role';
      }

      if (Object.keys(userErrors).length > 0) {
        errors[index] = userErrors;
        hasErrors = true;
      }
    });

    // Check for duplicate emails
    const emailCounts: Record<string, number[]> = {};
    usersToValidate.forEach((user, index) => {
      if (user.email) {
        if (!emailCounts[user.email]) {
          emailCounts[user.email] = [];
        }
        emailCounts[user.email].push(index);
      }
    });

    Object.entries(emailCounts).forEach(([email, indices]) => {
      if (indices.length > 1) {
        indices.forEach(index => {
          if (!errors[index]) errors[index] = {};
          errors[index].email = 'Duplicate email address';
        });
        hasErrors = true;
      }
    });

    setValidationErrors(errors);
    return !hasErrors;
  };

  const handleSubmit = async () => {
    const usersToImport = importMethod === 'csv' ? csvData : users;

    if (!validateUsers(usersToImport)) {
      setError('Please fix validation errors before proceeding');
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);

      const response = await api.post('/users/bulk/create', {
        users: usersToImport,
        sendInvitations
      });

      onSuccess(response.data);
    } catch (err: any) {
      console.error('Error importing users:', err);
      setError(err.response?.data?.message || 'Failed to import users');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadTemplate = () => {
    const csvContent = 'email,firstname,lastname,role,password\<EMAIL>,John,Doe,employee,password123\n';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'user_import_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">Bulk User Import</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Import Method Selection */}
        <div className="mb-6">
          <div className="flex space-x-4">
            <button
              onClick={() => setImportMethod('manual')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${importMethod === 'manual'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
            >
              Manual Entry
            </button>
            <button
              onClick={() => setImportMethod('csv')}
              className={`px-4 py-2 rounded-md text-sm font-medium ${importMethod === 'csv'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
            >
              CSV Upload
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* CSV Upload */}
        {importMethod === 'csv' && (
          <div className="mb-6">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <div className="text-center">
                <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <div className="mt-4">
                  <label htmlFor="csv-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Upload CSV file
                    </span>
                    <input
                      id="csv-upload"
                      type="file"
                      accept=".csv"
                      onChange={handleCsvUpload}
                      className="sr-only"
                    />
                  </label>
                  <p className="mt-1 text-xs text-gray-500">
                    CSV file with columns: email, firstname, lastname, role (optional), password (optional)
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4 flex justify-between items-center">
              <button
                onClick={downloadTemplate}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                Download CSV Template
              </button>

              {csvData.length > 0 && (
                <span className="text-sm text-green-600">
                  {csvData.length} users loaded from CSV
                </span>
              )}
            </div>
          </div>
        )}

        {/* Manual Entry */}
        {importMethod === 'manual' && (
          <div className="mb-6">
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {users.map((user, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="text-sm font-medium text-gray-900">User {index + 1}</h4>
                    {users.length > 1 && (
                      <button
                        onClick={() => removeUser(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Remove
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Email *
                      </label>
                      <input
                        type="email"
                        value={user.email}
                        onChange={(e) => updateUser(index, 'email', e.target.value)}
                        className={`block w-full rounded-md text-sm ${validationErrors[index]?.email ? 'border-red-300' : 'border-gray-300'
                          }`}
                      />
                      {validationErrors[index]?.email && (
                        <p className="mt-1 text-xs text-red-600">{validationErrors[index].email}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        First Name *
                      </label>
                      <input
                        type="text"
                        value={user.firstName}
                        onChange={(e) => updateUser(index, 'firstName', e.target.value)}
                        className={`block w-full rounded-md text-sm ${validationErrors[index]?.firstName ? 'border-red-300' : 'border-gray-300'
                          }`}
                      />
                      {validationErrors[index]?.firstName && (
                        <p className="mt-1 text-xs text-red-600">{validationErrors[index].firstName}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        value={user.lastName}
                        onChange={(e) => updateUser(index, 'lastName', e.target.value)}
                        className={`block w-full rounded-md text-sm ${validationErrors[index]?.lastName ? 'border-red-300' : 'border-gray-300'
                          }`}
                      />
                      {validationErrors[index]?.lastName && (
                        <p className="mt-1 text-xs text-red-600">{validationErrors[index].lastName}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Role
                      </label>
                      <select
                        value={user.role}
                        onChange={(e) => updateUser(index, 'role', e.target.value)}
                        className="block w-full rounded-md border-gray-300 text-sm"
                      >
                        <option value={UserRole.EMPLOYEE}>Employee</option>
                        <option value={UserRole.MANAGER}>Manager</option>
                        <option value={UserRole.HR_ADMIN}>HR Admin</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Password *
                      </label>
                      <div className="flex">
                        <input
                          type="text"
                          value={user.password}
                          onChange={(e) => updateUser(index, 'password', e.target.value)}
                          className={`block w-full rounded-l-md text-sm ${validationErrors[index]?.password ? 'border-red-300' : 'border-gray-300'
                            }`}
                        />
                        <button
                          onClick={() => generatePassword(index)}
                          className="px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-xs hover:bg-gray-100"
                        >
                          Generate
                        </button>
                      </div>
                      {validationErrors[index]?.password && (
                        <p className="mt-1 text-xs text-red-600">{validationErrors[index].password}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <button
              onClick={addUser}
              className="mt-4 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Add Another User
            </button>
          </div>
        )}

        {/* Options */}
        <div className="mb-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={sendInvitations}
              onChange={(e) => setSendInvitations(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-700">
              Send invitation emails to new users
            </span>
          </label>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isProcessing || (importMethod === 'csv' && csvData.length === 0)}
            className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${isProcessing || (importMethod === 'csv' && csvData.length === 0)
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700'
              }`}
          >
            {isProcessing ? 'Importing...' : `Import ${importMethod === 'csv' ? csvData.length : users.length} User(s)`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BulkUserImport;
