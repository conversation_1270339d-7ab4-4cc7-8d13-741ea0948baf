import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { ApiService } from '../services/api';
import { OrganizationalUnit, User } from '../types';
import { validateOrganizationalUnit, validateUser, sanitizeInput } from '../utils/validation';

// Re-export types for components that import them from this context
export type { OrganizationalUnit, User } from '../types';

// 🔐 SECURITY: Using centralized type definitions from types/index.ts

// State interface
interface OrganizationState {
  units: OrganizationalUnit[];
  users: User[];
  loading: boolean;
  error: string | null;
  selectedUnit: OrganizationalUnit | null;
}

// Action types
type OrganizationAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_UNITS'; payload: OrganizationalUnit[] }
  | { type: 'SET_USERS'; payload: User[] }
  | { type: 'ADD_UNIT'; payload: OrganizationalUnit }
  | { type: 'UPDATE_UNIT'; payload: OrganizationalUnit }
  | { type: 'DELETE_UNIT'; payload: number }
  | { type: 'ADD_USER'; payload: User }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'DELETE_USER'; payload: number }
  | { type: 'SET_SELECTED_UNIT'; payload: OrganizationalUnit | null };

// 🔐 SECURITY: Initial state with NO mock data - all data must come from API
const initialState: OrganizationState = {
  units: [], // 🔐 No mock data - must be loaded from API
  users: [], // 🔐 No mock data - must be loaded from API
  loading: false,
  error: null,
  selectedUnit: null,
};

// Reducer
const organizationReducer = (state: OrganizationState, action: OrganizationAction): OrganizationState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    case 'SET_UNITS':
      return { ...state, units: action.payload, loading: false };
    case 'SET_USERS':
      return { ...state, users: action.payload, loading: false };
    case 'ADD_UNIT':
      return { ...state, units: [...state.units, action.payload] };
    case 'UPDATE_UNIT':
      return {
        ...state,
        units: state.units.map(unit =>
          unit.id === action.payload.id ? action.payload : unit
        )
      };
    case 'DELETE_UNIT':
      return {
        ...state,
        units: state.units.filter(unit => unit.id !== action.payload)
      };
    case 'ADD_USER':
      return { ...state, users: [...state.users, action.payload] };
    case 'UPDATE_USER':
      return {
        ...state,
        users: state.users.map(user =>
          user.id === action.payload.id ? action.payload : user
        )
      };
    case 'DELETE_USER':
      return {
        ...state,
        users: state.users.filter(user => user.id !== action.payload)
      };
    case 'SET_SELECTED_UNIT':
      return { ...state, selectedUnit: action.payload };
    default:
      return state;
  }
};

// Context type
interface OrganizationContextType {
  state: OrganizationState;
  actions: {
    loadData: () => Promise<void>;
    createUnit: (unitData: Partial<OrganizationalUnit>) => Promise<void>;
    updateUnit: (id: number, unitData: Partial<OrganizationalUnit>) => Promise<void>;
    deleteUnit: (id: number) => Promise<void>;
    createUser: (userData: Partial<User>) => Promise<void>;
    updateUser: (id: number, userData: Partial<User>) => Promise<void>;
    deleteUser: (id: number) => Promise<void>;
    assignUserToUnit: (userId: number, unitId: number) => Promise<void>;
    setError: (error: string | null) => void;
    setSelectedUnit: (unit: OrganizationalUnit | null) => void;
  };
}

// Context
const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

// Provider Component
export const OrganizationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(organizationReducer, initialState);

  // Actions
  const actions = {
    loadData: async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ERROR', payload: null });

        // Load from API with proper error handling
        const [units, users] = await Promise.all([
          ApiService.getOrganizationalUnits(),
          ApiService.getUsers()
        ]);

        dispatch({ type: 'SET_UNITS', payload: units });
        dispatch({ type: 'SET_USERS', payload: users });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to load data' });
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    createUnit: async (unitData: Partial<OrganizationalUnit>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ERROR', payload: null });

        // 🔐 SECURITY: Client-side validation before API call
        const validationData = {
          name: unitData.name || '',
          type: unitData.type || '',
          description: unitData.description,
          parentId: unitData.parentId,
          managerId: unitData.managerId,
          budget: unitData.budget
        };

        const validation = validateOrganizationalUnit(validationData);
        if (!validation.isValid) {
          const errorMessage = validation.errors.join(', ');
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          throw new Error(errorMessage);
        }

        // 🔐 SECURITY: Sanitize input data
        const sanitizedData = {
          ...unitData,
          name: sanitizeInput(unitData.name || ''),
          description: unitData.description ? sanitizeInput(unitData.description) : undefined
        };

        // 🔐 SECURITY: API-only data flow - no mock fallbacks
        const newUnit = await ApiService.createOrganizationalUnit(sanitizedData);
        dispatch({ type: 'ADD_UNIT', payload: newUnit });
      } catch (error: any) {
        const errorMessage = error.message || 'Failed to create organizational unit';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    updateUnit: async (id: number, unitData: Partial<OrganizationalUnit>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Update unit via API
        const updatedUnit = await ApiService.updateOrganizationalUnit(id, unitData);
        dispatch({ type: 'UPDATE_UNIT', payload: updatedUnit });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to update unit' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    deleteUnit: async (id: number) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ERROR', payload: null });

        // 🔐 SECURITY: API-only data flow - no mock fallbacks
        await ApiService.deleteOrganizationalUnit(id);
        dispatch({ type: 'DELETE_UNIT', payload: id });
      } catch (error: any) {
        const errorMessage = error.message || 'Failed to delete organizational unit';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    createUser: async (userData: Partial<User>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ERROR', payload: null });

        // 🔐 SECURITY: Client-side validation before API call
        const validationData = {
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          email: userData.email || '',
          role: userData.role || '',
          title: userData.title,
          phone: userData.phone,
          organizationalUnitId: userData.organizationalUnitId,
          managerId: userData.managerId
        };

        const validation = validateUser(validationData);
        if (!validation.isValid) {
          const errorMessage = validation.errors.join(', ');
          dispatch({ type: 'SET_ERROR', payload: errorMessage });
          throw new Error(errorMessage);
        }

        // 🔐 SECURITY: Sanitize input data
        const sanitizedData = {
          ...userData,
          firstName: sanitizeInput(userData.firstName || ''),
          lastName: sanitizeInput(userData.lastName || ''),
          email: sanitizeInput(userData.email || ''),
          title: userData.title ? sanitizeInput(userData.title) : undefined,
          phone: userData.phone ? sanitizeInput(userData.phone) : undefined
        };

        // 🔐 SECURITY: API-only data flow - no mock fallbacks
        const newUser = await ApiService.createUser(sanitizedData);
        dispatch({ type: 'ADD_USER', payload: newUser });
      } catch (error: any) {
        const errorMessage = error.message || 'Failed to create user';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    updateUser: async (id: number, userData: Partial<User>) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Update user via API
        const updatedUser = await ApiService.updateUser(id, userData);
        dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to update user' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    deleteUser: async (id: number) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ERROR', payload: null });

        // 🔐 SECURITY: API-only data flow - no mock fallbacks
        await ApiService.deleteUser(id);
        dispatch({ type: 'DELETE_USER', payload: id });
      } catch (error: any) {
        const errorMessage = error.message || 'Failed to delete user';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    assignUserToUnit: async (userId: number, unitId: number) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });

        // Assign user to unit via API
        const updatedUser = await ApiService.updateUser(userId, { organizationalUnitId: unitId });
        dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      } catch (error: any) {
        dispatch({ type: 'SET_ERROR', payload: error.message || 'Failed to assign user to unit' });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    },

    setError: (error: string | null) => {
      dispatch({ type: 'SET_ERROR', payload: error });
    },

    setSelectedUnit: (unit: OrganizationalUnit | null) => {
      dispatch({ type: 'SET_SELECTED_UNIT', payload: unit });
    },
  };

  // 🔐 SECURITY: Auto-load data from API on component mount
  useEffect(() => {
    actions.loadData();
  }, []);

  return (
    <OrganizationContext.Provider value={{ state, actions }}>
      {children}
    </OrganizationContext.Provider>
  );
};

// Hook to use the context
export const useOrganization = () => {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
};

export default OrganizationContext;
