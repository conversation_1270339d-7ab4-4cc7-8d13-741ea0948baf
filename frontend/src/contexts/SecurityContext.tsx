import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { ClientRateLimiter } from '../utils/validation';

// 🔐 SECURITY: Client-side security context for NIS2/GDPR/SOC2 compliance

interface SecurityState {
  sessionTimeout: number;
  lastActivity: number;
  isSecureConnection: boolean;
  cspViolations: number;
  rateLimiter: ClientRateLimiter;
  securityHeaders: Record<string, string>;
  encryptionStatus: 'enabled' | 'disabled' | 'unknown';
  privacyMode: boolean;
  auditTrail: SecurityEvent[];
}

interface SecurityEvent {
  id: string;
  timestamp: number;
  type: 'session' | 'navigation' | 'data_access' | 'security_violation';
  action: string;
  details?: any;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

type SecurityAction =
  | { type: 'UPDATE_ACTIVITY' }
  | { type: 'SESSION_TIMEOUT' }
  | { type: 'CSP_VIOLATION'; payload: any }
  | { type: 'ADD_SECURITY_EVENT'; payload: SecurityEvent }
  | { type: 'TOGGLE_PRIVACY_MODE' }
  | { type: 'UPDATE_SECURITY_HEADERS'; payload: Record<string, string> }
  | { type: 'SET_ENCRYPTION_STATUS'; payload: 'enabled' | 'disabled' | 'unknown' };

interface SecurityContextType {
  state: SecurityState;
  updateActivity: () => void;
  logSecurityEvent: (event: Omit<SecurityEvent, 'id' | 'timestamp'>) => void;
  checkRateLimit: (key: string) => boolean;
  togglePrivacyMode: () => void;
  isSessionValid: () => boolean;
  getSecurityMetrics: () => any;
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined);

const initialState: SecurityState = {
  sessionTimeout: 30 * 60 * 1000, // 30 minutes
  lastActivity: Date.now(),
  isSecureConnection: window.location.protocol === 'https:',
  cspViolations: 0,
  rateLimiter: new ClientRateLimiter(60000, 10), // 10 requests per minute
  securityHeaders: {},
  encryptionStatus: 'unknown',
  privacyMode: false,
  auditTrail: []
};

function securityReducer(state: SecurityState, action: SecurityAction): SecurityState {
  switch (action.type) {
    case 'UPDATE_ACTIVITY':
      return {
        ...state,
        lastActivity: Date.now()
      };

    case 'SESSION_TIMEOUT':
      return {
        ...state,
        lastActivity: 0
      };

    case 'CSP_VIOLATION':
      return {
        ...state,
        cspViolations: state.cspViolations + 1
      };

    case 'ADD_SECURITY_EVENT':
      const newEvent: SecurityEvent = {
        ...action.payload,
        id: crypto.randomUUID(),
        timestamp: Date.now()
      };
      
      return {
        ...state,
        auditTrail: [newEvent, ...state.auditTrail].slice(0, 100) // Keep last 100 events
      };

    case 'TOGGLE_PRIVACY_MODE':
      return {
        ...state,
        privacyMode: !state.privacyMode
      };

    case 'UPDATE_SECURITY_HEADERS':
      return {
        ...state,
        securityHeaders: action.payload
      };

    case 'SET_ENCRYPTION_STATUS':
      return {
        ...state,
        encryptionStatus: action.payload
      };

    default:
      return state;
  }
}

export const SecurityProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(securityReducer, initialState);

  // 🔐 SECURITY: Monitor user activity for session management
  useEffect(() => {
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const updateActivity = () => {
      dispatch({ type: 'UPDATE_ACTIVITY' });
    };

    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    return () => {
      activityEvents.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, []);

  // 🔐 SECURITY: Session timeout monitoring
  useEffect(() => {
    const checkSession = () => {
      const now = Date.now();
      if (now - state.lastActivity > state.sessionTimeout) {
        dispatch({ type: 'SESSION_TIMEOUT' });
        logSecurityEvent({
          type: 'session',
          action: 'session_timeout',
          riskLevel: 'medium',
          details: { timeoutDuration: state.sessionTimeout }
        });
      }
    };

    const interval = setInterval(checkSession, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [state.lastActivity, state.sessionTimeout]);

  // 🔐 SECURITY: CSP violation monitoring
  useEffect(() => {
    const handleCSPViolation = (event: SecurityPolicyViolationEvent) => {
      dispatch({ type: 'CSP_VIOLATION', payload: event });
      logSecurityEvent({
        type: 'security_violation',
        action: 'csp_violation',
        riskLevel: 'high',
        details: {
          violatedDirective: event.violatedDirective,
          blockedURI: event.blockedURI,
          documentURI: event.documentURI
        }
      });
    };

    document.addEventListener('securitypolicyviolation', handleCSPViolation);
    return () => document.removeEventListener('securitypolicyviolation', handleCSPViolation);
  }, []);

  // 🔐 SECURITY: Monitor navigation for audit trail
  useEffect(() => {
    const handleNavigation = () => {
      logSecurityEvent({
        type: 'navigation',
        action: 'page_navigation',
        riskLevel: 'low',
        details: {
          url: window.location.pathname,
          referrer: document.referrer
        }
      });
    };

    window.addEventListener('popstate', handleNavigation);
    return () => window.removeEventListener('popstate', handleNavigation);
  }, []);

  const updateActivity = () => {
    dispatch({ type: 'UPDATE_ACTIVITY' });
  };

  const logSecurityEvent = (event: Omit<SecurityEvent, 'id' | 'timestamp'>) => {
    dispatch({ type: 'ADD_SECURITY_EVENT', payload: event as SecurityEvent });
    
    // 🔐 SECURITY: Log critical events to console for monitoring
    if (event.riskLevel === 'critical' || event.riskLevel === 'high') {
      console.warn('🔐 [SECURITY-ALERT]', event);
    }
  };

  const checkRateLimit = (key: string): boolean => {
    const canProceed = state.rateLimiter.canMakeRequest(key);
    
    if (!canProceed) {
      logSecurityEvent({
        type: 'security_violation',
        action: 'rate_limit_exceeded',
        riskLevel: 'medium',
        details: { key, remainingRequests: state.rateLimiter.getRemainingRequests(key) }
      });
    }
    
    return canProceed;
  };

  const togglePrivacyMode = () => {
    dispatch({ type: 'TOGGLE_PRIVACY_MODE' });
    logSecurityEvent({
      type: 'session',
      action: state.privacyMode ? 'privacy_mode_disabled' : 'privacy_mode_enabled',
      riskLevel: 'low'
    });
  };

  const isSessionValid = (): boolean => {
    return Date.now() - state.lastActivity < state.sessionTimeout;
  };

  const getSecurityMetrics = () => {
    return {
      sessionStatus: isSessionValid() ? 'active' : 'expired',
      lastActivity: new Date(state.lastActivity).toISOString(),
      secureConnection: state.isSecureConnection,
      cspViolations: state.cspViolations,
      privacyMode: state.privacyMode,
      encryptionStatus: state.encryptionStatus,
      auditEvents: state.auditTrail.length,
      riskEvents: state.auditTrail.filter(e => e.riskLevel === 'high' || e.riskLevel === 'critical').length
    };
  };

  const contextValue: SecurityContextType = {
    state,
    updateActivity,
    logSecurityEvent,
    checkRateLimit,
    togglePrivacyMode,
    isSessionValid,
    getSecurityMetrics
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

export const useSecurity = (): SecurityContextType => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};

// 🔐 SECURITY: Hook for monitoring component access
export const useSecureComponent = (componentName: string) => {
  const { logSecurityEvent } = useSecurity();

  useEffect(() => {
    logSecurityEvent({
      type: 'data_access',
      action: 'component_access',
      riskLevel: 'low',
      details: { componentName }
    });
  }, [componentName, logSecurityEvent]);
};

// 🔐 SECURITY: Hook for secure data operations
export const useSecureOperation = () => {
  const { logSecurityEvent, checkRateLimit } = useSecurity();

  const executeSecureOperation = async (
    operationName: string,
    operation: () => Promise<any>,
    riskLevel: 'low' | 'medium' | 'high' = 'medium'
  ) => {
    // Check rate limiting
    if (!checkRateLimit(`operation:${operationName}`)) {
      throw new Error('Rate limit exceeded for this operation');
    }

    // Log operation start
    logSecurityEvent({
      type: 'data_access',
      action: `${operationName}_started`,
      riskLevel,
      details: { operationName }
    });

    try {
      const result = await operation();
      
      // Log successful operation
      logSecurityEvent({
        type: 'data_access',
        action: `${operationName}_success`,
        riskLevel: 'low',
        details: { operationName }
      });

      return result;
    } catch (error) {
      // Log failed operation
      logSecurityEvent({
        type: 'data_access',
        action: `${operationName}_failed`,
        riskLevel: 'high',
        details: { operationName, error: error instanceof Error ? error.message : String(error) }
      });

      throw error;
    }
  };

  return { executeSecureOperation };
};
