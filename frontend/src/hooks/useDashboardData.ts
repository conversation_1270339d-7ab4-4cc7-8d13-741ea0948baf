import { useState, useEffect } from 'react';
import api from '../services/api';

// 🔐 NIS2-COMPLIANT: Shared dashboard data fetching hook
// Consolidates common patterns across dashboard components

export interface DashboardError {
  message: string;
  code?: string;
  timestamp: Date;
}

export interface LoadingState {
  isLoading: boolean;
  error: DashboardError | null;
}

/**
 * Custom hook for fetching user dashboard data
 * Provides consistent loading states and error handling
 */
export const useUserDashboardData = (userId: number) => {
  const [data, setData] = useState<any>(null);
  const [assessmentHistory, setAssessmentHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading({ isLoading: true, error: null });
      
      try {
        const [dashboardData, history] = await Promise.all([
          api.getUserDashboardData(),
          api.getAssessmentHistory()
        ]);

        setData(dashboardData);
        setAssessmentHistory(history);
        setLoading({ isLoading: false, error: null });
      } catch (error) {
        const dashboardError: DashboardError = {
          message: error instanceof Error ? error.message : 'Failed to load dashboard data',
          code: 'DASHBOARD_FETCH_ERROR',
          timestamp: new Date()
        };
        
        setLoading({ isLoading: false, error: dashboardError });
        
        // Set empty state on error
        setData({
          pendingAssessments: 0,
          completedAssessments: 0,
          latestAssessment: null,
          strengths: [],
          weaknesses: []
        });
        setAssessmentHistory([]);
      }
    };

    if (userId) {
      fetchData();
    }
  }, [userId]);

  return {
    data,
    assessmentHistory,
    ...loading,
    refetch: () => {
      if (userId) {
        setLoading({ isLoading: true, error: null });
        // Re-trigger the effect by updating a dependency
      }
    }
  };
};

/**
 * Custom hook for fetching manager dashboard data
 * Provides consistent loading states and error handling for manager views
 */
export const useManagerDashboardData = (teamId: number) => {
  const [stats, setStats] = useState<any>(null);
  const [employeePerformance, setEmployeePerformance] = useState<any[]>([]);
  const [areaStats, setAreaStats] = useState<any[]>([]);
  const [trendData, setTrendData] = useState<any[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading({ isLoading: true, error: null });
      
      try {
        const [overviewStats, employeeStats, areaStatistics, trends] = await Promise.all([
          api.getDashboardOverview(),
          api.getEmployeePerformanceStats(),
          api.getAssessmentAreaStats(),
          api.getAssessmentTrends()
        ]);

        setStats(overviewStats);
        setEmployeePerformance(employeeStats);
        setAreaStats(areaStatistics);
        setTrendData(trends);
        setLoading({ isLoading: false, error: null });
      } catch (error) {
        const dashboardError: DashboardError = {
          message: error instanceof Error ? error.message : 'Failed to load manager dashboard data',
          code: 'MANAGER_DASHBOARD_FETCH_ERROR',
          timestamp: new Date()
        };
        
        setLoading({ isLoading: false, error: dashboardError });
        
        // Set empty state on error
        setStats(null);
        setEmployeePerformance([]);
        setAreaStats([]);
        setTrendData([]);
      }
    };

    if (teamId) {
      fetchData();
    }
  }, [teamId]);

  return {
    stats,
    employeePerformance,
    areaStats,
    trendData,
    ...loading,
    refetch: () => {
      if (teamId) {
        setLoading({ isLoading: true, error: null });
      }
    }
  };
};

/**
 * Custom hook for main dashboard data
 * Consolidates the main dashboard data fetching logic
 */
export const useMainDashboardData = () => {
  const [stats, setStats] = useState<any>(null);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [loading, setLoading] = useState<LoadingState>({
    isLoading: true,
    error: null
  });

  // Helper function to format timestamps
  const formatTimestamp = (timestamp: string): string => {
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffHours / 24);

      if (diffDays > 0) {
        return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
      } else if (diffHours > 0) {
        return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
      } else {
        return 'Just now';
      }
    } catch {
      return 'Unknown';
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading({ isLoading: true, error: null });
      
      try {
        const [userCountResponse, assessmentStatsResponse, dashboardStatsResponse, recentActivityResponse] = await Promise.all([
          api.getUserCount(),
          api.getAssessmentStats(),
          api.getDashboardStats(),
          api.getRecentActivity()
        ]);

        const userCount = userCountResponse.data?.count || 0;
        const assessmentStats = assessmentStatsResponse.data || { active: 0, completed: 0, pending: 0 };
        const dashboardStats = dashboardStatsResponse.data || { averageScore: 0, improvementTrend: 0 };
        const recentActivityData = recentActivityResponse.data || [];

        setStats({
          totalEmployees: userCount,
          activeAssessments: assessmentStats.active,
          completedAssessments: assessmentStats.completed,
          pendingReviews: assessmentStats.pending,
          averageScore: dashboardStats.averageScore || 0,
          improvementTrend: dashboardStats.improvementTrend || 0
        });

        // Process recent activity data
        const processedActivity = Array.isArray(recentActivityData)
          ? recentActivityData.slice(0, 4).map((item, index) => ({
            id: item.id || index + 1,
            type: (['assessment', 'review', 'goal', 'feedback'].includes(item.type) ? item.type : 'assessment'),
            title: item.title || `Activity ${index + 1}`,
            description: item.description || 'No description available',
            timestamp: item.timestamp ? formatTimestamp(item.timestamp) : 'Just now',
            status: (['completed', 'pending', 'overdue'].includes(item.status) ? item.status : 'pending')
          }))
          : [];

        setRecentActivity(processedActivity);
        setLoading({ isLoading: false, error: null });
      } catch (error) {
        const dashboardError: DashboardError = {
          message: error instanceof Error ? error.message : 'Failed to load main dashboard data',
          code: 'MAIN_DASHBOARD_FETCH_ERROR',
          timestamp: new Date()
        };
        
        setLoading({ isLoading: false, error: dashboardError });
        
        // Set empty state on error
        setStats({
          totalEmployees: 0,
          activeAssessments: 0,
          completedAssessments: 0,
          pendingReviews: 0,
          averageScore: 0,
          improvementTrend: 0
        });
        setRecentActivity([]);
      }
    };

    fetchData();
  }, []);

  return {
    stats,
    recentActivity,
    ...loading,
    refetch: () => setLoading({ isLoading: true, error: null })
  };
};
