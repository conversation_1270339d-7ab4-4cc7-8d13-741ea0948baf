import { useState, useEffect, useCallback } from 'react';
import enterpriseConfigService, {
  OrganizationConfig,
  UserPreferences,
  RoleBasedView
} from '../services/enterpriseConfig';

// 🔐 NIS2-COMPLIANT: Enterprise configuration hook
// Provides organization-specific dashboard customization and role-based views

export interface EnterpriseConfigState {
  organizationConfig: OrganizationConfig | null;
  userPreferences: UserPreferences | null;
  roleConfig: RoleBasedView | null;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook for managing enterprise configuration
 */
export const useEnterpriseConfig = (userId?: string, userRole?: string) => {
  const [state, setState] = useState<EnterpriseConfigState>({
    organizationConfig: null,
    userPreferences: null,
    roleConfig: null,
    isLoading: true,
    error: null
  });

  const loadConfiguration = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const [orgConfig, userPrefs, roleConfig] = await Promise.all([
        enterpriseConfigService.getOrganizationConfig(),
        userId ? enterpriseConfigService.getUserPreferences(userId) : Promise.resolve(null),
        userRole ? enterpriseConfigService.getRoleBasedConfig(userRole) : Promise.resolve(null)
      ]);

      setState({
        organizationConfig: orgConfig,
        userPreferences: userPrefs,
        roleConfig: roleConfig,
        isLoading: false,
        error: null
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load configuration'
      }));
    }
  }, [userId, userRole]);

  useEffect(() => {
    loadConfiguration();
  }, [loadConfiguration]);

  const updateOrganizationConfig = useCallback(async (config: Partial<OrganizationConfig>) => {
    try {
      const updatedConfig = await enterpriseConfigService.updateOrganizationConfig(config);
      setState(prev => ({
        ...prev,
        organizationConfig: updatedConfig
      }));
      return updatedConfig;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to update organization config'
      }));
      throw error;
    }
  }, []);

  const updateUserPreferences = useCallback(async (preferences: Partial<UserPreferences>) => {
    if (!userId) {
      throw new Error('User ID is required to update preferences');
    }

    try {
      const updatedPrefs = await enterpriseConfigService.updateUserPreferences(userId, preferences);
      setState(prev => ({
        ...prev,
        userPreferences: updatedPrefs
      }));
      return updatedPrefs;
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to update user preferences'
      }));
      throw error;
    }
  }, [userId]);

  const hasPermission = useCallback(async (permission: string): Promise<boolean> => {
    if (!userId || !userRole) {
      return false;
    }

    try {
      return await enterpriseConfigService.hasPermission(userId, userRole, permission);
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }, [userId, userRole]);

  const getAvailableWidgets = useCallback(async (): Promise<string[]> => {
    if (!userRole) {
      return [];
    }

    try {
      return await enterpriseConfigService.getAvailableWidgets(userRole);
    } catch (error) {
      console.error('Error getting available widgets:', error);
      return [];
    }
  }, [userRole]);

  const refreshConfiguration = useCallback(() => {
    enterpriseConfigService.clearCache();
    loadConfiguration();
  }, [loadConfiguration]);

  return {
    ...state,
    updateOrganizationConfig,
    updateUserPreferences,
    hasPermission,
    getAvailableWidgets,
    refreshConfiguration
  };
};

/**
 * Hook for theme configuration based on organization and user preferences
 */
export const useThemeConfig = (userId?: string) => {
  const { organizationConfig, userPreferences, isLoading } = useEnterpriseConfig(userId);

  const getTheme = useCallback(() => {
    if (isLoading || !organizationConfig) {
      return {
        mode: 'light',
        primaryColor: '#1976d2',
        secondaryColor: '#dc004e'
      };
    }

    const userTheme = userPreferences?.theme || organizationConfig.theme;
    const resolvedTheme = userTheme === 'auto'
      ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
      : userTheme;

    return {
      mode: resolvedTheme,
      primaryColor: organizationConfig.primaryColor,
      secondaryColor: organizationConfig.secondaryColor,
      logo: organizationConfig.logo,
      organizationName: organizationConfig.name
    };
  }, [organizationConfig, userPreferences, isLoading]);

  return {
    theme: getTheme(),
    isLoading
  };
};

/**
 * Hook for dashboard layout configuration
 */
export const useDashboardLayout = (userId?: string, userRole?: string) => {
  const { organizationConfig, userPreferences, roleConfig, isLoading } = useEnterpriseConfig(userId, userRole);

  const getLayoutConfig = useCallback(() => {
    if (isLoading || !organizationConfig || !roleConfig) {
      return {
        defaultView: 'overview',
        enabledWidgets: [],
        refreshInterval: 300,
        customLayout: null
      };
    }

    return {
      defaultView: organizationConfig.dashboardConfig.defaultView,
      enabledWidgets: organizationConfig.dashboardConfig.enabledWidgets.filter(widget =>
        roleConfig.enabledSections.includes(widget)
      ),
      refreshInterval: organizationConfig.dashboardConfig.refreshInterval,
      customLayout: roleConfig.customLayout || null,
      permissions: roleConfig.permissions
    };
  }, [organizationConfig, roleConfig, isLoading]);

  return {
    layout: getLayoutConfig(),
    isLoading
  };
};

/**
 * Hook for compliance settings
 */
export const useComplianceConfig = () => {
  const { organizationConfig, isLoading } = useEnterpriseConfig();

  const getComplianceSettings = useCallback(() => {
    if (isLoading || !organizationConfig) {
      return {
        dataRetentionPeriod: 365,
        auditLogRetention: 2555,
        encryptionRequired: true,
        accessLoggingEnabled: true,
        gdprCompliant: true,
        nis2Compliant: true
      };
    }

    return organizationConfig.complianceSettings;
  }, [organizationConfig, isLoading]);

  return {
    compliance: getComplianceSettings(),
    isLoading
  };
};

/**
 * Hook for feature flags based on organization configuration
 */
export const useFeatureFlags = () => {
  const { organizationConfig, isLoading } = useEnterpriseConfig();

  const getFeatureFlags = useCallback(() => {
    if (isLoading || !organizationConfig) {
      return {
        advancedAnalytics: false,
        customReports: false,
        apiAccess: false,
        ssoIntegration: false,
        auditLogging: false,
        dataExport: false
      };
    }

    return organizationConfig.features;
  }, [organizationConfig, isLoading]);

  const isFeatureEnabled = useCallback((feature: string): boolean => {
    const features = getFeatureFlags();
    return features[feature as keyof typeof features] || false;
  }, [getFeatureFlags]);

  return {
    features: getFeatureFlags(),
    isFeatureEnabled,
    isLoading
  };
};
