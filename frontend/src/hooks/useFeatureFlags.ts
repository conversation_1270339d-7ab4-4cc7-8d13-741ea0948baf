import { useState, useEffect, useCallback } from 'react';
import { configManager, OrganizationConfig } from '../utils/config';

/**
 * Custom hook for managing feature flags and organizational configuration
 * Provides real-time access to feature flags and configuration updates
 */
export const useFeatureFlags = () => {
  const [config, setConfig] = useState<OrganizationConfig>(configManager.getConfig());
  const [isLoading, setIsLoading] = useState(false);

  // Update local state when configuration changes
  useEffect(() => {
    const handleConfigChange = () => {
      setConfig(configManager.getConfig());
    };

    // Listen for storage changes (when config is updated in another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'ehrx_org_config' && e.newValue) {
        try {
          const newConfig = JSON.parse(e.newValue);
          setConfig(newConfig);
        } catch (error) {
          console.error('🔧 [FEATURE_FLAGS] Failed to parse config from storage:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  /**
   * Check if a specific feature is enabled
   */
  const isFeatureEnabled = useCallback((feature: keyof OrganizationConfig['features']): boolean => {
    return configManager.isFeatureEnabled(feature);
  }, [config]);

  /**
   * Check if a specific widget is enabled
   */
  const isWidgetEnabled = useCallback((widgetId: string): boolean => {
    return configManager.isWidgetEnabled(widgetId);
  }, [config]);

  /**
   * Check if a compliance feature is enabled
   */
  const isComplianceEnabled = useCallback((type: 'audit' | 'gdpr' | 'nis2'): boolean => {
    return configManager.isComplianceEnabled(type);
  }, [config]);

  /**
   * Get the current branding colors
   */
  const getBrandingColors = useCallback(() => {
    return configManager.getBrandingColors();
  }, [config]);

  /**
   * Get the refresh interval for analytics
   */
  const getRefreshInterval = useCallback((): number => {
    return configManager.getRefreshInterval();
  }, [config]);

  /**
   * Get the session timeout value
   */
  const getSessionTimeout = useCallback((): number => {
    return config.security.sessionTimeout;
  }, [config]);

  /**
   * Get the maximum number of users per team
   */
  const getMaxUsersPerTeam = useCallback((): number => {
    return config.features.maxUsersPerTeam;
  }, [config]);

  /**
   * Get the maximum number of teams per organization
   */
  const getMaxTeamsPerOrganization = useCallback((): number => {
    return config.features.maxTeamsPerOrganization;
  }, [config]);

  /**
   * Get the maximum number of assessments per month
   */
  const getMaxAssessmentsPerMonth = useCallback((): number => {
    return config.features.maxAssessmentsPerMonth;
  }, [config]);

  /**
   * Check if the current user can access a feature based on their role and organization limits
   */
  const canAccessFeature = useCallback((
    feature: keyof OrganizationConfig['features'],
    userRole?: string,
    currentUsage?: number
  ): { canAccess: boolean; reason?: string } => {
    // Check if feature is enabled
    if (!isFeatureEnabled(feature)) {
      return { canAccess: false, reason: 'Feature is disabled for this organization' };
    }

    // Check usage limits for numeric features
    if (typeof config.features[feature] === 'number' && currentUsage !== undefined) {
      const limit = config.features[feature] as number;
      if (currentUsage >= limit) {
        return { canAccess: false, reason: `Usage limit reached (${limit})` };
      }
    }

    // Role-based access control (can be extended based on requirements)
    const restrictedFeatures = ['enableAPIAccess', 'enableSSOIntegration', 'enableMultiTenant'];
    if (restrictedFeatures.includes(feature) && userRole !== 'admin' && userRole !== 'hr_admin') {
      return { canAccess: false, reason: 'Insufficient permissions' };
    }

    return { canAccess: true };
  }, [config, isFeatureEnabled]);

  /**
   * Update a specific feature flag
   */
  const updateFeatureFlag = useCallback(async (
    feature: keyof OrganizationConfig['features'],
    enabled: boolean
  ): Promise<void> => {
    setIsLoading(true);
    try {
      const updatedConfig = {
        ...config,
        features: {
          ...config.features,
          [feature]: enabled
        }
      };
      
      configManager.updateConfig(updatedConfig);
      setConfig(updatedConfig);
      
      console.log(`🔧 [FEATURE_FLAGS] Updated ${feature}: ${enabled}`);
    } catch (error) {
      console.error(`🔧 [FEATURE_FLAGS] Failed to update ${feature}:`, error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [config]);

  /**
   * Apply an organizational template
   */
  const applyTemplate = useCallback(async (
    templateName: string,
    customOverrides?: Partial<OrganizationConfig>
  ): Promise<void> => {
    setIsLoading(true);
    try {
      configManager.applyOrganizationTemplate(
        templateName as keyof typeof import('../utils/config').ORGANIZATION_TEMPLATES,
        customOverrides
      );
      setConfig(configManager.getConfig());
      
      console.log(`🔧 [FEATURE_FLAGS] Applied template: ${templateName}`);
    } catch (error) {
      console.error(`🔧 [FEATURE_FLAGS] Failed to apply template ${templateName}:`, error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Validate the current configuration
   */
  const validateConfiguration = useCallback(() => {
    return configManager.validateConfiguration();
  }, [config]);

  /**
   * Get environment-specific settings
   */
  const getEnvironmentInfo = useCallback(() => {
    return {
      environment: configManager.getEnvironment(),
      isDevelopment: configManager.isDevelopment(),
      isProduction: configManager.isProduction()
    };
  }, []);

  /**
   * Check if a feature should be shown based on environment and configuration
   */
  const shouldShowFeature = useCallback((
    feature: keyof OrganizationConfig['features'],
    developmentOnly = false
  ): boolean => {
    // Hide development-only features in production
    if (developmentOnly && configManager.isProduction()) {
      return false;
    }

    return isFeatureEnabled(feature);
  }, [isFeatureEnabled]);

  return {
    // Configuration state
    config,
    isLoading,

    // Feature flag checks
    isFeatureEnabled,
    isWidgetEnabled,
    isComplianceEnabled,
    canAccessFeature,
    shouldShowFeature,

    // Configuration getters
    getBrandingColors,
    getRefreshInterval,
    getSessionTimeout,
    getMaxUsersPerTeam,
    getMaxTeamsPerOrganization,
    getMaxAssessmentsPerMonth,

    // Configuration management
    updateFeatureFlag,
    applyTemplate,
    validateConfiguration,

    // Environment info
    getEnvironmentInfo,
  };
};

export default useFeatureFlags;
