import { useState, useEffect, useCallback } from 'react';
import { 
  ManagerDashboardMetrics, 
  DashboardReminderSettings,
  DashboardSummary,
  UpdateManagerDashboardMetricsDto,
  managerDashboardApi 
} from '../services/managerDashboardApi';
import { OrganizationalUnit, User } from '../types';

// ===================================================================
// TYPES
// ===================================================================

interface UseManagerDashboardState {
  dashboardMetrics: ManagerDashboardMetrics[];
  organizationalUnits: OrganizationalUnit[];
  managers: User[];
  reminderSettings: DashboardReminderSettings | null;
  summary: DashboardSummary | null;
  loading: boolean;
  error: string | null;
  total: number;
}

interface UseManagerDashboardActions {
  updateMetrics: (id: number, data: UpdateManagerDashboardMetricsDto) => Promise<ManagerDashboardMetrics>;
  refreshData: () => Promise<void>;
  loadPage: (page: number) => Promise<void>;
  updateReminderSettings: (data: any) => Promise<void>;
  calculateFte: (orgUnitId: number) => Promise<number>;
}

type UseManagerDashboardReturn = UseManagerDashboardState & UseManagerDashboardActions;

// ===================================================================
// CUSTOM HOOK
// ===================================================================

export const useManagerDashboard = (
  managerId?: number, 
  reportingPeriod?: Date,
  pageSize: number = 50
): UseManagerDashboardReturn => {
  const [state, setState] = useState<UseManagerDashboardState>({
    dashboardMetrics: [],
    organizationalUnits: [],
    managers: [],
    reminderSettings: null,
    summary: null,
    loading: false,
    error: null,
    total: 0,
  });

  const [currentPage, setCurrentPage] = useState(1);

  /**
   * Fetch dashboard data
   */
  const fetchDashboardData = useCallback(async (page: number = 1) => {
    if (!managerId) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const reportingPeriodStr = reportingPeriod 
        ? reportingPeriod.toISOString().split('T')[0] 
        : undefined;

      const [metricsResponse, orgUnitsData, managersData, summaryData] = await Promise.all([
        managerDashboardApi.getDashboardMetrics({
          managerId,
          reportingPeriod: reportingPeriodStr,
          page,
          limit: pageSize,
        }),
        managerDashboardApi.getManagerOrganizationalUnits(managerId),
        managerDashboardApi.getManagers(),
        managerDashboardApi.getDashboardSummary(managerId),
      ]);

      // Fetch reminder settings separately as it might not be critical
      let reminderSettings = null;
      try {
        reminderSettings = await managerDashboardApi.getReminderSettings(managerId);
      } catch (error) {
        console.warn('Failed to fetch reminder settings:', error);
      }

      setState(prev => ({
        ...prev,
        dashboardMetrics: metricsResponse.metrics || [],
        organizationalUnits: orgUnitsData || [],
        managers: managersData || [],
        reminderSettings,
        summary: summaryData,
        total: metricsResponse.total || 0,
        loading: false,
        error: null,
      }));

      setCurrentPage(page);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch dashboard data';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage,
        dashboardMetrics: [],
        organizationalUnits: [],
        managers: [],
        reminderSettings: null,
        summary: null,
        total: 0,
      }));
    }
  }, [managerId, reportingPeriod, pageSize]);

  /**
   * Update metrics
   */
  const updateMetrics = useCallback(
    async (metricsId: number, updatedData: UpdateManagerDashboardMetricsDto): Promise<ManagerDashboardMetrics> => {
      try {
        const updatedMetrics = await managerDashboardApi.updateMetrics(metricsId, updatedData);
        
        // Update local state
        setState(prev => ({
          ...prev,
          dashboardMetrics: prev.dashboardMetrics.map(metric =>
            metric.id === metricsId ? { ...metric, ...updatedMetrics } : metric
          ),
        }));

        return updatedMetrics;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update metrics';
        setState(prev => ({ ...prev, error: errorMessage }));
        throw new Error(errorMessage);
      }
    },
    []
  );

  /**
   * Refresh data
   */
  const refreshData = useCallback(async () => {
    await fetchDashboardData(currentPage);
  }, [fetchDashboardData, currentPage]);

  /**
   * Load specific page
   */
  const loadPage = useCallback(async (page: number) => {
    await fetchDashboardData(page);
  }, [fetchDashboardData]);

  /**
   * Update reminder settings
   */
  const updateReminderSettings = useCallback(
    async (data: any) => {
      if (!managerId) return;

      try {
        const updatedSettings = await managerDashboardApi.updateReminderSettings(managerId, data);
        setState(prev => ({
          ...prev,
          reminderSettings: updatedSettings,
        }));
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update reminder settings';
        setState(prev => ({ ...prev, error: errorMessage }));
        throw new Error(errorMessage);
      }
    },
    [managerId]
  );

  /**
   * Calculate FTE count
   */
  const calculateFte = useCallback(
    async (orgUnitId: number): Promise<number> => {
      try {
        const result = await managerDashboardApi.calculateFteCount(orgUnitId);
        return result.fteCount;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to calculate FTE';
        setState(prev => ({ ...prev, error: errorMessage }));
        throw new Error(errorMessage);
      }
    },
    []
  );

  /**
   * Effect to fetch data when dependencies change
   */
  useEffect(() => {
    if (managerId) {
      fetchDashboardData(1);
    }
  }, [managerId, reportingPeriod, fetchDashboardData]);

  return {
    ...state,
    updateMetrics,
    refreshData,
    loadPage,
    updateReminderSettings,
    calculateFte,
  };
};

// ===================================================================
// SIMPLIFIED HOOK FOR METRICS ONLY
// ===================================================================

export const useManagerDashboardMetrics = (
  managerId?: number,
  reportingPeriod?: Date
) => {
  const [metrics, setMetrics] = useState<ManagerDashboardMetrics[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = useCallback(async () => {
    if (!managerId) return;

    setLoading(true);
    setError(null);

    try {
      const reportingPeriodStr = reportingPeriod 
        ? reportingPeriod.toISOString().split('T')[0] 
        : undefined;

      const response = await managerDashboardApi.getManagerDashboard(managerId, reportingPeriodStr);
      setMetrics(response.metrics || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch metrics';
      setError(errorMessage);
      setMetrics([]);
    } finally {
      setLoading(false);
    }
  }, [managerId, reportingPeriod]);

  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  return {
    metrics,
    loading,
    error,
    refetch: fetchMetrics,
  };
};

// ===================================================================
// HOOK FOR SUMMARY DATA ONLY
// ===================================================================

export const useManagerDashboardSummary = (managerId?: number) => {
  const [summary, setSummary] = useState<DashboardSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSummary = useCallback(async () => {
    if (!managerId) return;

    setLoading(true);
    setError(null);

    try {
      const summaryData = await managerDashboardApi.getDashboardSummary(managerId);
      setSummary(summaryData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch summary';
      setError(errorMessage);
      setSummary(null);
    } finally {
      setLoading(false);
    }
  }, [managerId]);

  useEffect(() => {
    fetchSummary();
  }, [fetchSummary]);

  return {
    summary,
    loading,
    error,
    refetch: fetchSummary,
  };
};
