import { useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';

// Define role hierarchy and permissions
export enum UserRole {
  EMPLOYEE = 'employee',
  MANAGER = 'manager',
  DIRECTOR = 'director',
  VP = 'vp',
  CEO = 'ceo',
  HR_ADMIN = 'hr_admin',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

// Define permission levels
export enum Permission {
  VIEW_ANALYTICS = 'view_analytics',
  VIEW_MANAGER_DASHBOARD = 'view_manager_dashboard',
  EDIT_MANAGER_DASHBOARD = 'edit_manager_dashboard',
  VIEW_ALL_ANALYTICS = 'view_all_analytics',
  MANAGE_ANALYTICS = 'manage_analytics',
  VIEW_SENSITIVE_DATA = 'view_sensitive_data',
  EXPORT_DATA = 'export_data',
  MANAGE_USERS = 'manage_users',
  SYSTEM_ADMIN = 'system_admin'
}

// Role-permission mapping
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.EMPLOYEE]: [
    Permission.VIEW_ANALYTICS
  ],
  [UserRole.MANAGER]: [
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_MANAGER_DASHBOARD,
    Permission.EDIT_MANAGER_DASHBOARD,
    Permission.EXPORT_DATA
  ],
  [UserRole.DIRECTOR]: [
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_MANAGER_DASHBOARD,
    Permission.EDIT_MANAGER_DASHBOARD,
    Permission.VIEW_ALL_ANALYTICS,
    Permission.VIEW_SENSITIVE_DATA,
    Permission.EXPORT_DATA
  ],
  [UserRole.VP]: [
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_MANAGER_DASHBOARD,
    Permission.EDIT_MANAGER_DASHBOARD,
    Permission.VIEW_ALL_ANALYTICS,
    Permission.VIEW_SENSITIVE_DATA,
    Permission.EXPORT_DATA,
    Permission.MANAGE_ANALYTICS
  ],
  [UserRole.CEO]: [
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_MANAGER_DASHBOARD,
    Permission.EDIT_MANAGER_DASHBOARD,
    Permission.VIEW_ALL_ANALYTICS,
    Permission.VIEW_SENSITIVE_DATA,
    Permission.EXPORT_DATA,
    Permission.MANAGE_ANALYTICS
  ],
  [UserRole.HR_ADMIN]: [
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_MANAGER_DASHBOARD,
    Permission.EDIT_MANAGER_DASHBOARD,
    Permission.VIEW_ALL_ANALYTICS,
    Permission.VIEW_SENSITIVE_DATA,
    Permission.EXPORT_DATA,
    Permission.MANAGE_ANALYTICS,
    Permission.MANAGE_USERS
  ],
  [UserRole.ADMIN]: [
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_MANAGER_DASHBOARD,
    Permission.EDIT_MANAGER_DASHBOARD,
    Permission.VIEW_ALL_ANALYTICS,
    Permission.VIEW_SENSITIVE_DATA,
    Permission.EXPORT_DATA,
    Permission.MANAGE_ANALYTICS,
    Permission.MANAGE_USERS,
    Permission.SYSTEM_ADMIN
  ],
  [UserRole.SUPER_ADMIN]: [
    Permission.VIEW_ANALYTICS,
    Permission.VIEW_MANAGER_DASHBOARD,
    Permission.EDIT_MANAGER_DASHBOARD,
    Permission.VIEW_ALL_ANALYTICS,
    Permission.VIEW_SENSITIVE_DATA,
    Permission.EXPORT_DATA,
    Permission.MANAGE_ANALYTICS,
    Permission.MANAGE_USERS,
    Permission.SYSTEM_ADMIN
  ]
};

export interface RoleBasedAccess {
  hasPermission: (permission: Permission) => boolean;
  hasAnyPermission: (permissions: Permission[]) => boolean;
  hasAllPermissions: (permissions: Permission[]) => boolean;
  canAccessAnalytics: boolean;
  canAccessManagerDashboard: boolean;
  canEditManagerDashboard: boolean;
  canViewAllAnalytics: boolean;
  canManageAnalytics: boolean;
  canViewSensitiveData: boolean;
  canExportData: boolean;
  userRole: UserRole | null;
  isAuthenticated: boolean;
}

/**
 * Custom hook for role-based access control
 * Provides granular permission checking for Analytics features
 */
export const useRoleBasedAccess = (): RoleBasedAccess => {
  const { user, isAuthenticated } = useAuth();

  const roleBasedAccess = useMemo(() => {
    if (!isAuthenticated || !user?.role) {
      return {
        hasPermission: () => false,
        hasAnyPermission: () => false,
        hasAllPermissions: () => false,
        canAccessAnalytics: false,
        canAccessManagerDashboard: false,
        canEditManagerDashboard: false,
        canViewAllAnalytics: false,
        canManageAnalytics: false,
        canViewSensitiveData: false,
        canExportData: false,
        userRole: null,
        isAuthenticated: false,
      };
    }

    const userRole = user.role as UserRole;
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];

    const hasPermission = (permission: Permission): boolean => {
      return userPermissions.includes(permission);
    };

    const hasAnyPermission = (permissions: Permission[]): boolean => {
      return permissions.some(permission => hasPermission(permission));
    };

    const hasAllPermissions = (permissions: Permission[]): boolean => {
      return permissions.every(permission => hasPermission(permission));
    };

    return {
      hasPermission,
      hasAnyPermission,
      hasAllPermissions,
      canAccessAnalytics: hasPermission(Permission.VIEW_ANALYTICS),
      canAccessManagerDashboard: hasPermission(Permission.VIEW_MANAGER_DASHBOARD),
      canEditManagerDashboard: hasPermission(Permission.EDIT_MANAGER_DASHBOARD),
      canViewAllAnalytics: hasPermission(Permission.VIEW_ALL_ANALYTICS),
      canManageAnalytics: hasPermission(Permission.MANAGE_ANALYTICS),
      canViewSensitiveData: hasPermission(Permission.VIEW_SENSITIVE_DATA),
      canExportData: hasPermission(Permission.EXPORT_DATA),
      userRole,
      isAuthenticated: true,
    };
  }, [user, isAuthenticated]);

  return roleBasedAccess;
};

/**
 * Higher-order component for role-based access control
 */
export interface WithRoleAccessProps {
  requiredPermissions?: Permission[];
  requiredRole?: UserRole;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const WithRoleAccess: React.FC<WithRoleAccessProps> = ({
  requiredPermissions = [],
  requiredRole,
  fallback = null,
  children,
}) => {
  const { hasAllPermissions, userRole } = useRoleBasedAccess();

  // Check role requirement
  if (requiredRole && userRole !== requiredRole) {
    return <>{fallback}</>;
  }

  // Check permission requirements
  if (requiredPermissions.length > 0 && !hasAllPermissions(requiredPermissions)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * Utility functions for role checking
 */
export const isManagerOrAbove = (role: UserRole | null): boolean => {
  if (!role) return false;
  return [
    UserRole.MANAGER,
    UserRole.DIRECTOR,
    UserRole.VP,
    UserRole.CEO,
    UserRole.HR_ADMIN,
    UserRole.ADMIN,
    UserRole.SUPER_ADMIN
  ].includes(role);
};

export const isDirectorOrAbove = (role: UserRole | null): boolean => {
  if (!role) return false;
  return [
    UserRole.DIRECTOR,
    UserRole.VP,
    UserRole.CEO,
    UserRole.HR_ADMIN,
    UserRole.ADMIN,
    UserRole.SUPER_ADMIN
  ].includes(role);
};

export const isAdminRole = (role: UserRole | null): boolean => {
  if (!role) return false;
  return [
    UserRole.HR_ADMIN,
    UserRole.ADMIN,
    UserRole.SUPER_ADMIN
  ].includes(role);
};

/**
 * Hook for checking specific analytics permissions
 */
export const useAnalyticsPermissions = () => {
  const access = useRoleBasedAccess();
  
  return {
    ...access,
    canViewAnalyticsDashboard: access.canAccessAnalytics,
    canViewManagerDashboard: access.canAccessManagerDashboard,
    canEditOwnMetrics: access.canEditManagerDashboard,
    canViewAllManagerMetrics: access.canViewAllAnalytics,
    canManageAllAnalytics: access.canManageAnalytics,
    canExportAnalyticsData: access.canExportData,
    canViewSensitiveAnalytics: access.canViewSensitiveData,
  };
};
