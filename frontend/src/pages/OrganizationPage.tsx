import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  Paper,
  Breadcrumbs,
  Link,
  Chip,
  Alert,
  Snackbar
} from '@mui/material';
import { useOrganization, OrganizationalUnit } from '../contexts/OrganizationContext';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorAlert from '../components/common/ErrorAlert';
import UnitCard from '../components/organization/UnitCard';
import EditUnitModal from '../components/organization/modals/EditUnitModal';
import MemberSelectorModal from '../components/organization/modals/MemberSelectorModal';
import AddUnitModal from '../components/organization/modals/AddUnitModal';

const OrganizationPage: React.FC = () => {
  const { state, actions } = useOrganization();
  const [selectedUnit, setSelectedUnit] = useState<OrganizationalUnit | null>(null);
  const [editingUnit, setEditingUnit] = useState<OrganizationalUnit | null>(null);
  const [memberSelectorUnit, setMemberSelectorUnit] = useState<OrganizationalUnit | null>(null);
  const [addUnitParent, setAddUnitParent] = useState<OrganizationalUnit | null>(null);
  const [showAddRootUnit, setShowAddRootUnit] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Get root units (no parent)
  const rootUnits = state.units.filter(unit => !unit.parentId);

  // Get current level units to display
  const currentUnits = selectedUnit
    ? state.units.filter(unit => unit.parentId === selectedUnit.id)
    : rootUnits;

  // Get breadcrumb path
  const getBreadcrumbPath = (unit: OrganizationalUnit | null): OrganizationalUnit[] => {
    if (!unit) return [];
    const path: OrganizationalUnit[] = [];
    let current: OrganizationalUnit | null = unit;
    while (current) {
      path.unshift(current);
      current = state.units.find(u => u.id === current?.parentId) || null;
    }
    return path;
  };

  const breadcrumbPath = getBreadcrumbPath(selectedUnit);

  // Get members for a unit
  const getUnitMembers = (unitId: number) => {
    return state.users.filter(user => user.organizationalUnitId === unitId);
  };

  // Calculate total stats
  const totalUnits = state.units.length;
  const totalMembers = state.users.length;
  const totalBudget = state.units.reduce((sum, unit) => sum + (unit.budget || 0), 0);



  const handleEditUnit = (unit: OrganizationalUnit) => {
    setEditingUnit(unit);
  };

  const handleAddMember = (unit: OrganizationalUnit) => {
    setMemberSelectorUnit(unit);
  };

  const handleAddSubunit = (parentId: number) => {
    const parent = state.units.find(u => u.id === parentId);
    setAddUnitParent(parent || null);
  };

  const formatBudget = (budget: number) => {
    if (budget >= 1000000) {
      return `$${(budget / 1000000).toFixed(1)}M`;
    } else if (budget >= 1000) {
      return `$${(budget / 1000).toFixed(0)}K`;
    }
    return `$${budget.toLocaleString()}`;
  };

  if (state.loading) {
    return <LoadingSpinner message="Loading organizational data..." />;
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom color="primary" sx={{ fontWeight: 'bold' }}>
          🏢 Organization Management
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ mb: 3 }}>
          Enterprise Database-Connected Team & Member Management
        </Typography>

        {/* Stats */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                {totalUnits}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Units
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="success.main" sx={{ fontWeight: 'bold' }}>
                {totalMembers}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Members
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Paper sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" sx={{ fontWeight: 'bold' }}>
                {formatBudget(totalBudget)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Budget
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Error Display */}
      {state.error && (
        <Box sx={{ mb: 3 }}>
          <ErrorAlert
            error={state.error}
            onRetry={actions.loadData}
            onDismiss={() => actions.setError(null)}
          />
        </Box>
      )}

      {/* Breadcrumbs */}
      {breadcrumbPath.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Breadcrumbs>
            <Link
              component="button"
              variant="body1"
              onClick={() => setSelectedUnit(null)}
              sx={{ textDecoration: 'none' }}
            >
              🏠 Root
            </Link>
            {breadcrumbPath.map((unit, index) => (
              <Link
                key={unit.id}
                component="button"
                variant="body1"
                onClick={() => setSelectedUnit(index === breadcrumbPath.length - 1 ? unit : unit)}
                sx={{ textDecoration: 'none' }}
              >
                {unit.name}
              </Link>
            ))}
          </Breadcrumbs>
        </Box>
      )}

      {/* Action Buttons */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          color="primary"
          onClick={() => setShowAddRootUnit(true)}
        >
          ➕ Add {selectedUnit ? 'Sub-unit' : 'Root Unit'}
        </Button>
        <Button
          variant="outlined"
          onClick={actions.loadData}
          disabled={state.loading}
        >
          🔄 Refresh Data
        </Button>
        {selectedUnit && (
          <Button
            variant="outlined"
            onClick={() => setSelectedUnit(null)}
          >
            ⬆️ Back to Root
          </Button>
        )}
      </Box>

      {/* Current Level Info */}
      {selectedUnit && (
        <Box sx={{ mb: 3 }}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
              <Box>
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                  {selectedUnit.name}
                </Typography>
                <Chip label={selectedUnit.type} color="primary" sx={{ mb: 2 }} />
                {selectedUnit.description && (
                  <Typography variant="body1" color="text.secondary">
                    {selectedUnit.description}
                  </Typography>
                )}
              </Box>
              <Button
                variant="outlined"
                onClick={() => handleEditUnit(selectedUnit)}
              >
                ✏️ Edit Unit
              </Button>
            </Box>
          </Paper>
        </Box>
      )}

      {/* Units Grid */}
      <Grid container spacing={3}>
        {currentUnits.map((unit) => (
          <Grid item xs={12} sm={6} md={4} key={unit.id}>
            <UnitCard
              unit={unit}
              members={getUnitMembers(unit.id)}
              onEdit={handleEditUnit}
              onAddSubunit={handleAddSubunit}
              onViewMembers={handleAddMember}
            />
          </Grid>
        ))}

        {currentUnits.length === 0 && (
          <Grid item xs={12}>
            <Paper sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                {selectedUnit ? 'No sub-units found' : 'No organizational units found'}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                {selectedUnit
                  ? 'This unit doesn\'t have any sub-units yet.'
                  : 'Get started by creating your first organizational unit.'
                }
              </Typography>
              <Button
                variant="contained"
                onClick={() => setShowAddRootUnit(true)}
              >
                ➕ Add {selectedUnit ? 'Sub-unit' : 'First Unit'}
              </Button>
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Modals */}
      {editingUnit && (
        <EditUnitModal
          unit={editingUnit}
          open={true}
          onClose={() => setEditingUnit(null)}
          onAddMember={handleAddMember}
          onAddSubunit={handleAddSubunit}
        />
      )}

      {memberSelectorUnit && (
        <MemberSelectorModal
          unit={memberSelectorUnit}
          open={true}
          onClose={() => setMemberSelectorUnit(null)}
        />
      )}

      {(showAddRootUnit || addUnitParent) && (
        <AddUnitModal
          parentUnit={addUnitParent || selectedUnit || undefined}
          open={true}
          onClose={() => {
            setShowAddRootUnit(false);
            setAddUnitParent(null);
          }}
        />
      )}

      {/* Success Message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage(null)}
      >
        <Alert onClose={() => setSuccessMessage(null)} severity="success">
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default OrganizationPage;
