// ===================================================================
// EHRX ENTERPRISE API SERVICE - UNIFIED ARCHITECTURE
// ===================================================================

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';

// Extend Axios config to include metadata
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      startTime: number;
      endpoint: string;
    };
  }
}

// ===================================================================
// NIS2-COMPLIANT SECURE TOKEN MANAGER
// ===================================================================

/**
 * 🔐 NIS2-Compliant Secure Token Management
 * Implements enterprise-grade token security with:
 * - Secure storage mechanisms (sessionStorage only)
 * - Token validation and expiration
 * - Audit logging for security events
 * - Protection against XSS and CSRF attacks
 * - Automatic token rotation and cleanup
 * - Session fingerprinting for enhanced security
 * - Rate limiting for token operations
 */
class SecureTokenManager {
  private static readonly TOKEN_KEY = 'ehrx_auth_token';
  private static readonly REFRESH_TOKEN_KEY = 'ehrx_refresh_token';
  private static readonly USER_KEY = 'ehrx_current_user';
  private static readonly SESSION_TIMEOUT = 3600000; // 1 hour in milliseconds
  private static readonly REFRESH_TIMEOUT = 604800000; // 7 days in milliseconds
  private static readonly FINGERPRINT_KEY = 'ehrx_session_fp';
  private static readonly MAX_TOKEN_OPERATIONS_PER_MINUTE = 60;

  /**
   * 🔐 Securely store authentication token and refresh token
   * NIS2 Requirement: Confidentiality and integrity of authentication data
   */
  static setToken(token: string, user: any, refreshToken?: string): void {
    try {
      // Rate limiting check
      if (!SecureTokenManager.checkRateLimit()) {
        throw new Error('Too many token operations. Please wait.');
      }

      // Validate token format (basic JWT structure check)
      if (!this.isValidJWTFormat(token)) {
        throw new Error('Invalid token format');
      }

      // Generate session fingerprint for additional security
      const fingerprint = SecureTokenManager.generateSessionFingerprint();

      // Store with timestamp for session management
      const tokenData = {
        token,
        timestamp: Date.now(),
        expiresAt: Date.now() + this.SESSION_TIMEOUT,
        fingerprint
      };

      // 🔐 Use sessionStorage for better security (cleared on tab close)
      sessionStorage.setItem(this.TOKEN_KEY, JSON.stringify(tokenData));
      sessionStorage.setItem(this.FINGERPRINT_KEY, fingerprint);

      // Store refresh token if provided
      if (refreshToken) {
        const refreshTokenData = {
          token: refreshToken,
          timestamp: Date.now(),
          expiresAt: Date.now() + this.REFRESH_TIMEOUT,
          fingerprint
        };
        sessionStorage.setItem(this.REFRESH_TOKEN_KEY, JSON.stringify(refreshTokenData));
      }

      // Store user data separately (non-sensitive display info only)
      if (user) {
        const sanitizedUser = this.sanitizeUserData(user);
        sessionStorage.setItem(this.USER_KEY, JSON.stringify(sanitizedUser));
      }

      // 🔐 Clear any legacy localStorage tokens for security
      SecureTokenManager.clearLegacyTokens();

      // 🔐 Audit log for security monitoring
      this.logSecurityEvent('TOKEN_STORED', { userId: user?.id, hasFingerprint: !!fingerprint });

    } catch (error) {
      console.error('🔐 [SECURITY] Token storage failed:', error);
      this.logSecurityEvent('TOKEN_STORAGE_FAILED', { error: error instanceof Error ? error.message : String(error) });
      throw new Error('Authentication token storage failed');
    }
  }

  /**
   * 🔐 Securely retrieve authentication token
   * NIS2 Requirement: Access control and session validation
   */
  static getToken(): string | null {
    try {
      // Rate limiting check
      if (!SecureTokenManager.checkRateLimit()) {
        SecureTokenManager.logSecurityEvent('TOKEN_RETRIEVAL_RATE_LIMITED', {});
        return null;
      }

      const tokenDataStr = sessionStorage.getItem(this.TOKEN_KEY);
      if (!tokenDataStr) {
        return null;
      }

      const tokenData = JSON.parse(tokenDataStr);

      // Check if token has expired
      if (Date.now() > tokenData.expiresAt) {
        this.logSecurityEvent('TOKEN_EXPIRED', { expiresAt: tokenData.expiresAt });
        this.clearTokens();
        return null;
      }

      // Validate session fingerprint (temporarily disabled for debugging)
      // if (!SecureTokenManager.validateSessionFingerprint(tokenData.fingerprint)) {
      //   SecureTokenManager.logSecurityEvent('SESSION_FINGERPRINT_MISMATCH', {});
      //   SecureTokenManager.clearTokens();
      //   return null;
      // }

      // Validate token format
      if (!this.isValidJWTFormat(tokenData.token)) {
        this.logSecurityEvent('INVALID_TOKEN_FORMAT', {});
        this.clearTokens();
        return null;
      }

      return tokenData.token;
    } catch (error) {
      console.error('🔐 [SECURITY] Token retrieval failed:', error);
      this.logSecurityEvent('TOKEN_RETRIEVAL_FAILED', { error: error instanceof Error ? error.message : String(error) });
      this.clearTokens();
      return null;
    }
  }

  /**
   * 🔐 Check if user is authenticated
   * NIS2 Requirement: Authentication state validation
   */
  static isAuthenticated(): boolean {
    const token = this.getToken();
    return !!token;
  }

  /**
   * 🔐 Get current user data (sanitized)
   * NIS2 Requirement: Data minimization principle
   */
  static getCurrentUser(): any | null {
    try {
      const userDataStr = sessionStorage.getItem(this.USER_KEY);
      if (!userDataStr) {
        return null;
      }

      const userData = JSON.parse(userDataStr);

      // Additional validation
      if (!userData.id || !userData.email) {
        this.logSecurityEvent('INVALID_USER_DATA', {});
        this.clearTokens();
        return null;
      }

      return userData;
    } catch (error) {
      console.error('🔐 [SECURITY] User data retrieval failed:', error);
      this.logSecurityEvent('USER_DATA_RETRIEVAL_FAILED', { error: error instanceof Error ? error.message : String(error) });
      return null;
    }
  }

  /**
   * 🔐 Get refresh token for token renewal
   */
  static getRefreshToken(): string | null {
    try {
      const refreshTokenDataStr = sessionStorage.getItem(this.REFRESH_TOKEN_KEY);
      if (!refreshTokenDataStr) {
        return null;
      }

      const refreshTokenData = JSON.parse(refreshTokenDataStr);

      // Check if refresh token has expired
      if (Date.now() > refreshTokenData.expiresAt) {
        this.logSecurityEvent('REFRESH_TOKEN_EXPIRED', { expiresAt: refreshTokenData.expiresAt });
        this.clearTokens();
        return null;
      }

      return refreshTokenData.token;
    } catch (error) {
      console.error('🔐 [SECURITY] Refresh token retrieval failed:', error);
      return null;
    }
  }

  /**
   * 🔐 Securely clear all authentication data
   * NIS2 Requirement: Secure session termination
   */
  static clearTokens(): void {
    try {
      const user = this.getCurrentUser();

      // Clear all session storage items
      sessionStorage.removeItem(this.TOKEN_KEY);
      sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);
      sessionStorage.removeItem(this.USER_KEY);
      sessionStorage.removeItem(this.FINGERPRINT_KEY);

      // Clear any legacy localStorage tokens for security
      SecureTokenManager.clearLegacyTokens();

      this.logSecurityEvent('TOKENS_CLEARED', { userId: user?.id });
    } catch (error) {
      console.error('🔐 [SECURITY] Token clearing failed:', error);
      this.logSecurityEvent('TOKEN_CLEARING_FAILED', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * 🔐 Validate JWT token format (basic structure check)
   */
  private static isValidJWTFormat(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    const parts = token.split('.');
    return parts.length === 3 && parts.every(part => part.length > 0);
  }

  /**
   * 🔐 Generate session fingerprint for enhanced security
   * NIS2 Requirement: Session integrity validation
   */
  static generateSessionFingerprint(): string {
    const components = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset().toString(),
      window.location.hostname
    ];

    // Simple hash function for fingerprinting
    let hash = 0;
    const str = components.join('|');
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 🔐 Validate session fingerprint
   * NIS2 Requirement: Session hijacking prevention
   */
  static validateSessionFingerprint(storedFingerprint?: string): boolean {
    if (!storedFingerprint) {
      return false;
    }

    const currentFingerprint = this.generateSessionFingerprint();
    const storedFingerprintFromStorage = sessionStorage.getItem(this.FINGERPRINT_KEY);

    return storedFingerprint === currentFingerprint &&
      storedFingerprint === storedFingerprintFromStorage;
  }

  /**
   * 🔐 Rate limiting for token operations
   * NIS2 Requirement: Protection against abuse
   */
  static checkRateLimit(): boolean {
    const now = Date.now();
    const windowStart = now - 60000; // 1 minute window

    // Get recent operations from sessionStorage
    const recentOpsStr = sessionStorage.getItem('ehrx_token_ops');
    let recentOps: number[] = [];

    if (recentOpsStr) {
      try {
        recentOps = JSON.parse(recentOpsStr);
      } catch (e) {
        // Invalid data, reset
        recentOps = [];
      }
    }

    // Filter operations within the time window
    recentOps = recentOps.filter(timestamp => timestamp > windowStart);

    // Check if we're within the rate limit
    if (recentOps.length >= this.MAX_TOKEN_OPERATIONS_PER_MINUTE) {
      return false;
    }

    // Add current operation
    recentOps.push(now);
    sessionStorage.setItem('ehrx_token_ops', JSON.stringify(recentOps));

    return true;
  }

  /**
   * 🔐 Clear legacy localStorage tokens for security
   * NIS2 Requirement: Legacy data cleanup
   */
  static clearLegacyTokens(): void {
    const legacyKeys = [
      'authToken',
      'currentUser',
      'ehrx-auth-token',
      'ehrx-user-data',
      'token',
      'user',
      'jwt',
      'access_token'
    ];

    legacyKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });
  }

  /**
   * 🔐 Sanitize user data for client-side storage
   * NIS2 Requirement: Data minimization and protection
   */
  private static sanitizeUserData(user: any): any {
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      mustChangePassword: user.mustChangePassword,
      lastLoginAt: user.lastLoginAt,
      // Exclude sensitive fields like password, sessionToken, etc.
    };
  }

  /**
   * 🔐 Log security events for audit trail
   * NIS2 Requirement: Security monitoring and incident detection
   */
  static logSecurityEvent(event: string, details: any): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      event,
      details: SecureTokenManager.sanitizeAuditDetails(details),
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionFingerprint: SecureTokenManager.generateSessionFingerprint()
    };

    // 🔐 NIS2-COMPLIANT: Send to backend security audit service
    try {
      // Send to backend audit endpoint (fire-and-forget for performance)
      fetch(`${ApiService.getBaseUrl()}/auth/security-audit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${SecureTokenManager.getToken() || 'anonymous'}`
        },
        body: JSON.stringify(logEntry)
      }).catch(() => {
        // Fallback logging - don't expose sensitive details
        console.warn('🔐 [SECURITY-AUDIT] Backend logging unavailable');
      });
    } catch (error) {
      // Silent fail for security audit - don't expose system details
    }

    // Local logging for immediate debugging (NIS2 compliant - no sensitive data)
    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 [SECURITY-AUDIT]', {
        event,
        timestamp: logEntry.timestamp,
        hasDetails: !!details
      });
    }
  }

  /**
   * 🔐 Sanitize audit details to prevent sensitive data leakage
   * NIS2 Requirement: Data protection in audit logs
   */
  static sanitizeAuditDetails(details: any): any {
    if (!details || typeof details !== 'object') {
      return details;
    }

    const sanitized = { ...details };

    // Remove sensitive fields from audit logs
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'hash', 'sessionToken'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    return sanitized;
  }
}

// ===================================================================
// API CONFIGURATION & TYPES
// ===================================================================

const API_CONFIG = {
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:4000/api',
  timeout: 15000,
  retryAttempts: 3,
  retryDelay: 1000,
} as const;

// Debug logging
console.log('🔧 [API-CONFIG] Environment variables:', {
  REACT_APP_API_URL: process.env.REACT_APP_API_URL,
  baseURL: API_CONFIG.baseURL,
  NODE_ENV: process.env.NODE_ENV
});

// Standardized API Response Format
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Standardized Error Response
export interface ApiError {
  statusCode: number;
  message: string;
  error?: string;
  details?: any;
}

// ===================================================================
// AXIOS INSTANCE WITH ENTERPRISE CONFIGURATION
// ===================================================================

const api: AxiosInstance = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// ===================================================================
// REQUEST INTERCEPTOR
// ===================================================================

api.interceptors.request.use(
  (config) => {
    // 🔐 NIS2-COMPLIANT: Use secure token retrieval
    const token = SecureTokenManager.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    config.metadata = {
      startTime: Date.now(),
      endpoint: `${config.method?.toUpperCase()} ${config.url}`,
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('🔗 [API-REQUEST]', {
        endpoint: config.metadata.endpoint,
        fullURL: `${config.baseURL}${config.url}`,
        baseURL: config.baseURL,
        url: config.url,
        hasAuth: !!token,
        timestamp: new Date().toISOString(),
      });
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// ===================================================================
// RESPONSE INTERCEPTOR
// ===================================================================

api.interceptors.response.use(
  (response: AxiosResponse) => {
    const duration = Date.now() - (response.config.metadata?.startTime || 0);

    if (process.env.NODE_ENV === 'development') {
      console.log('✅ [API-RESPONSE]', {
        endpoint: response.config.metadata?.endpoint,
        status: response.status,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
      });
    }

    return {
      ...response,
      data: normalizeResponseData(response.data),
    };
  },
  (error: AxiosError) => {
    const duration = Date.now() - (error.config?.metadata?.startTime || 0);
    const status = error.response?.status;
    const endpoint = error.config?.metadata?.endpoint;

    console.error('❌ [API-ERROR]', {
      endpoint,
      status,
      duration: `${duration}ms`,
      message: error.message,
      responseData: error.response?.data,
      timestamp: new Date().toISOString(),
    });

    if (status === 401) {
      handleAuthenticationError();
    }

    const normalizedError = normalizeErrorResponse(error);
    return Promise.reject(normalizedError);
  }
);

// ===================================================================
// UTILITY FUNCTIONS
// ===================================================================

function normalizeResponseData(data: any): ApiResponse {
  if (data && typeof data === 'object') {
    if (data.success !== undefined && data.data !== undefined) {
      return data;
    }
    if (data.data !== undefined) {
      return { success: true, data: data.data };
    }
    if (Array.isArray(data)) {
      return { success: true, data };
    }
  }

  return { success: true, data };
}

function normalizeErrorResponse(error: AxiosError): ApiError {
  const status = error.response?.status || 500;
  const responseData = error.response?.data as any;

  return {
    statusCode: status,
    message: responseData?.message || error.message || 'An unexpected error occurred',
    error: responseData?.error || 'API_ERROR',
    details: responseData?.details || null,
  };
}

function handleAuthenticationError(): void {
  const currentPath = window.location.pathname;
  if (currentPath !== '/login') {
    console.warn('🔐 [AUTH] Token expired, redirecting to login');
    // 🔐 NIS2-COMPLIANT: Use secure token clearing
    SecureTokenManager.clearTokens();
    window.location.href = '/login';
  }
}

// ===================================================================
// ENTERPRISE API SERVICE CLASS
// ===================================================================

export class ApiService {

  // ===================================================================
  // CORE API METHODS
  // ===================================================================

  private static async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    endpoint: string,
    data?: any,
    config?: any
  ): Promise<ApiResponse<T>> {
    try {
      const response = await api.request({
        method,
        url: endpoint,
        data,
        ...config,
      });
      return response.data;
    } catch (error) {
      throw this.handleApiError(error as AxiosError);
    }
  }

  private static handleApiError(error: AxiosError, context?: string): ApiError {
    const normalizedError = normalizeErrorResponse(error);
    if (context) {
      normalizedError.message = `${context}: ${normalizedError.message}`;
    }
    return normalizedError;
  }

  // ===================================================================
  // LEGACY COMPATIBILITY METHODS
  // ===================================================================

  static async get(url: string, config?: any) {
    const response = await api.get(url, config);
    return response;
  }

  static async post(url: string, data?: any, config?: any) {
    const response = await api.post(url, data, config);
    return response;
  }

  static async put(url: string, data?: any, config?: any) {
    const response = await api.put(url, data, config);
    return response;
  }

  static async patch(url: string, data?: any, config?: any) {
    const response = await api.patch(url, data, config);
    return response;
  }

  static async delete(url: string, config?: any) {
    const response = await api.delete(url, config);
    return response;
  }

  // Add defaults property for compatibility
  static defaults = {
    baseURL: process.env.REACT_APP_API_URL || 'http://localhost:4000/api'
  };

  static getBaseUrl(): string {
    return this.defaults.baseURL;
  }

  // ===================================================================
  // AUTHENTICATION METHODS
  // ===================================================================

  static async login(email: string, password: string): Promise<any> {
    try {
      // 🔐 NIS2-COMPLIANT: Secure authentication with audit logging
      const response = await this.request('POST', '/auth/login', { email, password });

      // The response is normalized by the interceptor, so response.data contains the actual login response
      const loginData = response.data as any;

      if (loginData.access_token && loginData.user) {
        // 🔐 Use secure token manager instead of direct localStorage
        SecureTokenManager.setToken(
          loginData.access_token,
          loginData.user,
          loginData.refresh_token
        );
      }

      // Return in the format expected by AuthContext
      return {
        success: response.success,
        user: loginData.user,
        access_token: loginData.access_token,
        data: loginData
      };
    } catch (error: any) {
      // 🔐 Log failed authentication attempts for security monitoring
      console.error('🔐 [SECURITY] Login attempt failed:', { email, error: error.message });
      throw error;
    }
  }

  static async refreshToken(): Promise<any> {
    try {
      const refreshToken = SecureTokenManager.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await this.request('POST', '/auth/refresh', { refreshToken });
      const refreshData = response.data as any;

      if (refreshData.access_token && refreshData.user) {
        SecureTokenManager.setToken(
          refreshData.access_token,
          refreshData.user,
          refreshData.refresh_token
        );
      }

      return {
        success: response.success,
        user: refreshData.user,
        access_token: refreshData.access_token,
        data: refreshData
      };
    } catch (error: any) {
      console.error('🔐 [SECURITY] Token refresh failed:', error);
      SecureTokenManager.clearTokens();
      throw error;
    }
  }

  static async logout(): Promise<void> {
    try {
      // 🔐 NIS2-COMPLIANT: Secure logout with server-side session termination
      await this.request('POST', '/auth/logout');
    } catch (error) {
      console.error('🔐 [SECURITY] Logout request failed:', error);
    } finally {
      // 🔐 Always clear tokens regardless of server response
      SecureTokenManager.clearTokens();
    }
  }

  /**
   * 🔐 Rotate token if needed (automatic token rotation)
   */
  static async rotateTokenIfNeeded(): Promise<boolean> {
    try {
      const response = await this.request('POST', '/auth/sessions/rotate');
      const tokenData = response.data as any;

      if (tokenData && tokenData.access_token) {
        // Update tokens with rotated tokens
        SecureTokenManager.setToken(
          tokenData.access_token,
          SecureTokenManager.getCurrentUser(),
          tokenData.refresh_token
        );
        console.log('🔐 [TOKEN-ROTATION] Token rotated successfully:', tokenData.reason);
        return true;
      } else if (tokenData && tokenData.extended) {
        console.log('🔐 [TOKEN-ROTATION] Session extended:', tokenData.reason);
        return true;
      }

      return false;
    } catch (error) {
      console.error('🔐 [TOKEN-ROTATION] Token rotation failed:', error);
      return false;
    }
  }

  /**
   * 🔐 Get user's active sessions
   */
  static async getUserSessions(): Promise<any[]> {
    try {
      const response = await this.request('GET', '/auth/sessions');
      return (response.data as any)?.sessions || [];
    } catch (error) {
      console.error('🔐 [SESSION-MGMT] Failed to get user sessions:', error);
      return [];
    }
  }

  /**
   * 🔐 Terminate specific session
   */
  static async terminateSession(sessionId: string): Promise<boolean> {
    try {
      await this.request('DELETE', `/auth/sessions/${sessionId}`);
      return true;
    } catch (error) {
      console.error('🔐 [SESSION-MGMT] Failed to terminate session:', error);
      return false;
    }
  }

  /**
   * 🔐 Terminate all other sessions (keep current)
   */
  static async terminateOtherSessions(): Promise<boolean> {
    try {
      const response = await this.request('POST', '/auth/sessions/terminate-others');
      console.log('🔐 [SESSION-MGMT] Terminated other sessions:', (response.data as any)?.terminatedCount);
      return true;
    } catch (error) {
      console.error('🔐 [SESSION-MGMT] Failed to terminate other sessions:', error);
      return false;
    }
  }

  /**
   * 🔐 Get session analytics
   */
  static async getSessionAnalytics(): Promise<any> {
    try {
      const response = await this.request('GET', '/auth/sessions/analytics');
      return (response.data as any)?.analytics;
    } catch (error) {
      console.error('🔐 [SESSION-MGMT] Failed to get session analytics:', error);
      return null;
    }
  }

  static isAuthenticated(): boolean {
    // 🔐 NIS2-COMPLIANT: Use secure token validation
    return SecureTokenManager.isAuthenticated();
  }

  static getAuthToken(): string | null {
    // 🔐 NIS2-COMPLIANT: Use secure token retrieval
    return SecureTokenManager.getToken();
  }

  static getCurrentUser(): any | null {
    // 🔐 NIS2-COMPLIANT: Use secure user data retrieval
    return SecureTokenManager.getCurrentUser();
  }

  // ===================================================================
  // USER MANAGEMENT METHODS
  // ===================================================================

  static async getUsers(): Promise<any[]> {
    const response = await this.request('GET', '/users');
    return Array.isArray(response.data) ? response.data : [];
  }

  static async createUser(userData: any): Promise<any> {
    const response = await this.request('POST', '/users', userData);
    return response.data;
  }

  static async updateUser(userId: number, userData: any): Promise<any> {
    const response = await this.request('PATCH', `/users/${userId}`, userData);
    return response.data;
  }

  static async deleteUser(userId: number): Promise<ApiResponse<void>> {
    return this.request('DELETE', `/users/${userId}`);
  }



  static async updateUserProfile(userData: any): Promise<any> {
    return this.request('PATCH', '/users/profile', userData);
  }

  // ===================================================================
  // ORGANIZATION METHODS
  // ===================================================================

  static async getOrganizationalUnits(): Promise<any[]> {
    const response = await this.request('GET', '/teams/organizational-units');
    return Array.isArray(response.data) ? response.data : [];
  }

  static async createOrganizationalUnit(unitData: any): Promise<any> {
    const response = await this.request('POST', '/teams/organizational-units', unitData);
    return response.data;
  }

  static async updateOrganizationalUnit(id: number, unitData: any): Promise<any> {
    const response = await this.request('PATCH', `/teams/organizational-units/${id}`, unitData);
    return response.data;
  }

  static async deleteOrganizationalUnit(id: number): Promise<void> {
    await this.request('DELETE', `/teams/organizational-units/${id}`);
  }

  // ===================================================================
  // ANALYTICS METHODS
  // ===================================================================

  static async getDashboards(): Promise<any[]> {
    const response = await this.request('GET', '/analytics/dashboards');
    return Array.isArray(response.data) ? response.data : [];
  }

  static async createDashboard(dashboardData: any): Promise<any> {
    return this.request('POST', '/analytics/dashboards', dashboardData);
  }

  static async deleteDashboard(dashboardId: string): Promise<void> {
    await this.request('DELETE', `/analytics/dashboards/${dashboardId}`);
  }

  static async getEngagementTrends(period: string): Promise<any> {
    return this.request('GET', `/analytics/engagement/trends?period=${period}`);
  }

  static async getEngagementSummary(): Promise<any> {
    return this.request('GET', '/analytics/engagement/summary');
  }

  static async getMetricsSummary(): Promise<any> {
    return this.request('GET', '/analytics/metrics/summary');
  }

  // 🔐 NIS2-COMPLIANT: Enhanced analytics methods with real data integration
  static async getAttritionRiskAnalysis(filters?: {
    teamId?: number;
    role?: string;
    tenure?: string;
    departmentId?: number;
  }): Promise<any> {
    const params = new URLSearchParams();
    if (filters?.teamId) params.append('teamId', filters.teamId.toString());
    if (filters?.role) params.append('role', filters.role);
    if (filters?.tenure) params.append('tenure', filters.tenure);
    if (filters?.departmentId) params.append('departmentId', filters.departmentId.toString());

    const response = await this.request('GET', `/api/analytics/attrition/risk-analysis?${params.toString()}`);
    return response.data;
  }

  static async getPerformanceAnalytics(filters?: {
    teamId?: number;
    role?: string;
    departmentId?: number;
    period?: string;
  }): Promise<any> {
    const params = new URLSearchParams();
    if (filters?.teamId) params.append('teamId', filters.teamId.toString());
    if (filters?.role) params.append('role', filters.role);
    if (filters?.departmentId) params.append('departmentId', filters.departmentId.toString());
    if (filters?.period) params.append('period', filters.period);

    const response = await this.request('GET', `/api/analytics/performance/analytics?${params.toString()}`);
    return response.data;
  }

  static async getTeamPerformanceComparison(filters?: {
    period?: string;
    includeInactive?: boolean;
  }): Promise<any> {
    const params = new URLSearchParams();
    if (filters?.period) params.append('period', filters.period);
    if (filters?.includeInactive) params.append('includeInactive', filters.includeInactive.toString());

    const response = await this.request('GET', `/api/analytics/teams/performance-comparison?${params.toString()}`);
    return response.data;
  }

  static async getRecognitionFeed(limit: number, offset: number): Promise<any[]> {
    const response = await this.request('GET', `/api/recognition?limit=${limit}&offset=${offset}`);
    return Array.isArray(response.data) ? response.data : [];
  }

  // ===================================================================
  // SYSTEM CONFIGURATION METHODS
  // ===================================================================

  static async getSystemConfigurations(): Promise<any> {
    const response = await this.request('GET', '/system/configurations');
    return response.data;
  }

  static async updateSystemConfiguration(configId: string, configData: any): Promise<any> {
    const response = await this.request('PUT', `/system/configurations/${configId}`, configData);
    return response.data;
  }

  // ===================================================================
  // MONTHLY DASHBOARDS METHODS
  // ===================================================================

  static async getMonthlyDashboardSubmissions(params: { year: number; month: number }): Promise<any> {
    const response = await this.request('GET', `/monthly-dashboards/submissions?year=${params.year}&month=${params.month}`);
    return response;
  }

  static async getMonthlyDashboardStatistics(params: { year: number; month: number }): Promise<any> {
    const response = await this.request('GET', `/monthly-dashboards/statistics?year=${params.year}&month=${params.month}`);
    return response;
  }

  static async getMonthlyDashboardOverview(params?: { year?: number; month?: number }): Promise<any> {
    const queryParams = new URLSearchParams();
    if (params?.year) queryParams.append('year', params.year.toString());
    if (params?.month) queryParams.append('month', params.month.toString());

    const response = await this.request('GET', `/monthly-dashboards/overview?${queryParams}`);
    return response;
  }

  // KPI Management Methods
  static async createKpi(kpiData: any): Promise<any> {
    const response = await this.request('POST', '/monthly-dashboards/kpis', kpiData);
    return response;
  }

  static async getAllKpis(includeInactive: boolean = false): Promise<any> {
    const queryParams = includeInactive ? '?includeInactive=true' : '';
    const response = await this.request('GET', `/monthly-dashboards/kpis${queryParams}`);
    return response;
  }

  static async getKpiById(id: number): Promise<any> {
    const response = await this.request('GET', `/monthly-dashboards/kpis/${id}`);
    return response;
  }

  static async updateKpi(id: number, kpiData: any): Promise<any> {
    const response = await this.request('PUT', `/monthly-dashboards/kpis/${id}`, kpiData);
    return response;
  }

  static async toggleKpiStatus(id: number): Promise<any> {
    const response = await this.request('POST', `/monthly-dashboards/kpis/${id}/toggle-status`);
    return response;
  }

  // Submission Management Methods
  static async createSubmission(submissionData: any): Promise<any> {
    const response = await this.request('POST', '/monthly-dashboards/submissions', submissionData);
    return response;
  }

  static async updateSubmission(id: number, submissionData: any): Promise<any> {
    const response = await this.request('PUT', `/monthly-dashboards/submissions/${id}`, submissionData);
    return response;
  }

  static async getSubmissionById(id: number): Promise<any> {
    const response = await this.request('GET', `/monthly-dashboards/submissions/${id}`);
    return response;
  }

  static async deleteSubmission(id: number): Promise<any> {
    const response = await this.request('DELETE', `/monthly-dashboards/submissions/${id}`);
    return response;
  }

  // KPI Trends Analysis Methods
  static async getKpiTrends(params: {
    kpiId: number;
    organizationalUnitId?: number;
    months?: number;
  }): Promise<any> {
    const queryParams = new URLSearchParams();
    queryParams.append('kpiId', params.kpiId.toString());
    if (params.organizationalUnitId) {
      queryParams.append('organizationalUnitId', params.organizationalUnitId.toString());
    }
    if (params.months) {
      queryParams.append('months', params.months.toString());
    }

    const response = await this.request('GET', `/monthly-dashboards/trends?${queryParams}`);
    return response;
  }







  // ===================================================================
  // ASSESSMENTS METHODS
  // ===================================================================

  static async getAssessments(filters?: any): Promise<any> {
    const queryParams = new URLSearchParams();
    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null) {
          queryParams.append(key, filters[key].toString());
        }
      });
    }

    const response = await this.request('GET', `/assessments?${queryParams}`);
    return response;
  }

  static async getAssessment(id: number): Promise<any> {
    const response = await this.request('GET', `/assessments/${id}`);
    return response;
  }

  static async createAssessment(data: any): Promise<any> {
    const response = await this.request('POST', '/assessments', data);
    return response;
  }

  static async updateAssessment(id: number, data: any): Promise<any> {
    const response = await this.request('PATCH', `/assessments/${id}`, data);
    return response;
  }

  static async submitAssessment(id: number): Promise<any> {
    const response = await this.request('POST', `/assessments/${id}/submit`);
    return response;
  }

  // ===================================================================
  // DASHBOARD METHODS
  // ===================================================================

  static async getUserCount(): Promise<any> {
    const response = await this.request('GET', '/users/count');
    return response;
  }

  static async getAssessmentStats(): Promise<any> {
    const response = await this.request('GET', '/assessments/workflow/stats');
    return response;
  }

  static async getDashboardStats(): Promise<any> {
    const response = await this.request('GET', '/analytics/metrics/summary');
    return response;
  }

  static async getRecentActivity(): Promise<any> {
    const response = await this.request('GET', '/system/dashboard/recent-activity');
    return response;
  }

  // ===================================================================
  // TEMPLATES METHODS
  // ===================================================================

  static async getAssessmentTemplates(): Promise<any> {
    const response = await this.request('GET', '/assessments/templates');
    return response;
  }

  static async getAssessmentTemplate(id: number): Promise<any> {
    const response = await this.request('GET', `/assessments/templates/${id}`);
    return response;
  }

  static async createAssessmentTemplate(data: any): Promise<any> {
    const response = await this.request('POST', '/assessments/templates', data);
    return response;
  }

  static async updateAssessmentTemplate(id: number, data: any): Promise<any> {
    const response = await this.request('PUT', `/assessments/templates/${id}`, data);
    return response;
  }

  static async deleteAssessmentTemplate(id: number): Promise<any> {
    const response = await this.request('DELETE', `/assessments/templates/${id}`);
    return response;
  }

  // ===================================================================
  // DATABASE MANAGEMENT METHODS
  // ===================================================================

  static async getDatabaseTables(): Promise<any[]> {
    const response = await this.request('GET', '/database/tables');
    return Array.isArray(response.data) ? response.data : [];
  }

  static async getTableData(tableName: string, page: number = 1, limit: number = 20): Promise<any> {
    const response = await this.request('GET', `/database/tables/${tableName}?page=${page}&limit=${limit}`);
    return response.data;
  }

  static async updateTableRecord(tableName: string, recordId: string, recordData: any): Promise<any> {
    const response = await this.request('PUT', `/database/tables/${tableName}/${recordId}`, recordData);
    return response.data;
  }

  // ===================================================================
  // API CONFIGURATION METHODS
  // ===================================================================

  static async getApiEndpoints(): Promise<any[]> {
    const response = await this.request('GET', '/endpoints');
    return Array.isArray(response.data) ? response.data : [];
  }

  static async updateApiEndpoint(endpointId: string, endpointData: any): Promise<any> {
    const response = await this.request('PUT', `/endpoints/${endpointId}`, endpointData);
    return response.data;
  }

  static async getApiKeys(): Promise<any[]> {
    const response = await this.request('GET', '/keys');
    return Array.isArray(response.data) ? response.data : [];
  }

  static async updateApiKey(keyId: string, keyData: any): Promise<any> {
    const response = await this.request('PUT', `/keys/${keyId}`, keyData);
    return response.data;
  }

  static async getRateLimitConfigs(): Promise<any[]> {
    const response = await this.request('GET', '/rate-limits');
    return Array.isArray(response.data) ? response.data : [];
  }








  // ===================================================================
  // ADDITIONAL MISSING METHODS
  // ===================================================================

  static async changePassword(currentPassword: string, newPassword: string, confirmPassword: string): Promise<any> {
    const response = await this.request('POST', '/auth/change-password', {
      currentPassword,
      newPassword,
      confirmPassword
    });
    return response.data;
  }

  static async getRoles(): Promise<any[]> {
    const response = await this.request('GET', '/roles');
    return Array.isArray(response.data) ? response.data : [];
  }

  static async createRecord(tableName: string, recordData: any): Promise<any> {
    const response = await this.request('POST', `/database/tables/${tableName}`, recordData);
    return response.data;
  }

  static async updateRecord(tableName: string, recordId: string, recordData: any): Promise<any> {
    const response = await this.request('PUT', `/database/tables/${tableName}/${recordId}`, recordData);
    return response.data;
  }

  static async deleteRecord(tableName: string, recordId: string): Promise<void> {
    await this.request('DELETE', `/database/tables/${tableName}/${recordId}`);
  }


  // ===================================================================
  // DASHBOARD API METHODS
  // ===================================================================

  /**
   * 🔐 NIS2-COMPLIANT: Get user dashboard data
   */
  static async getUserDashboardData(): Promise<any> {
    try {
      const response = await this.request('GET', '/dashboard/user-dashboard');
      SecureTokenManager.logSecurityEvent('DASHBOARD_DATA_ACCESSED', { endpoint: '/dashboard/user-dashboard' });
      return response.data;
    } catch (error: any) {
      SecureTokenManager.logSecurityEvent('DASHBOARD_ACCESS_FAILED', { error: error.message });
      throw error;
    }
  }

  /**
   * 🔐 NIS2-COMPLIANT: Get assessment history for user
   */
  static async getAssessmentHistory(): Promise<any> {
    try {
      const response = await this.request('GET', '/assessments/history');
      SecureTokenManager.logSecurityEvent('ASSESSMENT_HISTORY_ACCESSED', { endpoint: '/assessments/history' });
      return response.data;
    } catch (error: any) {
      SecureTokenManager.logSecurityEvent('ASSESSMENT_HISTORY_ACCESS_FAILED', { error: error.message });
      throw error;
    }
  }

  /**
   * 🔐 NIS2-COMPLIANT: Get dashboard overview stats
   */
  static async getDashboardOverview(): Promise<any> {
    try {
      const response = await this.request('GET', '/dashboard/overview');
      SecureTokenManager.logSecurityEvent('DASHBOARD_OVERVIEW_ACCESSED', { endpoint: '/dashboard/overview' });
      return response.data;
    } catch (error: any) {
      SecureTokenManager.logSecurityEvent('DASHBOARD_OVERVIEW_ACCESS_FAILED', { error: error.message });
      throw error;
    }
  }

  /**
   * 🔐 NIS2-COMPLIANT: Get employee performance stats
   */
  static async getEmployeePerformanceStats(): Promise<any> {
    try {
      const response = await this.request('GET', '/dashboard/employee-performance');
      SecureTokenManager.logSecurityEvent('EMPLOYEE_PERFORMANCE_ACCESSED', { endpoint: '/dashboard/employee-performance' });
      return response.data;
    } catch (error: any) {
      SecureTokenManager.logSecurityEvent('EMPLOYEE_PERFORMANCE_ACCESS_FAILED', { error: error.message });
      throw error;
    }
  }

  /**
   * 🔐 NIS2-COMPLIANT: Get assessment area statistics
   */
  static async getAssessmentAreaStats(): Promise<any> {
    try {
      const response = await this.request('GET', '/dashboard/area-stats');
      SecureTokenManager.logSecurityEvent('AREA_STATS_ACCESSED', { endpoint: '/dashboard/area-stats' });
      return response.data;
    } catch (error: any) {
      SecureTokenManager.logSecurityEvent('AREA_STATS_ACCESS_FAILED', { error: error.message });
      throw error;
    }
  }

  /**
   * 🔐 NIS2-COMPLIANT: Get assessment trends by month
   */
  static async getAssessmentTrends(): Promise<any> {
    try {
      const response = await this.request('GET', '/dashboard/trends');
      SecureTokenManager.logSecurityEvent('ASSESSMENT_TRENDS_ACCESSED', { endpoint: '/dashboard/trends' });
      return response.data;
    } catch (error: any) {
      SecureTokenManager.logSecurityEvent('ASSESSMENT_TRENDS_ACCESS_FAILED', { error: error.message });
      throw error;
    }
  }

}

// ===================================================================
// ADDITIONAL EXPORTS FOR COMPATIBILITY
// ===================================================================

// Import and re-export types from the unified types file
import type { User as UserType } from '../types';
export type { User, UserRole, AnalyticsDashboard, RecognitionInstance } from '../types';

// Additional interfaces for API compatibility
export interface OrganizationalUnit {
  id: number;
  name: string;
  description?: string;
  parentId?: number;
  managerId?: number;
  level: number;
  path: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  children?: OrganizationalUnit[];
  parent?: OrganizationalUnit;
  manager?: UserType;
  members?: UserType[];
  // Additional properties for enhanced functionality
  type?: 'organization' | 'division' | 'department' | 'team' | 'squad' | 'unit';
  budget?: number;
}

export interface CreateUserDto {
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  title?: string;
  phone?: string;
  organizationalUnitId?: number;
  managerId?: number;
  password?: string;
  location?: string;
}

export interface CreateOrganizationalUnitDto {
  name: string;
  description?: string;
  parentId?: number;
  managerId?: number;
  type?: 'organization' | 'division' | 'department' | 'team' | 'squad' | 'unit';
  budget?: number;
}

export default ApiService;

// Create alias for monthly dashboards API
export const monthlyDashboardsApi = {
  getDashboardOverview: ApiService.getMonthlyDashboardOverview.bind(ApiService),
  getSubmissions: ApiService.getMonthlyDashboardSubmissions.bind(ApiService),
  getStatistics: ApiService.getMonthlyDashboardStatistics.bind(ApiService),
  createKpi: ApiService.createKpi.bind(ApiService),
  getAllKpis: ApiService.getAllKpis.bind(ApiService),
  getKpiById: ApiService.getKpiById.bind(ApiService),
  updateKpi: ApiService.updateKpi.bind(ApiService),
  toggleKpiStatus: ApiService.toggleKpiStatus.bind(ApiService),
  createSubmission: ApiService.createSubmission.bind(ApiService),
  updateSubmission: ApiService.updateSubmission.bind(ApiService),
  getKpiTrends: ApiService.getKpiTrends.bind(ApiService),
  getSubmissionById: ApiService.getSubmissionById.bind(ApiService),
  deleteSubmission: ApiService.deleteSubmission.bind(ApiService)
};
