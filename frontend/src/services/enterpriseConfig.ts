// 🔐 NIS2-COMPLIANT: Enterprise configuration service
// Provides organization-specific dashboard customization and role-based views

import api from './api';

export interface OrganizationConfig {
  id: string;
  name: string;
  logo?: string;
  primaryColor: string;
  secondaryColor: string;
  theme: 'light' | 'dark' | 'auto';
  timezone: string;
  locale: string;
  features: {
    advancedAnalytics: boolean;
    customReports: boolean;
    apiAccess: boolean;
    ssoIntegration: boolean;
    auditLogging: boolean;
    dataExport: boolean;
  };
  dashboardConfig: DashboardConfig;
  complianceSettings: ComplianceSettings;
}

export interface DashboardConfig {
  defaultView: 'overview' | 'analytics' | 'assessments';
  enabledWidgets: string[];
  customWidgets: CustomWidget[];
  refreshInterval: number; // in seconds
  maxDataRetention: number; // in days
  roleBasedViews: Record<string, RoleBasedView>;
}

export interface CustomWidget {
  id: string;
  name: string;
  type: 'chart' | 'metric' | 'list' | 'table';
  dataSource: string;
  config: Record<string, any>;
  permissions: string[];
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface RoleBasedView {
  role: string;
  enabledSections: string[];
  hiddenSections: string[];
  customLayout?: {
    widgets: string[];
    order: number[];
  };
  permissions: {
    canEdit: boolean;
    canExport: boolean;
    canViewDetails: boolean;
    canManageTeam: boolean;
  };
}

export interface ComplianceSettings {
  dataRetentionPeriod: number; // in days
  auditLogRetention: number; // in days
  encryptionRequired: boolean;
  accessLoggingEnabled: boolean;
  dataAnonymization: boolean;
  gdprCompliant: boolean;
  nis2Compliant: boolean;
  customComplianceRules: ComplianceRule[];
}

export interface ComplianceRule {
  id: string;
  name: string;
  description: string;
  type: 'data_access' | 'data_retention' | 'user_activity' | 'system_security';
  enabled: boolean;
  config: Record<string, any>;
}

export interface UserPreferences {
  userId: string;
  dashboardLayout: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    inApp: boolean;
    frequency: 'immediate' | 'daily' | 'weekly';
  };
  language: string;
  timezone: string;
  customSettings: Record<string, any>;
}

class EnterpriseConfigService {
  private configCache: OrganizationConfig | null = null;
  private userPreferencesCache: UserPreferences | null = null;

  /**
   * Get organization configuration
   */
  async getOrganizationConfig(): Promise<OrganizationConfig> {
    if (this.configCache) {
      return this.configCache;
    }

    try {
      const response = await api.get('/organization/config');
      this.configCache = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch organization config:', error);
      
      // Return default configuration
      return this.getDefaultConfig();
    }
  }

  /**
   * Update organization configuration
   */
  async updateOrganizationConfig(config: Partial<OrganizationConfig>): Promise<OrganizationConfig> {
    try {
      const response = await api.put('/organization/config', config);
      this.configCache = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to update organization config:', error);
      throw error;
    }
  }

  /**
   * Get user preferences
   */
  async getUserPreferences(userId: string): Promise<UserPreferences> {
    if (this.userPreferencesCache && this.userPreferencesCache.userId === userId) {
      return this.userPreferencesCache;
    }

    try {
      const response = await api.get(`/users/${userId}/preferences`);
      this.userPreferencesCache = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to fetch user preferences:', error);
      
      // Return default preferences
      return this.getDefaultUserPreferences(userId);
    }
  }

  /**
   * Update user preferences
   */
  async updateUserPreferences(userId: string, preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    try {
      const response = await api.put(`/users/${userId}/preferences`, preferences);
      this.userPreferencesCache = response.data;
      return response.data;
    } catch (error) {
      console.error('Failed to update user preferences:', error);
      throw error;
    }
  }

  /**
   * Get role-based dashboard configuration
   */
  async getRoleBasedConfig(role: string): Promise<RoleBasedView> {
    const orgConfig = await this.getOrganizationConfig();
    return orgConfig.dashboardConfig.roleBasedViews[role] || this.getDefaultRoleView(role);
  }

  /**
   * Get available dashboard widgets for a role
   */
  async getAvailableWidgets(role: string): Promise<string[]> {
    const roleConfig = await this.getRoleBasedConfig(role);
    const orgConfig = await this.getOrganizationConfig();
    
    return orgConfig.dashboardConfig.enabledWidgets.filter(widget => 
      roleConfig.enabledSections.includes(widget)
    );
  }

  /**
   * Check if user has permission for a specific action
   */
  async hasPermission(userId: string, role: string, permission: string): Promise<boolean> {
    const roleConfig = await this.getRoleBasedConfig(role);
    const userPrefs = await this.getUserPreferences(userId);
    
    // Check role-based permissions
    const rolePermissions = roleConfig.permissions as any;
    if (rolePermissions[permission] !== undefined) {
      return rolePermissions[permission];
    }
    
    // Check custom user permissions
    if (userPrefs.customSettings?.permissions?.[permission] !== undefined) {
      return userPrefs.customSettings.permissions[permission];
    }
    
    return false;
  }

  /**
   * Get default organization configuration
   */
  private getDefaultConfig(): OrganizationConfig {
    return {
      id: 'default',
      name: 'Default Organization',
      primaryColor: '#1976d2',
      secondaryColor: '#dc004e',
      theme: 'light',
      timezone: 'UTC',
      locale: 'en-US',
      features: {
        advancedAnalytics: true,
        customReports: true,
        apiAccess: true,
        ssoIntegration: false,
        auditLogging: true,
        dataExport: true
      },
      dashboardConfig: {
        defaultView: 'overview',
        enabledWidgets: ['stats', 'charts', 'recent-activity', 'quick-actions'],
        customWidgets: [],
        refreshInterval: 300,
        maxDataRetention: 365,
        roleBasedViews: {
          employee: this.getDefaultRoleView('employee'),
          manager: this.getDefaultRoleView('manager'),
          admin: this.getDefaultRoleView('admin')
        }
      },
      complianceSettings: {
        dataRetentionPeriod: 365,
        auditLogRetention: 2555, // 7 years
        encryptionRequired: true,
        accessLoggingEnabled: true,
        dataAnonymization: false,
        gdprCompliant: true,
        nis2Compliant: true,
        customComplianceRules: []
      }
    };
  }

  /**
   * Get default role-based view configuration
   */
  private getDefaultRoleView(role: string): RoleBasedView {
    const baseConfig = {
      role,
      enabledSections: ['dashboard'],
      hiddenSections: [],
      permissions: {
        canEdit: false,
        canExport: false,
        canViewDetails: true,
        canManageTeam: false
      }
    };

    switch (role) {
      case 'admin':
        return {
          ...baseConfig,
          enabledSections: ['dashboard', 'analytics', 'organization', 'settings', 'audit'],
          permissions: {
            canEdit: true,
            canExport: true,
            canViewDetails: true,
            canManageTeam: true
          }
        };
      case 'manager':
        return {
          ...baseConfig,
          enabledSections: ['dashboard', 'analytics', 'team-management'],
          permissions: {
            canEdit: true,
            canExport: true,
            canViewDetails: true,
            canManageTeam: true
          }
        };
      case 'employee':
      default:
        return {
          ...baseConfig,
          enabledSections: ['dashboard', 'my-assessments'],
          permissions: {
            canEdit: false,
            canExport: false,
            canViewDetails: true,
            canManageTeam: false
          }
        };
    }
  }

  /**
   * Get default user preferences
   */
  private getDefaultUserPreferences(userId: string): UserPreferences {
    return {
      userId,
      dashboardLayout: 'default',
      theme: 'light',
      notifications: {
        email: true,
        inApp: true,
        frequency: 'daily'
      },
      language: 'en-US',
      timezone: 'UTC',
      customSettings: {}
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.configCache = null;
    this.userPreferencesCache = null;
  }
}

export const enterpriseConfigService = new EnterpriseConfigService();
export default enterpriseConfigService;
