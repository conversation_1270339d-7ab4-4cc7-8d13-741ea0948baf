import { ApiService } from './api';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  TRACE = 'trace',
}

export enum LogSource {
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  DATABASE = 'database',
  EXTERNAL_API = 'external_api',
  SYSTEM = 'system',
}

export interface BrowserError {
  message: string;
  filename?: string;
  lineno?: number;
  colno?: number;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  userAgent: string;
  url: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

export interface CustomLogEntry {
  level: LogLevel;
  source: LogSource;
  message: string;
  details?: any;
  component?: string;
  action?: string;
}

class LoggingService {
  private readonly apiBaseUrl = `${ApiService.getBaseUrl()}/logs`;
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second
  private sessionId: string;
  private userId?: string;
  private logQueue: Array<BrowserError | CustomLogEntry> = [];
  private isOnline = navigator.onLine;

  constructor() {
    this.sessionId = this.generateSessionId();
    // Temporarily disable logging to prevent 404 errors
    // this.setupErrorHandlers();
    // this.setupNetworkHandlers();
    // this.setupPerformanceLogging();

    // Process queued logs when coming back online
    // this.processQueuedLogs();
  }

  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  setUserId(userId: string): void {
    this.userId = userId;
  }

  private setupErrorHandlers(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.logBrowserError({
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error ? {
          name: event.error.name,
          message: event.error.message,
          stack: event.error.stack,
        } : undefined,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date(),
        userId: this.userId,
        sessionId: this.sessionId,
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.logBrowserError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        error: {
          name: 'UnhandledPromiseRejection',
          message: String(event.reason),
          stack: event.reason?.stack,
        },
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date(),
        userId: this.userId,
        sessionId: this.sessionId,
      });
    });

    // React error boundary integration
    this.setupReactErrorBoundary();
  }

  private setupReactErrorBoundary(): void {
    // This will be called by React Error Boundaries
    (window as any).__EHRX_LOG_REACT_ERROR__ = (error: Error, errorInfo: any) => {
      this.logBrowserError({
        message: `React Error: ${error.message}`,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date(),
        userId: this.userId,
        sessionId: this.sessionId,
      });

      // Log additional React error info
      this.logCustom({
        level: LogLevel.ERROR,
        source: LogSource.FRONTEND,
        message: 'React Component Stack',
        details: errorInfo,
        component: 'react-error-boundary',
        action: 'component_error',
      });
    };
  }

  private setupNetworkHandlers(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processQueuedLogs();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  private setupPerformanceLogging(): void {
    // Log page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

        this.logCustom({
          level: LogLevel.INFO,
          source: LogSource.FRONTEND,
          message: 'Page Load Performance',
          details: {
            loadTime: perfData.loadEventEnd - perfData.loadEventStart,
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            firstPaint: this.getFirstPaint(),
            firstContentfulPaint: this.getFirstContentfulPaint(),
          },
          component: 'performance',
          action: 'page_load',
        });
      }, 0);
    });
  }

  private getFirstPaint(): number | null {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : null;
  }

  private getFirstContentfulPaint(): number | null {
    const paintEntries = performance.getEntriesByType('paint');
    const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return firstContentfulPaint ? firstContentfulPaint.startTime : null;
  }

  async logBrowserError(error: BrowserError): Promise<void> {
    if (!this.isOnline) {
      this.logQueue.push(error);
      return;
    }

    try {
      await this.sendToServer('/browser-error', error);
    } catch (err) {
      console.error('Failed to log browser error:', err);
      this.logQueue.push(error);
    }
  }

  async logCustom(entry: CustomLogEntry): Promise<void> {
    if (!this.isOnline) {
      this.logQueue.push(entry);
      return;
    }

    try {
      await this.sendToServer('/custom', entry);
    } catch (err) {
      console.error('Failed to log custom entry:', err);
      this.logQueue.push(entry);
    }
  }

  // Convenience methods
  async logError(message: string, details?: any, component?: string): Promise<void> {
    return this.logCustom({
      level: LogLevel.ERROR,
      source: LogSource.FRONTEND,
      message,
      details,
      component,
      action: 'manual_error',
    });
  }

  async logWarning(message: string, details?: any, component?: string): Promise<void> {
    return this.logCustom({
      level: LogLevel.WARN,
      source: LogSource.FRONTEND,
      message,
      details,
      component,
      action: 'manual_warning',
    });
  }

  async logInfo(message: string, details?: any, component?: string): Promise<void> {
    return this.logCustom({
      level: LogLevel.INFO,
      source: LogSource.FRONTEND,
      message,
      details,
      component,
      action: 'manual_info',
    });
  }

  async logUserAction(action: string, details?: any, component?: string): Promise<void> {
    return this.logCustom({
      level: LogLevel.INFO,
      source: LogSource.FRONTEND,
      message: `User Action: ${action}`,
      details,
      component,
      action: 'user_action',
    });
  }

  private async sendToServer(endpoint: string, data: any): Promise<void> {
    const url = `${this.apiBaseUrl}${endpoint}`;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (response.ok) {
          return;
        }

        if (attempt === this.maxRetries) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        if (attempt === this.maxRetries) {
          throw error;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
      }
    }
  }

  private async processQueuedLogs(): Promise<void> {
    if (!this.isOnline || this.logQueue.length === 0) {
      return;
    }

    const logsToProcess = [...this.logQueue];
    this.logQueue = [];

    for (const log of logsToProcess) {
      try {
        if ('error' in log) {
          await this.logBrowserError(log as BrowserError);
        } else {
          await this.logCustom(log as CustomLogEntry);
        }
      } catch (error) {
        // If still failing, put back in queue
        this.logQueue.push(log);
      }
    }
  }

  // Get logging statistics for admin dashboard
  getSessionInfo() {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      queuedLogs: this.logQueue.length,
      isOnline: this.isOnline,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
  }
}

// Create singleton instance
export const loggingService = new LoggingService();

// Export for use in React components
export default loggingService;
