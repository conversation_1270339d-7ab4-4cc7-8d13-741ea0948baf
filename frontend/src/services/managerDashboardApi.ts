import { ApiService } from './api';

// ===================================================================
// MANAGER DASHBOARD TYPES
// ===================================================================

export interface ManagerDashboardMetrics {
  id: number;
  organizationalUnitId: number;
  managerId: number;
  reportingPeriod: string;
  fteCount: number;
  attritionResigned: number;
  attritionInvoluntary: number;
  slaPercentage?: number;
  utilizationPercentage?: number;
  axPercentage?: number;
  complianceScore?: number;
  abVariancePercentage?: number;
  vacationLeavePercentage?: number;
  inOfficePercentage?: number;
  status: 'draft' | 'submitted' | 'approved';
  notes?: string;
  lastUpdatedBy?: number;
  lastUpdatedAt: string;
  createdAt: string;
  organizationalUnit?: {
    id: number;
    name: string;
    parent?: {
      id: number;
      name: string;
    };
  };
  manager?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
  lastUpdatedByUser?: {
    id: number;
    firstName: string;
    lastName: string;
  };
}

export interface DashboardReminderSettings {
  id: number;
  managerId: number;
  reminderDayOfMonth: number;
  reminderEnabled: boolean;
  emailTemplateId: string;
  createdAt: string;
  updatedAt: string;
  manager?: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface CreateManagerDashboardMetricsDto {
  organizationalUnitId: number;
  managerId: number;
  reportingPeriod: string;
  fteCount?: number;
  attritionResigned?: number;
  attritionInvoluntary?: number;
  slaPercentage?: number;
  utilizationPercentage?: number;
  axPercentage?: number;
  complianceScore?: number;
  abVariancePercentage?: number;
  vacationLeavePercentage?: number;
  inOfficePercentage?: number;
  status?: 'draft' | 'submitted' | 'approved';
  notes?: string;
}

export interface UpdateManagerDashboardMetricsDto {
  fteCount?: number;
  attritionResigned?: number;
  attritionInvoluntary?: number;
  slaPercentage?: number;
  utilizationPercentage?: number;
  axPercentage?: number;
  complianceScore?: number;
  abVariancePercentage?: number;
  vacationLeavePercentage?: number;
  inOfficePercentage?: number;
  status?: 'draft' | 'submitted' | 'approved';
  notes?: string;
}

export interface DashboardQueryParams {
  managerId?: number;
  reportingPeriod?: string;
  organizationalUnitId?: number;
  status?: 'draft' | 'submitted' | 'approved';
  page?: number;
  limit?: number;
}

export interface UpdateReminderSettingsDto {
  reminderDayOfMonth?: number;
  reminderEnabled?: boolean;
  emailTemplateId?: string;
}

export interface DashboardSummary {
  totalFte: number;
  avgUtilization: number;
  totalUnits: number;
  totalAttrition: number;
  completedUnits: number;
  pendingUnits: number;
}

// ===================================================================
// MANAGER DASHBOARD API SERVICE
// ===================================================================

export class ManagerDashboardApiService {
  
  /**
   * Get dashboard metrics with filtering and pagination
   */
  static async getDashboardMetrics(params: DashboardQueryParams = {}) {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await ApiService.get(
      `/analytics/manager-dashboard/metrics?${queryParams.toString()}`
    );
    return response.data;
  }

  /**
   * Get dashboard metrics for a specific manager
   */
  static async getManagerDashboard(managerId: number, reportingPeriod?: string) {
    const params = reportingPeriod ? `?reportingPeriod=${reportingPeriod}` : '';
    const response = await ApiService.get(
      `/analytics/manager-dashboard/metrics/${managerId}${params}`
    );
    return response.data;
  }

  /**
   * Create new dashboard metrics
   */
  static async createMetrics(data: CreateManagerDashboardMetricsDto) {
    const response = await ApiService.post(
      '/analytics/manager-dashboard/metrics',
      data
    );
    return response.data;
  }

  /**
   * Update existing dashboard metrics
   */
  static async updateMetrics(id: number, data: UpdateManagerDashboardMetricsDto) {
    const response = await ApiService.put(
      `/analytics/manager-dashboard/metrics/${id}`,
      data
    );
    return response.data;
  }

  /**
   * Get organizational units managed by a user
   */
  static async getManagerOrganizationalUnits(managerId: number) {
    const response = await ApiService.get(
      `/analytics/manager-dashboard/organizational-units/${managerId}`
    );
    return response.data;
  }

  /**
   * Get list of managers for dropdown
   */
  static async getManagers() {
    const response = await ApiService.get('/analytics/manager-dashboard/managers');
    return response.data;
  }

  /**
   * Get dashboard summary statistics
   */
  static async getDashboardSummary(managerId: number): Promise<DashboardSummary> {
    const response = await ApiService.get(
      `/analytics/manager-dashboard/summary/${managerId}`
    );
    return response.data;
  }

  /**
   * Get reminder settings for a manager
   */
  static async getReminderSettings(managerId: number): Promise<DashboardReminderSettings> {
    const response = await ApiService.get(
      `/analytics/manager-dashboard/reminder-settings/${managerId}`
    );
    return response.data;
  }

  /**
   * Update reminder settings for a manager
   */
  static async updateReminderSettings(
    managerId: number, 
    data: UpdateReminderSettingsDto
  ): Promise<DashboardReminderSettings> {
    const response = await ApiService.put(
      `/analytics/manager-dashboard/reminder-settings/${managerId}`,
      data
    );
    return response.data;
  }

  /**
   * Calculate FTE count for an organizational unit
   */
  static async calculateFteCount(organizationalUnitId: number): Promise<{ fteCount: number }> {
    const response = await ApiService.get(
      `/analytics/manager-dashboard/calculate-fte/${organizationalUnitId}`
    );
    return response.data;
  }
}

// Export for backward compatibility
export const managerDashboardApi = ManagerDashboardApiService;
