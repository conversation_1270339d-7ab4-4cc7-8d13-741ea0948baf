/**
 * 🔐 NIS2-COMPLIANT: Shared Analytics Utilities
 * Centralized utility functions for analytics widgets to eliminate code duplication
 * and ensure consistent behavior across the application.
 */

/**
 * Standard risk color mapping for consistent UI across all analytics widgets
 */
export const getRiskColor = (riskLevel: string): string => {
  switch (riskLevel.toLowerCase()) {
    case 'low':
      return '#4caf50'; // Green
    case 'medium':
      return '#ff9800'; // Orange
    case 'high':
      return '#f44336'; // Red
    case 'critical':
      return '#d32f2f'; // Dark Red
    default:
      return '#9e9e9e'; // Grey
  }
};

/**
 * Performance score color mapping for consistent UI
 */
export const getPerformanceColor = (score: number): string => {
  if (score >= 90) return '#4caf50'; // Green - Excellent
  if (score >= 75) return '#2196f3'; // Blue - Good
  if (score >= 60) return '#ff9800'; // Orange - Satisfactory
  return '#f44336'; // Red - Needs Improvement
};

/**
 * Severity color mapping for skill gaps and other severity-based metrics
 */
export const getSeverityColor = (severity: string): 'error' | 'warning' | 'info' | 'success' | 'default' => {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'error';
    case 'high':
    case 'moderate':
      return 'warning';
    case 'medium':
    case 'minor':
      return 'info';
    case 'low':
      return 'success';
    default:
      return 'default';
  }
};

/**
 * Calculate risk intensity for heatmap visualizations
 */
export const getRiskIntensity = (riskScore: number): number => {
  return Math.min(1, Math.max(0, riskScore / 100));
};

/**
 * Format percentage values consistently
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Format score values consistently
 */
export const formatScore = (score: number, decimals: number = 1): string => {
  return score.toFixed(decimals);
};

/**
 * Calculate trend direction from historical data
 */
export const calculateTrend = (current: number, previous: number): 'up' | 'down' | 'stable' => {
  const threshold = 0.05; // 5% threshold for considering stable
  const change = (current - previous) / previous;
  
  if (Math.abs(change) < threshold) return 'stable';
  return change > 0 ? 'up' : 'down';
};

/**
 * Calculate percentage change between two values
 */
export const calculatePercentageChange = (current: number, previous: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

/**
 * Classify risk level based on numeric score
 */
export const classifyRiskLevel = (score: number): 'low' | 'medium' | 'high' | 'critical' => {
  if (score >= 0.8) return 'critical';
  if (score >= 0.6) return 'high';
  if (score >= 0.3) return 'medium';
  return 'low';
};

/**
 * Classify performance level based on numeric score
 */
export const classifyPerformanceLevel = (score: number): 'excellent' | 'good' | 'satisfactory' | 'needs-improvement' => {
  if (score >= 90) return 'excellent';
  if (score >= 75) return 'good';
  if (score >= 60) return 'satisfactory';
  return 'needs-improvement';
};

/**
 * Generate consistent date ranges for analytics filters
 */
export const getDateRange = (period: string): { startDate: Date; endDate: Date } => {
  const endDate = new Date();
  const startDate = new Date();

  switch (period) {
    case '1month':
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case '6months':
      startDate.setMonth(endDate.getMonth() - 6);
      break;
    case '1year':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case '2years':
      startDate.setFullYear(endDate.getFullYear() - 2);
      break;
    default: // 3months
      startDate.setMonth(endDate.getMonth() - 3);
  }

  return { startDate, endDate };
};

/**
 * Standard error handling for analytics widgets
 */
export const handleAnalyticsError = (error: any, widgetName: string): string => {
  console.error(`Error in ${widgetName}:`, error);
  
  if (error.response?.status === 401) {
    return 'Authentication required. Please log in again.';
  }
  
  if (error.response?.status === 403) {
    return 'Access denied. You do not have permission to view this data.';
  }
  
  if (error.response?.status === 404) {
    return 'Data not found. The requested analytics data is not available.';
  }
  
  if (error.response?.status >= 500) {
    return 'Server error. Please try again later or contact support.';
  }
  
  return `Failed to load ${widgetName.toLowerCase()} data. Please try again.`;
};

/**
 * Validate and sanitize filter parameters
 */
export const sanitizeFilters = (filters: any): any => {
  const sanitized: any = {};
  
  if (filters.period && typeof filters.period === 'string') {
    const validPeriods = ['1month', '3months', '6months', '1year', '2years'];
    sanitized.period = validPeriods.includes(filters.period) ? filters.period : '3months';
  }
  
  if (filters.teamId && !isNaN(Number(filters.teamId))) {
    sanitized.teamId = Number(filters.teamId);
  }
  
  if (filters.departmentId && !isNaN(Number(filters.departmentId))) {
    sanitized.departmentId = Number(filters.departmentId);
  }
  
  if (filters.role && typeof filters.role === 'string') {
    sanitized.role = filters.role.trim();
  }
  
  return sanitized;
};
