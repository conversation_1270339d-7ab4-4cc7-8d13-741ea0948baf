/**
 * 🔐 NIS2-COMPLIANT: Shared Assessment Utilities
 * Centralized utility functions for assessment components to eliminate code duplication
 * and ensure consistent behavior across the application.
 */

import { AssessmentStatus } from '../types';

/**
 * Get consistent status colors for assessment status badges
 */
export const getAssessmentStatusColor = (status: AssessmentStatus): string => {
  switch (status) {
    case AssessmentStatus.DRAFT:
      return 'bg-gray-100 text-gray-800';
    case AssessmentStatus.IN_PROGRESS:
      return 'bg-blue-100 text-blue-800';
    case AssessmentStatus.PENDING_REVIEW:
      return 'bg-yellow-100 text-yellow-800';
    case AssessmentStatus.COMPLETED:
      return 'bg-green-100 text-green-800';
    case AssessmentStatus.APPROVED:
      return 'bg-green-100 text-green-800';
    case AssessmentStatus.REJECTED:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

/**
 * Get consistent status colors for filter buttons
 */
export const getAssessmentFilterColor = (status: AssessmentStatus | 'all', currentFilter: string): string => {
  const isActive = currentFilter === status;
  return `px-3 py-1 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
    isActive 
      ? 'bg-blue-600 text-white' 
      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
  }`;
};

/**
 * Get score color based on performance level
 */
export const getScoreColor = (score: number): string => {
  if (score >= 90) return 'text-green-600';
  if (score >= 75) return 'text-blue-600';
  if (score >= 60) return 'text-yellow-600';
  return 'text-red-600';
};

/**
 * Format assessment status for display
 */
export const formatAssessmentStatus = (status: AssessmentStatus): string => {
  return status.replace(/_/g, ' ').toUpperCase();
};

/**
 * Check if assessment can be edited based on status and user role
 */
export const canEditAssessment = (
  assessment: any,
  userRole: string,
  userId: number
): boolean => {
  if (!assessment) return false;

  const editableStatuses = [AssessmentStatus.DRAFT, AssessmentStatus.IN_PROGRESS];
  
  // HR admins can edit any assessment in editable status
  if (userRole === 'HR_ADMIN') {
    return editableStatuses.includes(assessment.status);
  }

  // Managers can edit assessments they created
  if (userRole === 'MANAGER' && assessment.managerId === userId) {
    return editableStatuses.includes(assessment.status);
  }

  return false;
};

/**
 * Check if assessment can be deleted based on user role
 */
export const canDeleteAssessment = (
  assessment: any,
  userRole: string,
  userId: number
): boolean => {
  if (!assessment) return false;

  // HR admins can delete any assessment
  if (userRole === 'HR_ADMIN') {
    return true;
  }

  // Managers can delete assessments they created
  if (userRole === 'MANAGER' && assessment.managerId === userId) {
    return true;
  }

  return false;
};

/**
 * Validate assessment form data
 */
export const validateAssessmentForm = (
  selectedTemplate: any,
  selectedEmployee: any,
  dueDate: string,
  responses: any[]
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!selectedTemplate) {
    errors.push('Please select an assessment template.');
  }

  if (!selectedEmployee) {
    errors.push('Please select an employee.');
  }

  if (!dueDate) {
    errors.push('Please set a due date.');
  }

  // Check if due date is in the past
  if (dueDate && new Date(dueDate) < new Date()) {
    errors.push('Due date cannot be in the past.');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Check if assessment is overdue
 */
export const isAssessmentOverdue = (assessment: any): boolean => {
  if (!assessment.dueDate) return false;
  
  const completedStatuses = [AssessmentStatus.COMPLETED, AssessmentStatus.APPROVED];
  return new Date(assessment.dueDate) < new Date() && 
         !completedStatuses.includes(assessment.status);
};

/**
 * Get assessment progress percentage
 */
export const getAssessmentProgress = (responses: any[], totalAreas: number): number => {
  if (totalAreas === 0) return 0;
  
  const completedResponses = responses.filter(response => 
    response.score !== null && response.score !== undefined
  ).length;
  
  return Math.round((completedResponses / totalAreas) * 100);
};

/**
 * Format assessment score for display
 */
export const formatAssessmentScore = (score: number | null | undefined): string => {
  if (score === null || score === undefined) return '-';
  return score.toFixed(1);
};

/**
 * Get assessment priority based on due date and status
 */
export const getAssessmentPriority = (assessment: any): 'high' | 'medium' | 'low' => {
  if (isAssessmentOverdue(assessment)) return 'high';
  
  const daysUntilDue = Math.ceil(
    (new Date(assessment.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );
  
  if (daysUntilDue <= 3) return 'high';
  if (daysUntilDue <= 7) return 'medium';
  return 'low';
};

/**
 * Get priority color for assessment cards
 */
export const getPriorityColor = (priority: 'high' | 'medium' | 'low'): string => {
  switch (priority) {
    case 'high':
      return 'border-l-red-500';
    case 'medium':
      return 'border-l-yellow-500';
    case 'low':
      return 'border-l-green-500';
    default:
      return 'border-l-gray-300';
  }
};
