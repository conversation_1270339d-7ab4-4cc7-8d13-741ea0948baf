/**
 * 🔐 NIS2-COMPLIANT: Dynamic Configuration Management
 * Provides enterprise-grade configuration management for different organizations
 * and deployment environments with security and audit compliance.
 */

export interface OrganizationConfig {
  id: string;
  name: string;
  branding: {
    primaryColor: string;
    secondaryColor: string;
    logo?: string;
    favicon?: string;
  };
  analytics: {
    enabledWidgets: string[];
    defaultDashboard: string;
    refreshInterval: number; // in milliseconds
    maxDataRetention: number; // in days
    enablePredictiveAnalytics: boolean;
    enableAIInsights: boolean;
  };
  security: {
    sessionTimeout: number; // in milliseconds
    maxLoginAttempts: number;
    requireMFA: boolean;
    passwordPolicy: {
      minLength: number;
      requireSpecialChars: boolean;
      requireNumbers: boolean;
      requireUppercase: boolean;
    };
  };
  features: {
    enableTeamManagement: boolean;
    enableReports: boolean;
    enableAssessments: boolean;
    enableMonthlyDashboards: boolean;
    enableCustomDashboards: boolean;
    enableAdvancedAnalytics: boolean;
    enableAPIAccess: boolean;
    enableSSOIntegration: boolean;
    enableMultiTenant: boolean;
    enableCustomBranding: boolean;
    enableDataExport: boolean;
    enableAuditReports: boolean;
    maxUsersPerTeam: number;
    maxTeamsPerOrganization: number;
    maxAssessmentsPerMonth: number;
  };
  compliance: {
    enableAuditLogging: boolean;
    dataRetentionPeriod: number; // in days
    enableGDPRCompliance: boolean;
    enableNIS2Compliance: boolean;
  };
}

// Default configuration for new organizations
const DEFAULT_CONFIG: OrganizationConfig = {
  id: 'default',
  name: 'EHRX Organization',
  branding: {
    primaryColor: '#1976d2',
    secondaryColor: '#dc004e',
  },
  analytics: {
    enabledWidgets: [
      'performance_overview',
      'engagement_trends',
      'attrition_risk',
      'team_comparison',
      'recognition_feed',
      'team_performance'
    ],
    defaultDashboard: 'overview',
    refreshInterval: 300000, // 5 minutes
    maxDataRetention: 730, // 2 years
    enablePredictiveAnalytics: true,
    enableAIInsights: true,
  },
  security: {
    sessionTimeout: 3600000, // 1 hour
    maxLoginAttempts: 5,
    requireMFA: false,
    passwordPolicy: {
      minLength: 8,
      requireSpecialChars: true,
      requireNumbers: true,
      requireUppercase: true,
    },
  },
  features: {
    enableTeamManagement: true,
    enableReports: true,
    enableAssessments: true,
    enableMonthlyDashboards: true,
    enableCustomDashboards: true,
    enableAdvancedAnalytics: true,
    enableAPIAccess: false, // Disabled by default for security
    enableSSOIntegration: false, // Requires setup
    enableMultiTenant: false, // Single tenant by default
    enableCustomBranding: true,
    enableDataExport: true,
    enableAuditReports: true,
    maxUsersPerTeam: 50,
    maxTeamsPerOrganization: 100,
    maxAssessmentsPerMonth: 1000,
  },
  compliance: {
    enableAuditLogging: true,
    dataRetentionPeriod: 2555, // 7 years for NIS2 compliance
    enableGDPRCompliance: true,
    enableNIS2Compliance: true,
  },
};

// Environment-specific configurations
const ENVIRONMENT_CONFIGS = {
  development: {
    analytics: {
      refreshInterval: 10000, // 10 seconds for faster development
      enablePredictiveAnalytics: true,
      enableAIInsights: true,
    },
    security: {
      sessionTimeout: 86400000, // 24 hours for development convenience
      maxLoginAttempts: 10,
      requireMFA: false,
    },
    compliance: {
      enableAuditLogging: true, // Always enabled for testing
    },
  },
  production: {
    analytics: {
      refreshInterval: 300000, // 5 minutes
      enablePredictiveAnalytics: true,
      enableAIInsights: true,
    },
    security: {
      sessionTimeout: 3600000, // 1 hour
      maxLoginAttempts: 3,
      requireMFA: true, // Always require MFA in production
    },
    compliance: {
      enableAuditLogging: true,
      enableGDPRCompliance: true,
      enableNIS2Compliance: true,
    },
  },
  test: {
    analytics: {
      refreshInterval: 5000, // 5 seconds for testing
      enablePredictiveAnalytics: false,
      enableAIInsights: false,
    },
    security: {
      sessionTimeout: 3600000, // 1 hour
      maxLoginAttempts: 5,
      requireMFA: false,
    },
    compliance: {
      enableAuditLogging: false,
    },
  },
};

class ConfigurationManager {
  private static instance: ConfigurationManager;
  private currentConfig: OrganizationConfig;
  private environment: 'development' | 'production' | 'test';

  private constructor() {
    this.environment = this.detectEnvironment();
    this.currentConfig = this.loadConfiguration();
  }

  public static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  private detectEnvironment(): 'development' | 'production' | 'test' {
    if (process.env.NODE_ENV === 'production') return 'production';
    if (process.env.NODE_ENV === 'test') return 'test';
    return 'development';
  }

  private loadConfiguration(): OrganizationConfig {
    try {
      // Try to load organization-specific config from API or localStorage
      const savedConfig = localStorage.getItem('ehrx_org_config');
      let orgConfig = savedConfig ? JSON.parse(savedConfig) : {};

      // Merge with environment-specific overrides
      const envOverrides = ENVIRONMENT_CONFIGS[this.environment] || {};

      // Deep merge configurations
      const config = this.deepMerge(DEFAULT_CONFIG, orgConfig, envOverrides);

      console.log('🔧 [CONFIG] Loaded configuration:', {
        environment: this.environment,
        orgId: config.id,
        enabledFeatures: Object.keys(config.features).filter(key => config.features[key as keyof typeof config.features]),
        securityLevel: config.security.requireMFA ? 'high' : 'standard',
      });

      return config;
    } catch (error) {
      console.error('🔧 [CONFIG] Failed to load configuration, using defaults:', error);
      return { ...DEFAULT_CONFIG };
    }
  }

  private deepMerge(target: any, ...sources: any[]): any {
    if (!sources.length) return target;
    const source = sources.shift();

    if (this.isObject(target) && this.isObject(source)) {
      for (const key in source) {
        if (this.isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} });
          this.deepMerge(target[key], source[key]);
        } else {
          Object.assign(target, { [key]: source[key] });
        }
      }
    }

    return this.deepMerge(target, ...sources);
  }

  private isObject(item: any): boolean {
    return item && typeof item === 'object' && !Array.isArray(item);
  }

  public getConfig(): OrganizationConfig {
    return { ...this.currentConfig };
  }

  public updateConfig(updates: Partial<OrganizationConfig>): void {
    this.currentConfig = this.deepMerge(this.currentConfig, updates);

    // Save to localStorage for persistence
    try {
      localStorage.setItem('ehrx_org_config', JSON.stringify(this.currentConfig));
      console.log('🔧 [CONFIG] Configuration updated and saved');
    } catch (error) {
      console.error('🔧 [CONFIG] Failed to save configuration:', error);
    }
  }

  public isFeatureEnabled(feature: keyof OrganizationConfig['features']): boolean {
    const value = this.currentConfig.features[feature];
    return typeof value === 'boolean' ? value : Boolean(value);
  }

  public isWidgetEnabled(widgetId: string): boolean {
    return this.currentConfig.analytics.enabledWidgets.includes(widgetId);
  }

  public getRefreshInterval(): number {
    return this.currentConfig.analytics.refreshInterval;
  }

  public getSessionTimeout(): number {
    return this.currentConfig.security.sessionTimeout;
  }

  public getBrandingColors(): { primary: string; secondary: string } {
    return {
      primary: this.currentConfig.branding.primaryColor,
      secondary: this.currentConfig.branding.secondaryColor,
    };
  }

  public isComplianceEnabled(type: 'audit' | 'gdpr' | 'nis2'): boolean {
    switch (type) {
      case 'audit':
        return this.currentConfig.compliance.enableAuditLogging;
      case 'gdpr':
        return this.currentConfig.compliance.enableGDPRCompliance;
      case 'nis2':
        return this.currentConfig.compliance.enableNIS2Compliance;
      default:
        return false;
    }
  }

  public getEnvironment(): string {
    return this.environment;
  }

  public isDevelopment(): boolean {
    return this.environment === 'development';
  }

  public isProduction(): boolean {
    return this.environment === 'production';
  }

  /**
   * Apply an organizational template to the current configuration
   * @param templateName - The name of the template to apply
   * @param customOverrides - Optional custom overrides to apply after the template
   */
  public applyOrganizationTemplate(
    templateName: keyof typeof ORGANIZATION_TEMPLATES,
    customOverrides?: Partial<OrganizationConfig>
  ): void {
    const template = ORGANIZATION_TEMPLATES[templateName];
    if (!template) {
      console.error(`🔧 [CONFIG] Unknown template: ${templateName}`);
      return;
    }

    // Create a new config based on the template
    const templateConfig: Partial<OrganizationConfig> = {
      features: template.features,
      security: template.security,
      compliance: (template as any).compliance || this.currentConfig.compliance,
    };

    // Apply template and any custom overrides
    const newConfig = this.deepMerge(
      this.currentConfig,
      templateConfig,
      customOverrides || {}
    );

    this.currentConfig = newConfig;

    // Save to localStorage
    try {
      localStorage.setItem('ehrx_org_config', JSON.stringify(this.currentConfig));
      console.log(`🔧 [CONFIG] Applied ${template.name} template successfully`);
    } catch (error) {
      console.error('🔧 [CONFIG] Failed to save template configuration:', error);
    }
  }

  /**
   * Get available organizational templates
   */
  public getAvailableTemplates(): typeof ORGANIZATION_TEMPLATES {
    return ORGANIZATION_TEMPLATES;
  }

  /**
   * Validate current configuration against organizational limits
   */
  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = this.currentConfig;

    // Validate feature limits
    if (config.features.maxUsersPerTeam < 1) {
      errors.push('Maximum users per team must be at least 1');
    }

    if (config.features.maxTeamsPerOrganization < 1) {
      errors.push('Maximum teams per organization must be at least 1');
    }

    if (config.features.maxAssessmentsPerMonth < 1) {
      errors.push('Maximum assessments per month must be at least 1');
    }

    // Validate security settings
    if (config.security.sessionTimeout < 300000) { // 5 minutes minimum
      errors.push('Session timeout must be at least 5 minutes');
    }

    if (config.security.passwordPolicy.minLength < 6) {
      errors.push('Password minimum length must be at least 6 characters');
    }

    // Validate compliance settings
    if (config.compliance.dataRetentionPeriod < 365) {
      errors.push('Data retention period must be at least 1 year');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Organizational templates for different types of organizations
export const ORGANIZATION_TEMPLATES = {
  startup: {
    name: 'Startup Template',
    description: 'Optimized for small, agile teams with rapid growth',
    features: {
      enableTeamManagement: true,
      enableReports: true,
      enableAssessments: true,
      enableMonthlyDashboards: false, // Keep it simple
      enableCustomDashboards: false,
      enableAdvancedAnalytics: false,
      enableAPIAccess: false,
      enableSSOIntegration: false,
      enableMultiTenant: false,
      enableCustomBranding: true,
      enableDataExport: false,
      enableAuditReports: false,
      maxUsersPerTeam: 15,
      maxTeamsPerOrganization: 10,
      maxAssessmentsPerMonth: 100,
    },
    security: {
      sessionTimeout: 7200000, // 2 hours
      maxLoginAttempts: 3,
      requireMFA: false,
      passwordPolicy: {
        minLength: 6,
        requireSpecialChars: false,
        requireNumbers: true,
        requireUppercase: false,
      },
    },
  },
  enterprise: {
    name: 'Enterprise Template',
    description: 'Full-featured setup for large organizations',
    features: {
      enableTeamManagement: true,
      enableReports: true,
      enableAssessments: true,
      enableMonthlyDashboards: true,
      enableCustomDashboards: true,
      enableAdvancedAnalytics: true,
      enableAPIAccess: true,
      enableSSOIntegration: true,
      enableMultiTenant: true,
      enableCustomBranding: true,
      enableDataExport: true,
      enableAuditReports: true,
      maxUsersPerTeam: 100,
      maxTeamsPerOrganization: 500,
      maxAssessmentsPerMonth: 10000,
    },
    security: {
      sessionTimeout: 1800000, // 30 minutes
      maxLoginAttempts: 3,
      requireMFA: true,
      passwordPolicy: {
        minLength: 12,
        requireSpecialChars: true,
        requireNumbers: true,
        requireUppercase: true,
      },
    },
  },
  government: {
    name: 'Government/Public Sector Template',
    description: 'High security and compliance for government organizations',
    features: {
      enableTeamManagement: true,
      enableReports: true,
      enableAssessments: true,
      enableMonthlyDashboards: true,
      enableCustomDashboards: false, // Standardized dashboards
      enableAdvancedAnalytics: true,
      enableAPIAccess: false, // Restricted for security
      enableSSOIntegration: true,
      enableMultiTenant: false,
      enableCustomBranding: false, // Standardized branding
      enableDataExport: true,
      enableAuditReports: true,
      maxUsersPerTeam: 25,
      maxTeamsPerOrganization: 200,
      maxAssessmentsPerMonth: 5000,
    },
    security: {
      sessionTimeout: 900000, // 15 minutes
      maxLoginAttempts: 2,
      requireMFA: true,
      passwordPolicy: {
        minLength: 14,
        requireSpecialChars: true,
        requireNumbers: true,
        requireUppercase: true,
      },
    },
    compliance: {
      enableAuditLogging: true,
      dataRetentionPeriod: 3650, // 10 years
      enableGDPRCompliance: true,
      enableNIS2Compliance: true,
    },
  },
};

// Export singleton instance
export const configManager = ConfigurationManager.getInstance();

// Export utility functions
export const getConfig = () => configManager.getConfig();
export const isFeatureEnabled = (feature: keyof OrganizationConfig['features']) =>
  configManager.isFeatureEnabled(feature);
export const isWidgetEnabled = (widgetId: string) => configManager.isWidgetEnabled(widgetId);
export const getRefreshInterval = () => configManager.getRefreshInterval();
export const getBrandingColors = () => configManager.getBrandingColors();
export const isComplianceEnabled = (type: 'audit' | 'gdpr' | 'nis2') =>
  configManager.isComplianceEnabled(type);
