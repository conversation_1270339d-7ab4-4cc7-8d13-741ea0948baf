// 🔐 NIS2-COMPLIANT: Dashboard navigation utilities
// Provides consistent navigation patterns across dashboard components

export interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon?: string;
  description?: string;
  requiresAuth?: boolean;
  roles?: string[];
}

export interface DashboardRoute {
  assessments: string;
  analytics: string;
  organization: string;
  reports: string;
  settings: string;
  profile: string;
  help: string;
}

// Define dashboard routes
export const DASHBOARD_ROUTES: DashboardRoute = {
  assessments: '/assessments',
  analytics: '/analytics',
  organization: '/organization',
  reports: '/reports',
  settings: '/settings',
  profile: '/profile',
  help: '/help'
};

// Navigation items for different user roles
export const NAVIGATION_ITEMS: Record<string, NavigationItem[]> = {
  employee: [
    {
      id: 'my-assessments',
      label: 'My Assessments',
      path: DASHBOARD_ROUTES.assessments,
      description: 'View and complete your assessments',
      requiresAuth: true
    },
    {
      id: 'my-reports',
      label: 'My Reports',
      path: DASHBOARD_ROUTES.reports,
      description: 'View your performance reports',
      requiresAuth: true
    },
    {
      id: 'profile',
      label: 'Profile',
      path: DASHBOARD_ROUTES.profile,
      description: 'Manage your profile settings',
      requiresAuth: true
    }
  ],
  manager: [
    {
      id: 'team-assessments',
      label: 'Team Assessments',
      path: DASHBOARD_ROUTES.assessments,
      description: 'Manage team assessments',
      requiresAuth: true,
      roles: ['manager', 'admin']
    },
    {
      id: 'team-analytics',
      label: 'Team Analytics',
      path: DASHBOARD_ROUTES.analytics,
      description: 'View team performance analytics',
      requiresAuth: true,
      roles: ['manager', 'admin']
    },
    {
      id: 'organization',
      label: 'Organization',
      path: DASHBOARD_ROUTES.organization,
      description: 'Manage organization structure',
      requiresAuth: true,
      roles: ['manager', 'admin']
    }
  ],
  admin: [
    {
      id: 'all-assessments',
      label: 'All Assessments',
      path: DASHBOARD_ROUTES.assessments,
      description: 'Manage all assessments',
      requiresAuth: true,
      roles: ['admin']
    },
    {
      id: 'system-analytics',
      label: 'System Analytics',
      path: DASHBOARD_ROUTES.analytics,
      description: 'View system-wide analytics',
      requiresAuth: true,
      roles: ['admin']
    },
    {
      id: 'organization-management',
      label: 'Organization Management',
      path: DASHBOARD_ROUTES.organization,
      description: 'Full organization management',
      requiresAuth: true,
      roles: ['admin']
    },
    {
      id: 'system-settings',
      label: 'System Settings',
      path: DASHBOARD_ROUTES.settings,
      description: 'Configure system settings',
      requiresAuth: true,
      roles: ['admin']
    }
  ]
};

/**
 * Get navigation items for a specific user role
 */
export const getNavigationForRole = (role: string): NavigationItem[] => {
  return NAVIGATION_ITEMS[role] || NAVIGATION_ITEMS.employee;
};

/**
 * Check if user has access to a specific route
 */
export const hasAccessToRoute = (route: string, userRole: string, userRoles: string[] = []): boolean => {
  const allNavItems = Object.values(NAVIGATION_ITEMS).flat();
  const navItem = allNavItems.find(item => item.path === route);
  
  if (!navItem) return false;
  if (!navItem.requiresAuth) return true;
  if (!navItem.roles) return true;
  
  return navItem.roles.some(role => userRoles.includes(role) || userRole === role);
};

/**
 * Navigate to a dashboard section with proper validation
 */
export const navigateToDashboard = (
  route: string, 
  userRole: string, 
  userRoles: string[] = [],
  onNavigate?: (path: string) => void
): boolean => {
  if (!hasAccessToRoute(route, userRole, userRoles)) {
    console.warn(`Access denied to route: ${route} for role: ${userRole}`);
    return false;
  }
  
  if (onNavigate) {
    onNavigate(route);
    return true;
  }
  
  // Fallback to window navigation if no custom handler
  if (typeof window !== 'undefined') {
    window.location.href = route;
    return true;
  }
  
  return false;
};

/**
 * Get dashboard card actions based on user role and data type
 */
export const getDashboardCardActions = (
  dataType: 'assessment' | 'employee' | 'team' | 'report',
  userRole: string,
  onNavigate?: (path: string) => void
) => {
  const actions = [];
  
  switch (dataType) {
    case 'assessment':
      if (['manager', 'admin'].includes(userRole)) {
        actions.push({
          label: 'View Details',
          onClick: () => navigateToDashboard(DASHBOARD_ROUTES.assessments, userRole, [userRole], onNavigate)
        });
        actions.push({
          label: 'Analytics',
          onClick: () => navigateToDashboard(DASHBOARD_ROUTES.analytics, userRole, [userRole], onNavigate)
        });
      } else {
        actions.push({
          label: 'View My Assessments',
          onClick: () => navigateToDashboard(DASHBOARD_ROUTES.assessments, userRole, [userRole], onNavigate)
        });
      }
      break;
      
    case 'employee':
      if (['manager', 'admin'].includes(userRole)) {
        actions.push({
          label: 'Manage Team',
          onClick: () => navigateToDashboard(DASHBOARD_ROUTES.organization, userRole, [userRole], onNavigate)
        });
      }
      break;
      
    case 'team':
      if (['manager', 'admin'].includes(userRole)) {
        actions.push({
          label: 'Team Analytics',
          onClick: () => navigateToDashboard(DASHBOARD_ROUTES.analytics, userRole, [userRole], onNavigate)
        });
        actions.push({
          label: 'Manage Team',
          onClick: () => navigateToDashboard(DASHBOARD_ROUTES.organization, userRole, [userRole], onNavigate)
        });
      }
      break;
      
    case 'report':
      actions.push({
        label: 'View Reports',
        onClick: () => navigateToDashboard(DASHBOARD_ROUTES.reports, userRole, [userRole], onNavigate)
      });
      break;
  }
  
  return actions;
};

/**
 * Get contextual tooltip for dashboard cards
 */
export const getDashboardCardTooltip = (
  dataType: 'assessment' | 'employee' | 'team' | 'report',
  userRole: string
): string => {
  const tooltips = {
    assessment: {
      employee: 'Click to view your assessment history and progress',
      manager: 'Click to view team assessment overview and manage assessments',
      admin: 'Click to view system-wide assessment analytics and management'
    },
    employee: {
      employee: 'Your personal performance overview',
      manager: 'Click to view and manage your team members',
      admin: 'Click to view organization-wide employee management'
    },
    team: {
      employee: 'Your team performance overview',
      manager: 'Click to view detailed team analytics and management',
      admin: 'Click to view organization structure and team management'
    },
    report: {
      employee: 'Click to view your performance reports',
      manager: 'Click to view team performance reports',
      admin: 'Click to view system-wide reports and analytics'
    }
  };
  
  return tooltips[dataType]?.[userRole as keyof typeof tooltips[typeof dataType]] || 'Click for more details';
};
