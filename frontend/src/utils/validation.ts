// 🔐 SECURITY: Comprehensive client-side validation utilities
// These validations mirror the backend validation rules for consistency

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface OrganizationalUnitValidation {
  name: string;
  type: string;
  description?: string;
  parentId?: number;
  managerId?: number;
  budget?: number;
}

export interface UserValidation {
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  title?: string;
  phone?: string;
  organizationalUnitId?: number;
  managerId?: number;
}

// 🔐 SECURITY: Organizational Unit Validation
export const validateOrganizationalUnit = (data: OrganizationalUnitValidation): ValidationResult => {
  const errors: string[] = [];

  // Name validation
  if (!data.name || data.name.trim().length === 0) {
    errors.push('Organization name is required');
  } else if (data.name.trim().length < 2) {
    errors.push('Organization name must be at least 2 characters long');
  } else if (data.name.trim().length > 100) {
    errors.push('Organization name cannot exceed 100 characters');
  } else if (!/^[a-zA-Z0-9\s\-_&.()]+$/.test(data.name.trim())) {
    errors.push('Organization name can only contain letters, numbers, spaces, and basic punctuation (-, _, &, ., ())');
  }

  // Type validation
  const validTypes = ['organization', 'division', 'department', 'team', 'squad', 'unit'];
  if (!data.type || !validTypes.includes(data.type)) {
    errors.push('Organization type must be one of: organization, division, department, team, squad, unit');
  }

  // Description validation
  if (data.description && data.description.length > 500) {
    errors.push('Description cannot exceed 500 characters');
  }

  // Parent ID validation
  if (data.parentId !== undefined && data.parentId !== null) {
    if (!Number.isInteger(data.parentId) || data.parentId < 1) {
      errors.push('Parent ID must be a positive integer');
    }
  }

  // Manager ID validation
  if (data.managerId !== undefined && data.managerId !== null) {
    if (!Number.isInteger(data.managerId) || data.managerId < 1) {
      errors.push('Manager ID must be a positive integer');
    }
  }

  // Budget validation
  if (data.budget !== undefined && data.budget !== null) {
    if (typeof data.budget !== 'number' || isNaN(data.budget)) {
      errors.push('Budget must be a valid number');
    } else if (data.budget < 0) {
      errors.push('Budget cannot be negative');
    } else if (data.budget > 999999999.99) {
      errors.push('Budget cannot exceed 999,999,999.99');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// 🔐 SECURITY: User Validation
export const validateUser = (data: UserValidation): ValidationResult => {
  const errors: string[] = [];

  // First name validation
  if (!data.firstName || data.firstName.trim().length === 0) {
    errors.push('First name is required');
  } else if (data.firstName.trim().length < 2) {
    errors.push('First name must be at least 2 characters long');
  } else if (data.firstName.trim().length > 50) {
    errors.push('First name cannot exceed 50 characters');
  } else if (!/^[a-zA-Z\s\-']+$/.test(data.firstName.trim())) {
    errors.push('First name can only contain letters, spaces, hyphens, and apostrophes');
  }

  // Last name validation
  if (!data.lastName || data.lastName.trim().length === 0) {
    errors.push('Last name is required');
  } else if (data.lastName.trim().length < 2) {
    errors.push('Last name must be at least 2 characters long');
  } else if (data.lastName.trim().length > 50) {
    errors.push('Last name cannot exceed 50 characters');
  } else if (!/^[a-zA-Z\s\-']+$/.test(data.lastName.trim())) {
    errors.push('Last name can only contain letters, spaces, hyphens, and apostrophes');
  }

  // Email validation
  if (!data.email || data.email.trim().length === 0) {
    errors.push('Email is required');
  } else {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(data.email.trim())) {
      errors.push('Please enter a valid email address');
    } else if (data.email.trim().length > 255) {
      errors.push('Email cannot exceed 255 characters');
    }
  }

  // Role validation
  const validRoles = ['ceo', 'vp', 'director', 'manager', 'senior_engineer', 'engineer', 'junior_engineer', 'intern', 'hr_admin', 'guest'];
  if (!data.role || !validRoles.includes(data.role)) {
    errors.push('Please select a valid role');
  }

  // Title validation
  if (data.title && data.title.length > 100) {
    errors.push('Job title cannot exceed 100 characters');
  }

  // Phone validation
  if (data.phone && data.phone.trim().length > 0) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(data.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.push('Please enter a valid phone number');
    }
  }

  // Organizational unit ID validation
  if (data.organizationalUnitId !== undefined && data.organizationalUnitId !== null) {
    if (!Number.isInteger(data.organizationalUnitId) || data.organizationalUnitId < 1) {
      errors.push('Organizational unit ID must be a positive integer');
    }
  }

  // Manager ID validation
  if (data.managerId !== undefined && data.managerId !== null) {
    if (!Number.isInteger(data.managerId) || data.managerId < 1) {
      errors.push('Manager ID must be a positive integer');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// 🔐 SECURITY: Sanitize input to prevent XSS
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000); // Limit length
};

// 🔐 SECURITY: Validate file uploads
export const validateFileUpload = (file: File, allowedTypes: string[], maxSize: number): ValidationResult => {
  const errors: string[] = [];

  if (!file) {
    errors.push('File is required');
    return { isValid: false, errors };
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size ${(file.size / 1024 / 1024).toFixed(2)}MB exceeds maximum allowed size ${(maxSize / 1024 / 1024).toFixed(2)}MB`);
  }

  // Check file name
  if (!/^[a-zA-Z0-9\-_. ]+$/.test(file.name)) {
    errors.push('File name contains invalid characters. Only letters, numbers, spaces, hyphens, underscores, and dots are allowed');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// 🔐 SECURITY: Rate limiting helper for client-side
export class ClientRateLimiter {
  private requests: Map<string, number[]> = new Map();
  private readonly windowMs: number;
  private readonly maxRequests: number;

  constructor(windowMs: number = 60000, maxRequests: number = 10) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
  }

  canMakeRequest(key: string): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    if (validRequests.length >= this.maxRequests) {
      return false;
    }

    // Add current request
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }

  getRemainingRequests(key: string): number {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    const validRequests = requests.filter(time => now - time < this.windowMs);
    
    return Math.max(0, this.maxRequests - validRequests.length);
  }
}
