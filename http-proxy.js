const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const PORT = 80;

// Middleware to log requests
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Proxy API requests to backend (NestJS on port 4000)
app.use('/api', createProxyMiddleware({
  target: 'http://localhost:4000',
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying
  logLevel: 'info',
  onError: (err, req, res) => {
    console.error('Backend proxy error:', err.message);
    res.status(500).json({ error: 'Backend service unavailable' });
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`Proxying API request: ${req.method} ${req.url} -> http://localhost:4000${req.url}`);
  }
}));

// Proxy all other requests to frontend (React on port 3080)
app.use('/', createProxyMiddleware({
  target: 'http://localhost:3080',
  changeOrigin: true,
  ws: true, // Enable WebSocket proxying for React hot reload
  logLevel: 'info',
  onError: (err, req, res) => {
    console.error('Frontend proxy error:', err.message);
    res.status(500).send(`
      <html>
        <body>
          <h1>Frontend Service Unavailable</h1>
          <p>The React development server is not running.</p>
          <p>Please make sure the frontend is started on port 3080.</p>
          <p>Error: ${err.message}</p>
        </body>
      </html>
    `);
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log(`Proxying frontend request: ${req.method} ${req.url} -> http://localhost:3080${req.url}`);
  }
}));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start the proxy server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 eHRx HTTP Proxy Server running on port ${PORT}`);
  console.log(`🔗 Access your application at: http://dev.trusthansen.dk/`);
  console.log(`📡 Proxying API requests to: http://localhost:4000`);
  console.log(`🌐 Proxying frontend requests to: http://localhost:3080`);
  console.log('');
  console.log('Make sure both services are running:');
  console.log('  - Backend (NestJS): http://localhost:4000');
  console.log('  - Frontend (React): http://localhost:3080');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully');
  process.exit(0);
});
