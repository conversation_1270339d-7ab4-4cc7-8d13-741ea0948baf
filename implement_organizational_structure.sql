-- Implementation of the Organizational Structure
-- This script creates the complete organizational hierarchy as specified

-- First, clear existing organizational units (keeping the table structure)
DELETE FROM organizational_units;

-- Reset the sequence for clean IDs
ALTER SEQUENCE organizational_units_id_seq RESTART WITH 1;

-- Create users for all managers mentioned in the organizational structure
-- Using a consistent password hash for development (password: TempPass123!)
INSERT INTO users (email, password, first_name, last_name, title, role, is_active, account_status, must_change_password, failed_login_attempts) VALUES
-- Top Level Management
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'KARS', 'Manager', 'Managing Director', 'ceo', TRUE, 'active', TRUE, 0),

-- Directors Level
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<PERSON><PERSON> Imperial', 'Ong', 'Core Services & Digital Excellence, Associate Vice President (AVP)', 'director', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Tina Hernandez', 'Nanquil', 'Network & Cybersecurity Operations Director', 'director', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Johanna Concepcion Jover', 'Almazar', 'People & Culture Director', 'director', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Krezen', 'Angeles', 'Financial Controlling & Reporting Director', 'director', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Aldwin', 'Enriquez', 'Cloud Native Solutions Director', 'director', TRUE, 'active', TRUE, 0),

-- Operational Team Managers under CHOI
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'KADQ', 'Manager', '2nd Line Global & Incident Problem Management Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'MVBC', 'Manager', 'Patch Planning, Server Order & Change Management Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'FRGU', 'Manager', 'Automation & AI Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'TONY', 'Manager', 'Data Center Services, Unix/Linux & Database Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'DOYP', 'Manager', 'Project Services/Enterprise Technology Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'VLNG', 'Manager', 'Consulting & Advisory Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'CTHE', 'Manager', 'Consulting & Advisory Co-Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'VMHE', 'Manager', 'Commercial Manager', 'manager', TRUE, 'active', TRUE, 0),

-- Operational Team Managers under THNQ
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'EATS', 'Manager', 'IAM Operations & Security Operations Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'EZEK', 'Manager', 'Network Operations & NetScaler Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'SAVI', 'Manager', 'Virtual Workplace & Endpoint Management Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'RLLT', 'Manager', 'Service Desk Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'GLIC', 'Manager', 'Digital Workplace Manager', 'manager', TRUE, 'active', TRUE, 0),

-- Operational Team Managers under ALDW
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'VMIM', 'Manager', 'AIS Operations & SAP Basis Manager', 'manager', TRUE, 'active', TRUE, 0),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'MRAE', 'Manager', 'Windows Services & Cloud Infra Manager', 'manager', TRUE, 'active', TRUE, 0)

ON CONFLICT (email) DO UPDATE SET
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    title = EXCLUDED.title,
    role = EXCLUDED.role;

-- Now create the organizational structure
-- Level 0: Root Organization
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(1, 'EHRX Corporation', 'organization', 'Main organization managed by Managing Director KARS', NULL, 0, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 50000000.00, TRUE);

-- Level 1: Directors under Managing Director
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(2, 'Core Services & Digital Excellence', 'division', 'Associate Vice President (AVP) Chiqui Imperial Ong (CHOI)', 1, 1, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 15000000.00, TRUE),
(3, 'Network & Cybersecurity Operations', 'division', 'Director THNQ (Tina Hernandez Nanquil)', 1, 1, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 12000000.00, TRUE),
(4, 'People & Culture', 'division', 'Director JZAR (Johanna Concepcion Jover Almazar)', 1, 1, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 8000000.00, TRUE),
(5, 'Financial Controlling & Reporting', 'division', 'Director KREZ (Krezen Angeles)', 1, 1, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 10000000.00, TRUE),
(6, 'Cloud Native Solutions', 'division', 'Director ALDW (Aldwin Enriquez)', 1, 1, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 5000000.00, TRUE);

-- Level 2: Operational Teams under CHOI (Core Services & Digital Excellence)
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(7, '2nd Line Global', 'department', 'Manager KADQ', 2, 2, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 2000000.00, TRUE),
(8, 'Patch Planning and Server Order', 'department', 'Manager MVBC', 2, 2, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1500000.00, TRUE),
(9, 'Change Management, SR, CMS', 'department', 'Manager MVBC', 2, 2, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1200000.00, TRUE),
(10, 'Incident & Problem Management', 'department', 'Manager KADQ', 2, 2, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1800000.00, TRUE),
(11, 'Automation & AI', 'department', 'Manager FRGU', 2, 2, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 2500000.00, TRUE),
(12, 'Data Center Services', 'department', 'Manager TONY', 2, 2, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 3000000.00, TRUE),
(13, 'Project Services/Enterprise Technology', 'department', 'Manager DOYP', 2, 2, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 2200000.00, TRUE),
(14, 'Consulting & Advisory (Network & Cybersec)', 'department', 'Managers VLNG/CTHE', 2, 2, 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1800000.00, TRUE),
(15, 'Commercial', 'department', 'Manager VMHE', 2, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1000000.00, TRUE);

-- Level 2: Operational Teams under THNQ (Network & Cybersecurity Operations)
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(16, 'IAM Operations', 'department', 'Manager EATS', 3, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1500000.00, TRUE),
(17, 'Security Operations', 'department', 'Manager EATS', 3, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 2000000.00, TRUE),
(18, 'Network Operations & NetScaler', 'department', 'Manager EZEK', 3, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 2500000.00, TRUE),
(19, 'Virtual Workplace', 'department', 'Manager SAVI', 3, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1800000.00, TRUE),
(20, 'Endpoint Management', 'department', 'Manager SAVI', 3, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1200000.00, TRUE),
(21, 'Service Desk', 'department', 'Manager RLLT', 3, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1500000.00, TRUE),
(22, 'Digital Workplace', 'department', 'Manager GLIC', 3, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1500000.00, TRUE);

-- Level 2: Operational Teams under ALDW (Cloud Native Solutions)
INSERT INTO organizational_units (id, name, type, description, parent_id, level, manager_id, budget, is_active) VALUES
(23, 'AIS Operations', 'department', 'Manager VMIM', 6, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1200000.00, TRUE),
(24, 'Windows Services', 'department', 'Manager MRAE', 6, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1000000.00, TRUE),
(25, 'Unix/Linux Services', 'department', 'Manager TONY', 6, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1500000.00, TRUE),
(26, 'SAP Basis', 'department', 'Manager VMIM', 6, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 800000.00, TRUE),
(27, 'Cloud Infra', 'department', 'Manager MRAE', 6, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1200000.00, TRUE),
(28, 'Database', 'department', 'Manager TONY', 6, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 1000000.00, TRUE),
(29, 'Hypervisor Services', 'department', 'Manager TONY', 6, 2,
 (SELECT id FROM users WHERE email = '<EMAIL>'), 800000.00, TRUE);

-- Update the organizational_units sequence to continue from the next available ID
SELECT setval('organizational_units_id_seq', (SELECT MAX(id) FROM organizational_units));

-- Update users to assign them to their respective organizational units
UPDATE users SET organizational_unit_id = 1 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 2 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 3 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 4 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 5 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 6 WHERE email = '<EMAIL>';

-- Assign operational managers to their units
UPDATE users SET organizational_unit_id = 7 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 8 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 11 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 12 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 13 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 14 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 14 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 15 WHERE email = '<EMAIL>';

UPDATE users SET organizational_unit_id = 16 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 18 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 19 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 21 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 22 WHERE email = '<EMAIL>';

UPDATE users SET organizational_unit_id = 23 WHERE email = '<EMAIL>';
UPDATE users SET organizational_unit_id = 24 WHERE email = '<EMAIL>';

-- Set up manager relationships in users table
UPDATE users SET manager_id = (SELECT id FROM users WHERE email = '<EMAIL>')
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');

UPDATE users SET manager_id = (SELECT id FROM users WHERE email = '<EMAIL>')
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');

UPDATE users SET manager_id = (SELECT id FROM users WHERE email = '<EMAIL>')
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');

UPDATE users SET manager_id = (SELECT id FROM users WHERE email = '<EMAIL>')
WHERE email IN ('<EMAIL>', '<EMAIL>');
