#!/bin/bash

# Set PostgreSQL password to avoid prompts
export PGPASSWORD="EhrX2024!SecurePass"

echo "🔄 Starting database import process..."

# 1. Backup current users table
echo "📦 Backing up current users table..."
psql -h localhost -U ehrx_user -d ehrx -f backup_current_users.sql

# 2. Import the converted database
echo "📥 Importing converted database..."
psql -h localhost -U ehrx_user -d ehrx -f converted_postgresql_dump.sql

# 3. Restore our working admin user
echo "👤 Restoring working admin user..."
psql -h localhost -U ehrx_user -d ehrx -c "
INSERT INTO users (email, password, first_name, last_name, role, is_active, account_status, must_change_password, failed_login_attempts)
SELECT email, password, first_name, last_name, role, is_active, account_status, must_change_password, failed_login_attempts
FROM users_backup 
WHERE email = '<EMAIL>'
ON CONFLICT (email) DO UPDATE SET
  password = EXCLUDED.password,
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name,
  role = EXCLUDED.role,
  is_active = EXCLUDED.is_active,
  account_status = EXCLUDED.account_status,
  must_change_password = EXCLUDED.must_change_password,
  failed_login_attempts = EXCLUDED.failed_login_attempts;
"

# 4. Check final status
echo "✅ Checking final database status..."
psql -h localhost -U ehrx_user -d ehrx -c "\dt" | wc -l
psql -h localhost -U ehrx_user -d ehrx -c "SELECT COUNT(*) as total_users FROM users;"

echo "🎉 Database import completed!"
