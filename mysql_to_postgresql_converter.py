#!/usr/bin/env python3
"""
MySQL to PostgreSQL Database Converter
Converts MySQL dump to PostgreSQL compatible format
"""

import re
import sys

def convert_mysql_to_postgresql(input_file, output_file):
    """Convert MySQL dump to PostgreSQL format"""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remove MySQL specific comments and settings
    content = re.sub(r'/\*![0-9]+.*?\*/;', '', content, flags=re.DOTALL)
    content = re.sub(r'SET @.*?;', '', content)
    content = re.sub(r'SET @@.*?;', '', content)
    content = re.sub(r'LOCK TABLES.*?;', '', content)
    content = re.sub(r'UNLOCK TABLES;', '', content)
    
    # Convert data types
    content = re.sub(r'\bint\b(?!\s+NOT\s+NULL\s+AUTO_INCREMENT)', 'INTEGER', content, flags=re.IGNORECASE)
    content = re.sub(r'\bint\s+NOT\s+NULL\s+AUTO_INCREMENT', 'SERIAL', content, flags=re.IGNORECASE)
    content = re.sub(r'\btinyint\(1\)', 'BOOLEAN', content, flags=re.IGNORECASE)
    content = re.sub(r'\btinyint', 'SMALLINT', content, flags=re.IGNORECASE)
    content = re.sub(r'\bbigint\b(?!\s+NOT\s+NULL\s+AUTO_INCREMENT)', 'BIGINT', content, flags=re.IGNORECASE)
    content = re.sub(r'\bbigint\s+NOT\s+NULL\s+AUTO_INCREMENT', 'BIGSERIAL', content, flags=re.IGNORECASE)
    content = re.sub(r'\bdatetime\(6\)', 'TIMESTAMP', content, flags=re.IGNORECASE)
    content = re.sub(r'\bdatetime\b', 'TIMESTAMP', content, flags=re.IGNORECASE)
    content = re.sub(r'\btext\b', 'TEXT', content, flags=re.IGNORECASE)
    content = re.sub(r'\blongtext\b', 'TEXT', content, flags=re.IGNORECASE)
    content = re.sub(r'\bmediumtext\b', 'TEXT', content, flags=re.IGNORECASE)
    
    # Convert ENUM types - extract and create separate type definitions
    enum_types = {}
    enum_pattern = r'`(\w+)`\s+enum\((.*?)\)'
    
    def extract_enum(match):
        column_name = match.group(1)
        enum_values = match.group(2)
        type_name = f"{column_name}_type"
        enum_types[type_name] = enum_values
        return f'"{column_name}" {type_name}'
    
    content = re.sub(enum_pattern, extract_enum, content, flags=re.IGNORECASE)
    
    # Convert backticks to double quotes
    content = re.sub(r'`([^`]+)`', r'"\1"', content)
    
    # Convert AUTO_INCREMENT to SERIAL (already handled above)
    content = re.sub(r'\s+AUTO_INCREMENT', '', content, flags=re.IGNORECASE)
    
    # Convert ENGINE and CHARSET specifications
    content = re.sub(r'\s*ENGINE=\w+.*?;', ';', content, flags=re.IGNORECASE)
    
    # Convert DEFAULT CURRENT_TIMESTAMP
    content = re.sub(r'DEFAULT CURRENT_TIMESTAMP\(6\)', 'DEFAULT CURRENT_TIMESTAMP', content, flags=re.IGNORECASE)
    content = re.sub(r'ON UPDATE CURRENT_TIMESTAMP\(6\)', '', content, flags=re.IGNORECASE)
    content = re.sub(r'ON UPDATE CURRENT_TIMESTAMP', '', content, flags=re.IGNORECASE)
    
    # Fix PRIMARY INDEX to PRIMARY KEY
    content = re.sub(r'PRIMARY INDEX\s*\("([^"]+)"\)', r'PRIMARY KEY ("\1")', content, flags=re.IGNORECASE)

    # Fix FOREIGN INDEX to FOREIGN KEY
    content = re.sub(r'FOREIGN INDEX\s*\("([^"]+)"\)', r'FOREIGN KEY ("\1")', content, flags=re.IGNORECASE)

    # Remove MySQL specific index syntax and convert to PostgreSQL
    content = re.sub(r',\s*INDEX\s+"([^"]+)"\s*\([^)]+\)', '', content, flags=re.IGNORECASE)
    content = re.sub(r',\s*KEY\s+"([^"]+)"\s*\([^)]+\)', '', content, flags=re.IGNORECASE)

    # Fix UNIQUE INDEX syntax issues
    content = re.sub(r'UNIQUE INDEX\s+"([^"]+)"\s*\("([^"]+)"\)', r'UNIQUE ("\2")', content, flags=re.IGNORECASE)
    content = re.sub(r'UNIQUE INDEX\s+"([^"]+)"\s*\("([^"]+)","([^"]+)"\)', r'UNIQUE ("\2", "\3")', content, flags=re.IGNORECASE)

    # Fix malformed UNIQUE INDEX with multiple closing parentheses
    content = re.sub(r'UNIQUE INDEX\s+"[^"]+"\s*\("([^"]+)"\)[^,;]*\),\s*\("([^"]+)"\)[^,;]*\),', r'UNIQUE ("\1"), UNIQUE ("\2"),', content, flags=re.IGNORECASE)
    
    # Handle INSERT statements - convert MySQL specific syntax
    content = re.sub(r'INSERT INTO\s+"([^"]+)"\s+VALUES', r'INSERT INTO "\1" VALUES', content, flags=re.IGNORECASE)
    
    # Convert boolean values
    content = re.sub(r'\b1\b(?=\s*[,)])', 'TRUE', content)
    content = re.sub(r'\b0\b(?=\s*[,)])', 'FALSE', content)
    
    # Create ENUM type definitions at the beginning
    enum_definitions = []
    for type_name, enum_values in enum_types.items():
        enum_definitions.append(f"CREATE TYPE {type_name} AS ENUM ({enum_values});")
    
    # Combine everything
    if enum_definitions:
        enum_section = "\n-- ENUM Type Definitions\n" + "\n".join(enum_definitions) + "\n\n"
        content = enum_section + content
    
    # Write the converted content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- Converted from MySQL to PostgreSQL\n")
        f.write("-- Note: Manual review and testing recommended\n\n")
        f.write(content)
    
    print(f"Conversion completed: {input_file} -> {output_file}")
    print(f"Created {len(enum_types)} ENUM types")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python3 mysql_to_postgresql_converter.py input.sql output.sql")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    convert_mysql_to_postgresql(input_file, output_file)
