-- Monthly Dashboards Schema Extension
-- This file adds the database structure for the Monthly Dashboards feature
-- Designed to work with existing organizational_units and users tables

USE ehrx;

-- =====================================================
-- MONTHLY DASHBOARD KPI CONFIGURATION
-- =====================================================

-- Table for configurable KPI definitions
CREATE TABLE IF NOT EXISTS monthly_dashboard_kpis (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT 'KPI name (e.g., FTE, Attrition, SLA)',
  display_name VARCHAR(150) NOT NULL COMMENT 'User-friendly display name',
  description TEXT COMMENT 'Detailed description of the KPI',
  target_value DECIMAL(10,4) DEFAULT NULL COMMENT 'Default target value for this KPI',
  unit VARCHAR(50) DEFAULT NULL COMMENT 'Unit of measurement (%, count, days, etc.)',
  calculation_method ENUM('manual', 'auto_count', 'auto_percentage', 'auto_fte') DEFAULT 'manual' COMMENT 'How the KPI is calculated',
  help_text TEXT NOT NULL COMMENT 'Help text shown to team managers when filling data',
  is_active BOOLEAN DEFAULT TRUE COMMENT 'Whether this KPI is currently in use',
  sort_order INT DEFAULT 0 COMMENT 'Display order in the dashboard',
  
  -- Traffic light configuration (percentage deviation from target)
  traffic_light_green_min DECIMAL(5,2) DEFAULT -5.0 COMMENT 'Green zone minimum (% deviation)',
  traffic_light_green_max DECIMAL(5,2) DEFAULT 5.0 COMMENT 'Green zone maximum (% deviation)',
  traffic_light_yellow_min DECIMAL(5,2) DEFAULT -10.0 COMMENT 'Yellow zone minimum (% deviation)',
  traffic_light_yellow_max DECIMAL(5,2) DEFAULT 10.0 COMMENT 'Yellow zone maximum (% deviation)',
  
  -- Special rules for specific KPIs
  special_rules JSON DEFAULT NULL COMMENT 'Special calculation or validation rules',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_active_sort (is_active, sort_order),
  INDEX idx_calculation_method (calculation_method)
) ENGINE=InnoDB COMMENT='Configurable KPI definitions for monthly dashboards';

-- =====================================================
-- MONTHLY DASHBOARD SUBMISSIONS
-- =====================================================

-- Table for monthly dashboard submissions by teams
CREATE TABLE IF NOT EXISTS monthly_dashboard_submissions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  organizational_unit_id INT NOT NULL COMMENT 'Team/division submitting the dashboard',
  submitted_by_user_id INT NOT NULL COMMENT 'User who submitted the dashboard',
  submission_month INT NOT NULL COMMENT 'Month (1-12)',
  submission_year INT NOT NULL COMMENT 'Year (e.g., 2025)',
  submission_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'When the dashboard was submitted',
  completion_date DATE DEFAULT NULL COMMENT 'Date when team manager completed the dashboard',
  status ENUM('draft', 'submitted', 'approved', 'rejected') DEFAULT 'draft' COMMENT 'Submission status',
  notes TEXT DEFAULT NULL COMMENT 'Additional notes or comments',
  
  -- Audit fields
  approved_by_user_id INT DEFAULT NULL COMMENT 'User who approved the submission',
  approved_at TIMESTAMP DEFAULT NULL COMMENT 'When the submission was approved',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
  FOREIGN KEY (submitted_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (approved_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
  
  UNIQUE KEY unique_team_month_year (organizational_unit_id, submission_month, submission_year),
  INDEX idx_submission_period (submission_year, submission_month),
  INDEX idx_status (status),
  INDEX idx_submitted_by (submitted_by_user_id),
  INDEX idx_org_unit_period (organizational_unit_id, submission_year, submission_month)
) ENGINE=InnoDB COMMENT='Monthly dashboard submissions by teams';

-- =====================================================
-- MONTHLY DASHBOARD KPI VALUES
-- =====================================================

-- Table for actual KPI values submitted by teams
CREATE TABLE IF NOT EXISTS monthly_dashboard_kpi_values (
  id INT AUTO_INCREMENT PRIMARY KEY,
  submission_id INT NOT NULL COMMENT 'Reference to the dashboard submission',
  kpi_id INT NOT NULL COMMENT 'Reference to the KPI definition',
  value DECIMAL(12,4) DEFAULT NULL COMMENT 'Actual KPI value',
  target_value DECIMAL(12,4) DEFAULT NULL COMMENT 'Target value for this team/period',
  notes TEXT DEFAULT NULL COMMENT 'Additional notes about this KPI value',
  
  -- Traffic light status (calculated or manually overridden)
  traffic_light_status ENUM('green', 'yellow', 'red', 'na') DEFAULT 'na' COMMENT 'Visual status indicator',
  traffic_light_override BOOLEAN DEFAULT FALSE COMMENT 'Whether status was manually overridden',
  
  -- Additional data for complex KPIs
  additional_data JSON DEFAULT NULL COMMENT 'Extra data for complex calculations',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (submission_id) REFERENCES monthly_dashboard_submissions(id) ON DELETE CASCADE,
  FOREIGN KEY (kpi_id) REFERENCES monthly_dashboard_kpis(id) ON DELETE CASCADE,
  
  UNIQUE KEY unique_submission_kpi (submission_id, kpi_id),
  INDEX idx_kpi_values (kpi_id, value),
  INDEX idx_traffic_light (traffic_light_status)
) ENGINE=InnoDB COMMENT='Actual KPI values for monthly dashboard submissions';

-- =====================================================
-- TEAM-SPECIFIC KPI TARGETS
-- =====================================================

-- Table for team-specific KPI targets (overrides default targets)
CREATE TABLE IF NOT EXISTS monthly_dashboard_team_targets (
  id INT AUTO_INCREMENT PRIMARY KEY,
  organizational_unit_id INT NOT NULL COMMENT 'Team/division',
  kpi_id INT NOT NULL COMMENT 'KPI definition',
  target_value DECIMAL(12,4) NOT NULL COMMENT 'Team-specific target value',
  effective_from DATE NOT NULL COMMENT 'When this target becomes effective',
  effective_to DATE DEFAULT NULL COMMENT 'When this target expires (NULL = indefinite)',
  notes TEXT DEFAULT NULL COMMENT 'Reason for custom target',
  
  created_by_user_id INT NOT NULL COMMENT 'User who set this target',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
  FOREIGN KEY (kpi_id) REFERENCES monthly_dashboard_kpis(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
  
  INDEX idx_team_kpi_period (organizational_unit_id, kpi_id, effective_from, effective_to),
  INDEX idx_effective_period (effective_from, effective_to)
) ENGINE=InnoDB COMMENT='Team-specific KPI targets that override defaults';

-- =====================================================
-- USER EMPLOYMENT STATUS EXTENSION
-- =====================================================

-- Extend users table for attrition tracking
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS employment_status ENUM('active', 'resigned', 'terminated', 'on_leave', 'transferred') DEFAULT 'active' COMMENT 'Current employment status',
ADD COLUMN IF NOT EXISTS resignation_date DATE DEFAULT NULL COMMENT 'Date of resignation (if applicable)',
ADD COLUMN IF NOT EXISTS last_working_date DATE DEFAULT NULL COMMENT 'Last working date',
ADD COLUMN IF NOT EXISTS resignation_reason TEXT DEFAULT NULL COMMENT 'Reason for resignation',
ADD INDEX IF NOT EXISTS idx_employment_status (employment_status),
ADD INDEX IF NOT EXISTS idx_resignation_date (resignation_date);

-- =====================================================
-- DASHBOARD ACCESS PERMISSIONS
-- =====================================================

-- Table for fine-grained dashboard access control
CREATE TABLE IF NOT EXISTS monthly_dashboard_permissions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL COMMENT 'User with permission',
  organizational_unit_id INT DEFAULT NULL COMMENT 'Specific org unit access (NULL = all)',
  permission_type ENUM('view', 'edit', 'submit', 'approve', 'admin') NOT NULL COMMENT 'Type of permission',
  granted_by_user_id INT NOT NULL COMMENT 'User who granted this permission',
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP DEFAULT NULL COMMENT 'When permission expires (NULL = permanent)',
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id) ON DELETE CASCADE,
  FOREIGN KEY (granted_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
  
  UNIQUE KEY unique_user_orgunit_permission (user_id, organizational_unit_id, permission_type),
  INDEX idx_user_permissions (user_id, permission_type),
  INDEX idx_orgunit_permissions (organizational_unit_id, permission_type)
) ENGINE=InnoDB COMMENT='Fine-grained access control for monthly dashboards';
