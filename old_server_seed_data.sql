USE ehrx;

-- Insert organizational units (infinite hierarchy for IT Outsourcing/Server Hosting company)
INSERT INTO organizational_units (id, name, type, description, parent_id, level, budget) VALUES
-- Root Organization
(1, 'EHRX Corporation', 'organization', 'Enterprise Human Resources Excellence Corporation - IT Outsourcing & Server Hosting', NULL, 0, 45000000.00),

-- Level 1: Divisions
(2, 'Technology Division', 'division', 'Leading digital transformation and technology innovation', 1, 1, 18500000.00),
(3, 'Product Division', 'division', 'Product strategy, design, and user experience', 1, 1, 12800000.00),
(4, 'Operations Division', 'division', 'Business operations, analytics, and process optimization', 1, 1, 13700000.00),

-- Level 2: Departments under Technology Division
(5, 'Engineering Department', 'department', 'Software development, infrastructure, and DevOps', 2, 2, 12000000.00),
(6, 'Data & AI Department', 'department', 'Machine learning, data analytics, and AI research', 2, 2, 6500000.00),

-- Level 2: Departments under Product Division
(7, 'Design Department', 'department', 'UX design, visual design, and design systems', 3, 2, 7200000.00),
(8, 'Product Management Department', 'department', 'Product strategy, market research, and product analytics', 3, 2, 5600000.00),

-- Level 2: Departments under Operations Division
(9, 'Business Operations Department', 'department', 'Process optimization, quality assurance, and project management', 4, 2, 8200000.00),
(10, 'Analytics Department', 'department', 'Business intelligence, performance analytics, and reporting', 4, 2, 5500000.00),

-- Level 3: Teams under Engineering Department
(11, 'Frontend Engineering Team', 'team', 'React, TypeScript, and modern frontend development', 5, 3, 4200000.00),
(12, 'Backend Engineering Team', 'team', 'Node.js, Python, and scalable backend systems', 5, 3, 4800000.00),
(13, 'DevOps & Infrastructure Team', 'team', 'Cloud infrastructure, CI/CD, and system reliability', 5, 3, 3000000.00),

-- Level 3: Teams under Data & AI Department
(14, 'Machine Learning Team', 'team', 'AI research, ML models, and data science', 6, 3, 3600000.00),
(15, 'Data Engineering Team', 'team', 'Data pipelines, ETL, and big data infrastructure', 6, 3, 2900000.00),

-- Level 3: Teams under Design Department
(16, 'UX Research & Design Team', 'team', 'User research, UX design, and usability testing', 7, 3, 3600000.00),
(17, 'Visual & Brand Design Team', 'team', 'Visual design, branding, and creative assets', 7, 3, 3600000.00),

-- Level 3: Teams under Product Management Department
(18, 'Core Product Team', 'team', 'Product roadmap, feature development, and strategy', 8, 3, 2800000.00),
(19, 'Growth & Analytics Team', 'team', 'Growth metrics, A/B testing, and conversion optimization', 8, 3, 2800000.00),

-- Level 3: Teams under Business Operations Department
(20, 'Process Excellence Team', 'team', 'Process automation, quality frameworks, and efficiency', 9, 3, 4100000.00),
(21, 'Project Management Office', 'team', 'Project coordination, resource planning, and portfolio management', 9, 3, 4100000.00),

-- Level 3: Teams under Analytics Department
(22, 'Business Intelligence Team', 'team', 'Executive dashboards, self-service analytics, and data governance', 10, 3, 2750000.00),
(23, 'Performance Analytics Team', 'team', 'Performance KPIs, predictive models, and benchmarking', 10, 3, 2750000.00),

-- Level 4: Squads under Frontend Engineering Team (demonstrating infinite hierarchy)
(24, 'React Development Squad', 'squad', 'React.js applications and component libraries', 11, 4, 2100000.00),
(25, 'Mobile Frontend Squad', 'squad', 'React Native and mobile web applications', 11, 4, 2100000.00),

-- Level 4: Squads under Backend Engineering Team
(26, 'API Development Squad', 'squad', 'RESTful APIs, GraphQL, and microservices', 12, 4, 2400000.00),
(27, 'Database & Performance Squad', 'squad', 'Database optimization, caching, and performance tuning', 12, 4, 2400000.00),

-- Level 4: Squads under DevOps & Infrastructure Team
(28, 'Cloud Infrastructure Squad', 'squad', 'AWS, Azure, and cloud architecture', 13, 4, 1500000.00),
(29, 'CI/CD & Automation Squad', 'squad', 'Build pipelines, deployment automation, and monitoring', 13, 4, 1500000.00);

-- Insert comprehensive skillsets for IT Outsourcing/Server Hosting company
INSERT INTO skillsets (name, category, description, level_required, is_core_skill) VALUES
-- Programming Languages
('JavaScript', 'programming', 'Modern JavaScript (ES6+) for web development', 'intermediate', TRUE),
('TypeScript', 'programming', 'Typed superset of JavaScript for large-scale applications', 'intermediate', TRUE),
('Python', 'programming', 'Versatile programming language for backend, data science, and automation', 'intermediate', TRUE),
('Java', 'programming', 'Enterprise-grade programming language for scalable applications', 'intermediate', FALSE),
('C#', 'programming', '.NET framework development for enterprise applications', 'intermediate', FALSE),
('Go', 'programming', 'Modern systems programming language for cloud-native applications', 'advanced', FALSE),
('Rust', 'programming', 'Systems programming language focused on safety and performance', 'advanced', FALSE),
('PHP', 'programming', 'Server-side scripting language for web development', 'intermediate', FALSE),

-- Frontend Technologies
('React', 'frontend', 'Popular JavaScript library for building user interfaces', 'intermediate', TRUE),
('Vue.js', 'frontend', 'Progressive JavaScript framework for building UIs', 'intermediate', FALSE),
('Angular', 'frontend', 'TypeScript-based web application framework', 'intermediate', FALSE),
('HTML5', 'frontend', 'Latest version of HTML markup language', 'beginner', TRUE),
('CSS3', 'frontend', 'Advanced styling with modern CSS features', 'intermediate', TRUE),
('Sass/SCSS', 'frontend', 'CSS preprocessor for more maintainable stylesheets', 'intermediate', FALSE),
('Webpack', 'frontend', 'Module bundler for JavaScript applications', 'intermediate', FALSE),
('Next.js', 'frontend', 'React framework for production-ready applications', 'advanced', FALSE),

-- Backend Technologies
('Node.js', 'backend', 'JavaScript runtime for server-side development', 'intermediate', TRUE),
('Express.js', 'backend', 'Fast, unopinionated web framework for Node.js', 'intermediate', TRUE),
('NestJS', 'backend', 'Progressive Node.js framework for building scalable server-side applications', 'advanced', FALSE),
('Django', 'backend', 'High-level Python web framework', 'intermediate', FALSE),
('Flask', 'backend', 'Lightweight Python web framework', 'intermediate', FALSE),
('Spring Boot', 'backend', 'Java framework for building microservices', 'advanced', FALSE),
('ASP.NET Core', 'backend', 'Cross-platform framework for building modern web applications', 'advanced', FALSE),

-- Database Technologies
('MySQL', 'database', 'Popular open-source relational database', 'intermediate', TRUE),
('PostgreSQL', 'database', 'Advanced open-source relational database', 'intermediate', TRUE),
('MongoDB', 'database', 'NoSQL document database for modern applications', 'intermediate', FALSE),
('Redis', 'database', 'In-memory data structure store for caching and sessions', 'intermediate', FALSE),
('Elasticsearch', 'database', 'Distributed search and analytics engine', 'advanced', FALSE),
('Oracle Database', 'database', 'Enterprise-grade relational database system', 'advanced', FALSE),

-- Cloud Platforms
('AWS', 'cloud', 'Amazon Web Services cloud platform', 'intermediate', TRUE),
('Azure', 'cloud', 'Microsoft Azure cloud platform', 'intermediate', FALSE),
('Google Cloud Platform', 'cloud', 'Google Cloud Platform services', 'intermediate', FALSE),
('Docker', 'cloud', 'Containerization platform for application deployment', 'intermediate', TRUE),
('Kubernetes', 'cloud', 'Container orchestration platform', 'advanced', TRUE),

-- DevOps & Infrastructure
('CI/CD', 'devops', 'Continuous Integration and Continuous Deployment practices', 'intermediate', TRUE),
('Jenkins', 'devops', 'Open-source automation server for CI/CD', 'intermediate', FALSE),
('GitLab CI', 'devops', 'GitLab integrated CI/CD platform', 'intermediate', FALSE),
('GitHub Actions', 'devops', 'GitHub integrated workflow automation', 'intermediate', FALSE),
('Terraform', 'devops', 'Infrastructure as Code tool', 'advanced', FALSE),
('Ansible', 'devops', 'Configuration management and automation tool', 'advanced', FALSE),

-- Security
('Application Security', 'security', 'Secure coding practices and vulnerability assessment', 'intermediate', TRUE),
('Network Security', 'security', 'Network protocols, firewalls, and security monitoring', 'advanced', FALSE),
('Cloud Security', 'security', 'Cloud-specific security practices and compliance', 'advanced', FALSE),
('Penetration Testing', 'security', 'Ethical hacking and security testing methodologies', 'expert', FALSE),

-- Networking
('TCP/IP', 'networking', 'Internet protocol suite fundamentals', 'intermediate', TRUE),
('DNS', 'networking', 'Domain Name System configuration and management', 'intermediate', FALSE),
('Load Balancing', 'networking', 'Traffic distribution and high availability', 'advanced', FALSE),
('VPN', 'networking', 'Virtual Private Network setup and management', 'intermediate', FALSE),

-- Data & Analytics
('SQL', 'data', 'Structured Query Language for database operations', 'intermediate', TRUE),
('Data Modeling', 'data', 'Database design and data architecture', 'advanced', FALSE),
('ETL', 'data', 'Extract, Transform, Load processes for data integration', 'advanced', FALSE),
('Business Intelligence', 'data', 'BI tools and data visualization', 'intermediate', FALSE),

-- AI & Machine Learning
('Machine Learning', 'ai_ml', 'ML algorithms and model development', 'advanced', FALSE),
('TensorFlow', 'ai_ml', 'Open-source machine learning framework', 'advanced', FALSE),
('PyTorch', 'ai_ml', 'Deep learning framework', 'advanced', FALSE),
('Natural Language Processing', 'ai_ml', 'NLP techniques and applications', 'expert', FALSE),

-- Project Management
('Agile Methodology', 'project_management', 'Agile software development practices', 'intermediate', TRUE),
('Scrum', 'project_management', 'Scrum framework for project management', 'intermediate', TRUE),
('Kanban', 'project_management', 'Kanban methodology for workflow management', 'intermediate', FALSE),
('JIRA', 'project_management', 'Project tracking and issue management tool', 'intermediate', FALSE),

-- Soft Skills
('Leadership', 'soft_skills', 'Team leadership and people management', 'intermediate', FALSE),
('Communication', 'soft_skills', 'Effective verbal and written communication', 'intermediate', TRUE),
('Problem Solving', 'soft_skills', 'Analytical thinking and solution development', 'intermediate', TRUE),
('Mentoring', 'soft_skills', 'Coaching and developing team members', 'advanced', FALSE),
('Client Management', 'soft_skills', 'Managing client relationships and expectations', 'advanced', FALSE);

-- Insert users with proper organizational relationships (156+ employees)
-- Password is bcrypt hash of 'password123' for all users
INSERT INTO users (id, email, password, first_name, last_name, title, role, organizational_unit_id, manager_id, hire_date, salary, employment_type, location, phone) VALUES
-- C-Level & VPs (Level 0-1)
(1, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Robert', 'Chen', 'Chief Executive Officer', 'ceo', 1, NULL, '2020-01-15', 250000.00, 'full_time', 'San Francisco, CA', '******-0001'),
(2, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Johnson', 'VP of Technology', 'vp', 2, 1, '2020-02-01', 180000.00, 'full_time', 'San Francisco, CA', '******-0002'),
(3, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Michael', 'Davis', 'VP of Product', 'vp', 3, 1, '2020-02-15', 175000.00, 'full_time', 'San Francisco, CA', '******-0003'),
(4, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lisa', 'Wang', 'VP of Operations', 'vp', 4, 1, '2020-03-01', 170000.00, 'full_time', 'San Francisco, CA', '******-0004'),

-- Directors (Level 2)
(5, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'James', 'Wilson', 'Director of Engineering', 'director', 5, 2, '2020-04-01', 150000.00, 'full_time', 'San Francisco, CA', '******-0005'),
(6, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Emily', 'Brown', 'Director of Data & AI', 'director', 6, 2, '2020-04-15', 145000.00, 'full_time', 'San Francisco, CA', '******-0006'),
(7, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'David', 'Garcia', 'Director of Design', 'director', 7, 3, '2020-05-01', 140000.00, 'full_time', 'San Francisco, CA', '******-0007'),
(8, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jennifer', 'Martinez', 'Director of Product Management', 'director', 8, 3, '2020-05-15', 140000.00, 'full_time', 'San Francisco, CA', '******-0008'),
(9, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Christopher', 'Lee', 'Director of Business Operations', 'director', 9, 4, '2020-06-01', 135000.00, 'full_time', 'San Francisco, CA', '******-0009'),
(10, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Amanda', 'Taylor', 'Director of Analytics', 'director', 10, 4, '2020-06-15', 135000.00, 'full_time', 'San Francisco, CA', '******-0010'),

-- Team Managers (Level 3)
(11, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kevin', 'Anderson', 'Frontend Engineering Manager', 'manager', 11, 5, '2020-07-01', 125000.00, 'full_time', 'San Francisco, CA', '******-0011'),
(12, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Rachel', 'Thomas', 'Backend Engineering Manager', 'manager', 12, 5, '2020-07-15', 125000.00, 'full_time', 'San Francisco, CA', '******-0012'),
(13, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Daniel', 'Jackson', 'DevOps & Infrastructure Manager', 'manager', 13, 5, '2020-08-01', 125000.00, 'full_time', 'San Francisco, CA', '******-0013'),
(14, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Stephanie', 'White', 'Machine Learning Manager', 'manager', 14, 6, '2020-08-15', 125000.00, 'full_time', 'San Francisco, CA', '******-0014'),
(15, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Matthew', 'Harris', 'Data Engineering Manager', 'manager', 15, 6, '2020-09-01', 125000.00, 'full_time', 'San Francisco, CA', '******-0015'),
(16, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Nicole', 'Clark', 'UX Research & Design Manager', 'manager', 16, 7, '2020-09-15', 120000.00, 'full_time', 'San Francisco, CA', '******-0016'),
(17, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ryan', 'Lewis', 'Visual & Brand Design Manager', 'manager', 17, 7, '2020-10-01', 120000.00, 'full_time', 'San Francisco, CA', '******-0017'),
(18, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jessica', 'Robinson', 'Core Product Manager', 'manager', 18, 8, '2020-10-15', 120000.00, 'full_time', 'San Francisco, CA', '******-0018'),
(19, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Andrew', 'Walker', 'Growth & Analytics Manager', 'manager', 19, 8, '2020-11-01', 120000.00, 'full_time', 'San Francisco, CA', '******-0019'),
(20, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Michelle', 'Hall', 'Process Excellence Manager', 'manager', 20, 9, '2020-11-15', 115000.00, 'full_time', 'San Francisco, CA', '******-0020'),
(21, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Joshua', 'Allen', 'PMO Manager', 'manager', 21, 9, '2020-12-01', 115000.00, 'full_time', 'San Francisco, CA', '******-0021'),
(22, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Young', 'Business Intelligence Manager', 'manager', 22, 10, '2020-12-15', 115000.00, 'full_time', 'San Francisco, CA', '******-0022'),
(23, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Brandon', 'King', 'Performance Analytics Manager', 'manager', 23, 10, '2021-01-01', 115000.00, 'full_time', 'San Francisco, CA', '******-0023'),

-- Squad Leaders (Level 4)
(24, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Tyler', 'Wright', 'React Development Lead', 'manager', 24, 11, '2021-01-15', 110000.00, 'full_time', 'San Francisco, CA', '******-0024'),
(25, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Megan', 'Lopez', 'Mobile Frontend Lead', 'manager', 25, 11, '2021-02-01', 110000.00, 'full_time', 'San Francisco, CA', '******-0025'),
(26, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jacob', 'Hill', 'API Development Lead', 'manager', 26, 12, '2021-02-15', 110000.00, 'full_time', 'San Francisco, CA', '******-0026'),
(27, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ashley', 'Green', 'Database & Performance Lead', 'manager', 27, 12, '2021-03-01', 110000.00, 'full_time', 'San Francisco, CA', '******-0027'),
(28, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Nathan', 'Adams', 'Cloud Infrastructure Lead', 'manager', 28, 13, '2021-03-15', 110000.00, 'full_time', 'San Francisco, CA', '******-0028'),
(29, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Samantha', 'Baker', 'CI/CD & Automation Lead', 'manager', 29, 13, '2021-04-01', 110000.00, 'full_time', 'San Francisco, CA', '******-0029'),

-- Senior Engineers (30-69)
(30, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alex', 'Gonzalez', 'Senior Frontend Engineer', 'senior_engineer', 24, 24, '2021-04-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0030'),
(31, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Olivia', 'Nelson', 'Senior React Developer', 'senior_engineer', 24, 24, '2021-05-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0031'),
(32, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ethan', 'Carter', 'Senior TypeScript Developer', 'senior_engineer', 24, 24, '2021-05-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0032'),
(33, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sophia', 'Mitchell', 'Senior Mobile Developer', 'senior_engineer', 25, 25, '2021-06-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0033'),
(34, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'William', 'Perez', 'Senior React Native Developer', 'senior_engineer', 25, 25, '2021-06-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0034'),
(35, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Isabella', 'Roberts', 'Senior Backend Engineer', 'senior_engineer', 26, 26, '2021-07-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0035'),
(36, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'James', 'Turner', 'Senior API Developer', 'senior_engineer', 26, 26, '2021-07-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0036'),
(37, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Charlotte', 'Phillips', 'Senior Node.js Developer', 'senior_engineer', 26, 26, '2021-08-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0037'),
(38, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Benjamin', 'Campbell', 'Senior Database Engineer', 'senior_engineer', 27, 27, '2021-08-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0038'),
(39, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Amelia', 'Parker', 'Senior Performance Engineer', 'senior_engineer', 27, 27, '2021-09-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0039'),
(40, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lucas', 'Evans', 'Senior Cloud Engineer', 'senior_engineer', 28, 28, '2021-09-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0040'),
(41, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Harper', 'Edwards', 'Senior AWS Engineer', 'senior_engineer', 28, 28, '2021-10-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0041'),
(42, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mason', 'Collins', 'Senior DevOps Engineer', 'senior_engineer', 29, 29, '2021-10-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0042'),
(43, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Evelyn', 'Stewart', 'Senior Automation Engineer', 'senior_engineer', 29, 29, '2021-11-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0043'),
(44, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Logan', 'Sanchez', 'Senior ML Engineer', 'senior_engineer', 14, 14, '2021-11-15', 100000.00, 'full_time', 'San Francisco, CA', '******-0044'),
(45, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Abigail', 'Morris', 'Senior Data Scientist', 'senior_engineer', 14, 14, '2021-12-01', 100000.00, 'full_time', 'San Francisco, CA', '******-0045'),
(46, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jackson', 'Rogers', 'Senior Data Engineer', 'senior_engineer', 15, 15, '2021-12-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0046'),
(47, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Madison', 'Reed', 'Senior ETL Engineer', 'senior_engineer', 15, 15, '2022-01-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0047'),
(48, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Aiden', 'Cook', 'Senior UX Designer', 'senior_engineer', 16, 16, '2022-01-15', 90000.00, 'full_time', 'San Francisco, CA', '******-0048'),
(49, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Elizabeth', 'Morgan', 'Senior UI Designer', 'senior_engineer', 17, 17, '2022-02-01', 90000.00, 'full_time', 'San Francisco, CA', '******-0049'),
(50, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Carter', 'Bailey', 'Senior Product Manager', 'senior_engineer', 18, 18, '2022-02-15', 105000.00, 'full_time', 'San Francisco, CA', '******-0050'),
(51, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Victoria', 'Rivera', 'Senior Growth Analyst', 'senior_engineer', 19, 19, '2022-03-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0051'),
(52, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Wyatt', 'Cooper', 'Senior Process Engineer', 'senior_engineer', 20, 20, '2022-03-15', 90000.00, 'full_time', 'San Francisco, CA', '******-0052'),
(53, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Grace', 'Richardson', 'Senior Project Manager', 'senior_engineer', 21, 21, '2022-04-01', 90000.00, 'full_time', 'San Francisco, CA', '******-0053'),
(54, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Owen', 'Cox', 'Senior BI Analyst', 'senior_engineer', 22, 22, '2022-04-15', 90000.00, 'full_time', 'San Francisco, CA', '******-0054'),
(55, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Zoey', 'Ward', 'Senior Performance Analyst', 'senior_engineer', 23, 23, '2022-05-01', 90000.00, 'full_time', 'San Francisco, CA', '******-0055'),

-- Regular Engineers (56-120)
(56, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Grayson', 'Torres', 'Frontend Engineer', 'engineer', 24, 24, '2022-05-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0056'),
(57, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lily', 'Peterson', 'React Developer', 'engineer', 24, 24, '2022-06-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0057'),
(58, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Levi', 'Gray', 'JavaScript Developer', 'engineer', 24, 24, '2022-06-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0058'),
(59, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Layla', 'Ramirez', 'CSS/HTML Developer', 'engineer', 24, 24, '2022-07-01', 75000.00, 'full_time', 'San Francisco, CA', '******-0059'),
(60, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Hudson', 'James', 'Mobile Developer', 'engineer', 25, 25, '2022-07-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0060'),
(61, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Penelope', 'Watson', 'React Native Developer', 'engineer', 25, 25, '2022-08-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0061'),
(62, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Leo', 'Brooks', 'iOS Developer', 'engineer', 25, 25, '2022-08-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0062'),
(63, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Aurora', 'Kelly', 'Android Developer', 'engineer', 25, 25, '2022-09-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0063'),
(64, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lincoln', 'Sanders', 'Backend Engineer', 'engineer', 26, 26, '2022-09-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0064'),
(65, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Nova', 'Price', 'API Developer', 'engineer', 26, 26, '2022-10-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0065'),
(66, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Easton', 'Bennett', 'Node.js Developer', 'engineer', 26, 26, '2022-10-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0066'),
(67, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Emilia', 'Wood', 'Python Developer', 'engineer', 26, 26, '2022-11-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0067'),
(68, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Colton', 'Barnes', 'Database Developer', 'engineer', 27, 27, '2022-11-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0068'),
(69, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Adalynn', 'Ross', 'SQL Developer', 'engineer', 27, 27, '2022-12-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0069'),

-- Continue with more engineers (70-120)
(70, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Hunter', 'Henderson', 'Cloud Engineer', 'engineer', 28, 28, '2022-12-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0070'),
(71, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Kinsley', 'Coleman', 'AWS Engineer', 'engineer', 28, 28, '2023-01-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0071'),
(72, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Connor', 'Jenkins', 'Azure Engineer', 'engineer', 28, 28, '2023-01-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0072'),
(73, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Paisley', 'Perry', 'DevOps Engineer', 'engineer', 29, 29, '2023-02-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0073'),
(74, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Maverick', 'Powell', 'CI/CD Engineer', 'engineer', 29, 29, '2023-02-15', 80000.00, 'full_time', 'San Francisco, CA', '******-0074'),
(75, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Skylar', 'Long', 'Automation Engineer', 'engineer', 29, 29, '2023-03-01', 80000.00, 'full_time', 'San Francisco, CA', '******-0075'),

-- Junior Engineers (76-140)
(76, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Blake', 'Hughes', 'Junior Frontend Developer', 'junior_engineer', 24, 24, '2023-03-15', 65000.00, 'full_time', 'San Francisco, CA', '******-0076'),
(77, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Brooklyn', 'Flores', 'Junior React Developer', 'junior_engineer', 24, 24, '2023-04-01', 65000.00, 'full_time', 'San Francisco, CA', '******-0077'),
(78, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ryder', 'Washington', 'Junior JavaScript Developer', 'junior_engineer', 24, 24, '2023-04-15', 65000.00, 'full_time', 'San Francisco, CA', '******-0078'),
(79, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Savannah', 'Butler', 'Junior Mobile Developer', 'junior_engineer', 25, 25, '2023-05-01', 65000.00, 'full_time', 'San Francisco, CA', '******-0079'),
(80, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Roman', 'Simmons', 'Junior Backend Developer', 'junior_engineer', 26, 26, '2023-05-15', 65000.00, 'full_time', 'San Francisco, CA', '******-0080'),

-- HR and Support Staff (141-156)
(141, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'HR Administrator', 'hr_admin', 1, 1, '2020-01-01', 85000.00, 'full_time', 'San Francisco, CA', '******-0141'),
(142, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'HR', 'Manager', 'Human Resources Manager', 'manager', 1, 1, '2020-01-15', 95000.00, 'full_time', 'San Francisco, CA', '******-0142'),
(143, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Finance', 'Manager', 'Finance Manager', 'manager', 1, 1, '2020-02-01', 95000.00, 'full_time', 'San Francisco, CA', '******-0143'),
(144, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Legal', 'Counsel', 'Legal Counsel', 'manager', 1, 1, '2020-02-15', 120000.00, 'full_time', 'San Francisco, CA', '******-0144'),
(145, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Marketing', 'Manager', 'Marketing Manager', 'manager', 1, 1, '2020-03-01', 90000.00, 'full_time', 'San Francisco, CA', '******-0145'),
(146, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sales', 'Manager', 'Sales Manager', 'manager', 1, 1, '2020-03-15', 90000.00, 'full_time', 'San Francisco, CA', '******-0146'),
(147, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Support', 'Lead', 'Customer Support Lead', 'manager', 1, 1, '2020-04-01', 75000.00, 'full_time', 'San Francisco, CA', '******-0147'),
(148, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Security', 'Officer', 'Chief Security Officer', 'director', 1, 1, '2020-04-15', 140000.00, 'full_time', 'San Francisco, CA', '******-0148'),
(149, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Facilities', 'Manager', 'Facilities Manager', 'manager', 1, 1, '2020-05-01', 70000.00, 'full_time', 'San Francisco, CA', '******-0149'),
(150, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'IT', 'Support', 'IT Support Specialist', 'engineer', 1, 1, '2020-05-15', 65000.00, 'full_time', 'San Francisco, CA', '******-0150'),
(151, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Office', 'Manager', 'Office Manager', 'manager', 1, 1, '2020-06-01', 60000.00, 'full_time', 'San Francisco, CA', '******-0151'),
(152, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Reception', 'Staff', 'Receptionist', 'engineer', 1, 1, '2020-06-15', 45000.00, 'full_time', 'San Francisco, CA', '******-0152'),
(153, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Summer', 'Intern', 'Software Engineering Intern', 'intern', 24, 24, '2023-06-01', 25000.00, 'intern', 'San Francisco, CA', '******-0153'),
(154, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Data', 'Intern', 'Data Science Intern', 'intern', 14, 14, '2023-06-01', 25000.00, 'intern', 'San Francisco, CA', '******-0154'),
(155, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Design', 'Intern', 'UX Design Intern', 'intern', 16, 16, '2023-06-01', 25000.00, 'intern', 'San Francisco, CA', '******-0155'),
(156, '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Guest', 'User', 'Guest Account', 'guest', NULL, NULL, '2023-01-01', 0.00, 'part_time', 'Remote', '******-0156');

-- Update organizational units with proper manager assignments
UPDATE organizational_units SET manager_id = 1 WHERE id = 1;  -- CEO manages root
UPDATE organizational_units SET manager_id = 2 WHERE id = 2;  -- VP Tech manages Tech Division
UPDATE organizational_units SET manager_id = 3 WHERE id = 3;  -- VP Product manages Product Division
UPDATE organizational_units SET manager_id = 4 WHERE id = 4;  -- VP Ops manages Operations Division
UPDATE organizational_units SET manager_id = 5 WHERE id = 5;  -- Director manages Engineering Dept
UPDATE organizational_units SET manager_id = 6 WHERE id = 6;  -- Director manages Data & AI Dept
UPDATE organizational_units SET manager_id = 7 WHERE id = 7;  -- Director manages Design Dept
UPDATE organizational_units SET manager_id = 8 WHERE id = 8;  -- Director manages Product Mgmt Dept
UPDATE organizational_units SET manager_id = 9 WHERE id = 9;  -- Director manages Business Ops Dept
UPDATE organizational_units SET manager_id = 10 WHERE id = 10; -- Director manages Analytics Dept
UPDATE organizational_units SET manager_id = 11 WHERE id = 11; -- Manager manages Frontend Team
UPDATE organizational_units SET manager_id = 12 WHERE id = 12; -- Manager manages Backend Team
UPDATE organizational_units SET manager_id = 13 WHERE id = 13; -- Manager manages DevOps Team
UPDATE organizational_units SET manager_id = 14 WHERE id = 14; -- Manager manages ML Team
UPDATE organizational_units SET manager_id = 15 WHERE id = 15; -- Manager manages Data Eng Team
UPDATE organizational_units SET manager_id = 16 WHERE id = 16; -- Manager manages UX Team
UPDATE organizational_units SET manager_id = 17 WHERE id = 17; -- Manager manages Visual Design Team
UPDATE organizational_units SET manager_id = 18 WHERE id = 18; -- Manager manages Core Product Team
UPDATE organizational_units SET manager_id = 19 WHERE id = 19; -- Manager manages Growth Team
UPDATE organizational_units SET manager_id = 20 WHERE id = 20; -- Manager manages Process Team
UPDATE organizational_units SET manager_id = 21 WHERE id = 21; -- Manager manages PMO Team
UPDATE organizational_units SET manager_id = 22 WHERE id = 22; -- Manager manages BI Team
UPDATE organizational_units SET manager_id = 23 WHERE id = 23; -- Manager manages Performance Team
UPDATE organizational_units SET manager_id = 24 WHERE id = 24; -- Lead manages React Squad
UPDATE organizational_units SET manager_id = 25 WHERE id = 25; -- Lead manages Mobile Squad
UPDATE organizational_units SET manager_id = 26 WHERE id = 26; -- Lead manages API Squad
UPDATE organizational_units SET manager_id = 27 WHERE id = 27; -- Lead manages DB Squad
UPDATE organizational_units SET manager_id = 28 WHERE id = 28; -- Lead manages Cloud Squad
UPDATE organizational_units SET manager_id = 29 WHERE id = 29; -- Lead manages CI/CD Squad;

-- Insert user-skillset relationships (many-to-many)
-- Frontend Engineers and their skills
INSERT INTO user_skillsets (user_id, skillset_id, proficiency_level, years_experience, is_certified, last_used_date) VALUES
-- React Squad (24-29, 30-32, 56-59, 76-78)
(24, 1, 'expert', 5.0, TRUE, '2024-01-15'),      -- Tyler - JavaScript
(24, 2, 'expert', 4.5, TRUE, '2024-01-15'),      -- Tyler - TypeScript
(24, 11, 'expert', 5.0, TRUE, '2024-01-15'),     -- Tyler - React
(24, 14, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Tyler - HTML5
(24, 15, 'advanced', 3.5, FALSE, '2024-01-15'),  -- Tyler - CSS3
(24, 18, 'advanced', 2.0, FALSE, '2024-01-15'),  -- Tyler - Next.js

(30, 1, 'advanced', 3.5, FALSE, '2024-01-15'),   -- Alex - JavaScript
(30, 11, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Alex - React
(30, 14, 'intermediate', 2.5, FALSE, '2024-01-15'), -- Alex - HTML5
(30, 15, 'intermediate', 2.5, FALSE, '2024-01-15'), -- Alex - CSS3

(31, 1, 'advanced', 3.0, FALSE, '2024-01-15'),   -- Olivia - JavaScript
(31, 2, 'intermediate', 2.0, FALSE, '2024-01-15'), -- Olivia - TypeScript
(31, 11, 'advanced', 3.0, TRUE, '2024-01-15'),   -- Olivia - React
(31, 16, 'intermediate', 1.5, FALSE, '2024-01-15'), -- Olivia - Sass/SCSS

-- Backend Engineers and their skills
(26, 3, 'expert', 6.0, TRUE, '2024-01-15'),      -- Jacob - Python
(26, 19, 'expert', 5.5, TRUE, '2024-01-15'),     -- Jacob - Node.js
(26, 20, 'expert', 5.0, TRUE, '2024-01-15'),     -- Jacob - Express.js
(26, 25, 'advanced', 4.0, FALSE, '2024-01-15'),  -- Jacob - MySQL
(26, 26, 'advanced', 3.5, FALSE, '2024-01-15'),  -- Jacob - PostgreSQL

(35, 3, 'advanced', 4.0, FALSE, '2024-01-15'),   -- Isabella - Python
(35, 19, 'advanced', 4.5, FALSE, '2024-01-15'),  -- Isabella - Node.js
(35, 20, 'advanced', 4.0, FALSE, '2024-01-15'),  -- Isabella - Express.js
(35, 21, 'intermediate', 2.0, FALSE, '2024-01-15'), -- Isabella - NestJS

-- DevOps Engineers and their skills
(28, 31, 'expert', 5.0, TRUE, '2024-01-15'),     -- Nathan - AWS
(28, 34, 'expert', 4.5, TRUE, '2024-01-15'),     -- Nathan - Docker
(28, 35, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Nathan - Kubernetes
(28, 36, 'advanced', 4.0, FALSE, '2024-01-15'),  -- Nathan - CI/CD
(28, 40, 'intermediate', 2.5, FALSE, '2024-01-15'), -- Nathan - Terraform

(42, 31, 'advanced', 3.5, FALSE, '2024-01-15'),  -- Mason - AWS
(42, 34, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Mason - Docker
(42, 36, 'advanced', 3.5, FALSE, '2024-01-15'),  -- Mason - CI/CD
(42, 37, 'intermediate', 2.0, FALSE, '2024-01-15'), -- Mason - Jenkins

-- Data Scientists and ML Engineers
(44, 3, 'expert', 5.5, TRUE, '2024-01-15'),      -- Logan - Python
(44, 50, 'expert', 4.0, TRUE, '2024-01-15'),     -- Logan - Machine Learning
(44, 51, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Logan - TensorFlow
(44, 52, 'intermediate', 2.0, FALSE, '2024-01-15'), -- Logan - PyTorch
(44, 49, 'advanced', 3.5, FALSE, '2024-01-15'),  -- Logan - SQL

(45, 3, 'expert', 4.5, TRUE, '2024-01-15'),      -- Abigail - Python
(45, 50, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Abigail - Machine Learning
(45, 47, 'advanced', 3.5, FALSE, '2024-01-15'),  -- Abigail - Data Modeling
(45, 49, 'expert', 4.0, TRUE, '2024-01-15'),     -- Abigail - SQL

-- UX/UI Designers
(48, 57, 'expert', 4.0, TRUE, '2024-01-15'),     -- Aiden - Communication
(48, 58, 'expert', 4.5, TRUE, '2024-01-15'),     -- Aiden - Problem Solving
(48, 14, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Aiden - HTML5
(48, 15, 'advanced', 3.5, FALSE, '2024-01-15'),  -- Aiden - CSS3

(49, 14, 'expert', 4.0, TRUE, '2024-01-15'),     -- Elizabeth - HTML5
(49, 15, 'expert', 4.5, TRUE, '2024-01-15'),     -- Elizabeth - CSS3
(49, 16, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Elizabeth - Sass/SCSS
(49, 57, 'advanced', 3.5, FALSE, '2024-01-15'),  -- Elizabeth - Communication

-- Product Managers
(50, 54, 'expert', 4.0, TRUE, '2024-01-15'),     -- Carter - Agile Methodology
(50, 55, 'expert', 4.5, TRUE, '2024-01-15'),     -- Carter - Scrum
(50, 57, 'expert', 5.0, TRUE, '2024-01-15'),     -- Carter - Communication
(50, 58, 'expert', 4.5, TRUE, '2024-01-15'),     -- Carter - Problem Solving
(50, 60, 'advanced', 3.0, FALSE, '2024-01-15'),  -- Carter - Client Management

-- Add more comprehensive skill assignments for key roles
-- CEO and VPs (Leadership skills)
(1, 56, 'expert', 10.0, TRUE, '2024-01-15'),     -- Robert - Leadership
(1, 57, 'expert', 12.0, TRUE, '2024-01-15'),     -- Robert - Communication
(1, 58, 'expert', 15.0, TRUE, '2024-01-15'),     -- Robert - Problem Solving
(1, 60, 'expert', 12.0, TRUE, '2024-01-15'),     -- Robert - Client Management

(2, 56, 'expert', 8.0, TRUE, '2024-01-15'),      -- Sarah - Leadership
(2, 1, 'advanced', 6.0, FALSE, '2024-01-15'),    -- Sarah - JavaScript
(2, 3, 'advanced', 7.0, FALSE, '2024-01-15'),    -- Sarah - Python
(2, 31, 'advanced', 5.0, FALSE, '2024-01-15'),   -- Sarah - AWS

-- Directors (Management + Technical skills)
(5, 56, 'expert', 6.0, TRUE, '2024-01-15'),      -- James - Leadership
(5, 1, 'expert', 8.0, TRUE, '2024-01-15'),       -- James - JavaScript
(5, 3, 'expert', 7.0, TRUE, '2024-01-15'),       -- James - Python
(5, 54, 'expert', 5.0, TRUE, '2024-01-15'),      -- James - Agile Methodology

(6, 56, 'advanced', 5.0, FALSE, '2024-01-15'),   -- Emily - Leadership
(6, 3, 'expert', 8.0, TRUE, '2024-01-15'),       -- Emily - Python
(6, 50, 'expert', 6.0, TRUE, '2024-01-15'),      -- Emily - Machine Learning
(6, 49, 'expert', 7.0, TRUE, '2024-01-15'),      -- Emily - SQL

-- Add skills for junior engineers and interns
(76, 1, 'beginner', 1.0, FALSE, '2024-01-15'),   -- Blake - JavaScript
(76, 11, 'beginner', 0.5, FALSE, '2024-01-15'),  -- Blake - React
(76, 14, 'intermediate', 1.5, FALSE, '2024-01-15'), -- Blake - HTML5
(76, 15, 'intermediate', 1.5, FALSE, '2024-01-15'), -- Blake - CSS3

(153, 1, 'beginner', 0.5, FALSE, '2024-01-15'),  -- Summer Intern - JavaScript
(153, 11, 'beginner', 0.3, FALSE, '2024-01-15'), -- Summer Intern - React
(153, 14, 'beginner', 0.8, FALSE, '2024-01-15'), -- Summer Intern - HTML5

(154, 3, 'beginner', 0.5, FALSE, '2024-01-15'),  -- Data Intern - Python
(154, 49, 'beginner', 0.3, FALSE, '2024-01-15'), -- Data Intern - SQL
(154, 50, 'beginner', 0.2, FALSE, '2024-01-15'); -- Data Intern - Machine Learning

-- Insert assessment templates based on organizational levels
-- HR Level Templates (Company-wide)
INSERT INTO assessment_templates (id, name, description, version, created_by_id, organizational_unit_id, template_level, is_active) VALUES
(1, 'HR Corporate Standards Template', 'Company-wide mandatory assessment criteria template', '2.1', 1, NULL, 'hr_level', TRUE),
(2, 'Annual Performance Review Template', 'Comprehensive yearly evaluation with HR standards', '2.0', 1, NULL, 'hr_level', TRUE),
(3, 'New Employee Onboarding Assessment', 'Standard assessment for new hire evaluation', '1.5', 1, NULL, 'hr_level', TRUE);

-- Organizational Level Templates (Division-specific)
INSERT INTO assessment_templates (id, name, description, version, created_by_id, organizational_unit_id, template_level, is_active) VALUES
(4, 'Technology Division Excellence Template', 'Technical assessment criteria for technology teams', '1.8', 2, 2, 'organizational_level', TRUE),
(5, 'Product Division Innovation Template', 'Product-focused assessment criteria', '1.6', 3, 3, 'organizational_level', TRUE),
(6, 'Operations Division Efficiency Template', 'Operations-focused assessment criteria', '1.4', 4, 4, 'organizational_level', TRUE);

-- Team Level Templates (Team-specific)
INSERT INTO assessment_templates (id, name, description, version, created_by_id, organizational_unit_id, template_level, is_active) VALUES
(7, 'Frontend Engineering Assessment', 'Frontend development specific criteria', '2.2', 11, 11, 'team_level', TRUE),
(8, 'Backend Engineering Assessment', 'Backend development specific criteria', '2.0', 12, 12, 'team_level', TRUE),
(9, 'DevOps Engineering Assessment', 'DevOps and infrastructure specific criteria', '1.9', 13, 13, 'team_level', TRUE),
(10, 'Product Management Assessment', 'Product management specific criteria', '1.7', 14, 14, 'team_level', TRUE);

-- Insert assessment areas for the Annual Performance Review template
INSERT INTO assessment_areas (template_id, name, description, weight, max_score, order_index) VALUES
(1, 'Job Knowledge', 'Understanding of job duties, procedures, and skills required', 1.0, 10, 1),
(1, 'Quality of Work', 'Accuracy, thoroughness, and effectiveness of work performed', 1.2, 10, 2),
(1, 'Productivity', 'Volume of work, efficiency, and meeting deadlines', 1.0, 10, 3),
(1, 'Communication Skills', 'Verbal and written communication effectiveness', 0.8, 10, 4),
(1, 'Teamwork', 'Collaboration, cooperation, and team contribution', 1.0, 10, 5),
(1, 'Initiative', 'Self-motivation, problem-solving, and taking action', 0.9, 10, 6),
(1, 'Leadership', 'Guidance, mentoring, and influence on others', 1.1, 10, 7);

-- Insert assessment areas for the Quarterly Check-in template
INSERT INTO assessment_areas (template_id, name, description, weight, max_score, order_index) VALUES
(2, 'Goal Progress', 'Progress toward quarterly goals and objectives', 1.2, 10, 1),
(2, 'Key Achievements', 'Major accomplishments during the quarter', 1.0, 10, 2),
(2, 'Areas for Improvement', 'Skills or behaviors that need development', 0.9, 10, 3),
(2, 'Team Contribution', 'Impact and value added to team projects', 1.0, 10, 4);

-- Insert assessment areas for the New Hire 90-Day Evaluation template
INSERT INTO assessment_areas (template_id, name, description, weight, max_score, order_index) VALUES
(3, 'Technical Proficiency', 'Mastery of required technical skills', 1.1, 10, 1),
(3, 'Company Knowledge', 'Understanding of company policies and procedures', 0.8, 10, 2),
(3, 'Cultural Fit', 'Alignment with company values and culture', 1.0, 10, 3),
(3, 'Onboarding Progress', 'Completion of onboarding tasks and training', 0.9, 10, 4);

-- Insert scoring rules for Annual Performance Review template areas
INSERT INTO scoring_rules (area_id, rule_type, condition_field, condition_operator, condition_value, score_adjustment) VALUES
(1, 'threshold', 'score', '>=', '9', 1.0),
(2, 'conditional', 'errors', '<', '3', 0.5),
(5, 'bonus', NULL, NULL, NULL, 1.0);

-- Insert a few sample assessment instances
INSERT INTO assessment_instances (template_id, employee_id, evaluator_id, status, assessment_date, total_score) VALUES
(1, 4, 2, 'completed', CURDATE() - INTERVAL 30 DAY, 8.5),
(1, 5, 2, 'approved', CURDATE() - INTERVAL 45 DAY, 7.8),
(2, 4, 2, 'in_progress', CURDATE() - INTERVAL 5 DAY, NULL),
(3, 6, 3, 'draft', CURDATE(), NULL);

-- Insert sample assessment responses for completed assessments
INSERT INTO assessment_responses (assessment_id, area_id, score, evaluator_comments, employee_comments, area_weight, weighted_score) VALUES
(1, 1, 9.0, 'Excellent understanding of job requirements', 'Thank you for the feedback', 1.0, 9.0),
(1, 2, 8.0, 'Good quality work with occasional errors', 'Will work on reducing errors', 1.2, 9.6),
(1, 3, 8.5, 'Consistently meets deadlines', 'Aiming to exceed expectations next time', 1.0, 8.5),
(1, 4, 7.5, 'Communication is clear but could be more proactive', 'Will work on more proactive communication', 0.8, 6.0),
(1, 5, 9.5, 'Excellent team player', 'I enjoy working with the team', 1.0, 9.5),
(2, 1, 8.0, 'Strong job knowledge', '', 1.0, 8.0),
(2, 2, 7.5, 'Quality is good but needs improvement in reports', '', 1.2, 9.0),
(2, 3, 8.0, 'Meets productivity expectations', '', 1.0, 8.0),
(2, 4, 7.0, 'Communication needs improvement', '', 0.8, 5.6),
(2, 5, 8.5, 'Works well with the team', '', 1.0, 8.5);

-- Insert assessment criteria levels
INSERT INTO assessment_criteria_levels (id, level_type, name, description, priority_order) VALUES
(1, 'hr_level', 'HR Corporate Standards', 'Company-wide assessment criteria mandated by HR department', 1),
(2, 'organizational_level', 'Organizational Requirements', 'Assessment criteria specific to organizational units and their hierarchy', 2),
(3, 'team_level', 'Team-Specific Criteria', 'Custom assessment criteria defined by individual teams', 3);

-- Insert HR Level Assessment Criteria (Company-wide mandated by HR)
INSERT INTO assessment_criteria (id, name, description, criteria_level_id, organizational_unit_id, created_by_id, weight, max_score, scoring_method, is_mandatory, effective_from, order_index) VALUES
(1, 'Professional Ethics & Integrity', 'Adherence to company values, ethical conduct, and professional standards', 1, NULL, 1, 2.0, 100, 'percentage', TRUE, '2024-01-01', 1),
(2, 'Communication Skills', 'Verbal, written, and interpersonal communication effectiveness', 1, NULL, 1, 1.5, 100, 'percentage', TRUE, '2024-01-01', 2),
(3, 'Collaboration & Teamwork', 'Ability to work effectively with colleagues and contribute to team success', 1, NULL, 1, 1.5, 100, 'percentage', TRUE, '2024-01-01', 3),
(4, 'Adaptability & Learning', 'Openness to change, continuous learning, and skill development', 1, NULL, 1, 1.0, 100, 'percentage', TRUE, '2024-01-01', 4),
(5, 'Time Management & Reliability', 'Meeting deadlines, punctuality, and dependability', 1, NULL, 1, 1.0, 100, 'percentage', TRUE, '2024-01-01', 5),
(6, 'Customer Focus', 'Understanding and meeting internal/external customer needs', 1, NULL, 1, 1.0, 100, 'percentage', TRUE, '2024-01-01', 6);

-- Insert Organizational Level Assessment Criteria (Technology Division)
INSERT INTO assessment_criteria (id, name, description, criteria_level_id, organizational_unit_id, created_by_id, weight, max_score, scoring_method, is_mandatory, effective_from, order_index) VALUES
(7, 'Technical Excellence', 'Mastery of relevant technologies and technical problem-solving', 2, 2, 2, 2.0, 100, 'percentage', TRUE, '2024-01-01', 1),
(8, 'Code Quality & Best Practices', 'Writing clean, maintainable, and well-documented code', 2, 2, 2, 1.5, 100, 'percentage', TRUE, '2024-01-01', 2),
(9, 'System Design & Architecture', 'Ability to design scalable and efficient systems', 2, 2, 2, 1.5, 100, 'percentage', TRUE, '2024-01-01', 3),
(10, 'Security Awareness', 'Understanding and implementing security best practices', 2, 2, 2, 1.0, 100, 'percentage', TRUE, '2024-01-01', 4),
(11, 'Innovation & Problem Solving', 'Creative thinking and innovative solutions to technical challenges', 2, 2, 2, 1.0, 100, 'percentage', TRUE, '2024-01-01', 5);

-- Insert Organizational Level Assessment Criteria (Product Division)
INSERT INTO assessment_criteria (id, name, description, criteria_level_id, organizational_unit_id, created_by_id, weight, max_score, scoring_method, is_mandatory, effective_from, order_index) VALUES
(12, 'Product Strategy & Vision', 'Understanding market needs and defining product direction', 2, 3, 3, 2.0, 100, 'percentage', TRUE, '2024-01-01', 1),
(13, 'User Experience Focus', 'Designing products with excellent user experience', 2, 3, 3, 1.5, 100, 'percentage', TRUE, '2024-01-01', 2),
(14, 'Data-Driven Decision Making', 'Using analytics and metrics to guide product decisions', 2, 3, 3, 1.5, 100, 'percentage', TRUE, '2024-01-01', 3),
(15, 'Stakeholder Management', 'Effectively managing relationships with internal and external stakeholders', 2, 3, 3, 1.0, 100, 'percentage', TRUE, '2024-01-01', 4),
(16, 'Market Research & Analysis', 'Understanding competitive landscape and market trends', 2, 3, 3, 1.0, 100, 'percentage', TRUE, '2024-01-01', 5);

-- Insert Team Level Assessment Criteria (Frontend Team)
INSERT INTO assessment_criteria (id, name, description, criteria_level_id, organizational_unit_id, created_by_id, weight, max_score, scoring_method, is_mandatory, effective_from, order_index) VALUES
(17, 'React/TypeScript Proficiency', 'Advanced skills in React ecosystem and TypeScript development', 3, 11, 11, 2.0, 100, 'percentage', TRUE, '2024-01-01', 1),
(18, 'UI/UX Implementation', 'Translating designs into pixel-perfect, responsive interfaces', 3, 11, 11, 1.5, 100, 'percentage', TRUE, '2024-01-01', 2),
(19, 'Performance Optimization', 'Optimizing frontend performance and user experience', 3, 11, 11, 1.5, 100, 'percentage', TRUE, '2024-01-01', 3),
(20, 'Testing & Quality Assurance', 'Writing comprehensive tests and ensuring code quality', 3, 11, 11, 1.0, 100, 'percentage', TRUE, '2024-01-01', 4);

-- Insert Team Level Assessment Criteria (Backend Team)
INSERT INTO assessment_criteria (id, name, description, criteria_level_id, organizational_unit_id, created_by_id, weight, max_score, scoring_method, is_mandatory, effective_from, order_index) VALUES
(21, 'API Design & Development', 'Creating robust, scalable, and well-documented APIs', 3, 12, 12, 2.0, 100, 'percentage', TRUE, '2024-01-01', 1),
(22, 'Database Design & Optimization', 'Efficient database schema design and query optimization', 3, 12, 12, 1.5, 100, 'percentage', TRUE, '2024-01-01', 2),
(23, 'Microservices Architecture', 'Understanding and implementing microservices patterns', 3, 12, 12, 1.5, 100, 'percentage', TRUE, '2024-01-01', 3),
(24, 'Performance & Scalability', 'Building high-performance, scalable backend systems', 3, 12, 12, 1.0, 100, 'percentage', TRUE, '2024-01-01', 4);

-- Insert Team Level Assessment Criteria (DevOps Team)
INSERT INTO assessment_criteria (id, name, description, criteria_level_id, organizational_unit_id, created_by_id, weight, max_score, scoring_method, is_mandatory, effective_from, order_index) VALUES
(25, 'Infrastructure as Code', 'Managing infrastructure using code and automation tools', 3, 13, 13, 2.0, 100, 'percentage', TRUE, '2024-01-01', 1),
(26, 'CI/CD Pipeline Management', 'Designing and maintaining continuous integration/deployment pipelines', 3, 13, 13, 1.5, 100, 'percentage', TRUE, '2024-01-01', 2),
(27, 'Monitoring & Observability', 'Implementing comprehensive monitoring and alerting systems', 3, 13, 13, 1.5, 100, 'percentage', TRUE, '2024-01-01', 3),
(28, 'Cloud Platform Expertise', 'Advanced knowledge of cloud platforms and services', 3, 13, 13, 1.0, 100, 'percentage', TRUE, '2024-01-01', 4);

-- Insert assessment criteria inheritance (how criteria flow down the hierarchy)
-- HR Level criteria apply to ALL teams
INSERT INTO assessment_criteria_inheritance (criteria_id, target_organizational_unit_id, inherited_from_unit_id, inheritance_type, is_overridable) VALUES
-- HR criteria (1-6) apply to all teams
(1, 11, NULL, 'hr_mandated', FALSE), -- Ethics applies to Frontend Team
(1, 12, NULL, 'hr_mandated', FALSE), -- Ethics applies to Backend Team
(1, 13, NULL, 'hr_mandated', FALSE), -- Ethics applies to DevOps Team
(2, 11, NULL, 'hr_mandated', FALSE), -- Communication applies to Frontend Team
(2, 12, NULL, 'hr_mandated', FALSE), -- Communication applies to Backend Team
(2, 13, NULL, 'hr_mandated', FALSE), -- Communication applies to DevOps Team
(3, 11, NULL, 'hr_mandated', FALSE), -- Teamwork applies to Frontend Team
(3, 12, NULL, 'hr_mandated', FALSE), -- Teamwork applies to Backend Team
(3, 13, NULL, 'hr_mandated', FALSE), -- Teamwork applies to DevOps Team
(4, 11, NULL, 'hr_mandated', FALSE), -- Adaptability applies to Frontend Team
(4, 12, NULL, 'hr_mandated', FALSE), -- Adaptability applies to Backend Team
(4, 13, NULL, 'hr_mandated', FALSE), -- Adaptability applies to DevOps Team
(5, 11, NULL, 'hr_mandated', FALSE), -- Time Management applies to Frontend Team
(5, 12, NULL, 'hr_mandated', FALSE), -- Time Management applies to Backend Team
(5, 13, NULL, 'hr_mandated', FALSE), -- Time Management applies to DevOps Team
(6, 11, NULL, 'hr_mandated', FALSE), -- Customer Focus applies to Frontend Team
(6, 12, NULL, 'hr_mandated', FALSE), -- Customer Focus applies to Backend Team
(6, 13, NULL, 'hr_mandated', FALSE), -- Customer Focus applies to DevOps Team

-- Technology Division criteria (7-11) apply to all tech teams
(7, 11, 2, 'organizational_inherited', TRUE),  -- Technical Excellence to Frontend Team
(7, 12, 2, 'organizational_inherited', TRUE),  -- Technical Excellence to Backend Team
(7, 13, 2, 'organizational_inherited', TRUE),  -- Technical Excellence to DevOps Team
(8, 11, 2, 'organizational_inherited', TRUE),  -- Code Quality to Frontend Team
(8, 12, 2, 'organizational_inherited', TRUE),  -- Code Quality to Backend Team
(8, 13, 2, 'organizational_inherited', TRUE),  -- Code Quality to DevOps Team
(9, 11, 2, 'organizational_inherited', TRUE),  -- System Design to Frontend Team
(9, 12, 2, 'organizational_inherited', TRUE),  -- System Design to Backend Team
(9, 13, 2, 'organizational_inherited', TRUE),  -- System Design to DevOps Team
(10, 11, 2, 'organizational_inherited', TRUE), -- Security to Frontend Team
(10, 12, 2, 'organizational_inherited', TRUE), -- Security to Backend Team
(10, 13, 2, 'organizational_inherited', TRUE), -- Security to DevOps Team
(11, 11, 2, 'organizational_inherited', TRUE), -- Innovation to Frontend Team
(11, 12, 2, 'organizational_inherited', TRUE), -- Innovation to Backend Team
(11, 13, 2, 'organizational_inherited', TRUE), -- Innovation to DevOps Team

-- Team-specific criteria apply only to their respective teams
(17, 11, NULL, 'team_specific', FALSE), -- React/TypeScript to Frontend Team
(18, 11, NULL, 'team_specific', FALSE), -- UI/UX Implementation to Frontend Team
(19, 11, NULL, 'team_specific', FALSE), -- Performance Optimization to Frontend Team
(20, 11, NULL, 'team_specific', FALSE), -- Testing to Frontend Team
(21, 12, NULL, 'team_specific', FALSE), -- API Design to Backend Team
(22, 12, NULL, 'team_specific', FALSE), -- Database Design to Backend Team
(23, 12, NULL, 'team_specific', FALSE), -- Microservices to Backend Team
(24, 12, NULL, 'team_specific', FALSE), -- Performance & Scalability to Backend Team
(25, 13, NULL, 'team_specific', FALSE), -- Infrastructure as Code to DevOps Team
(26, 13, NULL, 'team_specific', FALSE), -- CI/CD Pipeline to DevOps Team
(27, 13, NULL, 'team_specific', FALSE), -- Monitoring to DevOps Team
(28, 13, NULL, 'team_specific', FALSE); -- Cloud Platform to DevOps Team

-- Link assessment criteria to templates
-- HR Level Template (Template ID 1) - HR Corporate Standards
INSERT INTO assessment_template_criteria (template_id, criteria_id, is_required, order_index) VALUES
(1, 1, TRUE, 1), -- Professional Ethics & Integrity
(1, 2, TRUE, 2), -- Communication Skills
(1, 3, TRUE, 3), -- Collaboration & Teamwork
(1, 4, TRUE, 4), -- Adaptability & Learning
(1, 5, TRUE, 5), -- Time Management & Reliability
(1, 6, TRUE, 6); -- Customer Focus

-- Annual Performance Review Template (Template ID 2) - Includes HR criteria
INSERT INTO assessment_template_criteria (template_id, criteria_id, is_required, order_index) VALUES
(2, 1, TRUE, 1), -- Professional Ethics & Integrity
(2, 2, TRUE, 2), -- Communication Skills
(2, 3, TRUE, 3), -- Collaboration & Teamwork
(2, 4, TRUE, 4), -- Adaptability & Learning
(2, 5, TRUE, 5), -- Time Management & Reliability
(2, 6, TRUE, 6); -- Customer Focus

-- Technology Division Template (Template ID 4)
INSERT INTO assessment_template_criteria (template_id, criteria_id, is_required, order_index) VALUES
(4, 7, TRUE, 1),  -- Technical Excellence
(4, 8, TRUE, 2),  -- Code Quality & Best Practices
(4, 9, TRUE, 3),  -- System Design & Architecture
(4, 10, TRUE, 4), -- Security Awareness
(4, 11, TRUE, 5); -- Innovation & Problem Solving

-- Product Division Template (Template ID 5)
INSERT INTO assessment_template_criteria (template_id, criteria_id, is_required, order_index) VALUES
(5, 12, TRUE, 1), -- Product Strategy & Vision
(5, 13, TRUE, 2), -- User Experience Focus
(5, 14, TRUE, 3), -- Data-Driven Decision Making
(5, 15, TRUE, 4), -- Stakeholder Management
(5, 16, TRUE, 5); -- Market Research & Analysis

-- Frontend Engineering Template (Template ID 7)
INSERT INTO assessment_template_criteria (template_id, criteria_id, is_required, order_index) VALUES
(7, 17, TRUE, 1), -- React/TypeScript Proficiency
(7, 18, TRUE, 2), -- UI/UX Implementation
(7, 19, TRUE, 3), -- Performance Optimization
(7, 20, TRUE, 4); -- Testing & Quality Assurance

-- Backend Engineering Template (Template ID 8)
INSERT INTO assessment_template_criteria (template_id, criteria_id, is_required, order_index) VALUES
(8, 21, TRUE, 1), -- API Design & Development
(8, 22, TRUE, 2), -- Database Design & Optimization
(8, 23, TRUE, 3), -- Microservices Architecture
(8, 24, TRUE, 4); -- Performance & Scalability

-- DevOps Engineering Template (Template ID 9)
INSERT INTO assessment_template_criteria (template_id, criteria_id, is_required, order_index) VALUES
(9, 25, TRUE, 1), -- Infrastructure as Code
(9, 26, TRUE, 2), -- CI/CD Pipeline Management
(9, 27, TRUE, 3), -- Monitoring & Observability
(9, 28, TRUE, 4); -- Cloud Platform Expertise

-- Insert assessment scoring scales
INSERT INTO assessment_scoring_scales (id, name, description, organizational_unit_id, created_by_id) VALUES
(1, 'Standard Performance Scale', 'Company-wide standard 5-level performance scoring scale', NULL, 1),
(2, 'Technical Excellence Scale', 'Technology Division technical performance scale', 2, 2),
(3, 'Product Innovation Scale', 'Product Division innovation and strategy scale', 3, 3),
(4, 'Engineering Team Scale', 'Engineering team specific technical scale', 11, 11),
(5, 'DevOps Operational Scale', 'DevOps team operational excellence scale', 13, 13);

-- Insert scoring scale levels for Standard Performance Scale (Company-wide)
INSERT INTO assessment_scoring_scale_levels (id, scoring_scale_id, level_name, level_code, min_performance_score, max_performance_score, points_awarded, description, order_index) VALUES
(1, 1, 'Below Expectations', 'D', 0.0, 59.99, 25, 'Performance does not meet minimum standards', 1),
(2, 1, 'Partially Meets Expectations', 'C', 60.0, 69.99, 50, 'Performance meets some but not all expectations', 2),
(3, 1, 'Meets Expectations', 'B', 70.0, 84.99, 75, 'Performance consistently meets all expectations', 3),
(4, 1, 'Exceeds Expectations', 'A', 85.0, 94.99, 90, 'Performance consistently exceeds expectations', 4),
(5, 1, 'Outstanding Performance', 'A+', 95.0, 100.0, 100, 'Exceptional performance that significantly exceeds all expectations', 5);

-- Insert scoring scale levels for Technical Excellence Scale
INSERT INTO assessment_scoring_scale_levels (id, scoring_scale_id, level_name, level_code, min_performance_score, max_performance_score, points_awarded, description, order_index) VALUES
(6, 2, 'Developing', 'D', 0.0, 64.99, 30, 'Technical skills are developing, requires guidance', 1),
(7, 2, 'Competent', 'C', 65.0, 74.99, 55, 'Solid technical competency, works independently', 2),
(8, 2, 'Proficient', 'B', 75.0, 84.99, 75, 'Strong technical skills, mentors others', 3),
(9, 2, 'Expert', 'A', 85.0, 94.99, 90, 'Technical expert, drives innovation', 4),
(10, 2, 'Technical Leader', 'A+', 95.0, 100.0, 100, 'Technical thought leader, shapes technology direction', 5);

-- Insert scoring scale levels for Product Innovation Scale
INSERT INTO assessment_scoring_scale_levels (id, scoring_scale_id, level_name, level_code, min_performance_score, max_performance_score, points_awarded, description, order_index) VALUES
(11, 3, 'Basic Understanding', 'D', 0.0, 64.99, 25, 'Basic product knowledge, follows established processes', 1),
(12, 3, 'Product Contributor', 'C', 65.0, 74.99, 50, 'Contributes to product decisions, understands user needs', 2),
(13, 3, 'Product Driver', 'B', 75.0, 84.99, 75, 'Drives product initiatives, strong market understanding', 3),
(14, 3, 'Product Innovator', 'A', 85.0, 94.99, 90, 'Innovates product solutions, anticipates market trends', 4),
(15, 3, 'Product Visionary', 'A+', 95.0, 100.0, 100, 'Sets product vision, transforms market understanding', 5);

-- Insert scoring scale levels for Engineering Team Scale
INSERT INTO assessment_scoring_scale_levels (id, scoring_scale_id, level_name, level_code, min_performance_score, max_performance_score, points_awarded, description, order_index) VALUES
(16, 4, 'Junior Level', 'D', 0.0, 69.99, 35, 'Junior engineering skills, requires supervision', 1),
(17, 4, 'Mid Level', 'C', 70.0, 79.99, 60, 'Solid engineering skills, works independently', 2),
(18, 4, 'Senior Level', 'B', 80.0, 89.99, 80, 'Senior engineering skills, leads technical decisions', 3),
(19, 4, 'Principal Level', 'A', 90.0, 96.99, 95, 'Principal engineer, architects complex systems', 4),
(20, 4, 'Distinguished Engineer', 'A+', 97.0, 100.0, 100, 'Distinguished technical leadership, industry recognition', 5);

-- Insert scoring scale levels for DevOps Operational Scale
INSERT INTO assessment_scoring_scale_levels (id, scoring_scale_id, level_name, level_code, min_performance_score, max_performance_score, points_awarded, description, order_index) VALUES
(21, 5, 'Operations Support', 'D', 0.0, 69.99, 30, 'Basic operational support, follows procedures', 1),
(22, 5, 'Operations Specialist', 'C', 70.0, 79.99, 55, 'Specialized operational knowledge, improves processes', 2),
(23, 5, 'Operations Expert', 'B', 80.0, 89.99, 75, 'Expert operational skills, designs robust systems', 3),
(24, 5, 'Operations Architect', 'A', 90.0, 96.99, 90, 'Architects operational excellence, drives automation', 4),
(25, 5, 'Operations Visionary', 'A+', 97.0, 100.0, 100, 'Visionary operational leadership, transforms infrastructure', 5);

-- =====================================================
-- MONTHLY DASHBOARDS SEED DATA
-- =====================================================

-- Insert Monthly Dashboard KPIs (10 required KPIs)
INSERT INTO monthly_dashboard_kpis (id, name, description, help_text, data_type, unit, calculation_method, green_threshold_min, green_threshold_max, yellow_threshold_min, yellow_threshold_max, red_threshold_min, red_threshold_max, is_higher_better, display_order, is_active) VALUES
(1, 'FTE', 'Full-Time Equivalent headcount', 'Total number of full-time equivalent employees in the team. Auto-calculated from HR system.', 'number', 'count', 'auto_calculated', NULL, NULL, NULL, NULL, NULL, NULL, TRUE, 1, TRUE),
(2, 'Dashboard Completion Date', 'Date when monthly dashboard was completed', 'The date when the team completed their monthly dashboard submission. Earlier completion is better.', 'number', 'days', 'manual', 1, 5, 6, 10, 11, 31, FALSE, 2, TRUE),
(3, 'Attrition Rate', 'Monthly employee attrition rate', 'Percentage of employees who left the team this month. Lower is better.', 'percentage', '%', 'manual', 0, 2, 2.1, 5, 5.1, 100, FALSE, 3, TRUE),
(4, 'SLA Performance', 'Service Level Agreement performance', 'Percentage of SLA targets met this month. Higher is better.', 'percentage', '%', 'manual', 95, 100, 90, 94.9, 0, 89.9, TRUE, 4, TRUE),
(5, 'Billable Utilization', 'Billable hours utilization rate', 'Percentage of available hours that were billable to clients. Target varies by role.', 'percentage', '%', 'manual', 75, 85, 65, 74.9, 0, 64.9, TRUE, 5, TRUE),
(6, 'Time Registration Compliance', 'Time registration compliance rate', 'Percentage of employees who properly registered their time. Higher is better.', 'percentage', '%', 'manual', 95, 100, 85, 94.9, 0, 84.9, TRUE, 6, TRUE),
(7, 'Compliance (Non-compliance count)', 'Number of compliance violations', 'Total number of compliance violations this month. Any violation results in red status.', 'count', 'violations', 'manual', 0, 0, NULL, NULL, 1, 999, FALSE, 7, TRUE),
(8, 'AB (Annual Budget Performance)', 'Annual budget performance percentage', 'Percentage of annual budget utilized vs planned. Target is 100% by year end.', 'percentage', '%', 'manual', 95, 105, 85, 94.9, 0, 84.9, TRUE, 8, TRUE),
(9, 'PTO (Vacation and Sick Leaves)', 'Planned Time Off utilization', 'Percentage of allocated PTO used. Balanced usage is optimal.', 'percentage', '%', 'manual', 60, 80, 40, 59.9, 0, 39.9, TRUE, 9, TRUE),
(10, 'RTO (Return to Office Compliance)', 'Return to office compliance rate', 'Percentage compliance with return-to-office policy. Higher is better.', 'percentage', '%', 'manual', 90, 100, 75, 89.9, 0, 74.9, TRUE, 10, TRUE);

-- Insert Monthly Dashboard Team Targets (sample targets for key teams)
INSERT INTO monthly_dashboard_team_targets (organizational_unit_id, kpi_id, target_value, effective_from, effective_until, notes, created_by_user_id) VALUES
-- Frontend Engineering Team (ID: 11) targets
(11, 1, 12, '2024-01-01', NULL, 'Target team size for frontend development', 11),
(11, 2, 3, '2024-01-01', NULL, 'Complete dashboard within 3 days of month end', 11),
(11, 3, 2, '2024-01-01', NULL, 'Keep attrition below 2% monthly', 11),
(11, 4, 95, '2024-01-01', NULL, 'Maintain 95% SLA performance', 11),
(11, 5, 80, '2024-01-01', NULL, '80% billable utilization target', 11),
(11, 6, 98, '2024-01-01', NULL, '98% time registration compliance', 11),
(11, 7, 0, '2024-01-01', NULL, 'Zero compliance violations', 11),
(11, 8, 100, '2024-01-01', NULL, 'On track with annual budget', 11),
(11, 9, 70, '2024-01-01', NULL, '70% PTO utilization target', 11),
(11, 10, 95, '2024-01-01', NULL, '95% RTO compliance', 11),

-- Backend Engineering Team (ID: 12) targets
(12, 1, 15, '2024-01-01', NULL, 'Target team size for backend development', 12),
(12, 2, 3, '2024-01-01', NULL, 'Complete dashboard within 3 days of month end', 12),
(12, 3, 1.5, '2024-01-01', NULL, 'Keep attrition below 1.5% monthly', 12),
(12, 4, 98, '2024-01-01', NULL, 'Maintain 98% SLA performance', 12),
(12, 5, 85, '2024-01-01', NULL, '85% billable utilization target', 12),
(12, 6, 99, '2024-01-01', NULL, '99% time registration compliance', 12),
(12, 7, 0, '2024-01-01', NULL, 'Zero compliance violations', 12),
(12, 8, 100, '2024-01-01', NULL, 'On track with annual budget', 12),
(12, 9, 75, '2024-01-01', NULL, '75% PTO utilization target', 12),
(12, 10, 90, '2024-01-01', NULL, '90% RTO compliance', 12),

-- DevOps & Infrastructure Team (ID: 13) targets
(13, 1, 8, '2024-01-01', NULL, 'Target team size for DevOps', 13),
(13, 2, 2, '2024-01-01', NULL, 'Complete dashboard within 2 days of month end', 13),
(13, 3, 1, '2024-01-01', NULL, 'Keep attrition below 1% monthly', 13),
(13, 4, 99, '2024-01-01', NULL, 'Maintain 99% SLA performance', 13),
(13, 5, 75, '2024-01-01', NULL, '75% billable utilization target', 13),
(13, 6, 100, '2024-01-01', NULL, '100% time registration compliance', 13),
(13, 7, 0, '2024-01-01', NULL, 'Zero compliance violations', 13),
(13, 8, 100, '2024-01-01', NULL, 'On track with annual budget', 13),
(13, 9, 65, '2024-01-01', NULL, '65% PTO utilization target', 13),
(13, 10, 85, '2024-01-01', NULL, '85% RTO compliance', 13);

-- Insert sample Monthly Dashboard Submissions for current month (July 2025)
INSERT INTO monthly_dashboard_submissions (id, organizational_unit_id, submission_year, submission_month, status, submitted_by_user_id, submitted_at, completion_date, due_date, notes) VALUES
(1, 11, 2025, 7, 'submitted', 11, '2025-07-03 14:30:00', '2025-07-03', '2025-07-05', 'Frontend team dashboard for July 2025'),
(2, 12, 2025, 7, 'approved', 12, '2025-07-02 16:45:00', '2025-07-02', '2025-07-05', 'Backend team dashboard for July 2025'),
(3, 13, 2025, 7, 'submitted', 13, '2025-07-04 10:15:00', '2025-07-04', '2025-07-05', 'DevOps team dashboard for July 2025'),
(4, 14, 2025, 7, 'draft', 14, NULL, NULL, '2025-07-05', 'ML team dashboard in progress'),
(5, 15, 2025, 7, 'submitted', 15, '2025-07-03 11:20:00', '2025-07-03', '2025-07-05', 'Data Engineering team dashboard for July 2025');

-- Insert sample KPI values for the submissions
INSERT INTO monthly_dashboard_kpi_values (submission_id, kpi_id, actual_value, target_value, traffic_light_status, comments, data_source) VALUES
-- Frontend Engineering Team (submission_id: 1)
(1, 1, 12, 12, 'green', 'Team at target size', 'HR System'),
(1, 2, 3, 3, 'green', 'Completed on time', 'Manual Entry'),
(1, 3, 0, 2, 'green', 'No attrition this month', 'HR System'),
(1, 4, 97, 95, 'green', 'Exceeded SLA targets', 'Monitoring System'),
(1, 5, 82, 80, 'green', 'Good billable utilization', 'Time Tracking'),
(1, 6, 96, 98, 'yellow', 'Slightly below target', 'Time Tracking'),
(1, 7, 0, 0, 'green', 'No compliance issues', 'Compliance System'),
(1, 8, 58, 58.3, 'green', 'On track with budget (July = 58.3% of year)', 'Finance System'),
(1, 9, 68, 70, 'yellow', 'PTO usage slightly below target', 'HR System'),
(1, 10, 94, 95, 'yellow', 'RTO compliance close to target', 'Badge System'),

-- Backend Engineering Team (submission_id: 2)
(2, 1, 15, 15, 'green', 'Team at target size', 'HR System'),
(2, 2, 2, 3, 'green', 'Completed early', 'Manual Entry'),
(2, 3, 0, 1.5, 'green', 'No attrition this month', 'HR System'),
(2, 4, 99, 98, 'green', 'Excellent SLA performance', 'Monitoring System'),
(2, 5, 87, 85, 'green', 'Excellent billable utilization', 'Time Tracking'),
(2, 6, 100, 99, 'green', 'Perfect time registration', 'Time Tracking'),
(2, 7, 0, 0, 'green', 'No compliance issues', 'Compliance System'),
(2, 8, 57.8, 58.3, 'green', 'Slightly under budget (good)', 'Finance System'),
(2, 9, 76, 75, 'green', 'Good PTO utilization', 'HR System'),
(2, 10, 91, 90, 'green', 'Good RTO compliance', 'Badge System'),

-- DevOps & Infrastructure Team (submission_id: 3)
(3, 1, 8, 8, 'green', 'Team at target size', 'HR System'),
(3, 2, 4, 2, 'yellow', 'Completed but later than target', 'Manual Entry'),
(3, 3, 0, 1, 'green', 'No attrition this month', 'HR System'),
(3, 4, 99.5, 99, 'green', 'Outstanding SLA performance', 'Monitoring System'),
(3, 5, 73, 75, 'yellow', 'Billable utilization below target', 'Time Tracking'),
(3, 6, 100, 100, 'green', 'Perfect time registration', 'Time Tracking'),
(3, 7, 0, 0, 'green', 'No compliance issues', 'Compliance System'),
(3, 8, 59.1, 58.3, 'yellow', 'Slightly over budget', 'Finance System'),
(3, 9, 62, 65, 'yellow', 'PTO usage below target', 'HR System'),
(3, 10, 83, 85, 'yellow', 'RTO compliance below target', 'Badge System'),

-- Data Engineering Team (submission_id: 5)
(5, 1, 7, 8, 'yellow', 'One position vacant', 'HR System'),
(5, 2, 3, 3, 'green', 'Completed on time', 'Manual Entry'),
(5, 3, 0, 1.5, 'green', 'No attrition this month', 'HR System'),
(5, 4, 96, 95, 'green', 'Good SLA performance', 'Monitoring System'),
(5, 5, 78, 80, 'yellow', 'Billable utilization slightly below', 'Time Tracking'),
(5, 6, 95, 98, 'yellow', 'Time registration needs improvement', 'Time Tracking'),
(5, 7, 0, 0, 'green', 'No compliance issues', 'Compliance System'),
(5, 8, 58.5, 58.3, 'green', 'On track with budget', 'Finance System'),
(5, 9, 71, 70, 'green', 'Good PTO utilization', 'HR System'),
(5, 10, 89, 90, 'yellow', 'RTO compliance close to target', 'Badge System');
