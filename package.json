{"name": "ehrx", "version": "1.0.0", "description": "A secure, scalable web application for employee performance evaluations, replacing the previous Excel-based system.", "main": "demo-server.js", "directories": {"doc": "docs"}, "scripts": {"start": "./scripts/ehrx-manager.sh start dev", "start:dev": "./scripts/ehrx-manager.sh start dev", "start:prod": "./scripts/ehrx-manager.sh start prod", "stop": "./scripts/ehrx-manager.sh stop", "stop:dev": "./scripts/ehrx-manager.sh stop dev", "stop:prod": "./scripts/ehrx-manager.sh stop prod", "restart": "./scripts/ehrx-manager.sh restart", "restart:dev": "./scripts/ehrx-manager.sh restart dev", "restart:prod": "./scripts/ehrx-manager.sh restart prod", "status": "./scripts/ehrx-manager.sh status", "health": "./scripts/ehrx-manager.sh health", "monitor": "./scripts/ehrx-manager.sh monitor", "logs": "./scripts/ehrx-manager.sh logs", "logs:error": "./scripts/ehrx-manager.sh logs error", "logs:audit": "./scripts/ehrx-manager.sh logs audit", "deps": "./scripts/ehrx-manager.sh deps", "fix-deps": "./scripts/ehrx-manager.sh fix-deps", "cleanup": "./scripts/ehrx-manager.sh cleanup", "demo": "node demo-server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"express": "^4.21.2", "http-proxy-middleware": "^3.0.5"}}