#!/bin/bash

# EHRX Application Runner Script
# This script starts both backend and frontend servers and opens a browser preview

# Colors for better visibility
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting EHRX Application...${NC}"

# Kill any existing node processes that might be running
echo -e "Stopping any existing processes..."
pkill -f "node" || true

# Start Backend (NestJS) Server
cd /var/www/ehrx/backend
echo -e "${GREEN}Starting backend server...${NC}"
npm run start:dev > /var/www/ehrx/backend-logs.txt 2>&1 &
BACKEND_PID=$!
echo -e "Backend server started with PID: $BACKEND_PID"

# Wait for backend to initialize
echo -e "Waiting for backend to initialize..."
sleep 5

# Start Frontend (React) Server
cd /var/www/ehrx/frontend
echo -e "${GREEN}Starting frontend server...${NC}"
npm run start > /var/www/ehrx/frontend-logs.txt 2>&1 &
FRONTEND_PID=$!
echo -e "Frontend server started with PID: $FRONTEND_PID"

# Save PIDs to file for later cleanup
echo "$BACKEND_PID $FRONTEND_PID" > /var/www/ehrx/app-pids.txt

echo -e "${GREEN}EHRX Application is starting!${NC}"
echo -e "Frontend will be available at: http://localhost:3000"
echo -e "Backend API available at: http://localhost:3001/api"
echo -e "Check logs at: /var/www/ehrx/frontend-logs.txt and /var/www/ehrx/backend-logs.txt"
echo -e "${YELLOW}Press F5 in Windsurf EDI to open the browser preview${NC}"
