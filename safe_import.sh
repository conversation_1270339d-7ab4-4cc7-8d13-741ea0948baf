#!/bin/bash

# Set PostgreSQL password to avoid prompts
export PGPASSWORD="EhrX2024!SecurePass"

echo "🔄 Starting safe database import process..."

# 1. Drop existing tables except users and users_backup
echo "🗑️  Dropping existing tables (except users)..."
psql -h localhost -U ehrx_user -d ehrx -c "
DO \$\$ DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename NOT IN ('users', 'users_backup')) LOOP
        EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
END \$\$;
"

# 2. Drop existing types to avoid conflicts
echo "🔧 Dropping existing types..."
psql -h localhost -U ehrx_user -d ehrx -c "
DO \$\$ DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT typname FROM pg_type WHERE typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public') AND typtype = 'e') LOOP
        EXECUTE 'DROP TYPE IF EXISTS ' || quote_ident(r.typname) || ' CASCADE';
    END LOOP;
END \$\$;
"

# 3. Import the final cleaned database
echo "📥 Importing final cleaned database..."
psql -h localhost -U ehrx_user -d ehrx -f final_postgresql_dump.sql 2>&1 | grep -E "(ERROR|CREATE|INSERT)" | head -20

# 4. Restore our working admin user if users table was recreated
echo "👤 Ensuring admin user exists..."
psql -h localhost -U ehrx_user -d ehrx -c "
INSERT INTO users (email, password, first_name, last_name, role, is_active, account_status, must_change_password, failed_login_attempts)
VALUES ('<EMAIL>', '\$2b\$10\$8pfFWzQpA8BOkiY3Z151QuwG7uJOghwpx39hrpdKW8sqHeYD7VjHe', 'Admin', 'User', 'hr_admin', true, 'active', false, 0)
ON CONFLICT (email) DO UPDATE SET
  password = EXCLUDED.password,
  role = EXCLUDED.role,
  is_active = EXCLUDED.is_active,
  account_status = EXCLUDED.account_status;
"

# 5. Check final status
echo "✅ Checking final database status..."
echo "Tables created:"
psql -h localhost -U ehrx_user -d ehrx -c "\dt" | wc -l
echo "Total users:"
psql -h localhost -U ehrx_user -d ehrx -c "SELECT COUNT(*) as total_users FROM users;"
echo "ENUM types created:"
psql -h localhost -U ehrx_user -d ehrx -c "SELECT COUNT(*) as total_enums FROM pg_type WHERE typtype = 'e';"

echo "🎉 Safe database import completed!"
