# EHRX Enterprise Service Management Scripts

This directory contains enterprise-grade scripts for managing the EHRX application with advanced features including PID management, dependency verification, environment-specific configurations, structured logging, and health monitoring with automatic restart capabilities.

## 🚀 Quick Start

### Master Script (Recommended)
```bash
# Start development environment
./scripts/ehrx-manager.sh start dev

# Start production environment
./scripts/ehrx-manager.sh start prod

# Check status
./scripts/ehrx-manager.sh status

# Run health checks
./scripts/ehrx-manager.sh health

# View logs
./scripts/ehrx-manager.sh logs main
```

### Individual Scripts
```bash
# Development
./scripts/start-dev.sh
./scripts/stop-dev.sh

# Production
./scripts/start-prod.sh
./scripts/stop-prod.sh

# Health monitoring
./scripts/health-monitor.sh &
```

## 📁 Script Overview

### Core Management Scripts

#### `ehrx-manager.sh` - Master Service Manager
**Enterprise-grade master script that orchestrates all EHRX services**

**Features:**
- Unified interface for all operations
- Environment-specific management (dev/prod)
- Integrated logging and monitoring
- Dependency verification
- Health checks and auto-restart

**Usage:**
```bash
./ehrx-manager.sh <command> [options]

Commands:
  start [dev|prod]     - Start services in specified environment
  stop [dev|prod]      - Stop services in specified environment
  restart [dev|prod]   - Restart services in specified environment
  status               - Show service status
  health               - Run health checks
  monitor              - Start health monitoring daemon
  logs [service]       - Show logs (main|error|audit|performance)
  deps                 - Check dependencies
  fix-deps             - Auto-fix dependency issues
  cleanup              - Clean up PID files and temporary data

Options:
  --env <env>          - Override environment (dev|prod)
  --verbose            - Enable verbose logging
  --force              - Force operation (skip confirmations)
```

### Environment-Specific Scripts

#### `start-dev.sh` - Development Environment Startup
**Optimized for development with hot reloading and debugging**

**Features:**
- Hot reloading for frontend and backend
- Debug logging enabled
- Automatic dependency installation
- Port conflict resolution
- Development-specific configurations

**Ports:**
- Backend: 4000 (NestJS with hot reload)
- Frontend: 3080 (React development server)

#### `start-prod.sh` - Production Environment Startup
**Optimized for production with security and performance**

**Features:**
- Production builds and optimizations
- SSL/HTTPS support
- PM2 process management
- Security headers and validation
- Performance monitoring
- Automated log rotation

**Requirements:**
- `.env.production` file with production variables
- SSL certificates (if HTTPS enabled)
- PM2 installed (optional but recommended)

#### `stop-dev.sh` / `stop-prod.sh` - Environment Stop Scripts
**Graceful shutdown with cleanup**

**Features:**
- Graceful process termination (TERM then KILL)
- PID file cleanup
- Port verification
- Resource cleanup

### Core Infrastructure Scripts

#### `pid-manager.sh` - Process Management System
**Enterprise-grade PID file management**

**Features:**
- PID file creation and management
- Process health checking
- Graceful shutdown with fallback
- Service status reporting
- Automatic cleanup

**Functions:**
- `save_pid(service, pid)` - Save process PID
- `read_pid(service)` - Read process PID
- `is_process_running(pid)` - Check if process is running
- `stop_service(service)` - Stop service gracefully
- `cleanup_all_pid_files()` - Clean up all PID files

#### `dependency-checker.sh` - Dependency Verification
**Comprehensive dependency and environment validation**

**Features:**
- Node.js version verification
- npm availability checking
- node_modules validation
- Environment variable verification
- Database connectivity testing
- System resource checking
- Auto-fix capabilities

**Checks:**
- ✅ Node.js version (>= 16.0.0)
- ✅ npm availability
- ✅ Backend dependencies (NestJS, TypeORM, etc.)
- ✅ Frontend dependencies (React, Material-UI, etc.)
- ✅ Environment variables
- ✅ Database connectivity
- ✅ System resources (memory, disk)
- ✅ Port availability

#### `logging-system.sh` - Enterprise Logging
**Structured logging with rotation and monitoring**

**Features:**
- Multiple log levels (TRACE, DEBUG, INFO, WARN, ERROR, FATAL, AUDIT, SECURITY)
- Structured JSON logging
- Automatic log rotation
- Log compression and archival
- Performance logging
- Security audit logging

**Log Files:**
- `/var/log/ehrx/scripts/ehrx-main.log` - Main application log
- `/var/log/ehrx/scripts/ehrx-error.log` - Error log
- `/var/log/ehrx/audit/ehrx-audit.log` - Audit log
- `/var/log/ehrx/scripts/ehrx-performance.log` - Performance log
- `/var/log/ehrx/audit/ehrx-security.log` - Security log

**Functions:**
- `log_info(message, component, context)` - Info logging
- `log_error(message, component, context)` - Error logging
- `log_audit(message, component, context)` - Audit logging
- `log_performance(operation, duration, status)` - Performance logging
- `rotate_logs()` - Manual log rotation

#### `health-monitor.sh` - Health Monitoring & Auto-Restart
**Continuous health monitoring with automatic recovery**

**Features:**
- Multi-layered health checks (process, port, HTTP)
- Automatic restart with exponential backoff
- Alert notifications (email, webhook)
- Configurable monitoring intervals
- Restart attempt limiting
- Performance metrics

**Health Checks:**
- Process health (PID validation)
- Port availability
- HTTP endpoint health
- Service-specific health endpoints

**Configuration:**
```bash
MONITOR_INTERVAL=30              # Check interval in seconds
MAX_RESTART_ATTEMPTS=3           # Maximum restart attempts
RESTART_COOLDOWN=60              # Cooldown between restarts
ENABLE_EMAIL_ALERTS=true         # Email notifications
ENABLE_WEBHOOK_ALERTS=true       # Webhook notifications
```

## 🔧 Configuration

### Environment Variables

#### Development (.env)
```bash
NODE_ENV=development
DB_HOST=localhost
DB_USERNAME=ehrx_user
DB_PASSWORD=your_password
DB_NAME=ehrx_dev
BACKEND_PORT=4000
FRONTEND_PORT=3080
LOG_LEVEL=debug
```

#### Production (.env.production)
```bash
NODE_ENV=production
DB_HOST=your_prod_host
DB_USERNAME=ehrx_prod_user
DB_PASSWORD=your_secure_password
DB_NAME=ehrx_production
JWT_SECRET=your_32_character_secret_key_here
BACKEND_PORT=4000
FRONTEND_PORT=3080
LOG_LEVEL=info
ENABLE_SSL=true
SSL_CERT_PATH=/etc/ssl/certs/ehrx.crt
SSL_KEY_PATH=/etc/ssl/private/ehrx.key
```

### Monitoring Configuration
```bash
MONITOR_INTERVAL=30
MAX_RESTART_ATTEMPTS=3
RESTART_COOLDOWN=60
HEALTH_CHECK_TIMEOUT=10
ENABLE_EMAIL_ALERTS=true
ALERT_EMAIL=<EMAIL>
ENABLE_WEBHOOK_ALERTS=true
WEBHOOK_URL=https://your-webhook-url.com/alerts
```

## 📊 Monitoring & Logging

### Log Locations
- **Script Logs:** `/var/log/ehrx/scripts/`
- **Service Logs:** `/var/log/ehrx/services/`
- **Audit Logs:** `/var/log/ehrx/audit/`
- **Archives:** `/var/log/ehrx/archive/`

### Log Rotation
- **Automatic:** When files exceed 10MB
- **Manual:** `./scripts/logging-system.sh rotate`
- **Retention:** 30 days
- **Compression:** Enabled

### Health Monitoring
- **Interval:** 30 seconds (configurable)
- **Restart Policy:** 3 attempts with 60s cooldown
- **Alerts:** Email and webhook notifications
- **Metrics:** Performance and availability tracking

## 🔒 Security Features

### NIS2 Compliance
- ✅ Security headers (Helmet.js)
- ✅ HTTPS enforcement
- ✅ CORS configuration
- ✅ Rate limiting
- ✅ Input validation
- ✅ Audit logging
- ✅ Security monitoring

### Authentication & Authorization
- ✅ JWT-based authentication
- ✅ Role-based access control
- ✅ Session management
- ✅ MFA support
- ✅ Password security (bcrypt)

### Database Security
- ✅ TypeORM (no direct SQL)
- ✅ Connection pooling
- ✅ SQL injection prevention
- ✅ Encrypted connections

## 🚨 Troubleshooting

### Common Issues

#### Services Won't Start
```bash
# Check dependencies
./scripts/ehrx-manager.sh deps

# Auto-fix dependencies
./scripts/ehrx-manager.sh fix-deps

# Check logs
./scripts/ehrx-manager.sh logs error
```

#### Port Conflicts
```bash
# Check what's using ports
lsof -i :4000
lsof -i :3080

# Force stop and cleanup
./scripts/ehrx-manager.sh stop --force
./scripts/ehrx-manager.sh cleanup
```

#### Database Connection Issues
```bash
# Test database connectivity
mysql -h$DB_HOST -u$DB_USERNAME -p$DB_PASSWORD $DB_NAME

# Check environment variables
./scripts/dependency-checker.sh
```

#### Performance Issues
```bash
# Check performance logs
./scripts/ehrx-manager.sh logs performance

# Monitor system resources
./scripts/ehrx-manager.sh health
```

### Log Analysis
```bash
# Analyze recent logs
./scripts/logging-system.sh analyze

# Analyze specific timeframe
./scripts/logging-system.sh analyze /var/log/ehrx/scripts/ehrx-main.log 24

# Check error patterns
grep -i error /var/log/ehrx/scripts/ehrx-error.log | tail -20
```

## 📈 Performance Optimization

### Development
- Hot reloading enabled
- Source maps for debugging
- Verbose logging
- Development middleware

### Production
- Minified builds
- Gzip compression
- PM2 clustering
- Log level optimization
- Resource monitoring

## 🔄 Maintenance

### Daily Tasks
- Monitor service health
- Check log files
- Verify backup status

### Weekly Tasks
- Rotate logs manually if needed
- Update dependencies
- Security audit review

### Monthly Tasks
- Performance analysis
- Capacity planning
- Security updates

## 📞 Support

For issues or questions:
1. Check logs: `./scripts/ehrx-manager.sh logs error`
2. Run health checks: `./scripts/ehrx-manager.sh health`
3. Review this documentation
4. Contact system administrator

---

**Note:** These scripts are designed for enterprise environments and include comprehensive error handling, logging, and monitoring. Always test in development before deploying to production.
