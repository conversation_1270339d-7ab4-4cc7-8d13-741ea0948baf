#!/bin/bash

# EHRX Dependency Verification System
# Comprehensive checks for database, node_modules, environment variables, and system requirements

# Source PID manager for logging
source "$(dirname "$0")/pid-manager.sh"

# Configuration
PROJECT_ROOT="/var/www/ehrx"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
DATABASE_TIMEOUT=10
NODE_VERSION_MIN="16.0.0"

# Dependency check results
DEPENDENCY_RESULTS=()

# Add result to tracking array
add_result() {
    local check_name=$1
    local status=$2
    local message=$3
    
    DEPENDENCY_RESULTS+=("$check_name:$status:$message")
    
    if [ "$status" = "PASS" ]; then
        log_message "INFO" "✅ $check_name: $message"
    elif [ "$status" = "WARN" ]; then
        log_message "WARN" "⚠️  $check_name: $message"
    else
        log_message "ERROR" "❌ $check_name: $message"
    fi
}

# Check Node.js version
check_node_version() {
    log_message "INFO" "Checking Node.js version"
    
    if ! command -v node &> /dev/null; then
        add_result "Node.js" "FAIL" "Node.js is not installed"
        return 1
    fi
    
    local node_version=$(node --version | sed 's/v//')
    local min_version="$NODE_VERSION_MIN"
    
    # Simple version comparison (works for major.minor.patch)
    if [ "$(printf '%s\n' "$min_version" "$node_version" | sort -V | head -n1)" = "$min_version" ]; then
        add_result "Node.js" "PASS" "Version $node_version (>= $min_version required)"
        return 0
    else
        add_result "Node.js" "FAIL" "Version $node_version is below minimum $min_version"
        return 1
    fi
}

# Check npm availability
check_npm() {
    log_message "INFO" "Checking npm availability"
    
    if ! command -v npm &> /dev/null; then
        add_result "npm" "FAIL" "npm is not installed"
        return 1
    fi
    
    local npm_version=$(npm --version)
    add_result "npm" "PASS" "Version $npm_version available"
    return 0
}

# Check backend node_modules
check_backend_dependencies() {
    log_message "INFO" "Checking backend dependencies"
    
    if [ ! -d "$BACKEND_DIR" ]; then
        add_result "Backend Directory" "FAIL" "Backend directory not found: $BACKEND_DIR"
        return 1
    fi
    
    cd "$BACKEND_DIR" || {
        add_result "Backend Directory" "FAIL" "Cannot access backend directory"
        return 1
    }
    
    if [ ! -f "package.json" ]; then
        add_result "Backend package.json" "FAIL" "package.json not found in backend directory"
        return 1
    fi
    
    if [ ! -d "node_modules" ]; then
        add_result "Backend Dependencies" "FAIL" "node_modules not found - run 'npm install' in backend directory"
        return 1
    fi
    
    # Check if node_modules is up to date
    if [ "package-lock.json" -nt "node_modules" ]; then
        add_result "Backend Dependencies" "WARN" "node_modules may be outdated - consider running 'npm install'"
        return 0
    fi
    
    # Check critical dependencies
    local critical_deps=("@nestjs/core" "@nestjs/common" "typeorm" "mysql2")
    local missing_deps=()
    
    for dep in "${critical_deps[@]}"; do
        if [ ! -d "node_modules/$dep" ]; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        add_result "Backend Dependencies" "FAIL" "Missing critical dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    add_result "Backend Dependencies" "PASS" "All dependencies installed and up to date"
    return 0
}

# Check frontend node_modules
check_frontend_dependencies() {
    log_message "INFO" "Checking frontend dependencies"
    
    if [ ! -d "$FRONTEND_DIR" ]; then
        add_result "Frontend Directory" "FAIL" "Frontend directory not found: $FRONTEND_DIR"
        return 1
    fi
    
    cd "$FRONTEND_DIR" || {
        add_result "Frontend Directory" "FAIL" "Cannot access frontend directory"
        return 1
    }
    
    if [ ! -f "package.json" ]; then
        add_result "Frontend package.json" "FAIL" "package.json not found in frontend directory"
        return 1
    fi
    
    if [ ! -d "node_modules" ]; then
        add_result "Frontend Dependencies" "FAIL" "node_modules not found - run 'npm install' in frontend directory"
        return 1
    fi
    
    # Check if node_modules is up to date
    if [ "package-lock.json" -nt "node_modules" ]; then
        add_result "Frontend Dependencies" "WARN" "node_modules may be outdated - consider running 'npm install'"
        return 0
    fi
    
    # Check critical dependencies
    local critical_deps=("react" "react-dom" "react-scripts" "@mui/material")
    local missing_deps=()
    
    for dep in "${critical_deps[@]}"; do
        if [ ! -d "node_modules/$dep" ]; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        add_result "Frontend Dependencies" "FAIL" "Missing critical dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    add_result "Frontend Dependencies" "PASS" "All dependencies installed and up to date"
    return 0
}

# Check environment variables
check_environment_variables() {
    log_message "INFO" "Checking environment variables"
    
    local required_vars=()
    local missing_vars=()
    local env_file=""
    
    # Determine environment and required variables
    if [ "${NODE_ENV:-development}" = "production" ]; then
        env_file="$BACKEND_DIR/.env.production"
        required_vars=("DB_HOST" "DB_USERNAME" "DB_PASSWORD" "DB_NAME" "JWT_SECRET")
    else
        env_file="$BACKEND_DIR/.env"
        required_vars=("DB_HOST" "DB_USERNAME" "DB_PASSWORD" "DB_NAME")
    fi
    
    # Check if environment file exists
    if [ -f "$env_file" ]; then
        source "$env_file"
        add_result "Environment File" "PASS" "Found environment file: $env_file"
    else
        add_result "Environment File" "WARN" "Environment file not found: $env_file"
    fi
    
    # Check required variables
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        add_result "Environment Variables" "FAIL" "Missing required variables: ${missing_vars[*]}"
        return 1
    fi
    
    # Check JWT secret strength in production
    if [ "${NODE_ENV:-development}" = "production" ] && [ -n "$JWT_SECRET" ]; then
        if [ ${#JWT_SECRET} -lt 32 ]; then
            add_result "JWT Secret" "FAIL" "JWT_SECRET must be at least 32 characters for production"
            return 1
        fi
    fi
    
    add_result "Environment Variables" "PASS" "All required environment variables are set"
    return 0
}

# Check database connectivity
check_database_connectivity() {
    log_message "INFO" "Checking database connectivity"
    
    # Load environment variables
    local env_file=""
    if [ "${NODE_ENV:-development}" = "production" ]; then
        env_file="$BACKEND_DIR/.env.production"
    else
        env_file="$BACKEND_DIR/.env"
    fi
    
    if [ -f "$env_file" ]; then
        source "$env_file"
    fi
    
    # Check if database variables are set
    if [ -z "$DB_HOST" ] || [ -z "$DB_USERNAME" ] || [ -z "$DB_PASSWORD" ] || [ -z "$DB_NAME" ]; then
        add_result "Database Config" "FAIL" "Database configuration variables not set"
        return 1
    fi
    
    # Check if mysql client is available
    if ! command -v mysql &> /dev/null; then
        add_result "MySQL Client" "WARN" "MySQL client not available - cannot test database connectivity"
        return 0
    fi
    
    # Test database connection
    local db_port="${DB_PORT:-3306}"
    local connection_test=$(timeout $DATABASE_TIMEOUT mysql -h"$DB_HOST" -P"$db_port" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SELECT 1;" 2>&1)
    
    if [ $? -eq 0 ]; then
        add_result "Database Connectivity" "PASS" "Successfully connected to database"
        
        # Test specific database
        local db_test=$(timeout $DATABASE_TIMEOUT mysql -h"$DB_HOST" -P"$db_port" -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT 1;" 2>&1)
        
        if [ $? -eq 0 ]; then
            add_result "Database Schema" "PASS" "Database '$DB_NAME' is accessible"
        else
            add_result "Database Schema" "FAIL" "Cannot access database '$DB_NAME'"
            return 1
        fi
    else
        add_result "Database Connectivity" "FAIL" "Cannot connect to database: $connection_test"
        return 1
    fi
    
    return 0
}

# Check system resources
check_system_resources() {
    log_message "INFO" "Checking system resources"
    
    # Check available memory
    local available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    local required_memory=512
    
    if [ "$available_memory" -lt "$required_memory" ]; then
        add_result "System Memory" "WARN" "Low available memory: ${available_memory}MB (${required_memory}MB recommended)"
    else
        add_result "System Memory" "PASS" "Sufficient memory available: ${available_memory}MB"
    fi
    
    # Check disk space
    local available_disk=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    local required_disk=1048576  # 1GB in KB
    
    if [ "$available_disk" -lt "$required_disk" ]; then
        add_result "Disk Space" "WARN" "Low disk space: $(($available_disk/1024))MB available"
    else
        add_result "Disk Space" "PASS" "Sufficient disk space: $(($available_disk/1024))MB available"
    fi
    
    # Check port availability
    local backend_port="${BACKEND_PORT:-4000}"
    local frontend_port="${FRONTEND_PORT:-3080}"
    
    if lsof -Pi :$backend_port -sTCP:LISTEN -t >/dev/null 2>&1; then
        add_result "Backend Port" "WARN" "Port $backend_port is already in use"
    else
        add_result "Backend Port" "PASS" "Port $backend_port is available"
    fi
    
    if lsof -Pi :$frontend_port -sTCP:LISTEN -t >/dev/null 2>&1; then
        add_result "Frontend Port" "WARN" "Port $frontend_port is already in use"
    else
        add_result "Frontend Port" "PASS" "Port $frontend_port is available"
    fi
}

# Run all dependency checks
run_all_checks() {
    log_message "INFO" "Starting comprehensive dependency verification"
    
    # Initialize arrays
    DEPENDENCY_RESULTS=()
    
    # Run all checks
    check_node_version
    check_npm
    check_backend_dependencies
    check_frontend_dependencies
    check_environment_variables
    check_database_connectivity
    check_system_resources
    
    # Generate summary
    generate_summary
}

# Generate summary report
generate_summary() {
    local total_checks=${#DEPENDENCY_RESULTS[@]}
    local passed=0
    local warnings=0
    local failed=0
    
    echo ""
    log_message "INFO" "Dependency Verification Summary"
    echo -e "${CYAN}=================================${NC}"
    
    for result in "${DEPENDENCY_RESULTS[@]}"; do
        IFS=':' read -r check_name status message <<< "$result"
        
        case $status in
            "PASS")
                ((passed++))
                echo -e "${GREEN}✅ $check_name${NC}"
                ;;
            "WARN")
                ((warnings++))
                echo -e "${YELLOW}⚠️  $check_name${NC}"
                ;;
            "FAIL")
                ((failed++))
                echo -e "${RED}❌ $check_name${NC}"
                ;;
        esac
    done
    
    echo ""
    echo -e "${CYAN}Results: ${GREEN}$passed passed${NC}, ${YELLOW}$warnings warnings${NC}, ${RED}$failed failed${NC} (Total: $total_checks)"
    
    if [ $failed -gt 0 ]; then
        log_message "ERROR" "Dependency verification failed with $failed critical issues"
        return 1
    elif [ $warnings -gt 0 ]; then
        log_message "WARN" "Dependency verification completed with $warnings warnings"
        return 0
    else
        log_message "INFO" "All dependency checks passed successfully"
        return 0
    fi
}

# Auto-fix common issues
auto_fix_dependencies() {
    log_message "INFO" "Attempting to auto-fix dependency issues"
    
    # Install backend dependencies if missing
    if [ ! -d "$BACKEND_DIR/node_modules" ]; then
        log_message "INFO" "Installing backend dependencies"
        cd "$BACKEND_DIR" && npm install
    fi
    
    # Install frontend dependencies if missing
    if [ ! -d "$FRONTEND_DIR/node_modules" ]; then
        log_message "INFO" "Installing frontend dependencies"
        cd "$FRONTEND_DIR" && npm install
    fi
    
    log_message "INFO" "Auto-fix completed"
}

# Export functions
export -f check_node_version
export -f check_npm
export -f check_backend_dependencies
export -f check_frontend_dependencies
export -f check_environment_variables
export -f check_database_connectivity
export -f check_system_resources
export -f run_all_checks
export -f auto_fix_dependencies

# If script is run directly, run all checks
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    init_pid_management
    run_all_checks
fi
