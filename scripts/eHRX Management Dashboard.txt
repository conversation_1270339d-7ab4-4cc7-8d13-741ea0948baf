# Product Requirements Document (PRD)
## AEVEN Manager Dashboard - Analytics Module

### Executive Summary
This PRD outlines the development of a comprehensive Manager Dashboard within the Analytics menu of the eHRx platform. The dashboard will digitize and enhance the existing Excel-based manager reporting system, providing real-time data updates, automated reminders, and enterprise-grade security compliance.

## 1. Project Overview

### 1.1 Objectives
- Transform Excel-based manager dashboard into a modern web interface
- Enable real-time data updates with role-based access control
- Implement automated monthly reminder system
- Ensure NIS2, GDPR, and SOC2 compliance
- Provide intuitive UX for managers to update team metrics

### 1.2 Success Criteria
- 100% feature parity with existing Excel dashboard
- Sub-2 second page load times
- 99.9% uptime with enterprise security standards
- Automated monthly reminder delivery
- Zero data breaches or security incidents

## 2. Technical Architecture

### 2.1 System Integration
- **Frontend**: React TypeScript component within existing Analytics module
- **Backend**: NestJS service extending current API architecture
- **Database**: Utilize existing `users`, `organizational_units`, and new `manager_dashboard_metrics` table
- **Authentication**: Leverage existing JWT with MFA system
- **Security**: Full compliance with existing security headers and audit logging

### 2.2 Database Schema Extension

````sql path=database/04-manager-dashboard-schema.sql mode=EDIT
-- Manager Dashboard Metrics Table
CREATE TABLE IF NOT EXISTS manager_dashboard_metrics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  organizational_unit_id INT NOT NULL,
  manager_id INT NOT NULL,
  reporting_period DATE NOT NULL,
  
  -- Core Metrics
  fte_count DECIMAL(4,1) DEFAULT 0,
  attrition_resigned INT DEFAULT 0,
  attrition_involuntary INT DEFAULT 0,
  sla_percentage DECIMAL(5,2),
  utilization_percentage DECIMAL(5,2),
  ax_percentage DECIMAL(5,2),
  compliance_score DECIMAL(5,2),
  ab_variance_percentage DECIMAL(6,2),
  vacation_leave_percentage DECIMAL(5,2),
  in_office_percentage DECIMAL(5,2),
  
  -- Metadata
  last_updated_by INT,
  last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status ENUM('draft', 'submitted', 'approved') DEFAULT 'draft',
  notes TEXT,
  
  -- Constraints
  FOREIGN KEY (organizational_unit_id) REFERENCES organizational_units(id),
  FOREIGN KEY (manager_id) REFERENCES users(id),
  FOREIGN KEY (last_updated_by) REFERENCES users(id),
  
  -- Indexes
  UNIQUE KEY unique_period_unit (organizational_unit_id, reporting_period),
  INDEX idx_manager_period (manager_id, reporting_period),
  INDEX idx_status (status)
) ENGINE=InnoDB;

-- Dashboard Reminder Settings
CREATE TABLE IF NOT EXISTS dashboard_reminder_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  manager_id INT NOT NULL,
  reminder_day_of_month INT DEFAULT 25,
  reminder_enabled BOOLEAN DEFAULT TRUE,
  email_template_id VARCHAR(50) DEFAULT 'monthly_dashboard_reminder',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (manager_id) REFERENCES users(id),
  UNIQUE KEY unique_manager_settings (manager_id)
) ENGINE=InnoDB;
````

## 3. Backend Implementation

### 3.1 Service Architecture

````typescript path=backend/src/analytics/manager-dashboard/manager-dashboard.module.ts mode=EDIT
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ManagerDashboardController } from './manager-dashboard.controller';
import { ManagerDashboardService } from './manager-dashboard.service';
import { ManagerDashboardMetrics } from './entities/manager-dashboard-metrics.entity';
import { DashboardReminderSettings } from './entities/dashboard-reminder-settings.entity';
import { OrganizationalUnit } from '../organizational-units/entities/organizational-unit.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ManagerDashboardMetrics,
      DashboardReminderSettings,
      OrganizationalUnit,
      User,
    ]),
  ],
  controllers: [ManagerDashboardController],
  providers: [ManagerDashboardService],
  exports: [ManagerDashboardService],
})
export class ManagerDashboardModule {}
````

### 3.2 Entity Definitions

````typescript path=backend/src/analytics/manager-dashboard/entities/manager-dashboard-metrics.entity.ts mode=EDIT
import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { OrganizationalUnit } from '../../organizational-units/entities/organizational-unit.entity';
import { User } from '../../users/entities/user.entity';

@Entity('manager_dashboard_metrics')
export class ManagerDashboardMetrics {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'organizational_unit_id' })
  organizationalUnitId: number;

  @Column({ name: 'manager_id' })
  managerId: number;

  @Column({ type: 'date', name: 'reporting_period' })
  reportingPeriod: Date;

  @Column({ type: 'decimal', precision: 4, scale: 1, name: 'fte_count', default: 0 })
  fteCount: number;

  @Column({ name: 'attrition_resigned', default: 0 })
  attritionResigned: number;

  @Column({ name: 'attrition_involuntary', default: 0 })
  attritionInvoluntary: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'sla_percentage', nullable: true })
  slaPercentage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'utilization_percentage', nullable: true })
  utilizationPercentage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'ax_percentage', nullable: true })
  axPercentage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'compliance_score', nullable: true })
  complianceScore: number;

  @Column({ type: 'decimal', precision: 6, scale: 2, name: 'ab_variance_percentage', nullable: true })
  abVariancePercentage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'vacation_leave_percentage', nullable: true })
  vacationLeavePercentage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'in_office_percentage', nullable: true })
  inOfficePercentage: number;

  @Column({ name: 'last_updated_by', nullable: true })
  lastUpdatedBy: number;

  @Column({ type: 'enum', enum: ['draft', 'submitted', 'approved'], default: 'draft' })
  status: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @ManyToOne(() => OrganizationalUnit)
  @JoinColumn({ name: 'organizational_unit_id' })
  organizationalUnit: OrganizationalUnit;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'manager_id' })
  manager: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'last_updated_by' })
  lastUpdatedByUser: User;

  @UpdateDateColumn({ name: 'last_updated_at' })
  lastUpdatedAt: Date;
}
````

### 3.3 Service Implementation

````typescript path=backend/src/analytics/manager-dashboard/manager-dashboard.service.ts mode=EDIT
import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ManagerDashboardMetrics } from './entities/manager-dashboard-metrics.entity';
import { CreateManagerDashboardMetricsDto, UpdateManagerDashboardMetricsDto } from './dto';
import { User } from '../users/entities/user.entity';
import { OrganizationalUnit } from '../organizational-units/entities/organizational-unit.entity';

@Injectable()
export class ManagerDashboardService {
  constructor(
    @InjectRepository(ManagerDashboardMetrics)
    private readonly metricsRepository: Repository<ManagerDashboardMetrics>,
    @InjectRepository(OrganizationalUnit)
    private readonly orgUnitRepository: Repository<OrganizationalUnit>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async getDashboardMetricsForManager(
    managerId: number,
    reportingPeriod: string,
    requestingUserId: number,
  ): Promise<ManagerDashboardMetrics[]> {
    // Verify access permissions
    await this.validateManagerAccess(managerId, requestingUserId);

    const metrics = await this.metricsRepository.find({
      where: {
        managerId,
        reportingPeriod: new Date(reportingPeriod),
      },
      relations: ['organizationalUnit', 'manager', 'lastUpdatedByUser'],
      order: { organizationalUnit: { name: 'ASC' } },
    });

    return metrics;
  }

  async updateMetrics(
    id: number,
    updateDto: UpdateManagerDashboardMetricsDto,
    requestingUserId: number,
  ): Promise<ManagerDashboardMetrics> {
    const existingMetrics = await this.metricsRepository.findOne({
      where: { id },
      relations: ['manager'],
    });

    if (!existingMetrics) {
      throw new NotFoundException('Dashboard metrics not found');
    }

    await this.validateManagerAccess(existingMetrics.managerId, requestingUserId);

    const updatedMetrics = await this.metricsRepository.save({
      ...existingMetrics,
      ...updateDto,
      lastUpdatedBy: requestingUserId,
      lastUpdatedAt: new Date(),
    });

    return this.metricsRepository.findOne({
      where: { id: updatedMetrics.id },
      relations: ['organizationalUnit', 'manager', 'lastUpdatedByUser'],
    });
  }

  private async validateManagerAccess(managerId: number, requestingUserId: number): Promise<void> {
    const requestingUser = await this.userRepository.findOne({
      where: { id: requestingUserId },
    });

    if (!requestingUser) {
      throw new ForbiddenException('Invalid user');
    }

    // Allow access if user is the manager or has admin/director role
    if (
      requestingUserId === managerId ||
      ['admin', 'director', 'super_admin'].includes(requestingUser.role)
    ) {
      return;
    }

    throw new ForbiddenException('Insufficient permissions to access this dashboard');
  }

  async calculateFteCount(organizationalUnitId: number): Promise<number> {
    const activeUsers = await this.userRepository.count({
      where: {
        organizationalUnitId,
        // Add conditions for active employees
      },
    });

    return activeUsers;
  }
}
````

### 3.4 Controller Implementation

````typescript path=backend/src/analytics/manager-dashboard/manager-dashboard.controller.ts mode=EDIT
import { Controller, Get, Put, Body, Param, Query, UseGuards, Request } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { ManagerDashboardService } from './manager-dashboard.service';
import { UpdateManagerDashboardMetricsDto } from './dto';

@Controller('api/analytics/manager-dashboard')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ManagerDashboardController {
  constructor(private readonly dashboardService: ManagerDashboardService) {}

  @Get('metrics/:managerId')
  @Roles('manager', 'director', 'admin', 'super_admin')
  async getManagerDashboard(
    @Param('managerId') managerId: number,
    @Query('period') reportingPeriod: string,
    @Request() req: any,
  ) {
    return this.dashboardService.getDashboardMetricsForManager(
      managerId,
      reportingPeriod,
      req.user.id,
    );
  }

  @Put('metrics/:id')
  @Roles('manager', 'director', 'admin', 'super_admin')
  async updateMetrics(
    @Param('id') id: number,
    @Body() updateDto: UpdateManagerDashboardMetricsDto,
    @Request() req: any,
  ) {
    return this.dashboardService.updateMetrics(id, updateDto, req.user.id);
  }

  @Get('organizational-units/:managerId')
  @Roles('manager', 'director', 'admin', 'super_admin')
  async getManagerOrganizationalUnits(
    @Param('managerId') managerId: number,
    @Request() req: any,
  ) {
    return this.dashboardService.getManagerOrganizationalUnits(managerId, req.user.id);
  }
}
````

## 4. Frontend Implementation

### 4.1 Main Dashboard Component

````typescript path=frontend/src/components/analytics/ManagerDashboard/ManagerDashboard.tsx mode=EDIT
import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DashboardTable } from './DashboardTable';
import { MetricsCard } from './MetricsCard';
import { useManagerDashboard } from '../hooks/useManagerDashboard';
import { ManagerDashboardMetrics, OrganizationalUnit } from '../../../types';

interface ManagerDashboardProps {
  managerId?: number;
}

export const ManagerDashboard: React.FC<ManagerDashboardProps> = ({ managerId }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<Date>(new Date());
  const [selectedManagerId, setSelectedManagerId] = useState<number>(managerId || 0);
  
  const {
    dashboardMetrics,
    organizationalUnits,
    managers,
    loading,
    error,
    updateMetrics,
    refreshData,
  } = useManagerDashboard(selectedManagerId, selectedPeriod);

  const handleMetricsUpdate = useCallback(
    async (metricsId: number, updatedData: Partial<ManagerDashboardMetrics>) => {
      try {
        await updateMetrics(metricsId, updatedData);
        await refreshData();
      } catch (error) {
        console.error('Failed to update metrics:', error);
      }
    },
    [updateMetrics, refreshData],
  );

  const calculateSummaryStats = useCallback(() => {
    if (!dashboardMetrics?.length) return null;

    const totalFte = dashboardMetrics.reduce((sum, metric) => sum + metric.fteCount, 0);
    const avgUtilization = dashboardMetrics.reduce(
      (sum, metric) => sum + (metric.utilizationPercentage || 0),
      0,
    ) / dashboardMetrics.length;

    return {
      totalFte,
      avgUtilization: Math.round(avgUtilization * 100) / 100,
      totalUnits: dashboardMetrics.length,
    };
  }, [dashboardMetrics]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  const summaryStats = calculateSummaryStats();

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          AEVEN Manager Dashboard
        </Typography>

        {/* Controls */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Manager</InputLabel>
                <Select
                  value={selectedManagerId}
                  onChange={(e) => setSelectedManagerId(Number(e.target.value))}
                  label="Manager"
                >
                  {managers?.map((manager) => (
                    <MenuItem key={manager.id} value={manager.id}>
                      {manager.firstName} {manager.lastName} ({manager.initials})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <DatePicker
                label="Reporting Period"
                value={selectedPeriod}
                onChange={(date) => setSelectedPeriod(date || new Date())}
                views={['year', 'month']}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                variant="contained"
                onClick={refreshData}
                disabled={loading}
                fullWidth
              >
                Refresh Data
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Summary Cards */}
        {summaryStats && (
          <Grid container spacing={2} sx={{ mb: 3 }}>
            <Grid item xs={12} md={4}>
              <MetricsCard
                title="Total FTE"
                value={summaryStats.totalFte}
                subtitle="Full-Time Equivalents"
                color="primary"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <MetricsCard
                title="Average Utilization"
                value={`${summaryStats.avgUtilization}%`}
                subtitle="Across all units"
                color="success"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <MetricsCard
                title="Organizational Units"
                value={summaryStats.totalUnits}
                subtitle="Under management"
                color="info"
              />
            </Grid>
          </Grid>
        )}

        {/* Main Dashboard Table */}
        <Paper sx={{ overflow: 'hidden' }}>
          <DashboardTable
            metrics={dashboardMetrics || []}
            onUpdateMetrics={handleMetricsUpdate}
            loading={loading}
          />
        </Paper>
      </Box>
    </LocalizationProvider>
  );
};
````

### 4.2 Dashboard Table Component

````typescript path=frontend/src/components/analytics/ManagerDashboard/DashboardTable.tsx mode=EDIT
import React, { useState, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  IconButton,
  Chip,
  Box,
  Tooltip,
} from '@mui/material';
import { Edit as EditIcon, Save as SaveIcon, Cancel as CancelIcon } from '@mui/icons-material';
import { ManagerDashboardMetrics } from '../../../types';
import { MetricCell } from './MetricCell';

interface DashboardTableProps {
  metrics: ManagerDashboardMetrics[];
  onUpdateMetrics: (id: number, data: Partial<ManagerDashboardMetrics>) => Promise<void>;
  loading: boolean;
}

export const DashboardTable: React.FC<DashboardTableProps> = ({
  metrics,
  onUpdateMetrics,
  loading,
}) => {
  const [editingRow, setEditingRow] = useState<number | null>(null);
  const [editData, setEditData] = useState<Partial<ManagerDashboardMetrics>>({});

  const handleStartEdit = useCallback((metric: ManagerDashboardMetrics) => {
    setEditingRow(metric.id);
    setEditData({ ...metric });
  }, []);

  const handleSaveEdit = useCallback(async () => {
    if (editingRow && editData) {
      await onUpdateMetrics(editingRow, editData);
      setEditingRow(null);
      setEditData({});
    }
  }, [editingRow, editData, onUpdateMetrics]);

  const handleCancelEdit = useCallback(() => {
    setEditingRow(null);
    setEditData({});
  }, []);

  const handleFieldChange = useCallback(
    (field: keyof ManagerDashboardMetrics, value: any) => {
      setEditData((prev) => ({ ...prev, [field]: value }));
    },
    [],
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'submitted':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <TableContainer>
      <Table stickyHeader>
        <TableHead>
          <TableRow>
            <TableCell>Director</TableCell>
            <TableCell>Sub Area</TableCell>
            <TableCell>Manager</TableCell>
            <TableCell>Date for Update</TableCell>
            <TableCell>FTE</TableCell>
            <TableCell>Attrition</TableCell>
            <TableCell>SLA</TableCell>
            <TableCell>Util</TableCell>
            <TableCell>AX</TableCell>
            <TableCell>Compliance</TableCell>
            <TableCell>AB</TableCell>
            <TableCell>VL</TableCell>
            <TableCell>In-Office</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {metrics.map((metric) => {
            const isEditing = editingRow === metric.id;
            const currentData = isEditing ? editData : metric;

            return (
              <TableRow key={metric.id} hover>
                <TableCell>{metric.organizationalUnit?.parent?.name || 'N/A'}</TableCell>
                <TableCell>{metric.organizationalUnit?.name}</TableCell>
                <TableCell>
                  {metric.manager?.firstName} {metric.manager?.lastName}
                </TableCell>
                <TableCell>
                  {new Date(metric.reportingPeriod).toLocaleDateString()}
                </TableCell>
                
                <MetricCell
                  value={currentData.fteCount}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('fteCount', value)}
                  type="number"
                  step={0.1}
                />
                
                <TableCell>
                  <Box display="flex" gap={1}>
                    <MetricCell
                      value={currentData.attritionResigned}
                      isEditing={isEditing}
                      onChange={(value) => handleFieldChange('attritionResigned', value)}
                      type="number"
                      label="R"
                    />
                    <MetricCell
                      value={currentData.attritionInvoluntary}
                      isEditing={isEditing}
                      onChange={(value) => handleFieldChange('attritionInvoluntary', value)}
                      type="number"
                      label="I"
                    />
                  </Box>
                </TableCell>

                <MetricCell
                  value={currentData.slaPercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('slaPercentage', value)}
                  type="percentage"
                />

                <MetricCell
                  value={currentData.utilizationPercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('utilizationPercentage', value)}
                  type="percentage"
                />

                <MetricCell
                  value={currentData.axPercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('axPercentage', value)}
                  type="percentage"
                />

                <MetricCell
                  value={currentData.complianceScore}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('complianceScore', value)}
                  type="percentage"
                />

                <MetricCell
                  value={currentData.abVariancePercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('abVariancePercentage', value)}
                  type="percentage"
                  allowNegative
                />

                <MetricCell
                  value={currentData.vacationLeavePercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('vacationLeavePercentage', value)}
                  type="percentage"
                />

                <MetricCell
                  value={currentData.inOfficePercentage}
                  isEditing={isEditing}
                  onChange={(value) => handleFieldChange('inOfficePercentage', value)}
                  type="percentage"
                />

                <TableCell>
                  <Chip
                    label={metric.status}
                    color={getStatusColor(metric.status)}
                    size="small"
                  />
                </TableCell>

                <TableCell>
                  {isEditing ? (
                    <Box display="flex" gap={1}>
                      <Tooltip title="Save">
                        <IconButton onClick={handleSaveEdit} color="primary" size="small">
                          <SaveIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Cancel">
                        <IconButton onClick={handleCancelEdit} size="small">
                          <CancelIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  ) : (
                    <Tooltip title="Edit">
                      <IconButton
                        onClick={() => handleStartEdit(metric)}
                        size="small"
                        disabled={loading}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
````

### 4.3 Custom Hook for Data Management

````typescript path=frontend/src/hooks/useManagerDashboard.ts mode=EDIT
import { useState, useEffect, useCallback } from 'react';
import { ManagerDashboardMetrics, OrganizationalUnit, User } from '../types';
import { managerDashboardApi } from '../services/managerDashboardApi';

export const useManagerDashboard = (managerId: number, reportingPeriod: Date) => {
  const [dashboardMetrics, setDashboardMetrics] = useState<ManagerDashboardMetrics[]>([]);
  const [organizationalUnits, setOrganizationalUnits] = useState<OrganizationalUnit[]>([]);
  const [managers, setManagers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    if (!managerId) return;

    setLoading(true);
    setError(null);

    try {
      const [metricsData, orgUnitsData, managersData] = await Promise.all([
        managerDashboardApi.getManagerDashboard(managerId, reportingPeriod),
        managerDashboardApi.getManagerOrganizationalUnits(managerId),
        managerDashboardApi.getManagers(),
      ]);

      setDashboardMetrics(metricsData);
      setOrganizationalUnits(orgUnitsData);
      setManagers(managersData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  }, [managerId, reportingPeriod]);

  const updateMetrics = useCallback(
    async (metricsId: number, updatedData: Partial<ManagerDashboardMetrics>) => {
      try {
        const updatedMetrics = await managerDashboardApi.updateMetrics(metricsId, updatedData);
        
        setDashboardMetrics((prev) =>
          prev.map((metric) =>
            metric.id === metricsId ? { ...metric, ...updatedMetrics } : metric,
          ),
        );

        return updatedMetrics;
      } catch (err) {
        throw new Error(err instanceof Error ? err.message : 'Failed to update metrics');
      }
    },
    [],
  );

  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  return {
    dashboardMetrics,
    organizationalUnits,
    managers,
    loading,
    error,
    updateMetrics,
    refreshData: fetchDashboardData,
  };
};
````

## 5. Security & Compliance Implementation

### 5.1 Input Validation & Sanitization

````typescript path=backend/src/analytics/manager-dashboard/dto/update-manager-dashboard-metrics.dto.ts mode=EDIT
import { IsOptional, IsNumber, IsString, IsEnum, Min, Max, IsDecimal } from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateManagerDashboardMetricsDto {
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 1 })
  @Min(0)
  @Max(999.9)
  fteCount?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(999)
  attritionResigned?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(999)
  attritionInvoluntary?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  slaPercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  utilizationPercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  axPercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  complianceScore?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(-999.99)
  @Max(999.99)
  abVariancePercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  vacationLeavePercentage?: number;

  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  inOfficePercentage?: number;

  @IsOptional()
  @IsEnum(['draft', 'submitted', 'approved'])
  status?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  notes?: string;
}
````

### 5.2 Audit Logging Integration

````typescript path=backend/src/analytics/manager-dashboard/manager-dashboard.service.ts mode=EDIT
// Add to existing service
import { AuditLogService } from '../audit/audit-log.service';

@Injectable()
export class ManagerDashboardService {
  constructor(
    // ... existing dependencies
    private readonly auditLogService: AuditLogService,
  ) {}

  async updateMetrics(
    id: number,
    updateDto: UpdateManagerDashboardMetricsDto,
    requestingUserId: number,
  ): Promise<ManagerDashboardMetrics> {
    const existingMetrics = await this.metricsRepository.findOne({
      where: { id },
      relations: ['manager', 'organizationalUnit'],
    });

    if (!existingMetrics) {
      throw new NotFoundException('Dashboard metrics not found');
    }

    await this.validateManagerAccess(existingMetrics.managerId, requestingUserId);

    // Audit log before update
    await this.auditLogService.log({
      userId: requestingUserId,
      action: 'UPDATE_MANAGER_DASHBOARD_METRICS',
      resourceType: 'manager_dashboard_metrics',
      resourceId: id.toString(),
      details: {
        organizationalUnit: existingMetrics.organizationalUnit.name,
        changes: updateDto,
        previousValues: {
          fteCount: existingMetrics.fteCount,
          slaPercentage: existingMetrics.slaPercentage,
          // ... other relevant fields
        },
      },
      ipAddress: this.getRequestIp(),
      userAgent: this.getRequestUserAgent(),
    });

    const updatedMetrics = await this.metricsRepository.save({
      ...existingMetrics,
      ...updateDto,
      lastUpdatedBy: requestingUserId,
      lastUpdatedAt: new Date(),
    });

    return this.metricsRepository.findOne({
      where: { id: updatedMetrics.id },
      relations: ['organizationalUnit', 'manager', 'lastUpdatedByUser'],
    });
  }
}
````

## 6. Email Reminder System

### 6.1 Reminder Service Implementation

````typescript path=backend/src/analytics/manager-dashboard/reminder.service.ts mode=EDIT
import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EmailService } from '../email/email.service';
import { DashboardReminderSettings } from './entities/dashboard-reminder-settings.entity';
import { User } from '../users/entities/user.entity';

@Injectable()
export class DashboardReminderService {
  constructor(
    @InjectRepository(DashboardReminderSettings)
    private readonly reminderSettingsRepository: Repository<DashboardReminderSettings>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly emailService: EmailService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async checkAndSendReminders(): Promise<void> {
    const today = new Date();
    const currentDay = today.getDate();

    // Find all managers who should receive reminders today
    const reminderSettings = await this.reminderSettingsRepository.find({
      where: {
        reminderDayOfMonth: currentDay,
        reminderEnabled: true,
      },
      relations: ['manager'],
    });

    for (const setting of reminderSettings) {
      await this.sendDashboardReminder(setting.manager, setting.emailTemplateId);
    }
  }

  private async sendDashboardReminder(manager: User, templateId: string): Promise<void> {
    const reportingPeriod = new Date();
    reportingPeriod.setMonth(reportingPeriod.getMonth() - 1); // Previous month

    const emailData = {
      managerName: `${manager.firstName} ${manager.lastName}`,
      reportingPeriod: reportingPeriod.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
      }),
      dashboardUrl: `${process.env.FRONTEND_URL}/analytics/manager-dashboard`,
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString(), // 7 days from now
    };

    await this.emailService.sendTemplatedEmail({
      to: manager.email,
      templateId,
      data: emailData,
      subject: `Monthly Dashboard Update Required - ${emailData.reportingPeriod}`,
    });
  }
}
````

## 7. Performance Optimization

### 7.1 Database Indexing Strategy

````sql path=database/05-manager-dashboard-indexes.sql mode=EDIT
-- Performance optimization indexes for manager dashboard
CREATE INDEX idx_manager_dashboard_metrics_composite 
ON manager_dashboard_metrics (manager_id, reporting_period, status);

CREATE INDEX idx_manager_dashboard_metrics_org_period 
ON manager_dashboard_metrics (organizational_unit_id, reporting_period);

CREATE INDEX idx_manager_dashboard_metrics_updated 
ON manager_dashboard_metrics (last_updated_at DESC);

-- Optimize organizational unit queries
CREATE INDEX idx_organizational_units_manager 
ON organizational_units (manager_id) WHERE manager_id IS NOT NULL;

CREATE INDEX idx_organizational_units_active 
ON organizational_units (is_active, parent_id) WHERE is_active = TRUE;
````

### 7.2 Frontend Performance Optimization

````typescript path=frontend/src/components/analytics/ManagerDashboard/ManagerDashboard.tsx mode=EDIT
// Add to existing component
import { memo, useMemo } from 'react';
import { debounce } from 'lodash';

export const ManagerDashboard: React.FC<ManagerDashboardProps> = memo(({ managerId }) => {
  // Memoize expensive calculations
  const memoizedSummaryStats = useMemo(() => calculateSummaryStats(), [dashboardMetrics]);

  // Debounce API calls for real-time updates
  const debouncedUpdateMetrics = useMemo(
    () => debounce(handleMetricsUpdate, 500),
    [handleMetricsUpdate],
  );

  // Virtualization for large datasets
  const visibleMetrics = useMemo(() => {
    return dashboardMetrics?.slice(0, 100) || []; // Implement virtual scrolling for larger datasets
  }, [dashboardMetrics]);

  // ... rest of component
});
````

## 8. Testing Strategy

### 8.1 Backend Unit Tests

````typescript path=backend/src/analytics/manager-dashboard/manager-dashboard.service.spec.ts mode=EDIT
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ManagerDashboardService } from './manager-dashboard.service';
import { ManagerDashboardMetrics } from './entities/manager-dashboard-metrics.entity';

describe('ManagerDashboardService', () => {
  let service: ManagerDashboardService;
  let metricsRepository: Repository<ManagerDashboardMetrics>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ManagerDashboardService,
        {
          provide: getRepositoryToken(ManagerDashboardMetrics),
          useClass: Repository,
        },
        // ... other mocked dependencies
      ],
    }).compile();

    service = module.get<ManagerDashboardService>(ManagerDashboardService);
    metricsRepository = module.get<Repository<ManagerDashboardMetrics>>(
      getRepositoryToken(ManagerDashboardMetrics),
    );
  });

  describe('updateMetrics', () => {
    it('should update metrics successfully', async () => {
      // Test implementation
    });

    it('should validate manager access', async () => {
      // Test implementation
    });

    it('should audit log changes', async () => {
      // Test implementation
    });
  });
});
````

### 8.2 Frontend Integration Tests

````typescript path=frontend/src/components/analytics/ManagerDashboard/__tests__/ManagerDashboard.test.tsx mode=EDIT
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ManagerDashboard } from '../ManagerDashboard';
import { managerDashboardApi } from '../../../services/managerDashboardApi';

jest.mock('../../../services/managerDashboardApi');

describe('ManagerDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render dashboard with metrics', async () => {
    const mockMetrics = [
      {
        id: 1,
        organizationalUnit: { name: 'Test Unit' },
        manager: { firstName: 'John', lastName: 'Doe' },
        fteCount: 10,
        // ... other properties
      },
    ];

    (managerDashboardApi.getManagerDashboard as jest.Mock).mockResolvedValue(mockMetrics);

    render(<ManagerDashboard managerId={1} />);

    await waitFor(() => {
      expect(screen.getByText('AEVEN Manager Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Test Unit')).toBeInTheDocument();
    });
  });

  it('should handle metric updates', async () => {
    // Test implementation
  });
});
````

## 9. Deployment & Environment Configuration

### 9.1 Environment Variables

````env path=backend/.env.example mode=EDIT
# Manager Dashboard Configuration
DASHBOARD_REMINDER_ENABLED=true
DASHBOARD_EMAIL_TEMPLATE_PATH=/templates/dashboard-reminder.html
DASHBOARD_CACHE_TTL=300
DASHBOARD_MAX_RECORDS_PER_REQUEST=100

# Email Configuration for Reminders
EMAIL_REMINDER_FROM=<EMAIL>
EMAIL_REMINDER_REPLY_TO=<EMAIL>
````

### 9.2 Production Optimization

````typescript path=backend/src/analytics/manager-dashboard/manager-dashboard.module.ts mode=EDIT
// Add caching and rate limiting for production
import { CacheModule } from '@nestjs/cache-manager';
import { ThrottlerModule } from '@nestjs/throttler';

@Module({
  imports: [
    // ... existing imports
    CacheModule.register({
      ttl: parseInt(process.env.DASHBOARD_CACHE_TTL) || 300,
      max: 100,
    }),
    ThrottlerModule.forRoot({
      ttl: 60,
      limit: 10, // 10 requests per minute per user
    }),
  ],
  // ... rest of module
})
export class ManagerDashboardModule {}
````

## 10. Documentation & Maintenance

### 10.1 API Documentation

````typescript path=backend/src/analytics/manager-dashboard/manager-dashboard.controller.ts mode=EDIT
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('Manager Dashboard')
@ApiBearerAuth()
@Controller('api/analytics/manager-dashboard')
export class ManagerDashboardController {
  @Get('metrics/:managerId')
  @ApiOperation({ summary: 'Get manager dashboard metrics' })
  @ApiResponse({ status: 200, description: 'Dashboard metrics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async getManagerDashboard(
    // ... parameters
  ) {
    // ... implementation
  }
}
````

## 11. Success Metrics & Monitoring

### 11.1 Performance Monitoring

````typescript path=backend/src/analytics/manager-dashboard/manager-dashboard.service.ts mode=EDIT
// Add performance monitoring
import { Logger } from '@nestjs/common';

@Injectable()
export class ManagerDashboardService {
  private readonly logger = new Logger(ManagerDashboardService.name);

  async getDashboardMetricsForManager(
    managerId: number,
    reportingPeriod: string,
    requestingUserId: number,
  ): Promise<ManagerDashboardMetrics[]> {
    const startTime = Date.now();
    
    try {
      const result = await this.performDashboardQuery(managerId, reportingPeriod, requestingUserId);
      
      const duration = Date.now() - startTime;
      this.logger.log(`Dashboard query completed in ${duration}ms for manager ${managerId}`);
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Dashboard query failed after ${duration}ms for manager ${managerId}`, error);
      throw error;
    }
  }
}
````

This comprehensive PRD provides a complete roadmap for implementing the AEVEN Manager Dashboard with enterprise-grade security, performance, and user experience. The implementation follows all specified compliance requirements and integrates seamlessly with the existing eHRx platform architecture.
