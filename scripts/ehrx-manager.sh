#!/bin/bash

# EHRX Enterprise Service Manager
# Master script for managing EHRX services with enterprise-grade features

set -e

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source all required modules
source "$SCRIPT_DIR/logging-system.sh"
source "$SCRIPT_DIR/pid-manager.sh"
source "$SCRIPT_DIR/dependency-checker.sh"

# Configuration
PROJECT_ROOT="/var/www/ehrx"
DEFAULT_ENVIRONMENT="development"
ENVIRONMENT="${NODE_ENV:-$DEFAULT_ENVIRONMENT}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Display usage information
show_usage() {
    echo -e "${CYAN}EHRX Enterprise Service Manager${NC}"
    echo -e "${CYAN}===============================${NC}"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo -e "  ${GREEN}start [dev|prod]${NC}     - Start services in specified environment"
    echo -e "  ${GREEN}stop [dev|prod]${NC}      - Stop services in specified environment"
    echo -e "  ${GREEN}restart [dev|prod]${NC}   - Restart services in specified environment"
    echo -e "  ${GREEN}status${NC}               - Show service status"
    echo -e "  ${GREEN}health${NC}               - Run health checks"
    echo -e "  ${GREEN}monitor${NC}              - Start health monitoring daemon"
    echo -e "  ${GREEN}logs [service]${NC}       - Show logs (service: main|error|audit|performance)"
    echo -e "  ${GREEN}deps${NC}                 - Check dependencies"
    echo -e "  ${GREEN}fix-deps${NC}             - Auto-fix dependency issues"
    echo -e "  ${GREEN}cleanup${NC}              - Clean up PID files and temporary data"
    echo ""
    echo "Options:"
    echo -e "  ${YELLOW}--env <env>${NC}          - Override environment (dev|prod)"
    echo -e "  ${YELLOW}--verbose${NC}            - Enable verbose logging"
    echo -e "  ${YELLOW}--force${NC}              - Force operation (skip confirmations)"
    echo ""
    echo "Examples:"
    echo "  $0 start dev              # Start development environment"
    echo "  $0 start prod --verbose   # Start production with verbose logging"
    echo "  $0 status                 # Show current status"
    echo "  $0 health                 # Run health checks"
    echo "  $0 logs error             # Show error logs"
}

# Parse command line arguments
parse_arguments() {
    COMMAND=""
    ENVIRONMENT_ARG=""
    VERBOSE=false
    FORCE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            start|stop|restart|status|health|monitor|logs|deps|fix-deps|cleanup)
                COMMAND="$1"
                shift
                
                # Check for environment argument
                if [[ $# -gt 0 && "$1" =~ ^(dev|prod|development|production)$ ]]; then
                    ENVIRONMENT_ARG="$1"
                    shift
                fi
                ;;
            --env)
                ENVIRONMENT_ARG="$2"
                shift 2
                ;;
            --verbose)
                VERBOSE=true
                export LOG_LEVEL="DEBUG"
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                if [ -z "$COMMAND" ]; then
                    COMMAND="$1"
                else
                    # Additional argument (like service name for logs)
                    EXTRA_ARG="$1"
                fi
                shift
                ;;
        esac
    done
    
    # Set environment
    if [ -n "$ENVIRONMENT_ARG" ]; then
        case "$ENVIRONMENT_ARG" in
            dev|development)
                ENVIRONMENT="development"
                ;;
            prod|production)
                ENVIRONMENT="production"
                ;;
            *)
                log_error "Invalid environment: $ENVIRONMENT_ARG. Use 'dev' or 'prod'" "MANAGER"
                exit 1
                ;;
        esac
    fi
    
    export NODE_ENV="$ENVIRONMENT"
}

# Initialize the manager
init_manager() {
    log_info "Initializing EHRX Service Manager" "MANAGER"
    log_info "Environment: $ENVIRONMENT" "MANAGER"
    log_info "Verbose logging: $VERBOSE" "MANAGER"
    
    # Initialize all subsystems
    init_logging_system
    init_pid_management
    
    log_info "EHRX Service Manager initialized" "MANAGER"
}

# Start services
cmd_start() {
    log_info "Starting EHRX services in $ENVIRONMENT mode" "MANAGER"
    
    # Run dependency checks first
    if ! run_all_checks; then
        if [ "$FORCE" = "true" ]; then
            log_warn "Dependency checks failed, but continuing due to --force flag" "MANAGER"
        else
            log_error "Dependency checks failed. Use --force to override or run 'fix-deps'" "MANAGER"
            return 1
        fi
    fi
    
    # Start appropriate environment
    case "$ENVIRONMENT" in
        "development")
            time_function "$SCRIPT_DIR/start-dev.sh"
            ;;
        "production")
            time_function "$SCRIPT_DIR/start-prod.sh"
            ;;
        *)
            log_error "Unknown environment: $ENVIRONMENT" "MANAGER"
            return 1
            ;;
    esac
    
    log_audit "Services started in $ENVIRONMENT mode" "MANAGER"
}

# Stop services
cmd_stop() {
    log_info "Stopping EHRX services in $ENVIRONMENT mode" "MANAGER"
    
    case "$ENVIRONMENT" in
        "development")
            time_function "$SCRIPT_DIR/stop-dev.sh"
            ;;
        "production")
            time_function "$SCRIPT_DIR/stop-prod.sh"
            ;;
        *)
            log_error "Unknown environment: $ENVIRONMENT" "MANAGER"
            return 1
            ;;
    esac
    
    log_audit "Services stopped in $ENVIRONMENT mode" "MANAGER"
}

# Restart services
cmd_restart() {
    log_info "Restarting EHRX services in $ENVIRONMENT mode" "MANAGER"
    
    cmd_stop
    sleep 5
    cmd_start
    
    log_audit "Services restarted in $ENVIRONMENT mode" "MANAGER"
}

# Show service status
cmd_status() {
    log_info "Checking EHRX service status" "MANAGER"
    
    echo -e "${CYAN}EHRX Service Status${NC}"
    echo -e "${CYAN}===================${NC}"
    echo ""
    
    # Show PID-based status
    show_status
    
    echo ""
    echo -e "${CYAN}Port Status:${NC}"
    
    # Check backend port
    local backend_port="${BACKEND_PORT:-4000}"
    if lsof -Pi :$backend_port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend port $backend_port: In use${NC}"
    else
        echo -e "${RED}❌ Backend port $backend_port: Free${NC}"
    fi
    
    # Check frontend port
    local frontend_port="${FRONTEND_PORT:-3080}"
    if lsof -Pi :$frontend_port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Frontend port $frontend_port: In use${NC}"
    else
        echo -e "${RED}❌ Frontend port $frontend_port: Free${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}Environment: ${YELLOW}$ENVIRONMENT${NC}"
    echo -e "${CYAN}Project Root: ${YELLOW}$PROJECT_ROOT${NC}"
}

# Run health checks
cmd_health() {
    log_info "Running EHRX health checks" "MANAGER"
    
    echo -e "${CYAN}EHRX Health Check${NC}"
    echo -e "${CYAN}=================${NC}"
    echo ""
    
    # Run dependency checks
    run_all_checks
    
    echo ""
    echo -e "${CYAN}Service Health:${NC}"
    
    # Check if health monitor script exists and use it
    if [ -f "$SCRIPT_DIR/health-monitor.sh" ]; then
        source "$SCRIPT_DIR/health-monitor.sh"
        
        # Check backend health
        if check_service_health "backend"; then
            echo -e "${GREEN}✅ Backend: Healthy${NC}"
        else
            echo -e "${RED}❌ Backend: Unhealthy${NC}"
        fi
        
        # Check frontend health
        if check_service_health "frontend"; then
            echo -e "${GREEN}✅ Frontend: Healthy${NC}"
        else
            echo -e "${RED}❌ Frontend: Unhealthy${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Health monitoring not available${NC}"
    fi
}

# Start monitoring daemon
cmd_monitor() {
    log_info "Starting EHRX health monitoring daemon" "MANAGER"
    
    if [ -f "$SCRIPT_DIR/health-monitor.sh" ]; then
        "$SCRIPT_DIR/health-monitor.sh" &
        local monitor_pid=$!
        save_pid "monitor" "$monitor_pid"
        
        log_info "Health monitoring daemon started (PID: $monitor_pid)" "MANAGER"
        echo -e "${GREEN}✅ Health monitoring daemon started${NC}"
        echo -e "${CYAN}Monitor PID: $monitor_pid${NC}"
        echo -e "${CYAN}To stop: kill $monitor_pid${NC}"
    else
        log_error "Health monitor script not found" "MANAGER"
        return 1
    fi
}

# Show logs
cmd_logs() {
    local log_type="${EXTRA_ARG:-main}"
    
    log_info "Showing $log_type logs" "MANAGER"
    
    case "$log_type" in
        "main")
            tail -f "$MAIN_LOG" 2>/dev/null || echo "Main log not found"
            ;;
        "error")
            tail -f "$ERROR_LOG" 2>/dev/null || echo "Error log not found"
            ;;
        "audit")
            tail -f "$AUDIT_LOG" 2>/dev/null || echo "Audit log not found"
            ;;
        "performance")
            tail -f "$PERFORMANCE_LOG" 2>/dev/null || echo "Performance log not found"
            ;;
        "security")
            tail -f "$SECURITY_LOG" 2>/dev/null || echo "Security log not found"
            ;;
        *)
            echo "Available log types: main, error, audit, performance, security"
            return 1
            ;;
    esac
}

# Check dependencies
cmd_deps() {
    log_info "Checking EHRX dependencies" "MANAGER"
    run_all_checks
}

# Fix dependencies
cmd_fix_deps() {
    log_info "Auto-fixing EHRX dependencies" "MANAGER"
    auto_fix_dependencies
    
    # Re-run checks
    echo ""
    log_info "Re-running dependency checks" "MANAGER"
    run_all_checks
}

# Cleanup
cmd_cleanup() {
    log_info "Cleaning up EHRX resources" "MANAGER"
    
    if [ "$FORCE" = "false" ]; then
        echo -e "${YELLOW}This will clean up PID files and temporary data.${NC}"
        echo -e "${YELLOW}Are you sure? (y/N)${NC}"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_info "Cleanup cancelled" "MANAGER"
            return 0
        fi
    fi
    
    # Clean up PID files
    cleanup_all_pid_files
    
    # Clean up temporary files
    rm -f /tmp/ehrx-* 2>/dev/null || true
    rm -f /var/run/ehrx/*.tmp 2>/dev/null || true
    
    # Rotate logs
    rotate_logs
    
    log_info "Cleanup completed" "MANAGER"
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Main execution
main() {
    # Parse arguments
    parse_arguments "$@"
    
    # Show usage if no command provided
    if [ -z "$COMMAND" ]; then
        show_usage
        exit 1
    fi
    
    # Initialize manager
    init_manager
    
    # Execute command
    case "$COMMAND" in
        "start")
            cmd_start
            ;;
        "stop")
            cmd_stop
            ;;
        "restart")
            cmd_restart
            ;;
        "status")
            cmd_status
            ;;
        "health")
            cmd_health
            ;;
        "monitor")
            cmd_monitor
            ;;
        "logs")
            cmd_logs
            ;;
        "deps")
            cmd_deps
            ;;
        "fix-deps")
            cmd_fix_deps
            ;;
        "cleanup")
            cmd_cleanup
            ;;
        *)
            log_error "Unknown command: $COMMAND" "MANAGER"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
