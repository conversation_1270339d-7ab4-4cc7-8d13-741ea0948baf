#!/bin/bash

# EHRX Shortcuts and Aliases
# Source this file to add convenient shortcuts for EHRX operations
# Usage: source scripts/ehrx-shortcuts.sh

# Get script directory
EHRX_SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# Main EHRX management aliases
alias ehrx="$EHRX_SCRIPT_DIR/ehrx-manager.sh"
alias ehrx-start="$EHRX_SCRIPT_DIR/ehrx-manager.sh start dev"
alias ehrx-start-dev="$EHRX_SCRIPT_DIR/ehrx-manager.sh start dev"
alias ehrx-start-prod="$EHRX_SCRIPT_DIR/ehrx-manager.sh start prod"
alias ehrx-stop="$EHRX_SCRIPT_DIR/ehrx-manager.sh stop"
alias ehrx-restart="$EHRX_SCRIPT_DIR/ehrx-manager.sh restart"
alias ehrx-status="$EHRX_SCRIPT_DIR/ehrx-manager.sh status"
alias ehrx-health="$EHRX_SCRIPT_DIR/ehrx-manager.sh health"
alias ehrx-monitor="$EHRX_SCRIPT_DIR/ehrx-manager.sh monitor"
alias ehrx-logs="$EHRX_SCRIPT_DIR/ehrx-manager.sh logs"
alias ehrx-deps="$EHRX_SCRIPT_DIR/ehrx-manager.sh deps"
alias ehrx-fix="$EHRX_SCRIPT_DIR/ehrx-manager.sh fix-deps"
alias ehrx-cleanup="$EHRX_SCRIPT_DIR/ehrx-manager.sh cleanup"

# Quick log viewing aliases
alias ehrx-logs-main="$EHRX_SCRIPT_DIR/ehrx-manager.sh logs main"
alias ehrx-logs-error="$EHRX_SCRIPT_DIR/ehrx-manager.sh logs error"
alias ehrx-logs-audit="$EHRX_SCRIPT_DIR/ehrx-manager.sh logs audit"
alias ehrx-logs-perf="$EHRX_SCRIPT_DIR/ehrx-manager.sh logs performance"

# Development shortcuts
alias ehrx-dev="$EHRX_SCRIPT_DIR/ehrx-manager.sh start dev --verbose"
alias ehrx-prod="$EHRX_SCRIPT_DIR/ehrx-manager.sh start prod --verbose"

# Quick status checks
alias ehrx-ps="$EHRX_SCRIPT_DIR/ehrx-manager.sh status"
alias ehrx-check="$EHRX_SCRIPT_DIR/ehrx-manager.sh health"

# Convenience functions
ehrx-quick-start() {
    echo -e "${CYAN}🚀 Starting EHRX Development Environment...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh deps && \
    $EHRX_SCRIPT_DIR/ehrx-manager.sh start dev
}

ehrx-quick-stop() {
    echo -e "${YELLOW}🛑 Stopping EHRX Services...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh stop
}

ehrx-quick-restart() {
    echo -e "${BLUE}🔄 Restarting EHRX Services...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh restart
}

ehrx-full-check() {
    echo -e "${CYAN}🔍 Running Full EHRX Health Check...${NC}"
    echo ""
    echo -e "${CYAN}1. Checking Dependencies...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh deps
    echo ""
    echo -e "${CYAN}2. Checking Service Status...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh status
    echo ""
    echo -e "${CYAN}3. Running Health Checks...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh health
}

ehrx-dev-setup() {
    echo -e "${CYAN}🛠️  Setting up EHRX Development Environment...${NC}"
    echo ""
    echo -e "${CYAN}1. Checking Dependencies...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh deps
    echo ""
    echo -e "${CYAN}2. Auto-fixing Issues...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh fix-deps
    echo ""
    echo -e "${CYAN}3. Starting Development Environment...${NC}"
    $EHRX_SCRIPT_DIR/ehrx-manager.sh start dev
    echo ""
    echo -e "${GREEN}✅ Development environment ready!${NC}"
    echo -e "${CYAN}💡 Use 'ehrx-status' to check status${NC}"
    echo -e "${CYAN}💡 Use 'ehrx-logs' to view logs${NC}"
    echo -e "${CYAN}💡 Use 'ehrx-stop' to stop services${NC}"
}

ehrx-prod-deploy() {
    echo -e "${CYAN}🚀 Deploying EHRX Production Environment...${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  This will start production services. Continue? (y/N)${NC}"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo -e "${CYAN}1. Checking Dependencies...${NC}"
        $EHRX_SCRIPT_DIR/ehrx-manager.sh deps
        echo ""
        echo -e "${CYAN}2. Starting Production Environment...${NC}"
        $EHRX_SCRIPT_DIR/ehrx-manager.sh start prod
        echo ""
        echo -e "${CYAN}3. Starting Health Monitoring...${NC}"
        $EHRX_SCRIPT_DIR/ehrx-manager.sh monitor &
        echo ""
        echo -e "${GREEN}✅ Production environment deployed!${NC}"
        echo -e "${CYAN}💡 Use 'ehrx-status' to check status${NC}"
        echo -e "${CYAN}💡 Use 'ehrx-health' to run health checks${NC}"
        echo -e "${CYAN}💡 Monitoring is running in background${NC}"
    else
        echo -e "${YELLOW}Deployment cancelled.${NC}"
    fi
}

ehrx-troubleshoot() {
    echo -e "${CYAN}🔧 EHRX Troubleshooting Guide${NC}"
    echo -e "${CYAN}=============================${NC}"
    echo ""
    echo -e "${YELLOW}Common Commands:${NC}"
    echo -e "  ${GREEN}ehrx-status${NC}      - Check service status"
    echo -e "  ${GREEN}ehrx-health${NC}      - Run health checks"
    echo -e "  ${GREEN}ehrx-deps${NC}        - Check dependencies"
    echo -e "  ${GREEN}ehrx-fix${NC}         - Auto-fix common issues"
    echo -e "  ${GREEN}ehrx-logs-error${NC}  - View error logs"
    echo -e "  ${GREEN}ehrx-cleanup${NC}     - Clean up resources"
    echo ""
    echo -e "${YELLOW}Quick Fixes:${NC}"
    echo -e "  ${BLUE}Port conflicts:${NC}     ehrx-cleanup && ehrx-start"
    echo -e "  ${BLUE}Dependency issues:${NC}  ehrx-fix"
    echo -e "  ${BLUE}Service not starting:${NC} ehrx-logs-error"
    echo -e "  ${BLUE}Database issues:${NC}    ehrx-deps"
    echo ""
    echo -e "${YELLOW}Full Reset:${NC}"
    echo -e "  ${RED}ehrx-stop && ehrx-cleanup && ehrx-dev-setup${NC}"
}

ehrx-help() {
    echo -e "${CYAN}EHRX Shortcuts Help${NC}"
    echo -e "${CYAN}===================${NC}"
    echo ""
    echo -e "${YELLOW}Basic Commands:${NC}"
    echo -e "  ${GREEN}ehrx${NC}                 - Main script (same as ehrx-manager.sh)"
    echo -e "  ${GREEN}ehrx-start${NC}           - Start development environment"
    echo -e "  ${GREEN}ehrx-stop${NC}            - Stop all services"
    echo -e "  ${GREEN}ehrx-restart${NC}         - Restart services"
    echo -e "  ${GREEN}ehrx-status${NC}          - Check service status"
    echo -e "  ${GREEN}ehrx-health${NC}          - Run health checks"
    echo ""
    echo -e "${YELLOW}Environment-Specific:${NC}"
    echo -e "  ${GREEN}ehrx-start-dev${NC}       - Start development"
    echo -e "  ${GREEN}ehrx-start-prod${NC}      - Start production"
    echo -e "  ${GREEN}ehrx-dev${NC}             - Start dev with verbose logging"
    echo -e "  ${GREEN}ehrx-prod${NC}            - Start prod with verbose logging"
    echo ""
    echo -e "${YELLOW}Logs & Monitoring:${NC}"
    echo -e "  ${GREEN}ehrx-logs${NC}            - View main logs"
    echo -e "  ${GREEN}ehrx-logs-error${NC}      - View error logs"
    echo -e "  ${GREEN}ehrx-logs-audit${NC}      - View audit logs"
    echo -e "  ${GREEN}ehrx-monitor${NC}         - Start health monitoring"
    echo ""
    echo -e "${YELLOW}Maintenance:${NC}"
    echo -e "  ${GREEN}ehrx-deps${NC}            - Check dependencies"
    echo -e "  ${GREEN}ehrx-fix${NC}             - Auto-fix issues"
    echo -e "  ${GREEN}ehrx-cleanup${NC}         - Clean up resources"
    echo ""
    echo -e "${YELLOW}Convenience Functions:${NC}"
    echo -e "  ${GREEN}ehrx-quick-start${NC}     - Quick development setup"
    echo -e "  ${GREEN}ehrx-dev-setup${NC}       - Full development setup"
    echo -e "  ${GREEN}ehrx-prod-deploy${NC}     - Production deployment"
    echo -e "  ${GREEN}ehrx-full-check${NC}      - Complete health check"
    echo -e "  ${GREEN}ehrx-troubleshoot${NC}    - Troubleshooting guide"
    echo ""
    echo -e "${CYAN}💡 Tip: Add 'source scripts/ehrx-shortcuts.sh' to your ~/.bashrc for permanent aliases${NC}"
}

# Show help on source
echo -e "${GREEN}✅ EHRX shortcuts loaded!${NC}"
echo -e "${CYAN}💡 Type 'ehrx-help' for available commands${NC}"
echo -e "${CYAN}💡 Type 'ehrx-quick-start' to get started quickly${NC}"
