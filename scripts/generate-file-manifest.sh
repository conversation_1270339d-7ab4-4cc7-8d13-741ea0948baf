#!/bin/bash

# 🔐 eHRx File Manifest Generator
# Generates a complete file manifest for comparison between servers

echo "📋 Generating eHRx File Manifest"
echo "================================"
echo "Server: $(hostname)"
echo "Date: $(date)"
echo "Generated from: $(pwd)"
echo ""

MANIFEST_FILE="ehrx-file-manifest-$(date +%Y%m%d-%H%M%S).txt"

echo "Creating manifest: $MANIFEST_FILE"
echo ""

{
    echo "# eHRx File Manifest"
    echo "# Generated on: $(date)"
    echo "# Server: $(hostname)"
    echo "# Base directory: /var/www/ehrx"
    echo ""
    
    echo "## DIRECTORY STRUCTURE"
    find /var/www/ehrx -type d | sort
    echo ""
    
    echo "## ALL FILES WITH SIZES"
    find /var/www/ehrx -type f -exec ls -la {} \; | sort
    echo ""
    
    echo "## CRITICAL CONFIGURATION FILES"
    echo "### Backend .env"
    if [ -f "/var/www/ehrx/backend/.env" ]; then
        echo "EXISTS: /var/www/ehrx/backend/.env"
        wc -l /var/www/ehrx/backend/.env
    else
        echo "MISSING: /var/www/ehrx/backend/.env"
    fi
    
    echo "### Frontend .env"
    if [ -f "/var/www/ehrx/frontend/.env" ]; then
        echo "EXISTS: /var/www/ehrx/frontend/.env"
        wc -l /var/www/ehrx/frontend/.env
    else
        echo "MISSING: /var/www/ehrx/frontend/.env"
    fi
    
    echo ""
    echo "## PACKAGE.JSON FILES"
    find /var/www/ehrx -name "package.json" -exec echo "=== {} ===" \; -exec head -20 {} \;
    
    echo ""
    echo "## EXECUTABLE SCRIPTS"
    find /var/www/ehrx -type f -executable | sort
    
    echo ""
    echo "## LOG AND PID DIRECTORIES"
    echo "### /var/log/ehrx"
    if [ -d "/var/log/ehrx" ]; then
        ls -la /var/log/ehrx/
    else
        echo "MISSING: /var/log/ehrx"
    fi
    
    echo "### /var/run/ehrx"
    if [ -d "/var/run/ehrx" ]; then
        ls -la /var/run/ehrx/
    else
        echo "MISSING: /var/run/ehrx"
    fi
    
    echo ""
    echo "## SYSTEM DEPENDENCIES"
    echo "Node.js: $(node --version 2>/dev/null || echo 'NOT INSTALLED')"
    echo "npm: $(npm --version 2>/dev/null || echo 'NOT INSTALLED')"
    echo "PostgreSQL: $(psql --version 2>/dev/null || echo 'NOT INSTALLED')"
    echo "Git: $(git --version 2>/dev/null || echo 'NOT INSTALLED')"
    
    echo ""
    echo "## RUNNING PROCESSES"
    ps aux | grep -E "(node|npm|postgres)" | grep -v grep
    
    echo ""
    echo "## NETWORK PORTS"
    netstat -tlnp | grep -E ":(3080|4000|5432)"
    
} > "$MANIFEST_FILE"

echo "✅ Manifest generated: $MANIFEST_FILE"
echo ""
echo "📤 To compare with another server:"
echo "1. Copy this file to the other server"
echo "2. Run this script on the other server"
echo "3. Use 'diff' to compare the manifests"
echo ""
echo "Example comparison command:"
echo "diff ehrx-file-manifest-server1.txt ehrx-file-manifest-server2.txt"
