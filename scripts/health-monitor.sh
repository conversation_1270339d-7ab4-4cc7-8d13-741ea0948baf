#!/bin/bash

# EHRX Health Monitoring and Auto-Restart System
# Enterprise-grade monitoring with automatic recovery and alerting

set -e

# Source required modules
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/pid-manager.sh"
source "$SCRIPT_DIR/logging-system.sh"

# Configuration
MONITOR_INTERVAL="${MONITOR_INTERVAL:-30}"  # seconds
MAX_RESTART_ATTEMPTS="${MAX_RESTART_ATTEMPTS:-3}"
RESTART_COOLDOWN="${RESTART_COOLDOWN:-60}"  # seconds
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-10}"  # seconds

# Service configuration
BACKEND_PORT="${BACKEND_PORT:-4000}"
FRONTEND_PORT="${FRONTEND_PORT:-3080}"
BACKEND_HEALTH_URL="http://localhost:$BACKEND_PORT/api/system/health"
FRONTEND_HEALTH_URL="http://localhost:$FRONTEND_PORT"

# Monitoring state
MONITOR_STATE_FILE="/var/run/ehrx/monitor-state.json"
RESTART_ATTEMPTS_FILE="/var/run/ehrx/restart-attempts.json"

# Alert configuration
ENABLE_EMAIL_ALERTS="${ENABLE_EMAIL_ALERTS:-false}"
ALERT_EMAIL="${ALERT_EMAIL:-admin@localhost}"
ENABLE_WEBHOOK_ALERTS="${ENABLE_WEBHOOK_ALERTS:-false}"
WEBHOOK_URL="${WEBHOOK_URL:-}"

# Initialize monitoring system
init_monitoring() {
    log_info "Initializing health monitoring system" "MONITOR"
    
    # Create state files
    echo '{}' > "$MONITOR_STATE_FILE"
    echo '{}' > "$RESTART_ATTEMPTS_FILE"
    
    # Initialize restart attempt counters
    update_restart_attempts "backend" 0
    update_restart_attempts "frontend" 0
    
    log_info "Health monitoring system initialized" "MONITOR"
}

# Update restart attempt counter
update_restart_attempts() {
    local service=$1
    local count=$2
    
    local temp_file=$(mktemp)
    jq --arg service "$service" --argjson count "$count" \
       '.[$service] = $count' "$RESTART_ATTEMPTS_FILE" > "$temp_file"
    mv "$temp_file" "$RESTART_ATTEMPTS_FILE"
}

# Get restart attempt count
get_restart_attempts() {
    local service=$1
    jq -r --arg service "$service" '.[$service] // 0' "$RESTART_ATTEMPTS_FILE"
}

# Reset restart attempts
reset_restart_attempts() {
    local service=$1
    update_restart_attempts "$service" 0
    log_info "Reset restart attempts for $service" "MONITOR"
}

# Check service health via HTTP
check_http_health() {
    local url=$1
    local service_name=$2
    
    log_debug "Checking HTTP health for $service_name: $url" "HEALTH_CHECK"
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null --max-time "$HEALTH_CHECK_TIMEOUT" "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ] || [ "$response" = "204" ]; then
        log_debug "$service_name HTTP health check passed (HTTP $response)" "HEALTH_CHECK"
        return 0
    else
        log_warn "$service_name HTTP health check failed (HTTP $response)" "HEALTH_CHECK"
        return 1
    fi
}

# Check process health via PID
check_process_health() {
    local service_name=$1
    
    log_debug "Checking process health for $service_name" "HEALTH_CHECK"
    
    local pid=$(read_pid "$service_name" 2>/dev/null)
    
    if [ $? -eq 0 ] && is_process_running "$pid"; then
        log_debug "$service_name process health check passed (PID: $pid)" "HEALTH_CHECK"
        return 0
    else
        log_warn "$service_name process health check failed" "HEALTH_CHECK"
        return 1
    fi
}

# Check port availability
check_port_health() {
    local port=$1
    local service_name=$2
    
    log_debug "Checking port health for $service_name on port $port" "HEALTH_CHECK"
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_debug "$service_name port health check passed (port $port)" "HEALTH_CHECK"
        return 0
    else
        log_warn "$service_name port health check failed (port $port)" "HEALTH_CHECK"
        return 1
    fi
}

# Comprehensive service health check
check_service_health() {
    local service_name=$1
    local health_checks_passed=0
    local total_checks=0
    
    log_debug "Running comprehensive health check for $service_name" "HEALTH_CHECK"
    
    # Check 1: Process health
    ((total_checks++))
    if check_process_health "$service_name"; then
        ((health_checks_passed++))
    fi
    
    # Check 2: Port health
    local port=""
    case $service_name in
        "backend")
            port="$BACKEND_PORT"
            ;;
        "frontend")
            port="$FRONTEND_PORT"
            ;;
    esac
    
    if [ -n "$port" ]; then
        ((total_checks++))
        if check_port_health "$port" "$service_name"; then
            ((health_checks_passed++))
        fi
    fi
    
    # Check 3: HTTP health (if applicable)
    local health_url=""
    case $service_name in
        "backend")
            health_url="$BACKEND_HEALTH_URL"
            ;;
        "frontend")
            health_url="$FRONTEND_HEALTH_URL"
            ;;
    esac
    
    if [ -n "$health_url" ]; then
        ((total_checks++))
        if check_http_health "$health_url" "$service_name"; then
            ((health_checks_passed++))
        fi
    fi
    
    # Determine overall health
    local health_percentage=$((health_checks_passed * 100 / total_checks))
    
    log_debug "$service_name health: $health_checks_passed/$total_checks checks passed ($health_percentage%)" "HEALTH_CHECK"
    
    # Service is healthy if at least 66% of checks pass
    if [ $health_percentage -ge 66 ]; then
        log_debug "$service_name is healthy" "HEALTH_CHECK"
        return 0
    else
        log_warn "$service_name is unhealthy ($health_percentage% health)" "HEALTH_CHECK"
        return 1
    fi
}

# Send alert notification
send_alert() {
    local alert_type=$1
    local service_name=$2
    local message=$3
    
    log_security "ALERT: $alert_type for $service_name - $message" "ALERT"
    
    # Email alert
    if [ "$ENABLE_EMAIL_ALERTS" = "true" ] && command -v mail &> /dev/null; then
        local subject="EHRX Alert: $alert_type - $service_name"
        local body="Service: $service_name\nAlert Type: $alert_type\nMessage: $message\nTimestamp: $(date)\nHostname: $(hostname)"
        
        echo -e "$body" | mail -s "$subject" "$ALERT_EMAIL" 2>/dev/null || \
            log_error "Failed to send email alert to $ALERT_EMAIL" "ALERT"
    fi
    
    # Webhook alert
    if [ "$ENABLE_WEBHOOK_ALERTS" = "true" ] && [ -n "$WEBHOOK_URL" ]; then
        local payload=$(cat <<EOF
{
  "alert_type": "$alert_type",
  "service": "$service_name",
  "message": "$message",
  "timestamp": "$(date -u '+%Y-%m-%dT%H:%M:%S.%3NZ')",
  "hostname": "$(hostname)",
  "severity": "high"
}
EOF
)
        
        curl -s -X POST -H "Content-Type: application/json" -d "$payload" "$WEBHOOK_URL" >/dev/null 2>&1 || \
            log_error "Failed to send webhook alert to $WEBHOOK_URL" "ALERT"
    fi
}

# Restart service with backoff
restart_service_with_backoff() {
    local service_name=$1
    
    local current_attempts=$(get_restart_attempts "$service_name")
    
    if [ "$current_attempts" -ge "$MAX_RESTART_ATTEMPTS" ]; then
        log_error "Maximum restart attempts ($MAX_RESTART_ATTEMPTS) reached for $service_name" "RESTART"
        send_alert "MAX_RESTARTS_EXCEEDED" "$service_name" "Service has failed $MAX_RESTART_ATTEMPTS restart attempts"
        return 1
    fi
    
    log_warn "Attempting to restart $service_name (attempt $((current_attempts + 1))/$MAX_RESTART_ATTEMPTS)" "RESTART"
    
    # Stop the service first
    stop_service "$service_name" || true
    
    # Wait for cooldown
    log_info "Waiting $RESTART_COOLDOWN seconds before restart" "RESTART"
    sleep "$RESTART_COOLDOWN"
    
    # Increment restart attempts
    update_restart_attempts "$service_name" $((current_attempts + 1))
    
    # Restart based on environment
    local restart_success=false
    
    case $service_name in
        "backend")
            if [ "${NODE_ENV:-development}" = "production" ]; then
                if "$SCRIPT_DIR/start-prod.sh" >/dev/null 2>&1; then
                    restart_success=true
                fi
            else
                if "$SCRIPT_DIR/start-dev.sh" >/dev/null 2>&1; then
                    restart_success=true
                fi
            fi
            ;;
        "frontend")
            # Frontend restart is typically handled by the main start script
            log_info "Frontend restart requires full application restart" "RESTART"
            ;;
    esac
    
    if [ "$restart_success" = "true" ]; then
        log_info "Successfully restarted $service_name" "RESTART"
        send_alert "SERVICE_RESTARTED" "$service_name" "Service was automatically restarted after health check failure"
        
        # Reset attempts on successful restart
        sleep 30  # Wait for service to stabilize
        if check_service_health "$service_name"; then
            reset_restart_attempts "$service_name"
        fi
        
        return 0
    else
        log_error "Failed to restart $service_name" "RESTART"
        send_alert "RESTART_FAILED" "$service_name" "Automatic restart failed"
        return 1
    fi
}

# Monitor single service
monitor_service() {
    local service_name=$1
    
    log_debug "Monitoring $service_name" "MONITOR"
    
    if check_service_health "$service_name"; then
        # Service is healthy
        local current_attempts=$(get_restart_attempts "$service_name")
        if [ "$current_attempts" -gt 0 ]; then
            log_info "$service_name recovered, resetting restart attempts" "MONITOR"
            reset_restart_attempts "$service_name"
        fi
        return 0
    else
        # Service is unhealthy
        log_warn "$service_name is unhealthy, attempting restart" "MONITOR"
        send_alert "SERVICE_UNHEALTHY" "$service_name" "Service health check failed"
        
        if restart_service_with_backoff "$service_name"; then
            log_info "$service_name restart successful" "MONITOR"
            return 0
        else
            log_error "$service_name restart failed" "MONITOR"
            return 1
        fi
    fi
}

# Main monitoring loop
run_monitoring_loop() {
    log_info "Starting health monitoring loop (interval: ${MONITOR_INTERVAL}s)" "MONITOR"
    
    local loop_count=0
    
    while true; do
        ((loop_count++))
        log_debug "Monitoring loop iteration $loop_count" "MONITOR"
        
        # Monitor backend
        monitor_service "backend"
        
        # Monitor frontend
        monitor_service "frontend"
        
        # Log monitoring statistics every 10 iterations
        if [ $((loop_count % 10)) -eq 0 ]; then
            log_info "Monitoring statistics: $loop_count iterations completed" "MONITOR"
            
            # Rotate logs if needed
            if [ $((loop_count % 100)) -eq 0 ]; then
                rotate_logs
            fi
        fi
        
        # Sleep until next check
        sleep "$MONITOR_INTERVAL"
    done
}

# Graceful shutdown
shutdown_monitor() {
    log_info "Shutting down health monitor" "MONITOR"
    
    # Clean up state files
    rm -f "$MONITOR_STATE_FILE" "$RESTART_ATTEMPTS_FILE"
    
    log_info "Health monitor shutdown complete" "MONITOR"
    exit 0
}

# Signal handlers
trap 'shutdown_monitor' INT TERM

# Main execution
main() {
    log_info "=== EHRX Health Monitor Starting ===" "MONITOR"
    
    # Initialize monitoring
    init_monitoring
    
    # Save monitor PID
    save_pid "monitor" "$$"
    
    # Start monitoring loop
    run_monitoring_loop
}

# Export functions
export -f check_http_health check_process_health check_port_health
export -f check_service_health monitor_service
export -f restart_service_with_backoff send_alert

# If script is run directly, start monitoring
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
