#!/bin/bash

# EHRX Enterprise Logging System
# Structured logging with timestamps, levels, rotation, and monitoring

# Configuration
LOG_DIR="/var/log/ehrx"
SCRIPT_LOG_DIR="$LOG_DIR/scripts"
SERVICE_LOG_DIR="$LOG_DIR/services"
AUDIT_LOG_DIR="$LOG_DIR/audit"
ARCHIVE_DIR="$LOG_DIR/archive"

# Log file names
MAIN_LOG="$SCRIPT_LOG_DIR/ehrx-main.log"
ERROR_LOG="$SCRIPT_LOG_DIR/ehrx-error.log"
AUDIT_LOG="$AUDIT_LOG_DIR/ehrx-audit.log"
PERFORMANCE_LOG="$SCRIPT_LOG_DIR/ehrx-performance.log"
SECURITY_LOG="$AUDIT_LOG_DIR/ehrx-security.log"

# Log levels
declare -A LOG_LEVELS=(
    ["TRACE"]=0
    ["DEBUG"]=1
    ["INFO"]=2
    ["WARN"]=3
    ["ERROR"]=4
    ["FATAL"]=5
    ["AUDIT"]=6
    ["SECURITY"]=7
)

# Current log level (can be overridden by environment)
CURRENT_LOG_LEVEL="${LOG_LEVEL:-INFO}"
CURRENT_LOG_LEVEL_NUM=${LOG_LEVELS[$CURRENT_LOG_LEVEL]:-2}

# Colors for console output
declare -A LOG_COLORS=(
    ["TRACE"]='\033[0;37m'    # Light Gray
    ["DEBUG"]='\033[0;34m'    # Blue
    ["INFO"]='\033[0;32m'     # Green
    ["WARN"]='\033[1;33m'     # Yellow
    ["ERROR"]='\033[0;31m'    # Red
    ["FATAL"]='\033[1;31m'    # Bold Red
    ["AUDIT"]='\033[0;35m'    # Purple
    ["SECURITY"]='\033[1;35m' # Bold Purple
)

NC='\033[0m' # No Color

# Log rotation settings
MAX_LOG_SIZE="10M"
MAX_LOG_FILES=30
COMPRESS_LOGS=true

# Initialize logging system
init_logging_system() {
    # Create log directories
    local dirs=("$LOG_DIR" "$SCRIPT_LOG_DIR" "$SERVICE_LOG_DIR" "$AUDIT_LOG_DIR" "$ARCHIVE_DIR")
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            sudo mkdir -p "$dir"
            sudo chown $(whoami):$(whoami) "$dir"
            chmod 755 "$dir"
        fi
    done
    
    # Set proper permissions for audit logs
    chmod 750 "$AUDIT_LOG_DIR"
    
    # Initialize log files
    touch "$MAIN_LOG" "$ERROR_LOG" "$AUDIT_LOG" "$PERFORMANCE_LOG" "$SECURITY_LOG"
    chmod 644 "$MAIN_LOG" "$PERFORMANCE_LOG"
    chmod 640 "$ERROR_LOG" "$AUDIT_LOG" "$SECURITY_LOG"
    
    # Log system initialization
    write_log "INFO" "SYSTEM" "Logging system initialized" "" "init_logging_system"
}

# Core logging function
write_log() {
    local level=$1
    local component=$2
    local message=$3
    local context=${4:-""}
    local function_name=${5:-"${FUNCNAME[2]}"}
    local line_number=${6:-"${BASH_LINENO[1]}"}
    
    # Check if log level should be written
    local level_num=${LOG_LEVELS[$level]:-2}
    if [ $level_num -lt $CURRENT_LOG_LEVEL_NUM ]; then
        return 0
    fi
    
    # Generate timestamp
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')
    local iso_timestamp=$(date -u '+%Y-%m-%dT%H:%M:%S.%3NZ')
    
    # Generate unique log ID
    local log_id=$(date +%s%N | cut -b1-13)
    
    # Get process info
    local pid=$$
    local ppid=$PPID
    local user=$(whoami)
    local hostname=$(hostname)
    
    # Create structured log entry
    local structured_entry=$(cat <<EOF
{
  "timestamp": "$iso_timestamp",
  "level": "$level",
  "component": "$component",
  "message": "$message",
  "context": "$context",
  "metadata": {
    "log_id": "$log_id",
    "pid": $pid,
    "ppid": $ppid,
    "user": "$user",
    "hostname": "$hostname",
    "function": "$function_name",
    "line": $line_number,
    "script": "${BASH_SOURCE[2]:-unknown}"
  }
}
EOF
)
    
    # Create human-readable entry
    local human_entry="[$timestamp] [$level] [$component] $message"
    if [ -n "$context" ]; then
        human_entry="$human_entry | Context: $context"
    fi
    human_entry="$human_entry | PID: $pid | Function: $function_name"
    
    # Write to appropriate log files
    case $level in
        "ERROR"|"FATAL")
            echo "$human_entry" >> "$ERROR_LOG"
            echo "$structured_entry" >> "$ERROR_LOG.json" 2>/dev/null || true
            ;;
        "AUDIT")
            echo "$human_entry" >> "$AUDIT_LOG"
            echo "$structured_entry" >> "$AUDIT_LOG.json" 2>/dev/null || true
            ;;
        "SECURITY")
            echo "$human_entry" >> "$SECURITY_LOG"
            echo "$structured_entry" >> "$SECURITY_LOG.json" 2>/dev/null || true
            ;;
    esac
    
    # Always write to main log
    echo "$human_entry" >> "$MAIN_LOG"
    echo "$structured_entry" >> "$MAIN_LOG.json" 2>/dev/null || true
    
    # Console output with colors
    local color=${LOG_COLORS[$level]:-$NC}
    echo -e "${color}$human_entry${NC}" >&2
}

# Convenience logging functions
log_trace() {
    write_log "TRACE" "${2:-SCRIPT}" "$1" "$3" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}

log_debug() {
    write_log "DEBUG" "${2:-SCRIPT}" "$1" "$3" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}

log_info() {
    write_log "INFO" "${2:-SCRIPT}" "$1" "$3" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}

log_warn() {
    write_log "WARN" "${2:-SCRIPT}" "$1" "$3" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}

log_error() {
    write_log "ERROR" "${2:-SCRIPT}" "$1" "$3" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}

log_fatal() {
    write_log "FATAL" "${2:-SCRIPT}" "$1" "$3" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}

log_audit() {
    write_log "AUDIT" "${2:-AUDIT}" "$1" "$3" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}

log_security() {
    write_log "SECURITY" "${2:-SECURITY}" "$1" "$3" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}

# Performance logging
log_performance() {
    local operation=$1
    local duration=$2
    local status=$3
    local details=${4:-""}
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S.%3N')
    local entry="[$timestamp] [PERFORMANCE] Operation: $operation | Duration: ${duration}ms | Status: $status"
    
    if [ -n "$details" ]; then
        entry="$entry | Details: $details"
    fi
    
    echo "$entry" >> "$PERFORMANCE_LOG"
    
    # Also log to main log if duration is significant
    if [ "$duration" -gt 1000 ]; then
        log_warn "Slow operation detected: $operation took ${duration}ms" "PERFORMANCE" "$details"
    fi
}

# Function execution timer
time_function() {
    local func_name=$1
    shift
    
    local start_time=$(date +%s%3N)
    log_debug "Starting function: $func_name" "TIMER"
    
    # Execute function
    "$func_name" "$@"
    local exit_code=$?
    
    local end_time=$(date +%s%3N)
    local duration=$((end_time - start_time))
    
    local status="SUCCESS"
    if [ $exit_code -ne 0 ]; then
        status="FAILED"
    fi
    
    log_performance "$func_name" "$duration" "$status" "Exit code: $exit_code"
    log_debug "Completed function: $func_name (${duration}ms)" "TIMER"
    
    return $exit_code
}

# Log rotation function
rotate_logs() {
    log_info "Starting log rotation" "LOG_ROTATION"
    
    local rotated_count=0
    local log_files=("$MAIN_LOG" "$ERROR_LOG" "$AUDIT_LOG" "$PERFORMANCE_LOG" "$SECURITY_LOG")
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            local file_size=$(stat -f%z "$log_file" 2>/dev/null || stat -c%s "$log_file" 2>/dev/null || echo 0)
            local max_size_bytes=10485760  # 10MB
            
            if [ "$file_size" -gt "$max_size_bytes" ]; then
                local timestamp=$(date '+%Y%m%d_%H%M%S')
                local archived_name="$(basename "$log_file").$timestamp"
                local archive_path="$ARCHIVE_DIR/$archived_name"
                
                # Move current log to archive
                mv "$log_file" "$archive_path"
                
                # Compress if enabled
                if [ "$COMPRESS_LOGS" = "true" ]; then
                    gzip "$archive_path"
                    archive_path="$archive_path.gz"
                fi
                
                # Create new log file
                touch "$log_file"
                chmod 644 "$log_file"
                
                log_info "Rotated log file: $log_file -> $archive_path" "LOG_ROTATION"
                ((rotated_count++))
            fi
        fi
    done
    
    # Clean up old archives
    find "$ARCHIVE_DIR" -name "*.log.*" -mtime +$MAX_LOG_FILES -delete 2>/dev/null || true
    
    log_info "Log rotation completed. Rotated $rotated_count files" "LOG_ROTATION"
}

# Log analysis functions
analyze_logs() {
    local log_file=${1:-$MAIN_LOG}
    local hours=${2:-24}
    
    log_info "Analyzing logs from last $hours hours" "LOG_ANALYSIS"
    
    # Get timestamp for X hours ago
    local since_time=$(date -d "$hours hours ago" '+%Y-%m-%d %H:%M:%S' 2>/dev/null || date -v-${hours}H '+%Y-%m-%d %H:%M:%S' 2>/dev/null)
    
    echo "Log Analysis Report - Last $hours hours"
    echo "========================================"
    
    # Count by log level
    echo "Log Level Distribution:"
    awk -v since="$since_time" '$0 > since' "$log_file" | grep -o '\[.*\]' | sed -n 's/.*\[\([A-Z]*\)\].*/\1/p' | sort | uniq -c | sort -nr
    
    echo ""
    echo "Error Summary:"
    awk -v since="$since_time" '$0 > since && /\[ERROR\]|\[FATAL\]/' "$log_file" | tail -10
    
    echo ""
    echo "Warning Summary:"
    awk -v since="$since_time" '$0 > since && /\[WARN\]/' "$log_file" | tail -5
}

# Export log file paths for other scripts
export MAIN_LOG ERROR_LOG AUDIT_LOG PERFORMANCE_LOG SECURITY_LOG

# Export logging functions
export -f init_logging_system
export -f write_log
export -f log_trace log_debug log_info log_warn log_error log_fatal log_audit log_security
export -f log_performance time_function
export -f rotate_logs analyze_logs

# Backward compatibility with existing log_message function
log_message() {
    local level=$1
    local message=$2
    local component=${3:-"SCRIPT"}
    
    write_log "$level" "$component" "$message" "" "${FUNCNAME[1]}" "${BASH_LINENO[0]}"
}
export -f log_message

# Initialize logging system when sourced
if [ "${BASH_SOURCE[0]}" != "${0}" ]; then
    init_logging_system
fi

# If script is run directly, show log analysis
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    init_logging_system
    
    case "${1:-status}" in
        "rotate")
            rotate_logs
            ;;
        "analyze")
            analyze_logs "$2" "$3"
            ;;
        "status")
            echo "EHRX Logging System Status"
            echo "=========================="
            echo "Log Directory: $LOG_DIR"
            echo "Main Log: $MAIN_LOG"
            echo "Error Log: $ERROR_LOG"
            echo "Audit Log: $AUDIT_LOG"
            echo "Performance Log: $PERFORMANCE_LOG"
            echo "Security Log: $SECURITY_LOG"
            echo ""
            echo "Log Level: $CURRENT_LOG_LEVEL"
            echo "Log Files:"
            ls -lh "$SCRIPT_LOG_DIR"/*.log 2>/dev/null || echo "No log files found"
            ;;
        *)
            echo "Usage: $0 [status|rotate|analyze [log_file] [hours]]"
            exit 1
            ;;
    esac
fi
