#!/bin/bash

# EHRX PID Management System
# Provides enterprise-grade process tracking and management

# Configuration
PID_DIR="/var/run/ehrx"
LOG_DIR="/var/log/ehrx"
BACKEND_PID_FILE="$PID_DIR/backend.pid"
FRONTEND_PID_FILE="$PID_DIR/frontend.pid"
MONITOR_PID_FILE="$PID_DIR/monitor.pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Log to file
    echo "[$timestamp] [$level] $message" >> "$LOG_DIR/pid-manager.log"
    
    # Log to console with colors
    case $level in
        "ERROR")
            echo -e "${RED}[$timestamp] [ERROR] $message${NC}" >&2
            ;;
        "WARN")
            echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}"
            ;;
        "INFO")
            echo -e "${GREEN}[$timestamp] [INFO] $message${NC}"
            ;;
        "DEBUG")
            echo -e "${BLUE}[$timestamp] [DEBUG] $message${NC}"
            ;;
        *)
            echo -e "${CYAN}[$timestamp] [$level] $message${NC}"
            ;;
    esac
}

# Initialize PID management
init_pid_management() {
    log_message "INFO" "Initializing PID management system"
    
    # Create directories if they don't exist
    if [ ! -d "$PID_DIR" ]; then
        sudo mkdir -p "$PID_DIR"
        sudo chown $(whoami):$(whoami) "$PID_DIR"
        log_message "INFO" "Created PID directory: $PID_DIR"
    fi
    
    if [ ! -d "$LOG_DIR" ]; then
        sudo mkdir -p "$LOG_DIR"
        sudo chown $(whoami):$(whoami) "$LOG_DIR"
        log_message "INFO" "Created log directory: $LOG_DIR"
    fi
    
    # Set proper permissions
    chmod 755 "$PID_DIR" "$LOG_DIR"
    log_message "INFO" "PID management system initialized"
}

# Save PID to file
save_pid() {
    local service_name=$1
    local pid=$2
    local pid_file=""
    
    case $service_name in
        "backend")
            pid_file="$BACKEND_PID_FILE"
            ;;
        "frontend")
            pid_file="$FRONTEND_PID_FILE"
            ;;
        "monitor")
            pid_file="$MONITOR_PID_FILE"
            ;;
        *)
            log_message "ERROR" "Unknown service name: $service_name"
            return 1
            ;;
    esac
    
    echo "$pid" > "$pid_file"
    log_message "INFO" "Saved PID $pid for $service_name to $pid_file"
    return 0
}

# Read PID from file
read_pid() {
    local service_name=$1
    local pid_file=""
    
    case $service_name in
        "backend")
            pid_file="$BACKEND_PID_FILE"
            ;;
        "frontend")
            pid_file="$FRONTEND_PID_FILE"
            ;;
        "monitor")
            pid_file="$MONITOR_PID_FILE"
            ;;
        *)
            log_message "ERROR" "Unknown service name: $service_name"
            return 1
            ;;
    esac
    
    if [ -f "$pid_file" ]; then
        cat "$pid_file"
        return 0
    else
        log_message "WARN" "PID file not found for $service_name: $pid_file"
        return 1
    fi
}

# Check if process is running
is_process_running() {
    local pid=$1
    
    if [ -z "$pid" ]; then
        return 1
    fi
    
    if kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Check service status
check_service_status() {
    local service_name=$1
    local pid=$(read_pid "$service_name")
    
    if [ $? -eq 0 ] && is_process_running "$pid"; then
        log_message "INFO" "$service_name is running (PID: $pid)"
        return 0
    else
        log_message "WARN" "$service_name is not running"
        return 1
    fi
}

# Stop service by PID
stop_service() {
    local service_name=$1
    local force=${2:-false}
    local pid=$(read_pid "$service_name")
    
    if [ $? -ne 0 ]; then
        log_message "WARN" "No PID file found for $service_name"
        return 1
    fi
    
    if ! is_process_running "$pid"; then
        log_message "WARN" "$service_name (PID: $pid) is not running"
        cleanup_pid_file "$service_name"
        return 0
    fi
    
    log_message "INFO" "Stopping $service_name (PID: $pid)"
    
    if [ "$force" = "true" ]; then
        # Force kill
        kill -9 "$pid" 2>/dev/null
        log_message "INFO" "Force killed $service_name (PID: $pid)"
    else
        # Graceful shutdown
        kill -TERM "$pid" 2>/dev/null
        
        # Wait for graceful shutdown
        local attempts=0
        local max_attempts=10
        
        while [ $attempts -lt $max_attempts ] && is_process_running "$pid"; do
            sleep 1
            ((attempts++))
            log_message "DEBUG" "Waiting for $service_name to stop... ($attempts/$max_attempts)"
        done
        
        # Force kill if still running
        if is_process_running "$pid"; then
            log_message "WARN" "$service_name did not stop gracefully, force killing"
            kill -9 "$pid" 2>/dev/null
        fi
    fi
    
    # Verify process is stopped
    if is_process_running "$pid"; then
        log_message "ERROR" "Failed to stop $service_name (PID: $pid)"
        return 1
    else
        log_message "INFO" "Successfully stopped $service_name"
        cleanup_pid_file "$service_name"
        return 0
    fi
}

# Clean up PID file
cleanup_pid_file() {
    local service_name=$1
    local pid_file=""
    
    case $service_name in
        "backend")
            pid_file="$BACKEND_PID_FILE"
            ;;
        "frontend")
            pid_file="$FRONTEND_PID_FILE"
            ;;
        "monitor")
            pid_file="$MONITOR_PID_FILE"
            ;;
        *)
            log_message "ERROR" "Unknown service name: $service_name"
            return 1
            ;;
    esac
    
    if [ -f "$pid_file" ]; then
        rm -f "$pid_file"
        log_message "INFO" "Cleaned up PID file for $service_name"
    fi
}

# Clean up all PID files
cleanup_all_pid_files() {
    log_message "INFO" "Cleaning up all PID files"
    cleanup_pid_file "backend"
    cleanup_pid_file "frontend"
    cleanup_pid_file "monitor"
}

# Get all running services
get_running_services() {
    local services=()
    
    for service in "backend" "frontend" "monitor"; do
        if check_service_status "$service" >/dev/null 2>&1; then
            services+=("$service")
        fi
    done
    
    echo "${services[@]}"
}

# Display service status
show_status() {
    echo -e "${CYAN}EHRX Service Status${NC}"
    echo -e "${CYAN}==================${NC}"
    
    for service in "backend" "frontend" "monitor"; do
        local pid=$(read_pid "$service" 2>/dev/null)
        
        if [ $? -eq 0 ] && is_process_running "$pid"; then
            echo -e "${GREEN}✅ $service: Running (PID: $pid)${NC}"
        else
            echo -e "${RED}❌ $service: Not running${NC}"
        fi
    done
}

# Export functions for use in other scripts
export -f log_message
export -f init_pid_management
export -f save_pid
export -f read_pid
export -f is_process_running
export -f check_service_status
export -f stop_service
export -f cleanup_pid_file
export -f cleanup_all_pid_files
export -f get_running_services
export -f show_status

# If script is run directly, show status
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    init_pid_management
    show_status
fi
