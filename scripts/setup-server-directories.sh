#!/bin/bash

# 🔐 eHRx Server Directory Setup Script
# Creates all necessary directories and sets proper permissions

echo "🔧 Setting up eHRx Server Directories"
echo "====================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to create directory with proper permissions
create_directory() {
    local dir_path="$1"
    local description="$2"
    
    if [ ! -d "$dir_path" ]; then
        echo -e "${BLUE}Creating:${NC} $description ($dir_path)"
        sudo mkdir -p "$dir_path"
        sudo chown $(whoami):$(whoami) "$dir_path"
        sudo chmod 755 "$dir_path"
        echo -e "${GREEN}✅ Created${NC}: $dir_path"
    else
        echo -e "${GREEN}✅ Exists${NC}: $dir_path"
    fi
}

echo ""
echo "📁 Creating Core Directories"
echo "----------------------------"

# Core application directories (should already exist from git clone)
create_directory "/var/www/ehrx" "Project root"
create_directory "/var/www/ehrx/backend" "Backend application"
create_directory "/var/www/ehrx/frontend" "Frontend application"
create_directory "/var/www/ehrx/scripts" "Utility scripts"

echo ""
echo "📝 Creating Log Directories"
echo "---------------------------"

# Log directories
create_directory "/var/log/ehrx" "Main log directory"
sudo chown $(whoami):$(whoami) /var/log/ehrx
sudo chmod 755 /var/log/ehrx

echo ""
echo "🔄 Creating Runtime Directories"
echo "-------------------------------"

# Runtime directories
create_directory "/var/run/ehrx" "PID files directory"
sudo chown $(whoami):$(whoami) /var/run/ehrx
sudo chmod 755 /var/run/ehrx

echo ""
echo "🔐 Setting Script Permissions"
echo "-----------------------------"

# Make scripts executable
if [ -f "/var/www/ehrx/scripts/start-dev.sh" ]; then
    chmod +x /var/www/ehrx/scripts/start-dev.sh
    echo -e "${GREEN}✅ Made executable${NC}: start-dev.sh"
fi

if [ -f "/var/www/ehrx/scripts/pid-manager.sh" ]; then
    chmod +x /var/www/ehrx/scripts/pid-manager.sh
    echo -e "${GREEN}✅ Made executable${NC}: pid-manager.sh"
fi

if [ -f "/var/www/ehrx/scripts/dependency-checker.sh" ]; then
    chmod +x /var/www/ehrx/scripts/dependency-checker.sh
    echo -e "${GREEN}✅ Made executable${NC}: dependency-checker.sh"
fi

if [ -f "/var/www/ehrx/scripts/verify-server-completeness.sh" ]; then
    chmod +x /var/www/ehrx/scripts/verify-server-completeness.sh
    echo -e "${GREEN}✅ Made executable${NC}: verify-server-completeness.sh"
fi

if [ -f "/var/www/ehrx/scripts/generate-file-manifest.sh" ]; then
    chmod +x /var/www/ehrx/scripts/generate-file-manifest.sh
    echo -e "${GREEN}✅ Made executable${NC}: generate-file-manifest.sh"
fi

echo ""
echo "📊 Directory Setup Summary"
echo "=========================="
echo "Log directory: /var/log/ehrx"
ls -la /var/log/ehrx/ 2>/dev/null || echo "Directory not accessible"

echo ""
echo "PID directory: /var/run/ehrx"
ls -la /var/run/ehrx/ 2>/dev/null || echo "Directory not accessible"

echo ""
echo -e "${GREEN}🎉 Server directory setup complete!${NC}"
echo ""
echo "Next steps:"
echo "1. Run: ./scripts/verify-server-completeness.sh"
echo "2. If verification passes, start services with: ./scripts/start-dev.sh"
