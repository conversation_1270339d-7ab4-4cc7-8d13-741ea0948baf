#!/bin/bash

# EHRX Production Environment Startup Script
# Optimized for production with security, performance, and monitoring

set -e

# Source required modules
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/pid-manager.sh"
source "$SCRIPT_DIR/dependency-checker.sh"

# Production Configuration
export NODE_ENV="production"
export BACKEND_PORT="${BACKEND_PORT:-4000}"
export FRONTEND_PORT="${FRONTEND_PORT:-3080}"
export LOG_LEVEL="${LOG_LEVEL:-info}"

# Production-specific settings
ENABLE_SSL="${ENABLE_SSL:-true}"
ENABLE_MONITORING="${ENABLE_MONITORING:-true}"
REQUIRE_ENV_FILE=true
STRICT_DEPENDENCY_CHECK=true
BUILD_FRONTEND=true
USE_PM2="${USE_PM2:-true}"

# Project paths
PROJECT_ROOT="/var/www/ehrx"
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
BUILD_DIR="$FRONTEND_DIR/build"

# Initialize logging and PID management
init_pid_management

log_message "INFO" "Starting EHRX Production Environment"
log_message "INFO" "Environment: $NODE_ENV"
log_message "INFO" "Backend Port: $BACKEND_PORT"
log_message "INFO" "Frontend Port: $FRONTEND_PORT"
log_message "INFO" "SSL Enabled: $ENABLE_SSL"

# Function to validate production environment
validate_production_environment() {
    log_message "INFO" "Validating production environment"
    
    # Check for production environment file
    if [ ! -f "$BACKEND_DIR/.env.production" ]; then
        log_message "ERROR" "Production environment file not found: $BACKEND_DIR/.env.production"
        return 1
    fi
    
    # Source production environment
    source "$BACKEND_DIR/.env.production"
    
    # Validate critical production variables
    local required_vars=("DB_HOST" "DB_USERNAME" "DB_PASSWORD" "DB_NAME" "JWT_SECRET")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_message "ERROR" "Missing required production variables: ${missing_vars[*]}"
        return 1
    fi
    
    # Validate JWT secret strength
    if [ ${#JWT_SECRET} -lt 32 ]; then
        log_message "ERROR" "JWT_SECRET must be at least 32 characters for production"
        return 1
    fi
    
    # Check SSL certificates if SSL is enabled
    if [ "$ENABLE_SSL" = "true" ]; then
        local ssl_cert="${SSL_CERT_PATH:-/etc/ssl/certs/ehrx.crt}"
        local ssl_key="${SSL_KEY_PATH:-/etc/ssl/private/ehrx.key}"
        
        if [ ! -f "$ssl_cert" ] || [ ! -f "$ssl_key" ]; then
            log_message "WARN" "SSL certificates not found, disabling SSL"
            export ENABLE_SSL=false
        else
            log_message "INFO" "SSL certificates validated"
        fi
    fi
    
    log_message "INFO" "Production environment validation completed"
    return 0
}

# Function to build frontend for production
build_frontend_production() {
    log_message "INFO" "Building frontend for production"
    
    cd "$FRONTEND_DIR"
    
    # Set production environment variables
    export REACT_APP_API_URL="${REACT_APP_API_URL:-https://localhost:$BACKEND_PORT}"
    export REACT_APP_ENV="production"
    export GENERATE_SOURCEMAP=false
    
    # Clean previous build
    if [ -d "build" ]; then
        log_message "INFO" "Cleaning previous build"
        rm -rf build
    fi
    
    # Build frontend
    log_message "INFO" "Running production build (npm run build)"
    if ! npm run build; then
        log_message "ERROR" "Frontend build failed"
        return 1
    fi
    
    # Verify build output
    if [ ! -d "build" ] || [ ! -f "build/index.html" ]; then
        log_message "ERROR" "Frontend build output not found"
        return 1
    fi
    
    log_message "INFO" "Frontend build completed successfully"
    return 0
}

# Function to start backend in production mode
start_backend_production() {
    log_message "INFO" "Starting backend in production mode"
    
    cd "$BACKEND_DIR"
    
    # Source production environment
    source ".env.production"
    
    if [ "$USE_PM2" = "true" ] && command -v pm2 &> /dev/null; then
        log_message "INFO" "Starting backend with PM2"
        
        # Stop existing PM2 processes
        pm2 delete ehrx-backend 2>/dev/null || true
        
        # Start with PM2
        pm2 start npm --name "ehrx-backend" -- run start:prod
        
        # Get PM2 process ID
        local pm2_pid=$(pm2 jlist | jq -r '.[] | select(.name=="ehrx-backend") | .pid')
        save_pid "backend" "$pm2_pid"
        
        log_message "INFO" "Backend started with PM2 (PID: $pm2_pid)"
    else
        log_message "INFO" "Starting backend with npm (production mode)"
        
        # Start backend in production mode
        npm run start:prod &
        
        local backend_pid=$!
        save_pid "backend" "$backend_pid"
        
        log_message "INFO" "Backend started with PID: $backend_pid"
    fi
    
    return 0
}

# Function to start frontend production server
start_frontend_production() {
    log_message "INFO" "Starting frontend production server"
    
    # Check if build exists
    if [ ! -d "$BUILD_DIR" ]; then
        log_message "ERROR" "Frontend build not found. Run build first."
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    
    if [ "$USE_PM2" = "true" ] && command -v pm2 &> /dev/null; then
        log_message "INFO" "Starting frontend with PM2"
        
        # Stop existing PM2 processes
        pm2 delete ehrx-frontend 2>/dev/null || true
        
        # Start with PM2 using ecosystem config
        if [ -f "ecosystem.config.js" ]; then
            pm2 start ecosystem.config.js --only ehrx-frontend
        else
            # Fallback to direct PM2 start
            pm2 start "npx serve -s $BUILD_DIR -l $FRONTEND_PORT" --name "ehrx-frontend"
        fi
        
        # Get PM2 process ID
        local pm2_pid=$(pm2 jlist | jq -r '.[] | select(.name=="ehrx-frontend") | .pid')
        save_pid "frontend" "$pm2_pid"
        
        log_message "INFO" "Frontend started with PM2 (PID: $pm2_pid)"
    else
        log_message "INFO" "Starting frontend with serve"
        
        # Install serve if not available
        if ! command -v serve &> /dev/null; then
            log_message "INFO" "Installing serve globally"
            npm install -g serve
        fi
        
        # Start frontend server
        serve -s "$BUILD_DIR" -l "$FRONTEND_PORT" &
        
        local frontend_pid=$!
        save_pid "frontend" "$frontend_pid"
        
        log_message "INFO" "Frontend started with PID: $frontend_pid"
    fi
    
    return 0
}

# Function to setup production monitoring
setup_monitoring() {
    if [ "$ENABLE_MONITORING" = "true" ]; then
        log_message "INFO" "Setting up production monitoring"
        
        # Start health monitoring script
        if [ -f "$SCRIPT_DIR/health-monitor.sh" ]; then
            "$SCRIPT_DIR/health-monitor.sh" &
            local monitor_pid=$!
            save_pid "monitor" "$monitor_pid"
            log_message "INFO" "Health monitoring started (PID: $monitor_pid)"
        fi
        
        # Setup log rotation if not already configured
        if [ ! -f "/etc/logrotate.d/ehrx" ]; then
            log_message "INFO" "Setting up log rotation"
            sudo tee /etc/logrotate.d/ehrx > /dev/null <<EOF
/var/log/ehrx/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        # Reload services if needed
    endscript
}
EOF
        fi
    fi
}

# Function to wait for service with health check
wait_for_service_production() {
    local port=$1
    local service_name=$2
    local max_attempts=60
    local attempt=0
    
    log_message "INFO" "Waiting for $service_name to be ready on port $port"
    
    while [ $attempt -lt $max_attempts ]; do
        # Try multiple health check endpoints
        if curl -s -f "http://localhost:$port/health" >/dev/null 2>&1 || \
           curl -s -f "http://localhost:$port" >/dev/null 2>&1 || \
           curl -s -f "http://localhost:$port/api" >/dev/null 2>&1; then
            log_message "INFO" "$service_name is ready on port $port"
            return 0
        fi
        
        ((attempt++))
        log_message "DEBUG" "Waiting for $service_name... ($attempt/$max_attempts)"
        sleep 3
    done
    
    log_message "ERROR" "$service_name failed to start within expected time"
    return 1
}

# Function to show production URLs
show_production_urls() {
    echo ""
    log_message "INFO" "Production Environment Ready!"
    echo -e "${CYAN}=================================${NC}"
    
    if [ "$ENABLE_SSL" = "true" ]; then
        echo -e "${GREEN}🌐 Frontend (HTTPS): ${CYAN}https://localhost:$FRONTEND_PORT${NC}"
        echo -e "${GREEN}🔧 Backend API (HTTPS): ${CYAN}https://localhost:$BACKEND_PORT${NC}"
        echo -e "${GREEN}📚 API Documentation: ${CYAN}https://localhost:$BACKEND_PORT/api${NC}"
        echo -e "${GREEN}🔍 Health Check: ${CYAN}https://localhost:$BACKEND_PORT/health${NC}"
    else
        echo -e "${GREEN}🌐 Frontend: ${CYAN}http://localhost:$FRONTEND_PORT${NC}"
        echo -e "${GREEN}🔧 Backend API: ${CYAN}http://localhost:$BACKEND_PORT${NC}"
        echo -e "${GREEN}📚 API Documentation: ${CYAN}http://localhost:$BACKEND_PORT/api${NC}"
        echo -e "${GREEN}🔍 Health Check: ${CYAN}http://localhost:$BACKEND_PORT/health${NC}"
    fi
    
    echo -e "${CYAN}=================================${NC}"
    echo ""
    log_message "INFO" "Production features enabled:"
    echo -e "${YELLOW}  • Optimized builds and minification${NC}"
    echo -e "${YELLOW}  • Security headers and HTTPS${NC}"
    echo -e "${YELLOW}  • Performance monitoring${NC}"
    echo -e "${YELLOW}  • Automated log rotation${NC}"
    echo -e "${YELLOW}  • Health monitoring and auto-restart${NC}"
    echo ""
    log_message "INFO" "To stop services, run: ./scripts/stop-prod.sh"
}

# Main execution
main() {
    log_message "INFO" "=== EHRX Production Startup ==="
    
    # Step 1: Validate production environment
    log_message "INFO" "Step 1: Validating production environment"
    if ! validate_production_environment; then
        log_message "ERROR" "Production environment validation failed"
        exit 1
    fi
    
    # Step 2: Run strict dependency checks
    log_message "INFO" "Step 2: Running strict dependency verification"
    if ! run_all_checks; then
        log_message "ERROR" "Dependency verification failed for production"
        exit 1
    fi
    
    # Step 3: Build frontend
    if [ "$BUILD_FRONTEND" = "true" ]; then
        log_message "INFO" "Step 3: Building frontend for production"
        if ! build_frontend_production; then
            log_message "ERROR" "Frontend build failed"
            exit 1
        fi
    fi
    
    # Step 4: Start backend
    log_message "INFO" "Step 4: Starting backend service"
    if ! start_backend_production; then
        log_message "ERROR" "Failed to start backend"
        exit 1
    fi
    
    # Step 5: Wait for backend to be ready
    log_message "INFO" "Step 5: Waiting for backend to be ready"
    if ! wait_for_service_production "$BACKEND_PORT" "backend"; then
        log_message "ERROR" "Backend failed to start properly"
        stop_service "backend"
        exit 1
    fi
    
    # Step 6: Start frontend
    log_message "INFO" "Step 6: Starting frontend service"
    if ! start_frontend_production; then
        log_message "ERROR" "Failed to start frontend"
        stop_service "backend"
        exit 1
    fi
    
    # Step 7: Wait for frontend to be ready
    log_message "INFO" "Step 7: Waiting for frontend to be ready"
    if ! wait_for_service_production "$FRONTEND_PORT" "frontend"; then
        log_message "ERROR" "Frontend failed to start properly"
        stop_service "backend"
        stop_service "frontend"
        exit 1
    fi
    
    # Step 8: Setup monitoring
    log_message "INFO" "Step 8: Setting up production monitoring"
    setup_monitoring
    
    # Step 9: Show success message and URLs
    show_production_urls
    
    log_message "INFO" "Production environment started successfully"
    return 0
}

# Trap to cleanup on exit
trap 'log_message "INFO" "Received interrupt signal, cleaning up..."; cleanup_all_pid_files; exit 130' INT TERM

# Run main function
main "$@"
