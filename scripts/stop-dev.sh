#!/bin/bash

# EHRX Development Environment Stop Script
# Graceful shutdown for development services

set -e

# Source required modules
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/pid-manager.sh"

# Development Configuration
export NODE_ENV="development"
export BACKEND_PORT="${BACKEND_PORT:-4000}"
export FRONTEND_PORT="${FRONTEND_PORT:-3080}"

# Initialize logging and PID management
init_pid_management

log_message "INFO" "Stopping EHRX Development Environment"

# Function to stop development services
stop_development_services() {
    log_message "INFO" "Stopping development services"
    
    # Stop services using PID management
    local services_stopped=0
    
    # Stop frontend
    if stop_service "frontend"; then
        ((services_stopped++))
    fi
    
    # Stop backend
    if stop_service "backend"; then
        ((services_stopped++))
    fi
    
    # Also kill any processes on the ports (fallback)
    log_message "INFO" "Cleaning up any remaining processes on ports"
    
    # Kill processes on frontend port
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "INFO" "Killing remaining processes on port $FRONTEND_PORT"
        lsof -ti :$FRONTEND_PORT | xargs kill -TERM 2>/dev/null || true
        sleep 2
        lsof -ti :$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    fi
    
    # Kill processes on backend port
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "INFO" "Killing remaining processes on port $BACKEND_PORT"
        lsof -ti :$BACKEND_PORT | xargs kill -TERM 2>/dev/null || true
        sleep 2
        lsof -ti :$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    fi
    
    # Kill any node processes related to the project
    log_message "INFO" "Cleaning up project-related Node.js processes"
    pkill -f "ehrx" 2>/dev/null || true
    pkill -f "nest start" 2>/dev/null || true
    pkill -f "react-scripts start" 2>/dev/null || true
    
    log_message "INFO" "Stopped $services_stopped services"
    return 0
}

# Function to verify services are stopped
verify_services_stopped() {
    log_message "INFO" "Verifying services are stopped"
    
    local all_stopped=true
    
    # Check frontend port
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "WARN" "Port $FRONTEND_PORT is still in use"
        all_stopped=false
    else
        log_message "INFO" "Frontend port $FRONTEND_PORT is free"
    fi
    
    # Check backend port
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "WARN" "Port $BACKEND_PORT is still in use"
        all_stopped=false
    else
        log_message "INFO" "Backend port $BACKEND_PORT is free"
    fi
    
    if [ "$all_stopped" = "true" ]; then
        log_message "INFO" "All services stopped successfully"
        return 0
    else
        log_message "WARN" "Some services may still be running"
        return 1
    fi
}

# Main execution
main() {
    log_message "INFO" "=== EHRX Development Shutdown ==="
    
    # Stop services
    stop_development_services
    
    # Clean up PID files
    cleanup_all_pid_files
    
    # Verify everything is stopped
    verify_services_stopped
    
    echo ""
    log_message "INFO" "Development environment stopped"
    echo -e "${GREEN}✅ EHRX Development Environment Stopped${NC}"
    echo ""
    
    return 0
}

# Run main function
main "$@"
