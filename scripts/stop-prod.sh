#!/bin/bash

# EHRX Production Environment Stop Script
# Graceful shutdown for production services with monitoring cleanup

set -e

# Source required modules
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/pid-manager.sh"

# Production Configuration
export NODE_ENV="production"
export BACKEND_PORT="${BACKEND_PORT:-4000}"
export FRONTEND_PORT="${FRONTEND_PORT:-3080}"
USE_PM2="${USE_PM2:-true}"

# Initialize logging and PID management
init_pid_management

log_message "INFO" "Stopping EHRX Production Environment"

# Function to stop PM2 services
stop_pm2_services() {
    if [ "$USE_PM2" = "true" ] && command -v pm2 &> /dev/null; then
        log_message "INFO" "Stopping PM2 services"
        
        # Stop specific PM2 processes
        pm2 stop ehrx-backend 2>/dev/null || log_message "WARN" "ehrx-backend PM2 process not found"
        pm2 stop ehrx-frontend 2>/dev/null || log_message "WARN" "ehrx-frontend PM2 process not found"
        
        # Delete PM2 processes
        pm2 delete ehrx-backend 2>/dev/null || true
        pm2 delete ehrx-frontend 2>/dev/null || true
        
        log_message "INFO" "PM2 services stopped"
        return 0
    else
        log_message "INFO" "PM2 not in use, skipping PM2 cleanup"
        return 0
    fi
}

# Function to stop production services
stop_production_services() {
    log_message "INFO" "Stopping production services"
    
    # First try PM2 if enabled
    stop_pm2_services
    
    # Stop services using PID management
    local services_stopped=0
    
    # Stop monitor
    if stop_service "monitor"; then
        ((services_stopped++))
    fi
    
    # Stop frontend
    if stop_service "frontend"; then
        ((services_stopped++))
    fi
    
    # Stop backend
    if stop_service "backend"; then
        ((services_stopped++))
    fi
    
    # Also kill any processes on the ports (fallback)
    log_message "INFO" "Cleaning up any remaining processes on ports"
    
    # Kill processes on frontend port
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "INFO" "Killing remaining processes on port $FRONTEND_PORT"
        lsof -ti :$FRONTEND_PORT | xargs kill -TERM 2>/dev/null || true
        sleep 3
        lsof -ti :$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    fi
    
    # Kill processes on backend port
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "INFO" "Killing remaining processes on port $BACKEND_PORT"
        lsof -ti :$BACKEND_PORT | xargs kill -TERM 2>/dev/null || true
        sleep 3
        lsof -ti :$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    fi
    
    # Kill any production-related processes
    log_message "INFO" "Cleaning up production-related processes"
    pkill -f "ehrx" 2>/dev/null || true
    pkill -f "nest start" 2>/dev/null || true
    pkill -f "serve.*build" 2>/dev/null || true
    
    log_message "INFO" "Stopped $services_stopped services"
    return 0
}

# Function to cleanup production resources
cleanup_production_resources() {
    log_message "INFO" "Cleaning up production resources"
    
    # Stop any running health monitors
    pkill -f "health-monitor" 2>/dev/null || true
    
    # Clean up temporary files
    local temp_files=(
        "/tmp/ehrx-*"
        "/var/run/ehrx/*.tmp"
    )
    
    for pattern in "${temp_files[@]}"; do
        rm -f $pattern 2>/dev/null || true
    done
    
    # Rotate logs if needed
    if command -v logrotate &> /dev/null && [ -f "/etc/logrotate.d/ehrx" ]; then
        log_message "INFO" "Rotating logs"
        sudo logrotate -f /etc/logrotate.d/ehrx 2>/dev/null || true
    fi
    
    log_message "INFO" "Production resource cleanup completed"
}

# Function to verify services are stopped
verify_services_stopped() {
    log_message "INFO" "Verifying services are stopped"
    
    local all_stopped=true
    
    # Check frontend port
    if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "WARN" "Port $FRONTEND_PORT is still in use"
        all_stopped=false
    else
        log_message "INFO" "Frontend port $FRONTEND_PORT is free"
    fi
    
    # Check backend port
    if lsof -Pi :$BACKEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_message "WARN" "Port $BACKEND_PORT is still in use"
        all_stopped=false
    else
        log_message "INFO" "Backend port $BACKEND_PORT is free"
    fi
    
    # Check PM2 processes
    if [ "$USE_PM2" = "true" ] && command -v pm2 &> /dev/null; then
        local pm2_processes=$(pm2 jlist | jq -r '.[] | select(.name | test("ehrx")) | .name' 2>/dev/null || true)
        if [ -n "$pm2_processes" ]; then
            log_message "WARN" "PM2 processes still running: $pm2_processes"
            all_stopped=false
        else
            log_message "INFO" "No EHRX PM2 processes running"
        fi
    fi
    
    if [ "$all_stopped" = "true" ]; then
        log_message "INFO" "All services stopped successfully"
        return 0
    else
        log_message "WARN" "Some services may still be running"
        return 1
    fi
}

# Function to show shutdown summary
show_shutdown_summary() {
    echo ""
    log_message "INFO" "Production Environment Shutdown Complete"
    echo -e "${CYAN}=================================${NC}"
    echo -e "${GREEN}✅ Backend service stopped${NC}"
    echo -e "${GREEN}✅ Frontend service stopped${NC}"
    echo -e "${GREEN}✅ Monitoring services stopped${NC}"
    echo -e "${GREEN}✅ PM2 processes cleaned up${NC}"
    echo -e "${GREEN}✅ Ports freed${NC}"
    echo -e "${GREEN}✅ Resources cleaned up${NC}"
    echo -e "${CYAN}=================================${NC}"
    echo ""
    log_message "INFO" "Production environment is now offline"
}

# Main execution
main() {
    log_message "INFO" "=== EHRX Production Shutdown ==="
    
    # Stop services
    stop_production_services
    
    # Clean up production resources
    cleanup_production_resources
    
    # Clean up PID files
    cleanup_all_pid_files
    
    # Verify everything is stopped
    verify_services_stopped
    
    # Show summary
    show_shutdown_summary
    
    return 0
}

# Run main function
main "$@"
