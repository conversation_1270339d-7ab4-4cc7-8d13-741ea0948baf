#!/bin/bash

# 🔐 eHRx Server Completeness Verification Script
# This script verifies that all necessary files and configurations are present

echo "🔍 eHRx Server Completeness Verification"
echo "========================================"
echo "Server: $(hostname)"
echo "Date: $(date)"
echo "User: $(whoami)"
echo "Working Directory: $(pwd)"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to check and report
check_item() {
    local description="$1"
    local command="$2"
    local is_critical="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}: $description"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        if [ "$is_critical" = "critical" ]; then
            echo -e "${RED}❌ FAIL (CRITICAL)${NC}: $description"
        else
            echo -e "${YELLOW}⚠️  WARN${NC}: $description"
        fi
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

echo "📁 DIRECTORY STRUCTURE VERIFICATION"
echo "-----------------------------------"

# Core directories
check_item "Project root directory exists" "[ -d '/var/www/ehrx' ]" "critical"
check_item "Backend directory exists" "[ -d '/var/www/ehrx/backend' ]" "critical"
check_item "Frontend directory exists" "[ -d '/var/www/ehrx/frontend' ]" "critical"
check_item "Scripts directory exists" "[ -d '/var/www/ehrx/scripts' ]" "critical"

# Log directories
check_item "Log directory exists" "[ -d '/var/log/ehrx' ]" "critical"
check_item "Log directory is writable" "[ -w '/var/log/ehrx' ]" "critical"

# PID directories
check_item "PID directory exists" "[ -d '/var/run/ehrx' ]" "critical"
check_item "PID directory is writable" "[ -w '/var/run/ehrx' ]" "critical"

echo ""
echo "📄 CONFIGURATION FILES VERIFICATION"
echo "-----------------------------------"

# Environment files
check_item "Backend .env file exists" "[ -f '/var/www/ehrx/backend/.env' ]" "critical"
check_item "Frontend .env file exists" "[ -f '/var/www/ehrx/frontend/.env' ]" "critical"

# Package files
check_item "Backend package.json exists" "[ -f '/var/www/ehrx/backend/package.json' ]" "critical"
check_item "Frontend package.json exists" "[ -f '/var/www/ehrx/frontend/package.json' ]" "critical"
check_item "Backend node_modules exists" "[ -d '/var/www/ehrx/backend/node_modules' ]" "critical"
check_item "Frontend node_modules exists" "[ -d '/var/www/ehrx/frontend/node_modules' ]" "critical"

echo ""
echo "🔧 SCRIPTS VERIFICATION"
echo "-----------------------"

# Core scripts
check_item "start-dev.sh exists" "[ -f '/var/www/ehrx/scripts/start-dev.sh' ]" "critical"
check_item "pid-manager.sh exists" "[ -f '/var/www/ehrx/scripts/pid-manager.sh' ]" "critical"
check_item "dependency-checker.sh exists" "[ -f '/var/www/ehrx/scripts/dependency-checker.sh' ]" "critical"

# Script permissions
check_item "start-dev.sh is executable" "[ -x '/var/www/ehrx/scripts/start-dev.sh' ]" "critical"
check_item "pid-manager.sh is executable" "[ -x '/var/www/ehrx/scripts/pid-manager.sh' ]" "critical"
check_item "dependency-checker.sh is executable" "[ -x '/var/www/ehrx/scripts/dependency-checker.sh' ]" "critical"

echo ""
echo "🗄️  DATABASE VERIFICATION"
echo "-------------------------"

# PostgreSQL
check_item "PostgreSQL is installed" "command -v psql" "critical"
check_item "PostgreSQL service is running" "systemctl is-active postgresql" "critical"

echo ""
echo "📦 DEPENDENCIES VERIFICATION"
echo "----------------------------"

# Node.js and npm
check_item "Node.js is installed" "command -v node" "critical"
check_item "npm is installed" "command -v npm" "critical"

# Git
check_item "Git is installed" "command -v git" "critical"

echo ""
echo "🔐 SECURITY & PERMISSIONS VERIFICATION"
echo "--------------------------------------"

# File ownership
check_item "Project owned by current user" "[ -O '/var/www/ehrx' ]" "warning"
check_item "Backend owned by current user" "[ -O '/var/www/ehrx/backend' ]" "warning"
check_item "Frontend owned by current user" "[ -O '/var/www/ehrx/frontend' ]" "warning"

echo ""
echo "🌐 NETWORK & SERVICES VERIFICATION"
echo "----------------------------------"

# Port availability
check_item "Port 3080 is available or in use" "netstat -tlnp | grep -q ':3080 ' || ! netstat -tlnp | grep -q ':3080 '" "warning"
check_item "Port 4000 is available or in use" "netstat -tlnp | grep -q ':4000 ' || ! netstat -tlnp | grep -q ':4000 '" "warning"

echo ""
echo "📊 SUMMARY"
echo "=========="
echo -e "Total Checks: ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL CHECKS PASSED! Server appears to be complete.${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️  Some checks failed. Review the issues above.${NC}"
    exit 1
fi
