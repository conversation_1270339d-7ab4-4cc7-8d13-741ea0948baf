#!/bin/bash

echo "🔐 Setting up Let's Encrypt SSL Certificate for dev.trusthansen.dk"
echo "This will replace the self-signed certificate with a trusted one"
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run with sudo privileges"
    echo "   Usage: sudo ./setup-letsencrypt.sh"
    exit 1
fi

# Install certbot if not present
if ! command -v certbot &> /dev/null; then
    echo "📦 Installing Certbot..."
    apt update
    apt install -y certbot python3-certbot-nginx
else
    echo "✅ Certbot is already installed"
fi

# Stop nginx temporarily for standalone verification
echo "🔄 Stopping Nginx temporarily for certificate generation..."
systemctl stop nginx

# Generate Let's Encrypt certificate
echo "🔐 Generating Let's Encrypt certificate for dev.trusthansen.dk..."
echo "⚠️  Make sure your domain DNS points to this server's IP address!"
echo ""

certbot certonly --standalone \
    --non-interactive \
    --agree-tos \
    --email <EMAIL> \
    --domains dev.trusthansen.dk

if [ $? -eq 0 ]; then
    echo "✅ SSL certificate generated successfully!"
    
    # Update nginx configuration to use Let's Encrypt certificate
    echo "📝 Updating Nginx configuration to use Let's Encrypt certificate..."
    
    sed -i 's|ssl_certificate /var/www/ehrx/server.crt;|ssl_certificate /etc/letsencrypt/live/dev.trusthansen.dk/fullchain.pem;|' /etc/nginx/sites-available/ehrx
    sed -i 's|ssl_certificate_key /var/www/ehrx/server.key;|ssl_certificate_key /etc/letsencrypt/live/dev.trusthansen.dk/privkey.pem;|' /etc/nginx/sites-available/ehrx
    
    # Test nginx configuration
    if nginx -t; then
        echo "✅ Nginx configuration updated successfully"
        
        # Start nginx
        systemctl start nginx
        
        # Set up auto-renewal
        echo "🔄 Setting up automatic certificate renewal..."
        (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet --nginx") | crontab -
        
        echo ""
        echo "🎉 Let's Encrypt SSL setup complete!"
        echo "🔗 Your application is now available with trusted HTTPS at:"
        echo "   https://dev.trusthansen.dk/"
        echo ""
        echo "✅ No more security warnings in browsers!"
        echo "🔄 Certificate will auto-renew every 90 days"
        
    else
        echo "❌ Nginx configuration test failed"
        # Restore original configuration
        sed -i 's|ssl_certificate /etc/letsencrypt/live/dev.trusthansen.dk/fullchain.pem;|ssl_certificate /var/www/ehrx/server.crt;|' /etc/nginx/sites-available/ehrx
        sed -i 's|ssl_certificate_key /etc/letsencrypt/live/dev.trusthansen.dk/privkey.pem;|ssl_certificate_key /var/www/ehrx/server.key;|' /etc/nginx/sites-available/ehrx
        systemctl start nginx
        echo "🔄 Restored original configuration"
    fi
    
else
    echo "❌ Failed to generate Let's Encrypt certificate"
    echo "   This usually happens if:"
    echo "   - DNS is not pointing to this server"
    echo "   - Port 80 is blocked by firewall"
    echo "   - Domain is not accessible from the internet"
    echo ""
    echo "🔄 Starting Nginx with self-signed certificate..."
    systemctl start nginx
    echo "   You can still use https://dev.trusthansen.dk/ with the security warning"
fi
