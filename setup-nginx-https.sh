#!/bin/bash

echo "🔧 Setting up Nginx with HTT<PERSON> for eHRx"
echo "This will install and configure Nginx as a reverse proxy"
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run with sudo privileges"
    echo "   Usage: sudo ./setup-nginx-https.sh"
    exit 1
fi

# Install nginx if not present
if ! command -v nginx &> /dev/null; then
    echo "📦 Installing Nginx..."
    apt update
    apt install -y nginx
else
    echo "✅ Nginx is already installed"
fi

# Stop nginx if running
systemctl stop nginx 2>/dev/null || true

# Create nginx configuration
echo "📝 Creating Nginx configuration..."
cat > /etc/nginx/sites-available/ehrx << 'EOF'
server {
    listen 80;
    server_name dev.trusthansen.dk;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name dev.trusthansen.dk;

    # SSL Configuration
    ssl_certificate /var/www/ehrx/server.crt;
    ssl_certificate_key /var/www/ehrx/server.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Proxy API requests to backend
    location /api/ {
        proxy_pass http://localhost:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # Proxy frontend requests to React app
    location / {
        proxy_pass http://localhost:3080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # WebSocket support for React hot reload
    location /sockjs-node {
        proxy_pass http://localhost:3080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/ehrx /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
echo "🔍 Testing Nginx configuration..."
if nginx -t; then
    echo "✅ Nginx configuration is valid"
    
    # Start and enable nginx
    systemctl start nginx
    systemctl enable nginx
    
    echo ""
    echo "🎉 Nginx HTTPS setup complete!"
    echo "🔗 Your application is now available at:"
    echo "   https://dev.trusthansen.dk/"
    echo ""
    echo "📋 Services status:"
    echo "   - Nginx: Running on ports 80 and 443"
    echo "   - Backend: Should be running on port 4000"
    echo "   - Frontend: Should be running on port 3080"
    echo ""
    echo "⚠️  Note: You'll see a security warning due to self-signed certificate"
    echo "   Click 'Advanced' → 'Proceed to dev.trusthansen.dk' to continue"
    
else
    echo "❌ Nginx configuration test failed"
    exit 1
fi
