const express = require('express');
const path = require('path');
const app = express();
const PORT = 3080;

// Serve static files from the React build directory
app.use(express.static(path.join(__dirname, 'frontend/build')));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'EHRX Server is running' });
});

// Catch all handler: send back React's index.html file
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'frontend/build/index.html'));
});

app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 EHRX Server running on port ${PORT}`);
  console.log(`📱 Access the app at: http://localhost:${PORT}`);
});
