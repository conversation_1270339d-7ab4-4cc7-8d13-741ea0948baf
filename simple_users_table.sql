-- Create simple users table without complex ENUM types
DROP TABLE IF EXISTS users CASCADE;

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    title VA<PERSON>HAR(255),
    role VARCHAR(50) NOT NULL DEFAULT 'engineer',
    organizational_unit_id INTEGER,
    manager_id INTEGER,
    hire_date DATE,
    salary DECIMAL(10,2),
    employment_type VARCHAR(50) NOT NULL DEFAULT 'full_time',
    location VARCHAR(255),
    phone VARCHAR(255),
    emergency_contact_name <PERSON><PERSON><PERSON><PERSON>(255),
    emergency_contact_phone VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT true,
    account_status VARCHAR(50) NOT NULL DEFAULT 'active',
    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
    last_login_at TIMESTAMP,
    last_login_ip VARCHAR(255),
    password_changed_at TIMESTAMP,
    must_change_password BOOLEAN NOT NULL DEFAULT true,
    account_locked_until TIMESTAMP,
    two_factor_enabled BOOLEAN NOT NULL DEFAULT false,
    two_factor_secret VARCHAR(255),
    session_token VARCHAR(1000),
    session_expires_at TIMESTAMP,
    refresh_token VARCHAR(1000),
    refresh_token_expires_at TIMESTAMP,
    mfa_backup_codes TEXT,
    password_history TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Insert our working admin user
INSERT INTO users (
    email, password, first_name, last_name, role, is_active, 
    account_status, must_change_password, failed_login_attempts
) VALUES (
    '<EMAIL>',
    '$2b$10$8pfFWzQpA8BOkiY3Z151QuwG7uJOghwpx39hrpdKW8sqHeYD7VjHe',
    'Admin',
    'User',
    'hr_admin',
    true,
    'active',
    false,
    0
);
