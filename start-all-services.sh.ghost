#!/bin/bash

# EHRX Application - Complete Service Startup Script
# This script starts all required services for the EHRX application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="/var/www/ehrx"
FRONTEND_PORT=3080
BACKEND_PORT=4000

echo -e "${CYAN}🚀 EHRX Application - Starting All Services${NC}"
echo -e "${CYAN}================================================${NC}"

# Function to check if a port is in use
check_port() {
    local port=$1
    local service_name=$2

    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Port $port is already in use by $service_name${NC}"
        echo -e "${YELLOW}🔄 Automatically killing process on port $port...${NC}"

        # Kill existing process automatically
        lsof -ti:$port | xargs kill -TERM 2>/dev/null || true
        sleep 2

        # Force kill if still running
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo -e "${YELLOW}⚡ Force killing process on port $port...${NC}"
            lsof -ti:$port | xargs kill -9 2>/dev/null || true
            sleep 2
        fi

        # Verify port is free
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo -e "${RED}❌ Failed to free port $port for $service_name${NC}"
            return 1
        else
            echo -e "${GREEN}✅ Port $port freed for $service_name${NC}"
        fi
    else
        echo -e "${GREEN}✅ Port $port is available for $service_name${NC}"
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${BLUE}🔄 Waiting for $service_name to be ready on port $port...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$port >/dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready on port $port${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}⏳ Attempt $attempt/$max_attempts - $service_name not ready yet...${NC}"
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ $service_name failed to start within expected time${NC}"
    return 1
}

# Change to project root
cd "$PROJECT_ROOT" || {
    echo -e "${RED}❌ Failed to change to project root: $PROJECT_ROOT${NC}"
    exit 1
}

echo -e "${BLUE}📁 Working directory: $(pwd)${NC}"

# Step 1: Check and handle port conflicts
echo -e "\n${PURPLE}🔍 Step 1: Checking port availability${NC}"
check_port $BACKEND_PORT "Backend (NestJS)" || exit 1
check_port $FRONTEND_PORT "Frontend (React)" || exit 1

# Step 2: Start Backend Service
echo -e "\n${PURPLE}🔧 Step 2: Starting Backend Service (NestJS)${NC}"
cd "$PROJECT_ROOT/backend"

if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing backend dependencies...${NC}"
    npm install
fi

echo -e "${BLUE}🚀 Starting backend in development mode...${NC}"
npm run start:dev &
BACKEND_PID=$!

# Step 3: Start Frontend Service
echo -e "\n${PURPLE}🎨 Step 3: Starting Frontend Service (React)${NC}"
cd "$PROJECT_ROOT/frontend"

if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
    npm install
fi

# Set environment variables for frontend
export PORT=$FRONTEND_PORT
export HOST=0.0.0.0
export BROWSER=none
export CI=false

echo -e "${BLUE}🚀 Starting frontend development server...${NC}"
echo -e "${BLUE}📝 Frontend will be available at: http://localhost:$FRONTEND_PORT${NC}"

# Start with explicit environment variables
PORT=$FRONTEND_PORT HOST=0.0.0.0 BROWSER=none npm start &
FRONTEND_PID=$!

echo -e "${GREEN}✅ Frontend started with PID: $FRONTEND_PID${NC}"

# Step 4: Wait for services to be ready
echo -e "\n${PURPLE}⏳ Step 4: Waiting for services to initialize${NC}"

# Wait for backend
wait_for_service $BACKEND_PORT "Backend API" || {
    echo -e "${RED}❌ Backend failed to start${NC}"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    exit 1
}

# Wait for frontend
wait_for_service $FRONTEND_PORT "Frontend App" || {
    echo -e "${RED}❌ Frontend failed to start${NC}"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    exit 1
}

# Step 5: Display status and URLs
echo -e "\n${GREEN}🎉 All services started successfully!${NC}"
echo -e "${GREEN}=====================================${NC}"
echo -e "${CYAN}📊 Service Status:${NC}"
echo -e "  ${GREEN}✅ Backend API:${NC}     http://localhost:$BACKEND_PORT"
echo -e "  ${GREEN}✅ Frontend App:${NC}    http://localhost:$FRONTEND_PORT"
echo -e "  ${GREEN}✅ Public URL:${NC}      https://dev.trusthansen.dk/"
echo -e "  ${GREEN}✅ API Docs:${NC}        http://localhost:$BACKEND_PORT/api/docs"

echo -e "\n${CYAN}🔧 Process IDs:${NC}"
echo -e "  Backend PID: $BACKEND_PID"
echo -e "  Frontend PID: $FRONTEND_PID"

echo -e "\n${YELLOW}💡 Useful Commands:${NC}"
echo -e "  Stop all services:    ${CYAN}./stop-all-services.sh${NC}"
echo -e "  View backend logs:    ${CYAN}tail -f backend/logs/*.log${NC}"
echo -e "  Kill by port:         ${CYAN}lsof -ti:$BACKEND_PORT | xargs kill -9${NC}"

echo -e "\n${GREEN}🚀 EHRX Application is now running!${NC}"
echo -e "${BLUE}Press Ctrl+C to stop all services${NC}"

# Keep script running and handle Ctrl+C
trap 'echo -e "\n${YELLOW}🛑 Stopping all services...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true; exit 0' INT

# Wait for processes
wait
