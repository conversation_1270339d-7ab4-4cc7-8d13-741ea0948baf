#!/bin/bash

# EHRX Full Application Starter Script
# This script starts both the NestJS backend and React frontend servers

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting EHRX Application...${NC}"

# Kill existing node processes
echo -e "Stopping any existing processes..."
pkill -f "node" || true

# Check if MariaDB is running
echo -e "Checking if MariaDB is running..."
if which systemctl > /dev/null; then
  if ! systemctl is-active --quiet mariadb; then
    echo -e "${YELLOW}Starting MariaDB service...${NC}"
    sudo systemctl start mariadb
    sleep 2
    if ! systemctl is-active --quiet mariadb; then
      echo -e "${YELLOW}Warning: MariaDB service didn't start. The application may not function correctly.${NC}"
    else
      echo -e "${GREEN}MariaDB service started successfully.${NC}"
    fi
  else
    echo -e "${GREEN}MariaDB is already running.${NC}"
  fi
else
  echo -e "${YELLOW}Note: systemctl not available. Skipping MariaDB service check.${NC}"
  # On systems without systemctl, we might need alternative approaches
  # Could add additional checks using `mysqladmin ping` etc.
fi

# Start the application using Node.js launcher
echo -e "${GREEN}Starting EHRX application using Node.js launcher...${NC}"
node /var/www/ehrx/launch.js

echo -e "${GREEN}EHRX Application is starting!${NC}"
echo -e "${YELLOW}Your application will be available at: http://localhost:3000${NC}"
echo -e "${YELLOW}Status page available at: http://localhost:3030${NC}"

echo -e "${GREEN}EHRX Application is starting!${NC}"
echo -e "Backend API available at: http://localhost:3001/api"
echo -e "Frontend available at: http://localhost:3000"
echo -e "\nPress F5 or use browser preview to access the application"
echo -e "\nLogs available at:"
echo -e "  - Backend: /var/www/ehrx/backend-logs.txt"
echo -e "  - Frontend: /var/www/ehrx/frontend-logs.txt"
