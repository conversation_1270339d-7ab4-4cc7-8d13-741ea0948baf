#!/bin/bash

# 🚀 eHRx Unified Startup Script
# This script replaces all other startup scripts to eliminate port conflicts
# and provide a consistent, reliable server startup process.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="/var/www/ehrx"
LOG_DIR="/var/log/ehrx"

# 🔧 FIXED: Consistent port configuration
BACKEND_PORT="${BACKEND_PORT:-4000}"
FRONTEND_PORT="${FRONTEND_PORT:-3080}"
PROXY_HTTP_PORT="${PROXY_HTTP_PORT:-8080}"
PROXY_HTTPS_PORT="${PROXY_HTTPS_PORT:-8443}"

# Environment detection
NODE_ENV="${NODE_ENV:-development}"

echo -e "${BLUE}🚀 eHRx Unified Startup Script${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "Environment: ${CYAN}${NODE_ENV}${NC}"
echo -e "Project Root: ${CYAN}${PROJECT_ROOT}${NC}"
echo -e "Backend Port: ${CYAN}${BACKEND_PORT}${NC}"
echo -e "Frontend Port: ${CYAN}${FRONTEND_PORT}${NC}"
echo -e "Proxy HTTP Port: ${CYAN}${PROXY_HTTP_PORT}${NC}"
echo -e "Proxy HTTPS Port: ${CYAN}${PROXY_HTTPS_PORT}${NC}"
echo ""

# Function to check if a port is in use
check_port() {
    local port=$1
    local service_name=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Port $port is already in use (${service_name})${NC}"
        echo -e "${YELLOW}   Attempting to free port...${NC}"
        
        # Kill processes using the port
        local pids=$(lsof -Pi :$port -sTCP:LISTEN -t 2>/dev/null || true)
        if [ ! -z "$pids" ]; then
            echo "$pids" | xargs kill -9 2>/dev/null || true
            sleep 2
            
            # Check if port is now free
            if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
                echo -e "${RED}❌ Failed to free port $port${NC}"
                return 1
            else
                echo -e "${GREEN}✅ Port $port freed successfully${NC}"
            fi
        fi
    else
        echo -e "${GREEN}✅ Port $port is available (${service_name})${NC}"
    fi
    return 0
}

# Function to wait for service to be ready
wait_for_service() {
    local port=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}⏳ Waiting for ${service_name} to be ready on port ${port}...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:$port/health >/dev/null 2>&1 || \
           curl -s http://localhost:$port >/dev/null 2>&1; then
            echo -e "${GREEN}✅ ${service_name} is ready on port ${port}${NC}"
            return 0
        fi
        
        echo -e "${CYAN}   Attempt ${attempt}/${max_attempts}...${NC}"
        sleep 2
        ((attempt++))
    done
    
    echo -e "${RED}❌ ${service_name} failed to start on port ${port}${NC}"
    return 1
}

# Function to start backend
start_backend() {
    echo -e "${PURPLE}🔧 Starting Backend Service (NestJS)${NC}"
    cd "$PROJECT_ROOT/backend"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 Installing backend dependencies...${NC}"
        npm install
    fi
    
    # Set environment variables
    export PORT=$BACKEND_PORT
    export NODE_ENV=$NODE_ENV
    
    # Start backend in background
    if [ "$NODE_ENV" = "production" ]; then
        npm run start:prod > "$LOG_DIR/backend.log" 2>&1 &
    else
        npm run start:dev > "$LOG_DIR/backend.log" 2>&1 &
    fi
    
    local backend_pid=$!
    echo $backend_pid > "$LOG_DIR/backend.pid"
    echo -e "${GREEN}✅ Backend started with PID: ${backend_pid}${NC}"
    
    # Wait for backend to be ready
    if ! wait_for_service $BACKEND_PORT "Backend"; then
        echo -e "${RED}❌ Backend failed to start${NC}"
        return 1
    fi
}

# Function to start frontend
start_frontend() {
    echo -e "${PURPLE}🎨 Starting Frontend Service (React)${NC}"
    cd "$PROJECT_ROOT/frontend"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
        npm install
    fi
    
    # Set environment variables
    export PORT=$FRONTEND_PORT
    export REACT_APP_API_URL="http://localhost:$BACKEND_PORT/api"
    
    # Start frontend in background
    npm start > "$LOG_DIR/frontend.log" 2>&1 &
    
    local frontend_pid=$!
    echo $frontend_pid > "$LOG_DIR/frontend.pid"
    echo -e "${GREEN}✅ Frontend started with PID: ${frontend_pid}${NC}"
    
    # Wait for frontend to be ready
    if ! wait_for_service $FRONTEND_PORT "Frontend"; then
        echo -e "${RED}❌ Frontend failed to start${NC}"
        return 1
    fi
}

# Function to start proxy
start_proxy() {
    echo -e "${PURPLE}🌐 Starting Proxy Server${NC}"
    cd "$PROJECT_ROOT"
    
    # Set environment variables for proxy
    export HTTP_PORT=$PROXY_HTTP_PORT
    export HTTPS_PORT=$PROXY_HTTPS_PORT
    export BACKEND_PORT=$BACKEND_PORT
    export FRONTEND_PORT=$FRONTEND_PORT
    export NODE_ENV=$NODE_ENV
    
    # Start proxy in background
    node demo-server.js > "$LOG_DIR/proxy.log" 2>&1 &
    
    local proxy_pid=$!
    echo $proxy_pid > "$LOG_DIR/proxy.pid"
    echo -e "${GREEN}✅ Proxy started with PID: ${proxy_pid}${NC}"
    
    # Wait for proxy to be ready
    if ! wait_for_service $PROXY_HTTP_PORT "Proxy"; then
        echo -e "${RED}❌ Proxy failed to start${NC}"
        return 1
    fi
}

# Main execution
main() {
    echo -e "${BLUE}🔍 Step 1: Checking port availability${NC}"
    check_port $BACKEND_PORT "Backend" || exit 1
    check_port $FRONTEND_PORT "Frontend" || exit 1
    check_port $PROXY_HTTP_PORT "Proxy HTTP" || exit 1
    check_port $PROXY_HTTPS_PORT "Proxy HTTPS" || exit 1
    
    echo -e "\n${BLUE}🔧 Step 2: Starting Backend Service${NC}"
    start_backend || exit 1
    
    echo -e "\n${BLUE}🎨 Step 3: Starting Frontend Service${NC}"
    start_frontend || exit 1
    
    echo -e "\n${BLUE}🌐 Step 4: Starting Proxy Server${NC}"
    start_proxy || exit 1
    
    echo -e "\n${GREEN}🎉 All services started successfully!${NC}"
    echo -e "${GREEN}================================${NC}"
    echo -e "🔗 Application URL: ${CYAN}http://localhost:${PROXY_HTTP_PORT}${NC}"
    echo -e "🔗 Development URL: ${CYAN}http://dev.trusthansen.dk:${PROXY_HTTP_PORT}${NC}"
    echo -e "🔗 Backend API: ${CYAN}http://localhost:${BACKEND_PORT}/api${NC}"
    echo -e "🔗 Frontend Dev: ${CYAN}http://localhost:${FRONTEND_PORT}${NC}"
    echo -e "📊 Health Check: ${CYAN}http://localhost:${PROXY_HTTP_PORT}/health${NC}"
    echo -e "📝 Logs: ${CYAN}${LOG_DIR}/${NC}"
    echo ""
    echo -e "${YELLOW}💡 To stop all services, run: ./stop-ehrx.sh${NC}"
}

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Run main function
main "$@"
