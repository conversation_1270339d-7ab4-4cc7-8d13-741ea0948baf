#!/bin/bash

echo "🚀 Starting eHRx Proxy Server on standard ports (80 and 443)"
echo "⚠️  This requires sudo privileges to bind to ports 80 and 443"
echo ""

# Check if we have the required files
if [ ! -f "demo-server.js" ]; then
    echo "❌ demo-server.js not found in current directory"
    exit 1
fi

if [ ! -f "server.key" ] || [ ! -f "server.crt" ]; then
    echo "❌ SSL certificate files not found"
    echo "   Please make sure server.key and server.crt exist"
    exit 1
fi

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script must be run with sudo privileges"
    echo "   Usage: sudo ./start-proxy-standard-ports.sh"
    echo ""
    echo "🔄 Attempting to restart with sudo..."
    exec sudo "$0" "$@"
fi

# Kill any existing processes on ports 80 and 443
echo "🔄 Checking for existing processes on ports 80 and 443..."
fuser -k 80/tcp 2>/dev/null || true
fuser -k 443/tcp 2>/dev/null || true

# Set environment variables for standard ports
export HTTP_PORT=80
export HTTPS_PORT=443

# Start the proxy server
echo "🔄 Starting proxy server on standard ports..."
echo "   HTTP: http://dev.trusthansen.dk/"
echo "   HTTPS: https://dev.trusthansen.dk/"
echo ""

node demo-server.js
