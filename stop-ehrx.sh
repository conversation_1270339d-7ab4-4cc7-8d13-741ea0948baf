#!/bin/bash

# 🛑 eHRx Unified Stop Script
# This script safely stops all eHRx services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOG_DIR="/var/log/ehrx"

echo -e "${BLUE}🛑 eHRx Service Shutdown${NC}"
echo -e "${BLUE}========================${NC}"

# Function to stop service by PID file
stop_service() {
    local service_name=$1
    local pid_file="$LOG_DIR/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "${YELLOW}🛑 Stopping ${service_name} (PID: ${pid})...${NC}"
            kill -TERM "$pid" 2>/dev/null || true
            
            # Wait for graceful shutdown
            local attempts=0
            while kill -0 "$pid" 2>/dev/null && [ $attempts -lt 10 ]; do
                sleep 1
                ((attempts++))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "${RED}⚠️  Force killing ${service_name}...${NC}"
                kill -KILL "$pid" 2>/dev/null || true
            fi
            
            echo -e "${GREEN}✅ ${service_name} stopped${NC}"
        else
            echo -e "${YELLOW}⚠️  ${service_name} PID file exists but process not running${NC}"
        fi
        rm -f "$pid_file"
    else
        echo -e "${YELLOW}⚠️  No PID file found for ${service_name}${NC}"
    fi
}

# Function to kill processes by port
kill_by_port() {
    local port=$1
    local service_name=$2
    
    local pids=$(lsof -Pi :$port -sTCP:LISTEN -t 2>/dev/null || true)
    if [ ! -z "$pids" ]; then
        echo -e "${YELLOW}🛑 Killing processes on port ${port} (${service_name})...${NC}"
        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        sleep 2
        
        # Force kill if still running
        pids=$(lsof -Pi :$port -sTCP:LISTEN -t 2>/dev/null || true)
        if [ ! -z "$pids" ]; then
            echo "$pids" | xargs kill -KILL 2>/dev/null || true
        fi
        echo -e "${GREEN}✅ Port ${port} freed${NC}"
    fi
}

# Stop services by PID files
echo -e "${BLUE}🔍 Stopping services by PID files...${NC}"
stop_service "proxy"
stop_service "backend"
stop_service "frontend"

# Stop services by ports (fallback)
echo -e "\n${BLUE}🔍 Checking and freeing ports...${NC}"
kill_by_port 8080 "Proxy HTTP"
kill_by_port 8443 "Proxy HTTPS"
kill_by_port 4000 "Backend"
kill_by_port 3080 "Frontend"

# Kill any remaining node processes related to eHRx
echo -e "\n${BLUE}🔍 Cleaning up remaining processes...${NC}"
pkill -f "demo-server.js" 2>/dev/null || true
pkill -f "nest start" 2>/dev/null || true
pkill -f "react-scripts start" 2>/dev/null || true

echo -e "\n${GREEN}✅ All eHRx services stopped${NC}"
echo -e "${GREEN}========================${NC}"
echo -e "📝 Logs preserved in: ${LOG_DIR}"
echo -e "🚀 To restart services, run: ./start-ehrx.sh"
