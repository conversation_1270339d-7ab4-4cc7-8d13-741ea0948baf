// Test script to verify logging system functionality
// Run this in browser console to test frontend logging

console.log('🧪 Testing EHRX Logging System...');

// Test 1: Manual error logging
if (window.loggingService) {
  console.log('✅ Logging service found');
  
  // Test error logging
  window.loggingService.logError('Test error message', { 
    testData: 'This is a test error',
    timestamp: new Date(),
    testId: 'test-001'
  }, 'test-component');
  
  // Test warning logging
  window.loggingService.logWarning('Test warning message', {
    testData: 'This is a test warning',
    testId: 'test-002'
  }, 'test-component');
  
  // Test info logging
  window.loggingService.logInfo('Test info message', {
    testData: 'This is a test info log',
    testId: 'test-003'
  }, 'test-component');
  
  // Test user action logging
  window.loggingService.logUserAction('test_button_click', {
    buttonId: 'test-button',
    page: 'test-page',
    testId: 'test-004'
  }, 'test-component');
  
  console.log('✅ Manual logging tests completed');
} else {
  console.error('❌ Logging service not found');
}

// Test 2: Trigger JavaScript error
setTimeout(() => {
  console.log('🧪 Testing JavaScript error capture...');
  try {
    // This will trigger an error that should be captured
    nonExistentFunction();
  } catch (error) {
    console.log('✅ JavaScript error triggered and should be logged');
  }
}, 1000);

// Test 3: Trigger unhandled promise rejection
setTimeout(() => {
  console.log('🧪 Testing unhandled promise rejection...');
  Promise.reject(new Error('Test unhandled promise rejection'));
  console.log('✅ Unhandled promise rejection triggered');
}, 2000);

// Test 4: Test API call logging
setTimeout(async () => {
  console.log('🧪 Testing API call logging...');
  try {
    // This should trigger API logging
    const response = await fetch('/api/logs/health');
    console.log('✅ API call completed, should be logged');
  } catch (error) {
    console.log('✅ API error occurred, should be logged');
  }
}, 3000);

// Test 5: Test performance logging
setTimeout(() => {
  console.log('🧪 Testing performance logging...');
  // Simulate a slow operation
  const start = performance.now();
  for (let i = 0; i < 1000000; i++) {
    Math.random();
  }
  const duration = performance.now() - start;
  
  if (window.loggingService) {
    window.loggingService.logInfo('Performance test completed', {
      operation: 'random_number_generation',
      duration: duration,
      iterations: 1000000,
      testId: 'test-005'
    }, 'performance-test');
  }
  console.log('✅ Performance test completed');
}, 4000);

// Test 6: Get session info
setTimeout(() => {
  if (window.loggingService && window.loggingService.getSessionInfo) {
    const sessionInfo = window.loggingService.getSessionInfo();
    console.log('📊 Session Info:', sessionInfo);
  }
}, 5000);

console.log('🎯 All logging tests scheduled. Check browser network tab and server logs for results.');
console.log('📝 To view logs, visit the Log Management Dashboard in the admin section.');
