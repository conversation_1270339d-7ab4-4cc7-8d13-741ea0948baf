#!/bin/bash

# Test script to verify <PERSON> user creation
echo "🔍 Testing <PERSON> User Creation"
echo "======================================"
echo ""

# Check if backend is running
echo "📡 Checking backend status..."
if curl -s http://localhost:4000/auth/health > /dev/null; then
    echo "✅ Backend is running on port 4000"
else
    echo "❌ Backend is not running. Please start the backend first."
    echo "   Run: cd backend && npm run start:dev"
    exit 1
fi

echo ""

# Test login to get auth token
echo "🔑 Logging in to get authentication token..."
AUTH_RESPONSE=$(curl -s -X POST http://localhost:4000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"AdministratorX123"}')

if echo "$AUTH_RESPONSE" | grep -q "access_token"; then
    TOKEN=$(echo "$AUTH_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "✅ Authentication successful"
else
    echo "❌ Authentication failed"
    echo "📋 Response: $AUTH_RESPONSE"
    exit 1
fi

echo ""

# Search for <PERSON> in users
echo "👤 Searching for Henrik Thomsen in users..."
USERS_RESPONSE=$(curl -s -X GET "http://localhost:4000/users/search/advanced?search=Henrik" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo "📋 Users search response:"
echo "$USERS_RESPONSE" | jq '.' 2>/dev/null || echo "$USERS_RESPONSE"

echo ""

# Get all users to see the complete list
echo "👥 Getting all users..."
ALL_USERS_RESPONSE=$(curl -s -X GET "http://localhost:4000/users" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo "📋 All users response:"
echo "$ALL_USERS_RESPONSE" | jq '.' 2>/dev/null || echo "$ALL_USERS_RESPONSE"

echo ""

# Test the database table endpoint
echo "🗄️ Testing database table endpoint..."
DB_RESPONSE=$(curl -s -X GET "http://localhost:4000/database/tables/users" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json")

echo "📋 Database table response:"
echo "$DB_RESPONSE" | jq '.' 2>/dev/null || echo "$DB_RESPONSE"

echo ""

# Check if Henrik exists in the response
if echo "$USERS_RESPONSE" | grep -q "Henrik"; then
    echo "✅ Henrik Thomsen found in users API"
else
    echo "❌ Henrik Thomsen NOT found in users API"
fi

if echo "$DB_RESPONSE" | grep -q "Henrik"; then
    echo "✅ Henrik Thomsen found in database table API"
else
    echo "❌ Henrik Thomsen NOT found in database table API"
fi

echo ""
echo "🎯 Summary:"
echo "==========="
echo "1. Check if Henrik Thomsen appears in the users search"
echo "2. Check if Henrik Thomsen appears in all users list"
echo "3. Check if Henrik Thomsen appears in database table view"
echo "4. If Henrik is missing, the user creation may have failed"
echo "5. Try refreshing the Settings dashboard after running this test"
