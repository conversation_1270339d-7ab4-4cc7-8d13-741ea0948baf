const bcrypt = require('bcrypt');

async function hashPassword() {
    try {
        const password = 'TestPass123!';
        const hash = await bcrypt.hash(password, 10);
        console.log('Password:', password);
        console.log('Hash:', hash);
        
        // Test the hash
        const isValid = await bcrypt.compare(password, hash);
        console.log('Hash validation:', isValid);
        
        // Test against the existing hash in database
        const existingHash = '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
        const isExistingValid = await bcrypt.compare('TempPass123!', existingHash);
        console.log('Existing hash validation with TempPass123!:', isExistingValid);
        
        // Try some common passwords
        const commonPasswords = ['password', 'secret', 'admin', 'test', 'TestPassword123'];
        for (const pwd of commonPasswords) {
            const isCommonValid = await bcrypt.compare(pwd, existingHash);
            console.log(`Testing "${pwd}":`, isCommonValid);
        }
        
    } catch (error) {
        console.error('Error:', error);
    }
}

hashPassword();
